@echo off

echo ========================================
echo        Allure Report Viewer
echo ========================================

cd /d "%~dp0.."

REM Check test results
if not exist "report\result\temp\*-result.json" (
    echo Error: No test results found
    echo Please run: python run.py play
    pause
    exit /b 1
)

echo Regenerating report...
allure generate ./report/result/temp -o ./report/report --clean

echo Starting server...
cd /d "%~dp0"
start /b http_server.exe -port 5001

echo Waiting for server to start...
timeout /t 3 /nobreak >nul

echo Opening browser...
start http://127.0.0.1:5001

echo.
echo ========================================
echo Report opened! URL: http://127.0.0.1:5001
echo Press any key to stop server...
echo ========================================

pause

echo Stopping server...
taskkill /f /im http_server.exe >nul 2>&1
echo Done!
