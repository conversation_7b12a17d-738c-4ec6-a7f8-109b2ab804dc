#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
打开Allure报告的Python脚本
"""

import os
import sys
import subprocess
import time
import webbrowser
import threading
from pathlib import Path

def check_test_results():
    """检查是否有测试结果"""
    project_root = Path(__file__).parent.parent
    temp_dir = project_root / "report" / "result" / "temp"
    
    if not temp_dir.exists():
        print("错误：未找到测试结果目录")
        print("请先运行：python run.py play")
        return False
    
    result_files = list(temp_dir.glob("*-result.json"))
    if not result_files:
        print("错误：未找到测试结果文件")
        print("请先运行：python run.py play")
        return False
    
    print(f"找到 {len(result_files)} 个测试结果文件")
    return True

def generate_report():
    """生成Allure报告"""
    project_root = Path(__file__).parent.parent
    report_dir = project_root / "report" / "report"

    # 检查是否已有报告
    if report_dir.exists() and (report_dir / "index.html").exists():
        print("发现现有报告，直接使用")
        return True

    print("正在生成Allure报告...")
    os.chdir(project_root)

    # 尝试生成报告
    try:
        cmd = ["allure", "generate", "./report/result/temp", "-o", "./report/report", "--clean"]
        result = subprocess.run(cmd, capture_output=True, text=True)

        if result.returncode == 0:
            print("报告生成成功！")
            return True
        else:
            print(f"allure命令执行失败，尝试其他方法...")
            return try_alternative_report_generation()
    except FileNotFoundError:
        print("未找到allure命令，尝试其他方法...")
        return try_alternative_report_generation()

def try_alternative_report_generation():
    """尝试其他方法生成报告"""
    project_root = Path(__file__).parent.parent
    report_dir = project_root / "report" / "report"

    # 如果已有报告目录，直接使用
    if report_dir.exists() and (report_dir / "index.html").exists():
        print("使用现有报告")
        return True

    print("请先运行测试生成报告：python run.py play")
    return False

def start_server():
    """启动HTTP服务器"""
    report_dir = Path(__file__).parent
    os.chdir(report_dir)
    
    # 停止可能存在的服务器
    try:
        subprocess.run(["taskkill", "/f", "/im", "http_server.exe"], 
                      capture_output=True, check=False)
    except:
        pass
    
    print("启动HTTP服务器...")
    
    # 启动服务器
    process = subprocess.Popen(
        ["http_server.exe", "-port", "5001"],
        stdout=subprocess.DEVNULL,
        stderr=subprocess.DEVNULL
    )
    
    # 等待服务器启动
    time.sleep(3)
    
    return process

def open_browser():
    """打开浏览器"""
    url = "http://127.0.0.1:5001"
    print(f"正在打开浏览器：{url}")
    
    try:
        # 尝试打开浏览器
        webbrowser.open(url, new=2)  # new=2 表示在新标签页中打开
        return True
    except Exception as e:
        print(f"自动打开浏览器失败：{e}")
        print(f"请手动访问：{url}")
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("        Allure报告查看器")
    print("=" * 50)
    
    # 检查测试结果
    if not check_test_results():
        input("按回车键退出...")
        return
    
    # 生成报告
    if not generate_report():
        input("按回车键退出...")
        return
    
    # 启动服务器
    server_process = start_server()
    
    # 打开浏览器
    open_browser()
    
    print("\n" + "=" * 50)
    print("报告服务器已启动！")
    print("访问地址：http://127.0.0.1:5001")
    print("=" * 50)
    print("按回车键停止服务器...")
    
    try:
        input()
    except KeyboardInterrupt:
        pass
    
    # 停止服务器
    print("正在停止服务器...")
    try:
        server_process.terminate()
        subprocess.run(["taskkill", "/f", "/im", "http_server.exe"], 
                      capture_output=True, check=False)
    except:
        pass
    
    print("服务器已停止")

if __name__ == "__main__":
    main()
