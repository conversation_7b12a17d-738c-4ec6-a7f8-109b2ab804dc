{"uid": "fa58df2050a135fa", "name": "演出项目详情", "fullName": "testcase.play.test_play.TestPlay#test_play", "historyId": "0457c216d6185e839b1ab99163c7bbc6", "time": {"start": 1751866429468, "stop": 1751866430073, "duration": 605}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "capture_logs_for_allure", "time": {"start": 1751866428716, "stop": 1751866428716, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "print_info", "time": {"start": 1751866429467, "stop": 1751866429468, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "clear_extract", "time": {"start": 1751866415419, "stop": 1751866415726, "duration": 307}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "_session_faker", "time": {"start": 1751866415384, "stop": 1751866415419, "duration": 35}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "function_wait", "time": {"start": 1751866428716, "stop": 1751866429467, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "47cb390f58a4094b", "name": "接口地址", "source": "47cb390f58a4094b.json", "type": "application/json", "size": 51}, {"uid": "34d0c4e96e9bf902", "name": "接口名称", "source": "34d0c4e96e9bf902.json", "type": "application/json", "size": 18}, {"uid": "8c4ab142d0e724fa", "name": "请求方式", "source": "8c4ab142d0e724fa.json", "type": "application/json", "size": 3}, {"uid": "c6cf5c55cbe4093c", "name": "请求头", "source": "c6cf5c55cbe4093c.json", "type": "application/json", "size": 122}, {"uid": "33a5623ca75c3307", "name": "<PERSON><PERSON>", "source": "33a5623ca75c3307.json", "type": "application/json", "size": 12}, {"uid": "b05c85da688f58a3", "name": "测试用例名称", "source": "b05c85da688f58a3.json", "type": "application/json", "size": 30}, {"uid": "75fcc61ae122b1bb", "name": "参数类型", "source": "75fcc61ae122b1bb.json", "type": "application/json", "size": 6}, {"uid": "74020ba3c9e2981c", "name": "请求参数json格式", "source": "74020ba3c9e2981c.json", "type": "application/json", "size": 206}, {"uid": "10478e5baa90d3eb", "name": "请求参数实际入参", "source": "10478e5baa90d3eb.json", "type": "application/json", "size": 216}, {"uid": "d9e5b2b0b3b42aa9", "name": "接口实际响应信息", "source": "d9e5b2b0b3b42aa9.json", "type": "application/json", "size": 4313}, {"uid": "5057e2f39e3bdd28", "name": "状态码断言结果：成功", "source": "5057e2f39e3bdd28.txt", "type": "text/plain", "size": 37}, {"uid": "487d1627315fb200", "name": "相等断言结果：成功", "source": "487d1627315fb200.json", "type": "application/json", "size": 53}, {"uid": "950c179ffcbdd9", "name": "📋 测试执行日志", "source": "950c179ffcbdd9.txt", "type": "text/plain", "size": 14929}], "parameters": [], "hasContent": true, "stepsCount": 0, "attachmentsCount": 13, "shouldDisplayMessage": false}, "afterStages": [{"name": "capture_logs_for_allure::0", "time": {"start": 1751866430826, "stop": 1751866430840, "duration": 14}, "status": "passed", "steps": [], "attachments": [{"uid": "c5499c7d664442aa", "name": "📋 测试执行日志 (完整记录)", "source": "c5499c7d664442aa.txt", "type": "text/plain", "size": 5822}], "parameters": [], "hasContent": true, "stepsCount": 0, "attachmentsCount": 1, "shouldDisplayMessage": false}, {"name": "print_info::0", "time": {"start": 1751866430074, "stop": 1751866430074, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "function_wait::0", "time": {"start": 1751866430075, "stop": 1751866430825, "duration": 750}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}], "labels": [{"name": "story", "value": "C01_演出模块流程"}, {"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "16848-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "fa58df2050a135fa.json", "parameterValues": []}