{"uid": "63894d3012b5b5e8", "name": "查询影讯接口", "fullName": "testcase.demo_test.test_demo.TestFilmOrder#test_film_order", "historyId": "e9c2ea8fd9c81240c2b50d2a9668daf2", "time": {"start": 1751853256667, "stop": 1751853256971, "duration": 304}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "function_wait", "time": {"start": 1751853255916, "stop": 1751853256667, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "capture_logs_for_allure", "time": {"start": 1751853255916, "stop": 1751853255916, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "_session_faker", "time": {"start": 1751853229094, "stop": 1751853229142, "duration": 48}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "clear_extract", "time": {"start": 1751853229143, "stop": 1751853229413, "duration": 270}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "print_info", "time": {"start": 1751853256667, "stop": 1751853256667, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "315d0d29d407b7c4", "name": "接口地址", "source": "315d0d29d407b7c4.json", "type": "application/json", "size": 48}, {"uid": "85825d82255afe07", "name": "接口名称", "source": "85825d82255afe07.json", "type": "application/json", "size": 18}, {"uid": "1c5a24e2ca72ab3c", "name": "请求方式", "source": "1c5a24e2ca72ab3c.json", "type": "application/json", "size": 4}, {"uid": "1ea165c6b90ee262", "name": "请求头", "source": "1ea165c6b90ee262.json", "type": "application/json", "size": 15}, {"uid": "dee34d269a005317", "name": "<PERSON><PERSON>", "source": "dee34d269a005317.json", "type": "application/json", "size": 12}, {"uid": "871863771c724ef9", "name": "测试用例名称", "source": "871863771c724ef9.json", "type": "application/json", "size": 27}, {"uid": "d49969427fa35672", "name": "参数类型", "source": "d49969427fa35672.json", "type": "application/json", "size": 4}, {"uid": "413528e0e75f834a", "name": "请求参数json格式", "source": "413528e0e75f834a.json", "type": "application/json", "size": 493}, {"uid": "2f0cad87ce811c02", "name": "请求参数实际入参", "source": "2f0cad87ce811c02.json", "type": "application/json", "size": 436}, {"uid": "a54f3cb0e556dc8b", "name": "接口实际响应信息", "source": "a54f3cb0e556dc8b.json", "type": "application/json", "size": 52017}, {"uid": "e654be17c8af4cb9", "name": "状态码断言结果：成功", "source": "e654be17c8af4cb9.txt", "type": "text/plain", "size": 37}, {"uid": "6d8407621c1817f4", "name": "相等断言结果：成功", "source": "6d8407621c1817f4.json", "type": "application/json", "size": 53}, {"uid": "e9e2b7e231deb53d", "name": "相等断言结果：成功", "source": "e9e2b7e231deb53d.json", "type": "application/json", "size": 77}, {"uid": "e9da9951d532353e", "name": "📋 测试执行日志", "source": "e9da9951d532353e.txt", "type": "text/plain", "size": 2518}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 14, "hasContent": true, "stepsCount": 0}, "afterStages": [{"name": "function_wait::0", "time": {"start": 1751853256972, "stop": 1751853257722, "duration": 750}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "capture_logs_for_allure::0", "time": {"start": 1751853257723, "stop": 1751853257733, "duration": 10}, "status": "passed", "steps": [], "attachments": [{"uid": "82e7b288ef8501a", "name": "📋 测试执行日志 (完整记录)", "source": "82e7b288ef8501a.txt", "type": "text/plain", "size": 30264}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 1, "hasContent": true, "stepsCount": 0}, {"name": "print_info::0", "time": {"start": 1751853256971, "stop": 1751853256971, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}], "labels": [{"name": "feature", "value": "M02_电影订票模块接口测试"}, {"name": "story", "value": "C02_电影下单流程"}, {"name": "parentSuite", "value": "testcase.demo_test"}, {"name": "suite", "value": "test_demo"}, {"name": "subSuite", "value": "TestFilmOrder"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "28812-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.demo_test.test_demo"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "63894d3012b5b5e8.json", "parameterValues": []}