{"uid": "4386f61b3677d8d8", "name": "根据场次获取取票方式", "fullName": "testcase.play.test_play.TestPlay#test_play", "historyId": "169108c6786ad08586d73174286032ec", "time": {"start": 1751853011078, "stop": 1751853011579, "duration": 501}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "print_info", "time": {"start": 1751853011078, "stop": 1751853011078, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "clear_extract", "time": {"start": 1751852990929, "stop": 1751852991206, "duration": 277}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "_session_faker", "time": {"start": 1751852990879, "stop": 1751852990927, "duration": 48}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "capture_logs_for_allure", "time": {"start": 1751853010327, "stop": 1751853010327, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "function_wait", "time": {"start": 1751853010327, "stop": 1751853011078, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "9d6bb666eaa5d87e", "name": "接口地址", "source": "9d6bb666eaa5d87e.json", "type": "application/json", "size": 57}, {"uid": "1990394d0b2ad084", "name": "接口名称", "source": "1990394d0b2ad084.json", "type": "application/json", "size": 30}, {"uid": "9782f8dc699053c3", "name": "请求方式", "source": "9782f8dc699053c3.json", "type": "application/json", "size": 3}, {"uid": "158df05294d3518f", "name": "请求头", "source": "158df05294d3518f.json", "type": "application/json", "size": 122}, {"uid": "27aaac83e2c15af", "name": "<PERSON><PERSON>", "source": "27aaac83e2c15af.json", "type": "application/json", "size": 12}, {"uid": "51cae31e2cdaf49b", "name": "测试用例名称", "source": "51cae31e2cdaf49b.json", "type": "application/json", "size": 30}, {"uid": "3e31866d711519f4", "name": "参数类型", "source": "3e31866d711519f4.json", "type": "application/json", "size": 6}, {"uid": "10f609473255191c", "name": "请求参数json格式", "source": "10f609473255191c.json", "type": "application/json", "size": 199}, {"uid": "3e4e84253c4b0b43", "name": "请求参数实际入参", "source": "3e4e84253c4b0b43.json", "type": "application/json", "size": 209}, {"uid": "f790f9d9a546807b", "name": "接口实际响应信息", "source": "f790f9d9a546807b.json", "type": "application/json", "size": 599}, {"uid": "1349ba910b8f5e8b", "name": "状态码断言结果：成功", "source": "1349ba910b8f5e8b.txt", "type": "text/plain", "size": 37}, {"uid": "7cf4b49a895afbe9", "name": "相等断言结果：成功", "source": "7cf4b49a895afbe9.json", "type": "application/json", "size": 53}, {"uid": "3c0bc437534b5f91", "name": "📋 测试执行日志", "source": "3c0bc437534b5f91.txt", "type": "text/plain", "size": 4692}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 13, "hasContent": true, "stepsCount": 0}, "afterStages": [{"name": "print_info::0", "time": {"start": 1751853011580, "stop": 1751853011580, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "capture_logs_for_allure::0", "time": {"start": 1751853012332, "stop": 1751853012345, "duration": 13}, "status": "passed", "steps": [], "attachments": [{"uid": "e50cb06e749c967f", "name": "📋 测试执行日志 (完整记录)", "source": "e50cb06e749c967f.txt", "type": "text/plain", "size": 2377}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 1, "hasContent": true, "stepsCount": 0}, {"name": "function_wait::0", "time": {"start": 1751853011580, "stop": 1751853012331, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}], "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "37476-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [{"name": "api_info", "value": "{'baseInfo': {'module': '演出票', 'api_name': '根据场次获取取票方式', 'host': '${get_host(play)}', 'url': '/api/show/info/show_fetch_ticket_ways', 'method': 'get', 'headers': '${get_headers(app)}'}, 'testCase': {'case_name': '获取该场次的取票方式', 'validation': [{'code': 200}, {'eq': {'code': 0}}], 'extract': {'fetchTicketWayId': '$..fetchTicketWayId', 'fetchType': '$..fetchType'}, 'extract_list': np.float64(0.0), 'params': {'showId': '${get_extract_data(showInfo,showId)}', 'channelCode': '${appKey()}', 'token': '${get_extract_data(token)}'}}}"}], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "4386f61b3677d8d8.json", "parameterValues": ["{'baseInfo': {'module': '演出票', 'api_name': '根据场次获取取票方式', 'host': '${get_host(play)}', 'url': '/api/show/info/show_fetch_ticket_ways', 'method': 'get', 'headers': '${get_headers(app)}'}, 'testCase': {'case_name': '获取该场次的取票方式', 'validation': [{'code': 200}, {'eq': {'code': 0}}], 'extract': {'fetchTicketWayId': '$..fetchTicketWayId', 'fetchType': '$..fetchType'}, 'extract_list': np.float64(0.0), 'params': {'showId': '${get_extract_data(showInfo,showId)}', 'channelCode': '${appKey()}', 'token': '${get_extract_data(token)}'}}}"]}