{"uid": "f9239699f36211e", "name": "演出场次列表", "fullName": "testcase.play.test_play.TestPlay#test_play", "historyId": "6b8675e1b81e5293918eed8f21bfedcf", "time": {"start": 1751853006939, "stop": 1751853007470, "duration": 531}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "print_info", "time": {"start": 1751853006937, "stop": 1751853006937, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "capture_logs_for_allure", "time": {"start": 1751853006185, "stop": 1751853006185, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "clear_extract", "time": {"start": 1751852990929, "stop": 1751852991206, "duration": 277}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "_session_faker", "time": {"start": 1751852990879, "stop": 1751852990927, "duration": 48}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "function_wait", "time": {"start": 1751853006185, "stop": 1751853006936, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "a049cba84b260a8a", "name": "接口地址", "source": "a049cba84b260a8a.json", "type": "application/json", "size": 52}, {"uid": "5c564e1f809faf9c", "name": "接口名称", "source": "5c564e1f809faf9c.json", "type": "application/json", "size": 18}, {"uid": "2b9a6c05b3120563", "name": "请求方式", "source": "2b9a6c05b3120563.json", "type": "application/json", "size": 3}, {"uid": "1bef40ddde41064c", "name": "请求头", "source": "1bef40ddde41064c.json", "type": "application/json", "size": 122}, {"uid": "68e3551a4e970fce", "name": "<PERSON><PERSON>", "source": "68e3551a4e970fce.json", "type": "application/json", "size": 12}, {"uid": "4bdaa631fbe9d99e", "name": "测试用例名称", "source": "4bdaa631fbe9d99e.json", "type": "application/json", "size": 30}, {"uid": "83615d24831beaaa", "name": "参数类型", "source": "83615d24831beaaa.json", "type": "application/json", "size": 6}, {"uid": "c3e6f548bb8e3ba5", "name": "请求参数json格式", "source": "c3e6f548bb8e3ba5.json", "type": "application/json", "size": 206}, {"uid": "f5f1dbed5eb921b5", "name": "请求参数实际入参", "source": "f5f1dbed5eb921b5.json", "type": "application/json", "size": 216}, {"uid": "33f7b6f9a83c4a8d", "name": "接口实际响应信息", "source": "33f7b6f9a83c4a8d.json", "type": "application/json", "size": 1528}, {"uid": "c353966c1d2a9cae", "name": "状态码断言结果：成功", "source": "c353966c1d2a9cae.txt", "type": "text/plain", "size": 37}, {"uid": "4d00dd98d22f1fb7", "name": "相等断言结果：成功", "source": "4d00dd98d22f1fb7.json", "type": "application/json", "size": 53}, {"uid": "bc0f9c58459c6d59", "name": "📋 测试执行日志", "source": "bc0f9c58459c6d59.txt", "type": "text/plain", "size": 5822}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 13, "hasContent": true, "stepsCount": 0}, "afterStages": [{"name": "print_info::0", "time": {"start": 1751853007471, "stop": 1751853007471, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "capture_logs_for_allure::0", "time": {"start": 1751853008223, "stop": 1751853008234, "duration": 11}, "status": "passed", "steps": [], "attachments": [{"uid": "84ff6fd8af167931", "name": "📋 测试执行日志 (完整记录)", "source": "84ff6fd8af167931.txt", "type": "text/plain", "size": 2892}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 1, "hasContent": true, "stepsCount": 0}, {"name": "function_wait::0", "time": {"start": 1751853007472, "stop": 1751853008223, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}], "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "37476-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [{"name": "api_info", "value": "{'baseInfo': {'module': '演出票', 'api_name': '演出场次列表', 'host': '${get_host(play)}', 'url': '/api/show/info/performance_shows', 'method': 'get', 'headers': '${get_headers(app)}'}, 'testCase': {'case_name': '获取演出场次列表信息', 'validation': [{'code': 200}, {'eq': {'code': 0}}], 'extract': {'extract_auto': 'performance_shows'}, 'extract_list': np.float64(0.0), 'params': {'performanceId': '${get_extract_data(performance_id)}', 'channelCode': '${appKey()}', 'token': '${get_extract_data(token)}'}}}"}], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "f9239699f36211e.json", "parameterValues": ["{'baseInfo': {'module': '演出票', 'api_name': '演出场次列表', 'host': '${get_host(play)}', 'url': '/api/show/info/performance_shows', 'method': 'get', 'headers': '${get_headers(app)}'}, 'testCase': {'case_name': '获取演出场次列表信息', 'validation': [{'code': 200}, {'eq': {'code': 0}}], 'extract': {'extract_auto': 'performance_shows'}, 'extract_list': np.float64(0.0), 'params': {'performanceId': '${get_extract_data(performance_id)}', 'channelCode': '${appKey()}', 'token': '${get_extract_data(token)}'}}}"]}