{"uid": "50683e0ded2155ab", "name": "今日必抢", "fullName": "testcase.play.test_play.TestPlay#test_play", "historyId": "09729bd43bb947d1ebe3c16a80dc6e25", "time": {"start": 1751853888634, "stop": 1751853888938, "duration": 304}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "function_wait", "time": {"start": 1751853887883, "stop": 1751853888634, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "print_info", "time": {"start": 1751853888634, "stop": 1751853888634, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "capture_logs_for_allure", "time": {"start": 1751853887883, "stop": 1751853887883, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "clear_extract", "time": {"start": 1751853884946, "stop": 1751853885997, "duration": 1051}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "_session_faker", "time": {"start": 1751853884907, "stop": 1751853884946, "duration": 39}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "6475d77fb5b10c8b", "name": "接口地址", "source": "6475d77fb5b10c8b.json", "type": "application/json", "size": 55}, {"uid": "fbcfca64c60155ae", "name": "接口名称", "source": "fbcfca64c60155ae.json", "type": "application/json", "size": 12}, {"uid": "37b9ae612474a5f8", "name": "请求方式", "source": "37b9ae612474a5f8.json", "type": "application/json", "size": 4}, {"uid": "13014814bca752a4", "name": "请求头", "source": "13014814bca752a4.json", "type": "application/json", "size": 122}, {"uid": "b2a7e2f76e9d4c00", "name": "<PERSON><PERSON>", "source": "b2a7e2f76e9d4c00.json", "type": "application/json", "size": 12}, {"uid": "3138d3d751ef75f5", "name": "测试用例名称", "source": "3138d3d751ef75f5.json", "type": "application/json", "size": 30}, {"uid": "152e1fc7edee759", "name": "参数类型", "source": "152e1fc7edee759.json", "type": "application/json", "size": 4}, {"uid": "588e404773b9539f", "name": "请求参数json格式", "source": "588e404773b9539f.json", "type": "application/json", "size": 214}, {"uid": "ef960cefb6a925a1", "name": "请求参数实际入参", "source": "ef960cefb6a925a1.json", "type": "application/json", "size": 224}, {"uid": "5e849ef1b35e77a1", "name": "接口实际响应信息", "source": "5e849ef1b35e77a1.json", "type": "application/json", "size": 22188}, {"uid": "c5252d7d64196a70", "name": "状态码断言结果：成功", "source": "c5252d7d64196a70.txt", "type": "text/plain", "size": 37}, {"uid": "f7443345ed09372c", "name": "相等断言结果：成功", "source": "f7443345ed09372c.json", "type": "application/json", "size": 53}, {"uid": "ce245baad58f61da", "name": "📋 测试执行日志", "source": "ce245baad58f61da.txt", "type": "text/plain", "size": 4327}], "parameters": [], "hasContent": true, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 13}, "afterStages": [{"name": "function_wait::0", "time": {"start": 1751853888939, "stop": 1751853889690, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "print_info::0", "time": {"start": 1751853888938, "stop": 1751853888939, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "capture_logs_for_allure::0", "time": {"start": 1751853889690, "stop": 1751853889699, "duration": 9}, "status": "passed", "steps": [], "attachments": [{"uid": "a53a084013b0292d", "name": "📋 测试执行日志 (完整记录)", "source": "a53a084013b0292d.txt", "type": "text/plain", "size": 15249}], "parameters": [], "hasContent": true, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 1}], "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "39132-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "50683e0ded2155ab.json", "parameterValues": []}