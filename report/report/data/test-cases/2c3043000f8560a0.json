{"uid": "2c3043000f8560a0", "name": "演出项目详情", "fullName": "testcase.play.test_play.TestPlay#test_play", "historyId": "0457c216d6185e839b1ab99163c7bbc6", "time": {"start": 1751853899563, "stop": 1751853900106, "duration": 543}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "capture_logs_for_allure", "time": {"start": 1751853898812, "stop": 1751853898812, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "print_info", "time": {"start": 1751853899563, "stop": 1751853899563, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "clear_extract", "time": {"start": 1751853884946, "stop": 1751853885997, "duration": 1051}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "_session_faker", "time": {"start": 1751853884907, "stop": 1751853884946, "duration": 39}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "function_wait", "time": {"start": 1751853898812, "stop": 1751853899563, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "ac12b94dcdb7fb6", "name": "接口地址", "source": "ac12b94dcdb7fb6.json", "type": "application/json", "size": 51}, {"uid": "4110367daa3c4274", "name": "接口名称", "source": "4110367daa3c4274.json", "type": "application/json", "size": 18}, {"uid": "852d6a62adb10049", "name": "请求方式", "source": "852d6a62adb10049.json", "type": "application/json", "size": 3}, {"uid": "f4fca8d7bf73b578", "name": "请求头", "source": "f4fca8d7bf73b578.json", "type": "application/json", "size": 122}, {"uid": "e73d7044549639e9", "name": "<PERSON><PERSON>", "source": "e73d7044549639e9.json", "type": "application/json", "size": 12}, {"uid": "e407eeefb0496600", "name": "测试用例名称", "source": "e407eeefb0496600.json", "type": "application/json", "size": 30}, {"uid": "19997cea94105483", "name": "参数类型", "source": "19997cea94105483.json", "type": "application/json", "size": 6}, {"uid": "ac870aa38e4d6cad", "name": "请求参数json格式", "source": "ac870aa38e4d6cad.json", "type": "application/json", "size": 206}, {"uid": "991be0cb18586951", "name": "请求参数实际入参", "source": "991be0cb18586951.json", "type": "application/json", "size": 216}, {"uid": "5096a0da144bf47f", "name": "接口实际响应信息", "source": "5096a0da144bf47f.json", "type": "application/json", "size": 4313}, {"uid": "c2eaa4fc48313b73", "name": "状态码断言结果：成功", "source": "c2eaa4fc48313b73.txt", "type": "text/plain", "size": 37}, {"uid": "e1e74ad833182010", "name": "相等断言结果：成功", "source": "e1e74ad833182010.json", "type": "application/json", "size": 53}, {"uid": "eddab2b2014cd41f", "name": "📋 测试执行日志", "source": "eddab2b2014cd41f.txt", "type": "text/plain", "size": 15160}], "parameters": [], "hasContent": true, "stepsCount": 0, "attachmentsCount": 13, "shouldDisplayMessage": false}, "afterStages": [{"name": "capture_logs_for_allure::0", "time": {"start": 1751853900859, "stop": 1751853900875, "duration": 16}, "status": "passed", "steps": [], "attachments": [{"uid": "7202e38e17e94834", "name": "📋 测试执行日志 (完整记录)", "source": "7202e38e17e94834.txt", "type": "text/plain", "size": 6363}], "parameters": [], "hasContent": true, "stepsCount": 0, "attachmentsCount": 1, "shouldDisplayMessage": false}, {"name": "print_info::0", "time": {"start": 1751853900107, "stop": 1751853900107, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "function_wait::0", "time": {"start": 1751853900108, "stop": 1751853900859, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}], "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "39132-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "2c3043000f8560a0.json", "parameterValues": []}