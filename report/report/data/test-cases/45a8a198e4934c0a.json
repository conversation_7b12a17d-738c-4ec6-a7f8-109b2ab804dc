{"uid": "45a8a198e4934c0a", "name": "演唱会分类", "fullName": "testcase.play.test_play.TestPlay#test_play", "historyId": "47b52c96aa0c1d59c25f4bc63959c980", "time": {"start": 1751853895908, "stop": 1751853896233, "duration": 325}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "print_info", "time": {"start": 1751853895908, "stop": 1751853895908, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "function_wait", "time": {"start": 1751853895158, "stop": 1751853895908, "duration": 750}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "capture_logs_for_allure", "time": {"start": 1751853895157, "stop": 1751853895158, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "clear_extract", "time": {"start": 1751853884946, "stop": 1751853885997, "duration": 1051}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "_session_faker", "time": {"start": 1751853884907, "stop": 1751853884946, "duration": 39}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "61a6992457654532", "name": "接口地址", "source": "61a6992457654532.json", "type": "application/json", "size": 53}, {"uid": "dd8161c0bf5f25f7", "name": "接口名称", "source": "dd8161c0bf5f25f7.json", "type": "application/json", "size": 15}, {"uid": "99ff276eabdaa1e3", "name": "请求方式", "source": "99ff276eabdaa1e3.json", "type": "application/json", "size": 4}, {"uid": "1003d4013d9d0780", "name": "请求头", "source": "1003d4013d9d0780.json", "type": "application/json", "size": 122}, {"uid": "98212897a611a868", "name": "<PERSON><PERSON>", "source": "98212897a611a868.json", "type": "application/json", "size": 12}, {"uid": "721bd96fb3e0d59", "name": "测试用例名称", "source": "721bd96fb3e0d59.json", "type": "application/json", "size": 25}, {"uid": "8b1a63312773110c", "name": "参数类型", "source": "8b1a63312773110c.json", "type": "application/json", "size": 4}, {"uid": "37ec61793d72a941", "name": "请求参数json格式", "source": "37ec61793d72a941.json", "type": "application/json", "size": 207}, {"uid": "933f81e15a62932c", "name": "请求参数实际入参", "source": "933f81e15a62932c.json", "type": "application/json", "size": 217}, {"uid": "39439c51bbd09feb", "name": "接口实际响应信息", "source": "39439c51bbd09feb.json", "type": "application/json", "size": 22018}, {"uid": "75a67fce1a77cc52", "name": "状态码断言结果：成功", "source": "75a67fce1a77cc52.txt", "type": "text/plain", "size": 37}, {"uid": "6b47c4a7ab90286e", "name": "相等断言结果：成功", "source": "6b47c4a7ab90286e.json", "type": "application/json", "size": 53}, {"uid": "4d597daeeca1df73", "name": "📋 测试执行日志", "source": "4d597daeeca1df73.txt", "type": "text/plain", "size": 4779}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentsCount": 13, "shouldDisplayMessage": false}, "afterStages": [{"name": "print_info::0", "time": {"start": 1751853896234, "stop": 1751853896234, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "function_wait::0", "time": {"start": 1751853896235, "stop": 1751853896985, "duration": 750}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "capture_logs_for_allure::0", "time": {"start": 1751853896985, "stop": 1751853896995, "duration": 10}, "status": "passed", "steps": [], "attachments": [{"uid": "5d5c8278658f1139", "name": "📋 测试执行日志 (完整记录)", "source": "5d5c8278658f1139.txt", "type": "text/plain", "size": 14921}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentsCount": 1, "shouldDisplayMessage": false}], "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "39132-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "45a8a198e4934c0a.json", "parameterValues": []}