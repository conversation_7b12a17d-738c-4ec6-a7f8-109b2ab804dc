{"uid": "66f5263cb0898209", "name": "下单", "fullName": "testcase.play.test_play.TestPlay#test_play", "historyId": "df78d83b2c7879c3a2ecdde06190a07c", "time": {"start": 1751853907768, "stop": 1751853909033, "duration": 1265}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "function_wait", "time": {"start": 1751853907017, "stop": 1751853907767, "duration": 750}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "clear_extract", "time": {"start": 1751853884946, "stop": 1751853885997, "duration": 1051}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "print_info", "time": {"start": 1751853907767, "stop": 1751853907767, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "capture_logs_for_allure", "time": {"start": 1751853907017, "stop": 1751853907017, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "_session_faker", "time": {"start": 1751853884907, "stop": 1751853884946, "duration": 39}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "2d4458a596d9d451", "name": "接口地址", "source": "2d4458a596d9d451.json", "type": "application/json", "size": 42}, {"uid": "c60c13c83834902a", "name": "接口名称", "source": "c60c13c83834902a.json", "type": "application/json", "size": 6}, {"uid": "f64f909775175314", "name": "请求方式", "source": "f64f909775175314.json", "type": "application/json", "size": 4}, {"uid": "727ab19fdd195296", "name": "请求头", "source": "727ab19fdd195296.json", "type": "application/json", "size": 122}, {"uid": "32fab9831ec50e60", "name": "<PERSON><PERSON>", "source": "32fab9831ec50e60.json", "type": "application/json", "size": 12}, {"uid": "c14ec8024b6a4b34", "name": "测试用例名称", "source": "c14ec8024b6a4b34.json", "type": "application/json", "size": 12}, {"uid": "8de3e3b5844736d9", "name": "参数类型", "source": "8de3e3b5844736d9.json", "type": "application/json", "size": 4}, {"uid": "b0baacdccf32f6c5", "name": "请求参数json格式", "source": "b0baacdccf32f6c5.json", "type": "application/json", "size": 615}, {"uid": "fc027dc778d4ae35", "name": "请求参数实际入参", "source": "fc027dc778d4ae35.json", "type": "application/json", "size": 625}, {"uid": "e7c717794a67a1be", "name": "接口实际响应信息", "source": "e7c717794a67a1be.json", "type": "application/json", "size": 94}, {"uid": "5957dc70e0c38c53", "name": "状态码断言结果：成功", "source": "5957dc70e0c38c53.txt", "type": "text/plain", "size": 37}, {"uid": "3a9dc23f75075e83", "name": "相等断言结果：成功", "source": "3a9dc23f75075e83.json", "type": "application/json", "size": 53}, {"uid": "e289c8487393aea9", "name": "📋 测试执行日志", "source": "e289c8487393aea9.txt", "type": "text/plain", "size": 2608}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentsCount": 13, "shouldDisplayMessage": false}, "afterStages": [{"name": "function_wait::0", "time": {"start": 1751853909034, "stop": 1751853909784, "duration": 750}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "print_info::0", "time": {"start": 1751853909033, "stop": 1751853909033, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "capture_logs_for_allure::0", "time": {"start": 1751853909784, "stop": 1751853909794, "duration": 10}, "status": "passed", "steps": [], "attachments": [{"uid": "f09538cf852ca73c", "name": "📋 测试执行日志 (完整记录)", "source": "f09538cf852ca73c.txt", "type": "text/plain", "size": 2968}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentsCount": 1, "shouldDisplayMessage": false}], "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "39132-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "66f5263cb0898209.json", "parameterValues": []}