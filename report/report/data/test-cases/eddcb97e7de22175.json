{"uid": "eddcb97e7de22175", "name": "首页广告位", "fullName": "testcase.play.test_play.TestPlay#test_play", "historyId": "998f7fb53b18ea3045f71922b591e307", "time": {"start": 1751851573272, "stop": 1751851573608, "duration": 336}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "_session_faker", "time": {"start": 1751851564550, "stop": 1751851564591, "duration": 41}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "print_info", "time": {"start": 1751851573271, "stop": 1751851573272, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "clear_extract", "time": {"start": 1751851564591, "stop": 1751851564922, "duration": 331}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "function_wait", "time": {"start": 1751851572521, "stop": 1751851573271, "duration": 750}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "capture_logs_for_allure", "time": {"start": 1751851572521, "stop": 1751851572521, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "58f8c92921b005ef", "name": "接口地址", "source": "58f8c92921b005ef.json", "type": "application/json", "size": 44}, {"uid": "e20d0edb85b08255", "name": "接口名称", "source": "e20d0edb85b08255.json", "type": "application/json", "size": 15}, {"uid": "45d4e31b1df7d24b", "name": "请求方式", "source": "45d4e31b1df7d24b.json", "type": "application/json", "size": 4}, {"uid": "cf0702d277690ee", "name": "请求头", "source": "cf0702d277690ee.json", "type": "application/json", "size": 122}, {"uid": "eccbd3aa7306e013", "name": "<PERSON><PERSON>", "source": "eccbd3aa7306e013.json", "type": "application/json", "size": 12}, {"uid": "fea4d3b292b49eda", "name": "测试用例名称", "source": "fea4d3b292b49eda.json", "type": "application/json", "size": 24}, {"uid": "5521797cee6cbb3c", "name": "参数类型", "source": "5521797cee6cbb3c.json", "type": "application/json", "size": 4}, {"uid": "4352bdce4e0bdd2b", "name": "请求参数json格式", "source": "4352bdce4e0bdd2b.json", "type": "application/json", "size": 179}, {"uid": "401a07690bcdbb3d", "name": "请求参数实际入参", "source": "401a07690bcdbb3d.json", "type": "application/json", "size": 189}, {"uid": "882a070a8f16ffa0", "name": "接口实际响应信息", "source": "882a070a8f16ffa0.json", "type": "application/json", "size": 3479}, {"uid": "5d3e628407b9b2f6", "name": "状态码断言结果：成功", "source": "5d3e628407b9b2f6.txt", "type": "text/plain", "size": 37}, {"uid": "244828f1fcd77835", "name": "相等断言结果：成功", "source": "244828f1fcd77835.json", "type": "application/json", "size": 53}, {"uid": "3bbb17405b8128ee", "name": "📋 测试执行日志", "source": "3bbb17405b8128ee.txt", "type": "text/plain", "size": 27049}], "parameters": [], "hasContent": true, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 13}, "afterStages": [{"name": "print_info::0", "time": {"start": 1751851573608, "stop": 1751851573609, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "function_wait::0", "time": {"start": 1751851573609, "stop": 1751851574360, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "capture_logs_for_allure::0", "time": {"start": 1751851574361, "stop": 1751851574373, "duration": 12}, "status": "passed", "steps": [], "attachments": [{"uid": "4f17c86755ada062", "name": "📋 测试执行日志 (完整记录)", "source": "4f17c86755ada062.txt", "type": "text/plain", "size": 4549}], "parameters": [], "hasContent": true, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 1}], "labels": [{"name": "story", "value": "C01_演出模块流程"}, {"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "34968-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "eddcb97e7de22175.json", "parameterValues": []}