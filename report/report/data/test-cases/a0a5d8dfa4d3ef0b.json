{"uid": "a0a5d8dfa4d3ef0b", "name": "近期特惠", "fullName": "testcase.play.test_play.TestPlay#test_play", "historyId": "b9dc0ab2757f5b153151bfbd4cc293dd", "time": {"start": 1751853890455, "stop": 1751853890758, "duration": 303}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "function_wait", "time": {"start": 1751853889705, "stop": 1751853890455, "duration": 750}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "clear_extract", "time": {"start": 1751853884946, "stop": 1751853885997, "duration": 1051}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "capture_logs_for_allure", "time": {"start": 1751853889705, "stop": 1751853889705, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "_session_faker", "time": {"start": 1751853884907, "stop": 1751853884946, "duration": 39}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "print_info", "time": {"start": 1751853890455, "stop": 1751853890455, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "78c51da7024ee8a6", "name": "接口地址", "source": "78c51da7024ee8a6.json", "type": "application/json", "size": 54}, {"uid": "887f6ed185cdd310", "name": "接口名称", "source": "887f6ed185cdd310.json", "type": "application/json", "size": 12}, {"uid": "538c1ff1634c9233", "name": "请求方式", "source": "538c1ff1634c9233.json", "type": "application/json", "size": 4}, {"uid": "7922335e289fdb98", "name": "请求头", "source": "7922335e289fdb98.json", "type": "application/json", "size": 122}, {"uid": "22f60f7d47e2ee2a", "name": "<PERSON><PERSON>", "source": "22f60f7d47e2ee2a.json", "type": "application/json", "size": 12}, {"uid": "cbe99342f65d3738", "name": "测试用例名称", "source": "cbe99342f65d3738.json", "type": "application/json", "size": 30}, {"uid": "3725ed0f1ec46e21", "name": "参数类型", "source": "3725ed0f1ec46e21.json", "type": "application/json", "size": 4}, {"uid": "80d536d0f8db8dab", "name": "请求参数json格式", "source": "80d536d0f8db8dab.json", "type": "application/json", "size": 214}, {"uid": "8cc03249328ec82c", "name": "请求参数实际入参", "source": "8cc03249328ec82c.json", "type": "application/json", "size": 224}, {"uid": "36a3a7c8f0d7aee2", "name": "接口实际响应信息", "source": "36a3a7c8f0d7aee2.json", "type": "application/json", "size": 22188}, {"uid": "1d08dbd585a76bf1", "name": "状态码断言结果：成功", "source": "1d08dbd585a76bf1.txt", "type": "text/plain", "size": 37}, {"uid": "ad950449951cba91", "name": "相等断言结果：成功", "source": "ad950449951cba91.json", "type": "application/json", "size": 53}, {"uid": "6f1ca61bac04c5f3", "name": "📋 测试执行日志", "source": "6f1ca61bac04c5f3.txt", "type": "text/plain", "size": 15249}], "parameters": [], "hasContent": true, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 13}, "afterStages": [{"name": "function_wait::0", "time": {"start": 1751853890760, "stop": 1751853891511, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "capture_logs_for_allure::0", "time": {"start": 1751853891511, "stop": 1751853891520, "duration": 9}, "status": "passed", "steps": [], "attachments": [{"uid": "343f42f2fec0f20b", "name": "📋 测试执行日志 (完整记录)", "source": "343f42f2fec0f20b.txt", "type": "text/plain", "size": 14945}], "parameters": [], "hasContent": true, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "print_info::0", "time": {"start": 1751853890759, "stop": 1751853890760, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "39132-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "a0a5d8dfa4d3ef0b.json", "parameterValues": []}