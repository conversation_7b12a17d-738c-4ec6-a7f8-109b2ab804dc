{"uid": "d5340913bcc7ffb5", "name": "项目下场次票品列表", "fullName": "testcase.play.test_play.TestPlay#test_play", "historyId": "3287c72f85d7169cf4925a7d71668b28", "time": {"start": 1751853903726, "stop": 1751853904261, "duration": 535}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "capture_logs_for_allure", "time": {"start": 1751853902975, "stop": 1751853902975, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "clear_extract", "time": {"start": 1751853884946, "stop": 1751853885997, "duration": 1051}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "print_info", "time": {"start": 1751853903725, "stop": 1751853903726, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "_session_faker", "time": {"start": 1751853884907, "stop": 1751853884946, "duration": 39}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "function_wait", "time": {"start": 1751853902975, "stop": 1751853903725, "duration": 750}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "e40859edc5a4eac2", "name": "接口地址", "source": "e40859edc5a4eac2.json", "type": "application/json", "size": 52}, {"uid": "5274c14b63cd6182", "name": "接口名称", "source": "5274c14b63cd6182.json", "type": "application/json", "size": 27}, {"uid": "7e5e25c6129ff004", "name": "请求方式", "source": "7e5e25c6129ff004.json", "type": "application/json", "size": 3}, {"uid": "ad0c676c3f8df033", "name": "请求头", "source": "ad0c676c3f8df033.json", "type": "application/json", "size": 122}, {"uid": "387882f8f57d1b62", "name": "<PERSON><PERSON>", "source": "387882f8f57d1b62.json", "type": "application/json", "size": 12}, {"uid": "b94f9ac4c425227f", "name": "测试用例名称", "source": "b94f9ac4c425227f.json", "type": "application/json", "size": 39}, {"uid": "622bc62715a2f64b", "name": "参数类型", "source": "622bc62715a2f64b.json", "type": "application/json", "size": 6}, {"uid": "5e14840b66cbdcb1", "name": "请求参数json格式", "source": "5e14840b66cbdcb1.json", "type": "application/json", "size": 199}, {"uid": "e07e16db3174a1bf", "name": "请求参数实际入参", "source": "e07e16db3174a1bf.json", "type": "application/json", "size": 209}, {"uid": "f8b48dcf66009f5f", "name": "接口实际响应信息", "source": "f8b48dcf66009f5f.json", "type": "application/json", "size": 7483}, {"uid": "fdc25102ab6037f3", "name": "状态码断言结果：成功", "source": "fdc25102ab6037f3.txt", "type": "text/plain", "size": 37}, {"uid": "b61bb649657e0121", "name": "相等断言结果：成功", "source": "b61bb649657e0121.json", "type": "application/json", "size": 53}, {"uid": "4acee2cd95512630", "name": "📋 测试执行日志", "source": "4acee2cd95512630.txt", "type": "text/plain", "size": 3123}], "parameters": [], "hasContent": true, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 13}, "afterStages": [{"name": "capture_logs_for_allure::0", "time": {"start": 1751853905014, "stop": 1751853905023, "duration": 9}, "status": "passed", "steps": [], "attachments": [{"uid": "388f00b40d948a7e", "name": "📋 测试执行日志 (完整记录)", "source": "388f00b40d948a7e.txt", "type": "text/plain", "size": 5242}], "parameters": [], "hasContent": true, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "print_info::0", "time": {"start": 1751853904262, "stop": 1751853904262, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "function_wait::0", "time": {"start": 1751853904263, "stop": 1751853905014, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "39132-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "d5340913bcc7ffb5.json", "parameterValues": []}