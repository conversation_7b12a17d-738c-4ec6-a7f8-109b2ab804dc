{"uid": "710d86e95f7ef41e", "name": "近期特惠", "fullName": "testcase.play.test_play.TestPlay#test_play", "historyId": "b9dc0ab2757f5b153151bfbd4cc293dd", "time": {"start": 1751853890455, "stop": 1751853890758, "duration": 303}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "function_wait", "time": {"start": 1751853889705, "stop": 1751853890455, "duration": 750}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "clear_extract", "time": {"start": 1751853884946, "stop": 1751853885997, "duration": 1051}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "capture_logs_for_allure", "time": {"start": 1751853889705, "stop": 1751853889705, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "_session_faker", "time": {"start": 1751853884907, "stop": 1751853884946, "duration": 39}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "print_info", "time": {"start": 1751853890455, "stop": 1751853890455, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "f4f66aaf3e744fd", "name": "接口地址", "source": "f4f66aaf3e744fd.json", "type": "application/json", "size": 54}, {"uid": "3b2ca81945b0b01c", "name": "接口名称", "source": "3b2ca81945b0b01c.json", "type": "application/json", "size": 12}, {"uid": "7c3fc671bb6cb7dc", "name": "请求方式", "source": "7c3fc671bb6cb7dc.json", "type": "application/json", "size": 4}, {"uid": "a0885caf0095284f", "name": "请求头", "source": "a0885caf0095284f.json", "type": "application/json", "size": 122}, {"uid": "c4a1855c63233395", "name": "<PERSON><PERSON>", "source": "c4a1855c63233395.json", "type": "application/json", "size": 12}, {"uid": "4faa368e91c99e42", "name": "测试用例名称", "source": "4faa368e91c99e42.json", "type": "application/json", "size": 30}, {"uid": "adbb2591bd387e60", "name": "参数类型", "source": "adbb2591bd387e60.json", "type": "application/json", "size": 4}, {"uid": "c88d9afce6142cee", "name": "请求参数json格式", "source": "c88d9afce6142cee.json", "type": "application/json", "size": 214}, {"uid": "efe99b661ccc1d86", "name": "请求参数实际入参", "source": "efe99b661ccc1d86.json", "type": "application/json", "size": 224}, {"uid": "48a26563ab546575", "name": "接口实际响应信息", "source": "48a26563ab546575.json", "type": "application/json", "size": 22188}, {"uid": "398c9e18b6e26d09", "name": "状态码断言结果：成功", "source": "398c9e18b6e26d09.txt", "type": "text/plain", "size": 37}, {"uid": "60b01b0549d18783", "name": "相等断言结果：成功", "source": "60b01b0549d18783.json", "type": "application/json", "size": 53}, {"uid": "109ef75598d40ca6", "name": "📋 测试执行日志", "source": "109ef75598d40ca6.txt", "type": "text/plain", "size": 15249}], "parameters": [], "hasContent": true, "stepsCount": 0, "attachmentsCount": 13, "shouldDisplayMessage": false}, "afterStages": [{"name": "function_wait::0", "time": {"start": 1751853890760, "stop": 1751853891511, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "capture_logs_for_allure::0", "time": {"start": 1751853891511, "stop": 1751853891520, "duration": 9}, "status": "passed", "steps": [], "attachments": [{"uid": "de490eb6dab19e94", "name": "📋 测试执行日志 (完整记录)", "source": "de490eb6dab19e94.txt", "type": "text/plain", "size": 14945}], "parameters": [], "hasContent": true, "stepsCount": 0, "attachmentsCount": 1, "shouldDisplayMessage": false}, {"name": "print_info::0", "time": {"start": 1751853890759, "stop": 1751853890760, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}], "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "39132-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "710d86e95f7ef41e.json", "parameterValues": []}