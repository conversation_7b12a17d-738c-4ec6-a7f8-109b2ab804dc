{"uid": "190b48d01b7fe235", "name": "演出场次列表", "fullName": "testcase.play.test_play.TestPlay#test_play", "historyId": "6b8675e1b81e5293918eed8f21bfedcf", "time": {"start": 1751853744202, "stop": 1751853744783, "duration": 581}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "function_wait", "time": {"start": 1751853743451, "stop": 1751853744202, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "hasContent": false, "stepsCount": 0, "attachmentsCount": 0}, {"name": "print_info", "time": {"start": 1751853744202, "stop": 1751853744202, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "hasContent": false, "stepsCount": 0, "attachmentsCount": 0}, {"name": "_session_faker", "time": {"start": 1751853726731, "stop": 1751853726779, "duration": 48}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "hasContent": false, "stepsCount": 0, "attachmentsCount": 0}, {"name": "clear_extract", "time": {"start": 1751853726779, "stop": 1751853727168, "duration": 389}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "hasContent": false, "stepsCount": 0, "attachmentsCount": 0}, {"name": "capture_logs_for_allure", "time": {"start": 1751853743451, "stop": 1751853743451, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "hasContent": false, "stepsCount": 0, "attachmentsCount": 0}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "31e54cda17f951ec", "name": "接口地址", "source": "31e54cda17f951ec.json", "type": "application/json", "size": 52}, {"uid": "dfa2195ae10a21f5", "name": "接口名称", "source": "dfa2195ae10a21f5.json", "type": "application/json", "size": 18}, {"uid": "384bfbf4c3bc9cdf", "name": "请求方式", "source": "384bfbf4c3bc9cdf.json", "type": "application/json", "size": 3}, {"uid": "9953d932731a126c", "name": "请求头", "source": "9953d932731a126c.json", "type": "application/json", "size": 122}, {"uid": "f6d02ad11aa5a6ba", "name": "<PERSON><PERSON>", "source": "f6d02ad11aa5a6ba.json", "type": "application/json", "size": 12}, {"uid": "8d9c2009a7d87c9d", "name": "测试用例名称", "source": "8d9c2009a7d87c9d.json", "type": "application/json", "size": 30}, {"uid": "afbaca620304fe4e", "name": "参数类型", "source": "afbaca620304fe4e.json", "type": "application/json", "size": 6}, {"uid": "51eec8e01a165724", "name": "请求参数json格式", "source": "51eec8e01a165724.json", "type": "application/json", "size": 206}, {"uid": "69cc9488102a87fb", "name": "请求参数实际入参", "source": "69cc9488102a87fb.json", "type": "application/json", "size": 216}, {"uid": "6f065c54b3c4a43e", "name": "接口实际响应信息", "source": "6f065c54b3c4a43e.json", "type": "application/json", "size": 1528}, {"uid": "6f64f2776236082b", "name": "状态码断言结果：成功", "source": "6f64f2776236082b.txt", "type": "text/plain", "size": 37}, {"uid": "e089a1dd8b76b94b", "name": "相等断言结果：成功", "source": "e089a1dd8b76b94b.json", "type": "application/json", "size": 53}, {"uid": "37721e06015ed2d5", "name": "📋 测试执行日志", "source": "37721e06015ed2d5.txt", "type": "text/plain", "size": 5822}], "parameters": [], "shouldDisplayMessage": false, "hasContent": true, "stepsCount": 0, "attachmentsCount": 13}, "afterStages": [{"name": "function_wait::0", "time": {"start": 1751853744785, "stop": 1751853745536, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "hasContent": false, "stepsCount": 0, "attachmentsCount": 0}, {"name": "print_info::0", "time": {"start": 1751853744784, "stop": 1751853744785, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "hasContent": false, "stepsCount": 0, "attachmentsCount": 0}, {"name": "capture_logs_for_allure::0", "time": {"start": 1751853745537, "stop": 1751853745546, "duration": 9}, "status": "passed", "steps": [], "attachments": [{"uid": "facc277f28eac8da", "name": "📋 测试执行日志 (完整记录)", "source": "facc277f28eac8da.txt", "type": "text/plain", "size": 2892}], "parameters": [], "shouldDisplayMessage": false, "hasContent": true, "stepsCount": 0, "attachmentsCount": 1}], "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "39072-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [{"name": "api_info", "value": "{'baseInfo': {'module': '演出票', 'api_name': '演出场次列表', 'host': '${get_host(play)}', 'url': '/api/show/info/performance_shows', 'method': 'get', 'headers': '${get_headers(app)}'}, 'testCase': {'case_name': '获取演出场次列表信息', 'validation': [{'code': 200}, {'eq': {'code': 0}}], 'extract': {'extract_auto': 'performance_shows'}, 'extract_list': np.float64(0.0), 'params': {'performanceId': '${get_extract_data(performance_id)}', 'channelCode': '${appKey()}', 'token': '${get_extract_data(token)}'}}}"}], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "190b48d01b7fe235.json", "parameterValues": ["{'baseInfo': {'module': '演出票', 'api_name': '演出场次列表', 'host': '${get_host(play)}', 'url': '/api/show/info/performance_shows', 'method': 'get', 'headers': '${get_headers(app)}'}, 'testCase': {'case_name': '获取演出场次列表信息', 'validation': [{'code': 200}, {'eq': {'code': 0}}], 'extract': {'extract_auto': 'performance_shows'}, 'extract_list': np.float64(0.0), 'params': {'performanceId': '${get_extract_data(performance_id)}', 'channelCode': '${appKey()}', 'token': '${get_extract_data(token)}'}}}"]}