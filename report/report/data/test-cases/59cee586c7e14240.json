{"uid": "59cee586c7e14240", "name": "项目下场次票品列表", "fullName": "testcase.play.test_play.TestPlay#test_play", "historyId": "3287c72f85d7169cf4925a7d71668b28", "time": {"start": 1751853247021, "stop": 1751853247589, "duration": 568}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "print_info", "time": {"start": 1751853247021, "stop": 1751853247021, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "function_wait", "time": {"start": 1751853246271, "stop": 1751853247021, "duration": 750}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "_session_faker", "time": {"start": 1751853229094, "stop": 1751853229142, "duration": 48}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "clear_extract", "time": {"start": 1751853229143, "stop": 1751853229413, "duration": 270}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "capture_logs_for_allure", "time": {"start": 1751853246270, "stop": 1751853246271, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "253214db10aada54", "name": "接口地址", "source": "253214db10aada54.json", "type": "application/json", "size": 52}, {"uid": "6916ff7e777c6ce3", "name": "接口名称", "source": "6916ff7e777c6ce3.json", "type": "application/json", "size": 27}, {"uid": "681e3871b739ed1d", "name": "请求方式", "source": "681e3871b739ed1d.json", "type": "application/json", "size": 3}, {"uid": "aa3eb0ddc2e1a6d8", "name": "请求头", "source": "aa3eb0ddc2e1a6d8.json", "type": "application/json", "size": 122}, {"uid": "f0897b9abf8dabd9", "name": "<PERSON><PERSON>", "source": "f0897b9abf8dabd9.json", "type": "application/json", "size": 12}, {"uid": "e1b9f7659725df81", "name": "测试用例名称", "source": "e1b9f7659725df81.json", "type": "application/json", "size": 39}, {"uid": "4e45e736f3ee1a51", "name": "参数类型", "source": "4e45e736f3ee1a51.json", "type": "application/json", "size": 6}, {"uid": "3d41dc52f7c01f76", "name": "请求参数json格式", "source": "3d41dc52f7c01f76.json", "type": "application/json", "size": 199}, {"uid": "911f636a16b084fb", "name": "请求参数实际入参", "source": "911f636a16b084fb.json", "type": "application/json", "size": 209}, {"uid": "52914e3434beaff0", "name": "接口实际响应信息", "source": "52914e3434beaff0.json", "type": "application/json", "size": 7483}, {"uid": "1a6b5177b14c9f4c", "name": "状态码断言结果：成功", "source": "1a6b5177b14c9f4c.txt", "type": "text/plain", "size": 37}, {"uid": "26a9adc48e23fff9", "name": "相等断言结果：成功", "source": "26a9adc48e23fff9.json", "type": "application/json", "size": 53}, {"uid": "688492343aa11487", "name": "📋 测试执行日志", "source": "688492343aa11487.txt", "type": "text/plain", "size": 3323}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 13, "hasContent": true, "stepsCount": 0}, "afterStages": [{"name": "print_info::0", "time": {"start": 1751853247589, "stop": 1751853247590, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "function_wait::0", "time": {"start": 1751853247590, "stop": 1751853248341, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "capture_logs_for_allure::0", "time": {"start": 1751853248341, "stop": 1751853248351, "duration": 10}, "status": "passed", "steps": [], "attachments": [{"uid": "98c8dcf3d251283e", "name": "📋 测试执行日志 (完整记录)", "source": "98c8dcf3d251283e.txt", "type": "text/plain", "size": 5174}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 1, "hasContent": true, "stepsCount": 0}], "labels": [{"name": "story", "value": "C01_演出模块流程"}, {"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "28812-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "59cee586c7e14240.json", "parameterValues": []}