{"uid": "97c2f402246e4953", "name": "演出场次列表", "fullName": "testcase.play.test_play.TestPlay#test_play", "historyId": "ce6724bce5907ddc1a7c9b8a022c9530", "time": {"start": 1751853901628, "stop": 1751853902213, "duration": 585}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "print_info", "time": {"start": 1751853901628, "stop": 1751853901628, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "function_wait", "time": {"start": 1751853900877, "stop": 1751853901628, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "capture_logs_for_allure", "time": {"start": 1751853900877, "stop": 1751853900877, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "clear_extract", "time": {"start": 1751853884946, "stop": 1751853885997, "duration": 1051}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "_session_faker", "time": {"start": 1751853884907, "stop": 1751853884946, "duration": 39}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "529b00ee9bbe7efa", "name": "接口地址", "source": "529b00ee9bbe7efa.json", "type": "application/json", "size": 52}, {"uid": "1da6a14bc443b3bd", "name": "接口名称", "source": "1da6a14bc443b3bd.json", "type": "application/json", "size": 18}, {"uid": "46f0f7bc0f8f583c", "name": "请求方式", "source": "46f0f7bc0f8f583c.json", "type": "application/json", "size": 3}, {"uid": "bd15c879048d924f", "name": "请求头", "source": "bd15c879048d924f.json", "type": "application/json", "size": 122}, {"uid": "29f3fbbcb0679c2c", "name": "<PERSON><PERSON>", "source": "29f3fbbcb0679c2c.json", "type": "application/json", "size": 12}, {"uid": "efac0e95c120d64", "name": "测试用例名称", "source": "efac0e95c120d64.json", "type": "application/json", "size": 30}, {"uid": "cc5c0d6840da08c1", "name": "参数类型", "source": "cc5c0d6840da08c1.json", "type": "application/json", "size": 6}, {"uid": "1a918f8202b8781c", "name": "请求参数json格式", "source": "1a918f8202b8781c.json", "type": "application/json", "size": 206}, {"uid": "e84f7fd82ef6d935", "name": "请求参数实际入参", "source": "e84f7fd82ef6d935.json", "type": "application/json", "size": 216}, {"uid": "39401567c518e2e9", "name": "接口实际响应信息", "source": "39401567c518e2e9.json", "type": "application/json", "size": 1528}, {"uid": "de01efcac6af8c91", "name": "状态码断言结果：成功", "source": "de01efcac6af8c91.txt", "type": "text/plain", "size": 37}, {"uid": "9d838e826018b8c6", "name": "相等断言结果：成功", "source": "9d838e826018b8c6.json", "type": "application/json", "size": 53}, {"uid": "839749196f6b22fb", "name": "📋 测试执行日志", "source": "839749196f6b22fb.txt", "type": "text/plain", "size": 6363}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentsCount": 13, "shouldDisplayMessage": false}, "afterStages": [{"name": "print_info::0", "time": {"start": 1751853902214, "stop": 1751853902214, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "function_wait::0", "time": {"start": 1751853902215, "stop": 1751853902965, "duration": 750}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "capture_logs_for_allure::0", "time": {"start": 1751853902966, "stop": 1751853902973, "duration": 7}, "status": "passed", "steps": [], "attachments": [{"uid": "2e2cb46a320318b9", "name": "📋 测试执行日志 (完整记录)", "source": "2e2cb46a320318b9.txt", "type": "text/plain", "size": 3123}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentsCount": 1, "shouldDisplayMessage": false}], "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "39132-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "97c2f402246e4953.json", "parameterValues": []}