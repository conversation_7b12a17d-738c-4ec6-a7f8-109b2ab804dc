{"uid": "8ed8a391dc0a926a", "name": "为你推荐", "fullName": "testcase.play.test_play.TestPlay#test_play", "historyId": "a03ba38a51bcc9af12332b9cb9ea6b41", "time": {"start": 1751853892274, "stop": 1751853892596, "duration": 322}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "function_wait", "time": {"start": 1751853891522, "stop": 1751853892274, "duration": 752}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "capture_logs_for_allure", "time": {"start": 1751853891522, "stop": 1751853891522, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "clear_extract", "time": {"start": 1751853884946, "stop": 1751853885997, "duration": 1051}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "_session_faker", "time": {"start": 1751853884907, "stop": 1751853884946, "duration": 39}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "print_info", "time": {"start": 1751853892274, "stop": 1751853892274, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "4c941a2d8c53b2b5", "name": "接口地址", "source": "4c941a2d8c53b2b5.json", "type": "application/json", "size": 54}, {"uid": "a74b4044c9e44772", "name": "接口名称", "source": "a74b4044c9e44772.json", "type": "application/json", "size": 12}, {"uid": "bb46c7977eca3d94", "name": "请求方式", "source": "bb46c7977eca3d94.json", "type": "application/json", "size": 4}, {"uid": "a53493e5801d8fcb", "name": "请求头", "source": "a53493e5801d8fcb.json", "type": "application/json", "size": 122}, {"uid": "de591438ac3e85ef", "name": "<PERSON><PERSON>", "source": "de591438ac3e85ef.json", "type": "application/json", "size": 12}, {"uid": "be537d19adc8b047", "name": "测试用例名称", "source": "be537d19adc8b047.json", "type": "application/json", "size": 30}, {"uid": "9e8d58b831e1047b", "name": "参数类型", "source": "9e8d58b831e1047b.json", "type": "application/json", "size": 4}, {"uid": "e8d8f5c21f2ba0a2", "name": "请求参数json格式", "source": "e8d8f5c21f2ba0a2.json", "type": "application/json", "size": 214}, {"uid": "d5f8a9b73c950c5a", "name": "请求参数实际入参", "source": "d5f8a9b73c950c5a.json", "type": "application/json", "size": 224}, {"uid": "a4531d0fc7b5ca9b", "name": "接口实际响应信息", "source": "a4531d0fc7b5ca9b.json", "type": "application/json", "size": 44012}, {"uid": "3522b13e1f55f82a", "name": "状态码断言结果：成功", "source": "3522b13e1f55f82a.txt", "type": "text/plain", "size": 37}, {"uid": "45583067069395f0", "name": "相等断言结果：成功", "source": "45583067069395f0.json", "type": "application/json", "size": 53}, {"uid": "a8fb6640e25921de", "name": "📋 测试执行日志", "source": "a8fb6640e25921de.txt", "type": "text/plain", "size": 14945}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentsCount": 13, "shouldDisplayMessage": false}, "afterStages": [{"name": "function_wait::0", "time": {"start": 1751853892598, "stop": 1751853893349, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "capture_logs_for_allure::0", "time": {"start": 1751853893349, "stop": 1751853893358, "duration": 9}, "status": "passed", "steps": [], "attachments": [{"uid": "e0246f5a0b282e70", "name": "📋 测试执行日志 (完整记录)", "source": "e0246f5a0b282e70.txt", "type": "text/plain", "size": 27582}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentsCount": 1, "shouldDisplayMessage": false}, {"name": "print_info::0", "time": {"start": 1751853892597, "stop": 1751853892597, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}], "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "39132-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "8ed8a391dc0a926a.json", "parameterValues": []}