{"uid": "16f628f251a307a1", "name": "取消订单", "fullName": "testcase.play.test_play.TestPlay#test_play", "historyId": "097386a9d43be40d3c28c1874a6e169b", "time": {"start": 1751866441596, "stop": 1751866442868, "duration": 1272}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "capture_logs_for_allure", "time": {"start": 1751866440845, "stop": 1751866440845, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "function_wait", "time": {"start": 1751866440845, "stop": 1751866441596, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "clear_extract", "time": {"start": 1751866415419, "stop": 1751866415726, "duration": 307}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "print_info", "time": {"start": 1751866441596, "stop": 1751866441596, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "_session_faker", "time": {"start": 1751866415384, "stop": 1751866415419, "duration": 35}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "f14d5d6c4b150ae1", "name": "接口地址", "source": "f14d5d6c4b150ae1.json", "type": "application/json", "size": 42}, {"uid": "e3b64f6b43548810", "name": "接口名称", "source": "e3b64f6b43548810.json", "type": "application/json", "size": 12}, {"uid": "fe7b98d508621c5f", "name": "请求方式", "source": "fe7b98d508621c5f.json", "type": "application/json", "size": 4}, {"uid": "1b5716a3625fa544", "name": "请求头", "source": "1b5716a3625fa544.json", "type": "application/json", "size": 122}, {"uid": "7cf1c4df4d63cbb0", "name": "<PERSON><PERSON>", "source": "7cf1c4df4d63cbb0.json", "type": "application/json", "size": 12}, {"uid": "1e2ffde5bed4b82e", "name": "测试用例名称", "source": "1e2ffde5bed4b82e.json", "type": "application/json", "size": 12}, {"uid": "12bc9fcfc2d3c7e9", "name": "参数类型", "source": "12bc9fcfc2d3c7e9.json", "type": "application/json", "size": 4}, {"uid": "b83469a814d9b95f", "name": "请求参数json格式", "source": "b83469a814d9b95f.json", "type": "application/json", "size": 217}, {"uid": "3e5bec8c1cdbb4c1", "name": "请求参数实际入参", "source": "3e5bec8c1cdbb4c1.json", "type": "application/json", "size": 227}, {"uid": "6fc931de30e4577c", "name": "接口实际响应信息", "source": "6fc931de30e4577c.json", "type": "application/json", "size": 48}, {"uid": "5db12a0309c92d1", "name": "状态码断言结果：成功", "source": "5db12a0309c92d1.txt", "type": "text/plain", "size": 37}, {"uid": "214aecb6482a2604", "name": "相等断言结果：成功", "source": "214aecb6482a2604.json", "type": "application/json", "size": 53}, {"uid": "6e21a79d03577b27", "name": "📋 测试执行日志", "source": "6e21a79d03577b27.txt", "type": "text/plain", "size": 2439}], "parameters": [], "hasContent": true, "stepsCount": 0, "attachmentsCount": 13, "shouldDisplayMessage": false}, "afterStages": [{"name": "capture_logs_for_allure::0", "time": {"start": 1751866443621, "stop": 1751866443630, "duration": 9}, "status": "passed", "steps": [], "attachments": [{"uid": "81c9c0b7af7e7836", "name": "📋 测试执行日志 (完整记录)", "source": "81c9c0b7af7e7836.txt", "type": "text/plain", "size": 2076}], "parameters": [], "hasContent": true, "stepsCount": 0, "attachmentsCount": 1, "shouldDisplayMessage": false}, {"name": "function_wait::0", "time": {"start": 1751866442869, "stop": 1751866443620, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "print_info::0", "time": {"start": 1751866442869, "stop": 1751866442869, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}], "labels": [{"name": "story", "value": "C01_演出模块流程"}, {"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "16848-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "16f628f251a307a1.json", "parameterValues": []}