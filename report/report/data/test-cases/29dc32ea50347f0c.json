{"uid": "29dc32ea50347f0c", "name": "演出项目详情", "fullName": "testcase.play.test_play.TestPlay#test_play", "historyId": "0457c216d6185e839b1ab99163c7bbc6", "time": {"start": 1751853899563, "stop": 1751853900106, "duration": 543}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "capture_logs_for_allure", "time": {"start": 1751853898812, "stop": 1751853898812, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "print_info", "time": {"start": 1751853899563, "stop": 1751853899563, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "clear_extract", "time": {"start": 1751853884946, "stop": 1751853885997, "duration": 1051}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "_session_faker", "time": {"start": 1751853884907, "stop": 1751853884946, "duration": 39}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "function_wait", "time": {"start": 1751853898812, "stop": 1751853899563, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "ebdb776470a6bf8b", "name": "接口地址", "source": "ebdb776470a6bf8b.json", "type": "application/json", "size": 51}, {"uid": "debd6c2bcd26c602", "name": "接口名称", "source": "debd6c2bcd26c602.json", "type": "application/json", "size": 18}, {"uid": "73f4b7c1617fe35c", "name": "请求方式", "source": "73f4b7c1617fe35c.json", "type": "application/json", "size": 3}, {"uid": "53f96fcea922cf37", "name": "请求头", "source": "53f96fcea922cf37.json", "type": "application/json", "size": 122}, {"uid": "5e9a32a6cbfa1997", "name": "<PERSON><PERSON>", "source": "5e9a32a6cbfa1997.json", "type": "application/json", "size": 12}, {"uid": "81c41cded17852d6", "name": "测试用例名称", "source": "81c41cded17852d6.json", "type": "application/json", "size": 30}, {"uid": "7eb4e1db188841de", "name": "参数类型", "source": "7eb4e1db188841de.json", "type": "application/json", "size": 6}, {"uid": "a8e86ca11fbd8b7d", "name": "请求参数json格式", "source": "a8e86ca11fbd8b7d.json", "type": "application/json", "size": 206}, {"uid": "ac82089db399df8f", "name": "请求参数实际入参", "source": "ac82089db399df8f.json", "type": "application/json", "size": 216}, {"uid": "9f0da49c3e2301fd", "name": "接口实际响应信息", "source": "9f0da49c3e2301fd.json", "type": "application/json", "size": 4313}, {"uid": "68a5a8006245462f", "name": "状态码断言结果：成功", "source": "68a5a8006245462f.txt", "type": "text/plain", "size": 37}, {"uid": "32a3aa2e7a1f9628", "name": "相等断言结果：成功", "source": "32a3aa2e7a1f9628.json", "type": "application/json", "size": 53}, {"uid": "503ab96bd9549243", "name": "📋 测试执行日志", "source": "503ab96bd9549243.txt", "type": "text/plain", "size": 15160}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentsCount": 13, "shouldDisplayMessage": false}, "afterStages": [{"name": "capture_logs_for_allure::0", "time": {"start": 1751853900859, "stop": 1751853900875, "duration": 16}, "status": "passed", "steps": [], "attachments": [{"uid": "853c8116051491c2", "name": "📋 测试执行日志 (完整记录)", "source": "853c8116051491c2.txt", "type": "text/plain", "size": 6363}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentsCount": 1, "shouldDisplayMessage": false}, {"name": "print_info::0", "time": {"start": 1751853900107, "stop": 1751853900107, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "function_wait::0", "time": {"start": 1751853900108, "stop": 1751853900859, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}], "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "39132-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "29dc32ea50347f0c.json", "parameterValues": []}