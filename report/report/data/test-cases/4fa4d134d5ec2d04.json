{"uid": "4fa4d134d5ec2d04", "name": "演唱会分类", "fullName": "testcase.play.test_play.TestPlay#test_play", "historyId": "47b52c96aa0c1d59c25f4bc63959c980", "time": {"start": 1751866425803, "stop": 1751866426119, "duration": 316}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "function_wait", "time": {"start": 1751866425051, "stop": 1751866425802, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "capture_logs_for_allure", "time": {"start": 1751866425051, "stop": 1751866425051, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "clear_extract", "time": {"start": 1751866415419, "stop": 1751866415726, "duration": 307}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "print_info", "time": {"start": 1751866425802, "stop": 1751866425802, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "_session_faker", "time": {"start": 1751866415384, "stop": 1751866415419, "duration": 35}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "365b692692ee3916", "name": "接口地址", "source": "365b692692ee3916.json", "type": "application/json", "size": 53}, {"uid": "6a814f2a9e4f7874", "name": "接口名称", "source": "6a814f2a9e4f7874.json", "type": "application/json", "size": 15}, {"uid": "5c75ce1f8ed9423", "name": "请求方式", "source": "5c75ce1f8ed9423.json", "type": "application/json", "size": 4}, {"uid": "55849dbd157329a", "name": "请求头", "source": "55849dbd157329a.json", "type": "application/json", "size": 122}, {"uid": "5094dfeb41bf38cd", "name": "<PERSON><PERSON>", "source": "5094dfeb41bf38cd.json", "type": "application/json", "size": 12}, {"uid": "2c5a9895fad86c2a", "name": "测试用例名称", "source": "2c5a9895fad86c2a.json", "type": "application/json", "size": 25}, {"uid": "efb5af534c750cb9", "name": "参数类型", "source": "efb5af534c750cb9.json", "type": "application/json", "size": 4}, {"uid": "8c8517ba2c2a3041", "name": "请求参数json格式", "source": "8c8517ba2c2a3041.json", "type": "application/json", "size": 207}, {"uid": "ec7a6c909f29489e", "name": "请求参数实际入参", "source": "ec7a6c909f29489e.json", "type": "application/json", "size": 217}, {"uid": "136a74262a73ba47", "name": "接口实际响应信息", "source": "136a74262a73ba47.json", "type": "application/json", "size": 22018}, {"uid": "59702ed67ccf8901", "name": "状态码断言结果：成功", "source": "59702ed67ccf8901.txt", "type": "text/plain", "size": 37}, {"uid": "eb2f134101d3159c", "name": "相等断言结果：成功", "source": "eb2f134101d3159c.json", "type": "application/json", "size": 53}, {"uid": "5ddc12d6032de19e", "name": "📋 测试执行日志", "source": "5ddc12d6032de19e.txt", "type": "text/plain", "size": 4549}], "parameters": [], "hasContent": true, "stepsCount": 0, "attachmentsCount": 13, "shouldDisplayMessage": false}, "afterStages": [{"name": "function_wait::0", "time": {"start": 1751866426120, "stop": 1751866426871, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "capture_logs_for_allure::0", "time": {"start": 1751866426872, "stop": 1751866426888, "duration": 16}, "status": "passed", "steps": [], "attachments": [{"uid": "814abe3de27de681", "name": "📋 测试执行日志 (完整记录)", "source": "814abe3de27de681.txt", "type": "text/plain", "size": 14539}], "parameters": [], "hasContent": true, "stepsCount": 0, "attachmentsCount": 1, "shouldDisplayMessage": false}, {"name": "print_info::0", "time": {"start": 1751866426120, "stop": 1751866426120, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}], "labels": [{"name": "story", "value": "C01_演出模块流程"}, {"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "16848-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "4fa4d134d5ec2d04.json", "parameterValues": []}