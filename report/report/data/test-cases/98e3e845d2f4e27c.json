{"uid": "98e3e845d2f4e27c", "name": "演出项目详情", "fullName": "testcase.play.test_play.TestPlay#test_play", "historyId": "0457c216d6185e839b1ab99163c7bbc6", "time": {"start": 1751853242905, "stop": 1751853243436, "duration": 531}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "_session_faker", "time": {"start": 1751853229094, "stop": 1751853229142, "duration": 48}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "clear_extract", "time": {"start": 1751853229143, "stop": 1751853229413, "duration": 270}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "capture_logs_for_allure", "time": {"start": 1751853242154, "stop": 1751853242154, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "print_info", "time": {"start": 1751853242905, "stop": 1751853242905, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "function_wait", "time": {"start": 1751853242154, "stop": 1751853242905, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "10b11264ea0d8b74", "name": "接口地址", "source": "10b11264ea0d8b74.json", "type": "application/json", "size": 51}, {"uid": "5364ce237e25018f", "name": "接口名称", "source": "5364ce237e25018f.json", "type": "application/json", "size": 18}, {"uid": "ebbe4a908670dc59", "name": "请求方式", "source": "ebbe4a908670dc59.json", "type": "application/json", "size": 3}, {"uid": "207222165ad155ab", "name": "请求头", "source": "207222165ad155ab.json", "type": "application/json", "size": 122}, {"uid": "11459ef81cb1924f", "name": "<PERSON><PERSON>", "source": "11459ef81cb1924f.json", "type": "application/json", "size": 12}, {"uid": "5f231c4945274a4f", "name": "测试用例名称", "source": "5f231c4945274a4f.json", "type": "application/json", "size": 30}, {"uid": "986ee0edad156d6c", "name": "参数类型", "source": "986ee0edad156d6c.json", "type": "application/json", "size": 6}, {"uid": "d0ba1f8a4a3fb6ff", "name": "请求参数json格式", "source": "d0ba1f8a4a3fb6ff.json", "type": "application/json", "size": 206}, {"uid": "8c300628f04d0d10", "name": "请求参数实际入参", "source": "8c300628f04d0d10.json", "type": "application/json", "size": 216}, {"uid": "32472dcff2f7ce16", "name": "接口实际响应信息", "source": "32472dcff2f7ce16.json", "type": "application/json", "size": 4313}, {"uid": "52280814c83bafee", "name": "状态码断言结果：成功", "source": "52280814c83bafee.txt", "type": "text/plain", "size": 37}, {"uid": "4eab1b627f1cfc6c", "name": "相等断言结果：成功", "source": "4eab1b627f1cfc6c.json", "type": "application/json", "size": 53}, {"uid": "3176e07406d90d5b", "name": "📋 测试执行日志", "source": "3176e07406d90d5b.txt", "type": "text/plain", "size": 15355}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 13, "hasContent": true, "stepsCount": 0}, "afterStages": [{"name": "capture_logs_for_allure::0", "time": {"start": 1751853244190, "stop": 1751853244202, "duration": 12}, "status": "passed", "steps": [], "attachments": [{"uid": "7a3367ea755f4e5f", "name": "📋 测试执行日志 (完整记录)", "source": "7a3367ea755f4e5f.txt", "type": "text/plain", "size": 6304}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 1, "hasContent": true, "stepsCount": 0}, {"name": "print_info::0", "time": {"start": 1751853243437, "stop": 1751853243438, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "function_wait::0", "time": {"start": 1751853243438, "stop": 1751853244189, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}], "labels": [{"name": "story", "value": "C01_演出模块流程"}, {"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "28812-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "98e3e845d2f4e27c.json", "parameterValues": []}