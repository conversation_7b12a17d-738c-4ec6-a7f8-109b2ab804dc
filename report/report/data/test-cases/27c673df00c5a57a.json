{"uid": "27c673df00c5a57a", "name": "演出项目详情", "fullName": "testcase.play.test_play.TestPlay#test_play", "historyId": "f1d7da39199bd96c94383323f4027e2f", "time": {"start": 1751853004882, "stop": 1751853005406, "duration": 524}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "clear_extract", "time": {"start": 1751852990929, "stop": 1751852991206, "duration": 277}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "capture_logs_for_allure", "time": {"start": 1751853004130, "stop": 1751853004130, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "_session_faker", "time": {"start": 1751852990879, "stop": 1751852990927, "duration": 48}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "function_wait", "time": {"start": 1751853004131, "stop": 1751853004881, "duration": 750}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "print_info", "time": {"start": 1751853004881, "stop": 1751853004882, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "8c2cf859f3011ad5", "name": "接口地址", "source": "8c2cf859f3011ad5.json", "type": "application/json", "size": 51}, {"uid": "6051b803d4c413ac", "name": "接口名称", "source": "6051b803d4c413ac.json", "type": "application/json", "size": 18}, {"uid": "daf5910447212359", "name": "请求方式", "source": "daf5910447212359.json", "type": "application/json", "size": 3}, {"uid": "632f359497a73c89", "name": "请求头", "source": "632f359497a73c89.json", "type": "application/json", "size": 122}, {"uid": "56810c02e5981118", "name": "<PERSON><PERSON>", "source": "56810c02e5981118.json", "type": "application/json", "size": 12}, {"uid": "64c78a60475c5938", "name": "测试用例名称", "source": "64c78a60475c5938.json", "type": "application/json", "size": 30}, {"uid": "cfae87cf04690a", "name": "参数类型", "source": "cfae87cf04690a.json", "type": "application/json", "size": 6}, {"uid": "87b7a3ca22871b86", "name": "请求参数json格式", "source": "87b7a3ca22871b86.json", "type": "application/json", "size": 206}, {"uid": "4fda040208485777", "name": "请求参数实际入参", "source": "4fda040208485777.json", "type": "application/json", "size": 216}, {"uid": "f5bce7e7b0d88d30", "name": "接口实际响应信息", "source": "f5bce7e7b0d88d30.json", "type": "application/json", "size": 4313}, {"uid": "f57a06443ade8bbe", "name": "状态码断言结果：成功", "source": "f57a06443ade8bbe.txt", "type": "text/plain", "size": 37}, {"uid": "4a76cad085b98281", "name": "相等断言结果：成功", "source": "4a76cad085b98281.json", "type": "application/json", "size": 53}, {"uid": "801ddfdf1dc9d43", "name": "📋 测试执行日志", "source": "801ddfdf1dc9d43.txt", "type": "text/plain", "size": 14929}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 13, "hasContent": true, "stepsCount": 0}, "afterStages": [{"name": "capture_logs_for_allure::0", "time": {"start": 1751853006159, "stop": 1751853006183, "duration": 24}, "status": "passed", "steps": [], "attachments": [{"uid": "d077a28330a83e54", "name": "📋 测试执行日志 (完整记录)", "source": "d077a28330a83e54.txt", "type": "text/plain", "size": 5822}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 1, "hasContent": true, "stepsCount": 0}, {"name": "function_wait::0", "time": {"start": 1751853005408, "stop": 1751853006158, "duration": 750}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "print_info::0", "time": {"start": 1751853005407, "stop": 1751853005408, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}], "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "37476-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [{"name": "api_info", "value": "{'baseInfo': {'module': '演出票', 'api_name': '演出项目详情', 'host': '${get_host(play)}', 'url': '/api/show/info/performance_info', 'method': 'get', 'headers': '${get_headers(app)}'}, 'testCase': {'case_name': '获取演出项目详情信息', 'validation': [{'code': 200}, {'eq': {'code': 0}}], 'extract': 0, 'extract_list': np.float64(0.0), 'params': {'performanceId': '${get_extract_data(performance_id)}', 'channelCode': '${appKey()}', 'token': '${get_extract_data(token)}'}}}"}], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "27c673df00c5a57a.json", "parameterValues": ["{'baseInfo': {'module': '演出票', 'api_name': '演出项目详情', 'host': '${get_host(play)}', 'url': '/api/show/info/performance_info', 'method': 'get', 'headers': '${get_headers(app)}'}, 'testCase': {'case_name': '获取演出项目详情信息', 'validation': [{'code': 200}, {'eq': {'code': 0}}], 'extract': 0, 'extract_list': np.float64(0.0), 'params': {'performanceId': '${get_extract_data(performance_id)}', 'channelCode': '${appKey()}', 'token': '${get_extract_data(token)}'}}}"]}