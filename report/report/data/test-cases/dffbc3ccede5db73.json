{"uid": "dffbc3ccede5db73", "name": "演出分类", "fullName": "testcase.play.test_play.TestPlay#test_play", "historyId": "c36ab200ccb8723de7119149ea6c3088", "time": {"start": 1751851565677, "stop": 1751851566221, "duration": 544}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "_session_faker", "time": {"start": 1751851564550, "stop": 1751851564591, "duration": 41}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "print_info", "time": {"start": 1751851565673, "stop": 1751851565673, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "function_wait", "time": {"start": 1751851564922, "stop": 1751851565673, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "clear_extract", "time": {"start": 1751851564591, "stop": 1751851564922, "duration": 331}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "capture_logs_for_allure", "time": {"start": 1751851564922, "stop": 1751851564922, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "26d39395f4f2f38b", "name": "接口地址", "source": "26d39395f4f2f38b.json", "type": "application/json", "size": 48}, {"uid": "ab1be5b52604db83", "name": "接口名称", "source": "ab1be5b52604db83.json", "type": "application/json", "size": 12}, {"uid": "2f678a5ce3b906b7", "name": "请求方式", "source": "2f678a5ce3b906b7.json", "type": "application/json", "size": 4}, {"uid": "611fc0d40f46a2dc", "name": "请求头", "source": "611fc0d40f46a2dc.json", "type": "application/json", "size": 122}, {"uid": "b23b93cfdcdf2e9b", "name": "<PERSON><PERSON>", "source": "b23b93cfdcdf2e9b.json", "type": "application/json", "size": 12}, {"uid": "8c444333afe9e6ff", "name": "测试用例名称", "source": "8c444333afe9e6ff.json", "type": "application/json", "size": 24}, {"uid": "e0ab6cb8a26cfdfc", "name": "参数类型", "source": "e0ab6cb8a26cfdfc.json", "type": "application/json", "size": 4}, {"uid": "66b4d690407b6ed0", "name": "请求参数json格式", "source": "66b4d690407b6ed0.json", "type": "application/json", "size": 204}, {"uid": "d0ad032fb37fd199", "name": "请求参数实际入参", "source": "d0ad032fb37fd199.json", "type": "application/json", "size": 214}, {"uid": "b3847010b6f8a21c", "name": "接口实际响应信息", "source": "b3847010b6f8a21c.json", "type": "application/json", "size": 3148}, {"uid": "9a34838ba5d86cac", "name": "状态码断言结果：成功", "source": "9a34838ba5d86cac.txt", "type": "text/plain", "size": 37}, {"uid": "86ad5dc168bf9946", "name": "相等断言结果：成功", "source": "86ad5dc168bf9946.json", "type": "application/json", "size": 53}, {"uid": "27ec2bc481081e6c", "name": "📋 测试执行日志", "source": "27ec2bc481081e6c.txt", "type": "text/plain", "size": 6440}], "parameters": [], "hasContent": true, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 13}, "afterStages": [{"name": "print_info::0", "time": {"start": 1751851566221, "stop": 1751851566222, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "function_wait::0", "time": {"start": 1751851566222, "stop": 1751851566973, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "capture_logs_for_allure::0", "time": {"start": 1751851566973, "stop": 1751851566982, "duration": 9}, "status": "passed", "steps": [], "attachments": [{"uid": "840cebe3707fdab7", "name": "📋 测试执行日志 (完整记录)", "source": "840cebe3707fdab7.txt", "type": "text/plain", "size": 4097}], "parameters": [], "hasContent": true, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 1}], "labels": [{"name": "story", "value": "C01_演出模块流程"}, {"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "34968-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "dffbc3ccede5db73.json", "parameterValues": []}