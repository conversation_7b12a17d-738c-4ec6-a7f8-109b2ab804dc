{"uid": "11b6c7238b93c529", "name": "项目下场次票品列表", "fullName": "testcase.play.test_play.TestPlay#test_play", "historyId": "3287c72f85d7169cf4925a7d71668b28", "time": {"start": 1751852474303, "stop": 1751852474883, "duration": 580}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "capture_logs_for_allure", "time": {"start": 1751852473552, "stop": 1751852473552, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "_session_faker", "time": {"start": 1751852455733, "stop": 1751852455863, "duration": 130}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "function_wait", "time": {"start": 1751852473552, "stop": 1751852474303, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "print_info", "time": {"start": 1751852474303, "stop": 1751852474303, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "clear_extract", "time": {"start": 1751852455863, "stop": 1751852456123, "duration": 260}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "4aedade8a96484c2", "name": "接口地址", "source": "4aedade8a96484c2.json", "type": "application/json", "size": 52}, {"uid": "d87a47c92a504224", "name": "接口名称", "source": "d87a47c92a504224.json", "type": "application/json", "size": 27}, {"uid": "8c187331135f915c", "name": "请求方式", "source": "8c187331135f915c.json", "type": "application/json", "size": 3}, {"uid": "bf4bb40447f2dae7", "name": "请求头", "source": "bf4bb40447f2dae7.json", "type": "application/json", "size": 122}, {"uid": "325a3dcbb7a13a96", "name": "<PERSON><PERSON>", "source": "325a3dcbb7a13a96.json", "type": "application/json", "size": 12}, {"uid": "1829877c4bff4f90", "name": "测试用例名称", "source": "1829877c4bff4f90.json", "type": "application/json", "size": 39}, {"uid": "d26c0f98d7eb7d56", "name": "参数类型", "source": "d26c0f98d7eb7d56.json", "type": "application/json", "size": 6}, {"uid": "ee9a49185153c237", "name": "请求参数json格式", "source": "ee9a49185153c237.json", "type": "application/json", "size": 199}, {"uid": "98391cb207657fb4", "name": "请求参数实际入参", "source": "98391cb207657fb4.json", "type": "application/json", "size": 209}, {"uid": "5b8f8741b8c20a76", "name": "接口实际响应信息", "source": "5b8f8741b8c20a76.json", "type": "application/json", "size": 7483}, {"uid": "2f0e0d5f7a6a7ca0", "name": "状态码断言结果：成功", "source": "2f0e0d5f7a6a7ca0.txt", "type": "text/plain", "size": 37}, {"uid": "3f0505dd9c040d00", "name": "相等断言结果：成功", "source": "3f0505dd9c040d00.json", "type": "application/json", "size": 53}, {"uid": "e6b16e47635300ec", "name": "📋 测试执行日志", "source": "e6b16e47635300ec.txt", "type": "text/plain", "size": 3353}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 13, "hasContent": true, "stepsCount": 0}, "afterStages": [{"name": "capture_logs_for_allure::0", "time": {"start": 1751852475636, "stop": 1751852475640, "duration": 4}, "status": "passed", "steps": [], "attachments": [{"uid": "16518bec4c9b57ba", "name": "📋 测试执行日志 (完整记录)", "source": "16518bec4c9b57ba.txt", "type": "text/plain", "size": 4962}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 1, "hasContent": true, "stepsCount": 0}, {"name": "function_wait::0", "time": {"start": 1751852474885, "stop": 1751852475635, "duration": 750}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "print_info::0", "time": {"start": 1751852474884, "stop": 1751852474884, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}], "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "38092-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "11b6c7238b93c529.json", "parameterValues": []}