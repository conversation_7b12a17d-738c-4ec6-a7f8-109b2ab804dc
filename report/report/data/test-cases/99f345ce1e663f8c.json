{"uid": "99f345ce1e663f8c", "name": "下单", "fullName": "testcase.play.test_play.TestPlay#test_play", "historyId": "df78d83b2c7879c3a2ecdde06190a07c", "time": {"start": 1751853907768, "stop": 1751853909033, "duration": 1265}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "function_wait", "time": {"start": 1751853907017, "stop": 1751853907767, "duration": 750}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "clear_extract", "time": {"start": 1751853884946, "stop": 1751853885997, "duration": 1051}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "print_info", "time": {"start": 1751853907767, "stop": 1751853907767, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "capture_logs_for_allure", "time": {"start": 1751853907017, "stop": 1751853907017, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "_session_faker", "time": {"start": 1751853884907, "stop": 1751853884946, "duration": 39}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "6b4689223e73486f", "name": "接口地址", "source": "6b4689223e73486f.json", "type": "application/json", "size": 42}, {"uid": "65dd509a9cb37d1e", "name": "接口名称", "source": "65dd509a9cb37d1e.json", "type": "application/json", "size": 6}, {"uid": "fffc6600df60127c", "name": "请求方式", "source": "fffc6600df60127c.json", "type": "application/json", "size": 4}, {"uid": "4da958c9c29aa9f7", "name": "请求头", "source": "4da958c9c29aa9f7.json", "type": "application/json", "size": 122}, {"uid": "c4c1721396f66013", "name": "<PERSON><PERSON>", "source": "c4c1721396f66013.json", "type": "application/json", "size": 12}, {"uid": "caf0bd5dddaaadee", "name": "测试用例名称", "source": "caf0bd5dddaaadee.json", "type": "application/json", "size": 12}, {"uid": "7e29ac5ff257a2dd", "name": "参数类型", "source": "7e29ac5ff257a2dd.json", "type": "application/json", "size": 4}, {"uid": "a00e11ffca2f8767", "name": "请求参数json格式", "source": "a00e11ffca2f8767.json", "type": "application/json", "size": 615}, {"uid": "158d1c0c5dd146c7", "name": "请求参数实际入参", "source": "158d1c0c5dd146c7.json", "type": "application/json", "size": 625}, {"uid": "5b0e4883975cdf91", "name": "接口实际响应信息", "source": "5b0e4883975cdf91.json", "type": "application/json", "size": 94}, {"uid": "79afd280122d7b23", "name": "状态码断言结果：成功", "source": "79afd280122d7b23.txt", "type": "text/plain", "size": 37}, {"uid": "5c8d39fbc3e39b17", "name": "相等断言结果：成功", "source": "5c8d39fbc3e39b17.json", "type": "application/json", "size": 53}, {"uid": "81951d097fddb7ff", "name": "📋 测试执行日志", "source": "81951d097fddb7ff.txt", "type": "text/plain", "size": 2608}], "parameters": [], "hasContent": true, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 13}, "afterStages": [{"name": "function_wait::0", "time": {"start": 1751853909034, "stop": 1751853909784, "duration": 750}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "print_info::0", "time": {"start": 1751853909033, "stop": 1751853909033, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "capture_logs_for_allure::0", "time": {"start": 1751853909784, "stop": 1751853909794, "duration": 10}, "status": "passed", "steps": [], "attachments": [{"uid": "1ede7632b4dd238c", "name": "📋 测试执行日志 (完整记录)", "source": "1ede7632b4dd238c.txt", "type": "text/plain", "size": 2968}], "parameters": [], "hasContent": true, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 1}], "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "39132-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "99f345ce1e663f8c.json", "parameterValues": []}