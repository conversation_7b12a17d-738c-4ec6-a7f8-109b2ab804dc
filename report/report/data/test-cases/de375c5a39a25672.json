{"uid": "de375c5a39a25672", "name": "**搜索**", "fullName": "testcase.play.test_play.TestPlay#test_play", "historyId": "932055e7a8948657a1bdaf6fe3b4e09c", "time": {"start": 1751853003029, "stop": 1751853003354, "duration": 325}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "capture_logs_for_allure", "time": {"start": 1751853002277, "stop": 1751853002277, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "clear_extract", "time": {"start": 1751852990929, "stop": 1751852991206, "duration": 277}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "_session_faker", "time": {"start": 1751852990879, "stop": 1751852990927, "duration": 48}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "function_wait", "time": {"start": 1751853002277, "stop": 1751853003028, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "print_info", "time": {"start": 1751853003028, "stop": 1751853003029, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "e447e52252c16885", "name": "接口地址", "source": "e447e52252c16885.json", "type": "application/json", "size": 53}, {"uid": "9a48e458707913eb", "name": "接口名称", "source": "9a48e458707913eb.json", "type": "application/json", "size": 10}, {"uid": "cc4f6bb16bd8d8b5", "name": "请求方式", "source": "cc4f6bb16bd8d8b5.json", "type": "application/json", "size": 4}, {"uid": "e3f7915f5b0c9ce", "name": "请求头", "source": "e3f7915f5b0c9ce.json", "type": "application/json", "size": 122}, {"uid": "cfd31247dcad1154", "name": "<PERSON><PERSON>", "source": "cfd31247dcad1154.json", "type": "application/json", "size": 12}, {"uid": "fe2a825e3c73d26f", "name": "测试用例名称", "source": "fe2a825e3c73d26f.json", "type": "application/json", "size": 30}, {"uid": "b71b2d40c5cf59ab", "name": "参数类型", "source": "b71b2d40c5cf59ab.json", "type": "application/json", "size": 4}, {"uid": "f1dbefde54dfa138", "name": "请求参数json格式", "source": "f1dbefde54dfa138.json", "type": "application/json", "size": 201}, {"uid": "5208500a395cbe27", "name": "请求参数实际入参", "source": "5208500a395cbe27.json", "type": "application/json", "size": 211}, {"uid": "643c442a038d0a0", "name": "接口实际响应信息", "source": "643c442a038d0a0.json", "type": "application/json", "size": 22387}, {"uid": "962b1010c9588a51", "name": "状态码断言结果：成功", "source": "962b1010c9588a51.txt", "type": "text/plain", "size": 37}, {"uid": "7b535fb48b9afd31", "name": "相等断言结果：成功", "source": "7b535fb48b9afd31.json", "type": "application/json", "size": 53}, {"uid": "f60d70fd1b63c716", "name": "📋 测试执行日志", "source": "f60d70fd1b63c716.txt", "type": "text/plain", "size": 14539}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 13, "hasContent": true, "stepsCount": 0}, "afterStages": [{"name": "capture_logs_for_allure::0", "time": {"start": 1751853004110, "stop": 1751853004126, "duration": 16}, "status": "passed", "steps": [], "attachments": [{"uid": "d1bbd1a9a95db049", "name": "📋 测试执行日志 (完整记录)", "source": "d1bbd1a9a95db049.txt", "type": "text/plain", "size": 14929}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 1, "hasContent": true, "stepsCount": 0}, {"name": "function_wait::0", "time": {"start": 1751853003357, "stop": 1751853004108, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "print_info::0", "time": {"start": 1751853003355, "stop": 1751853003355, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}], "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "37476-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [{"name": "api_info", "value": "{'baseInfo': {'module': '演出票', 'api_name': '**搜索**', 'host': '${get_host(play)}', 'url': '/api/show/info/performance_search', 'method': 'post', 'headers': '${get_headers(app)}'}, 'testCase': {'case_name': '按演出名称关键字搜索', 'validation': [{'code': 200}, {'eq': {'code': 0}}], 'extract': 0, 'extract_list': np.float64(0.0), 'data': {'keyword': '演', 'channelCode': '${appKey()}', 'token': '${get_extract_data(token)}'}}}"}], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "de375c5a39a25672.json", "parameterValues": ["{'baseInfo': {'module': '演出票', 'api_name': '**搜索**', 'host': '${get_host(play)}', 'url': '/api/show/info/performance_search', 'method': 'post', 'headers': '${get_headers(app)}'}, 'testCase': {'case_name': '按演出名称关键字搜索', 'validation': [{'code': 200}, {'eq': {'code': 0}}], 'extract': 0, 'extract_list': np.float64(0.0), 'data': {'keyword': '演', 'channelCode': '${appKey()}', 'token': '${get_extract_data(token)}'}}}"]}