{"uid": "2415c8f851173e7", "name": "演出场次列表", "fullName": "testcase.play.test_play.TestPlay#test_play", "historyId": "ce6724bce5907ddc1a7c9b8a022c9530", "time": {"start": 1751853244955, "stop": 1751853245501, "duration": 546}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "capture_logs_for_allure", "time": {"start": 1751853244204, "stop": 1751853244204, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "function_wait", "time": {"start": 1751853244204, "stop": 1751853244954, "duration": 750}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "_session_faker", "time": {"start": 1751853229094, "stop": 1751853229142, "duration": 48}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "clear_extract", "time": {"start": 1751853229143, "stop": 1751853229413, "duration": 270}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "print_info", "time": {"start": 1751853244954, "stop": 1751853244954, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "903f87b08627aa9a", "name": "接口地址", "source": "903f87b08627aa9a.json", "type": "application/json", "size": 52}, {"uid": "900eba759fcd6d4a", "name": "接口名称", "source": "900eba759fcd6d4a.json", "type": "application/json", "size": 18}, {"uid": "b12354abdc5a5a69", "name": "请求方式", "source": "b12354abdc5a5a69.json", "type": "application/json", "size": 3}, {"uid": "65a0d485d327d87d", "name": "请求头", "source": "65a0d485d327d87d.json", "type": "application/json", "size": 122}, {"uid": "b636a80ef1337c5d", "name": "<PERSON><PERSON>", "source": "b636a80ef1337c5d.json", "type": "application/json", "size": 12}, {"uid": "8712e27d62ddc286", "name": "测试用例名称", "source": "8712e27d62ddc286.json", "type": "application/json", "size": 30}, {"uid": "de908e58edd15929", "name": "参数类型", "source": "de908e58edd15929.json", "type": "application/json", "size": 6}, {"uid": "791f19058ea173b1", "name": "请求参数json格式", "source": "791f19058ea173b1.json", "type": "application/json", "size": 206}, {"uid": "e4134af884fc1da7", "name": "请求参数实际入参", "source": "e4134af884fc1da7.json", "type": "application/json", "size": 216}, {"uid": "c791e6924ed75bff", "name": "接口实际响应信息", "source": "c791e6924ed75bff.json", "type": "application/json", "size": 1528}, {"uid": "bda1b2ff68cb895", "name": "状态码断言结果：成功", "source": "bda1b2ff68cb895.txt", "type": "text/plain", "size": 37}, {"uid": "241cb144f743e141", "name": "相等断言结果：成功", "source": "241cb144f743e141.json", "type": "application/json", "size": 53}, {"uid": "6e33297d64626d62", "name": "📋 测试执行日志", "source": "6e33297d64626d62.txt", "type": "text/plain", "size": 6304}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 13, "hasContent": true, "stepsCount": 0}, "afterStages": [{"name": "capture_logs_for_allure::0", "time": {"start": 1751853246256, "stop": 1751853246268, "duration": 12}, "status": "passed", "steps": [], "attachments": [{"uid": "a1ddd8758e9adb02", "name": "📋 测试执行日志 (完整记录)", "source": "a1ddd8758e9adb02.txt", "type": "text/plain", "size": 3323}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 1, "hasContent": true, "stepsCount": 0}, {"name": "function_wait::0", "time": {"start": 1751853245504, "stop": 1751853246256, "duration": 752}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "print_info::0", "time": {"start": 1751853245502, "stop": 1751853245502, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}], "labels": [{"name": "story", "value": "C01_演出模块流程"}, {"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "28812-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "2415c8f851173e7.json", "parameterValues": []}