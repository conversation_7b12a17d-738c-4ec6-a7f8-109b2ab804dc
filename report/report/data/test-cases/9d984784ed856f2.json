{"uid": "9d984784ed856f2", "name": "首页广告位", "fullName": "testcase.play.test_play.TestPlay#test_play", "historyId": "998f7fb53b18ea3045f71922b591e307", "time": {"start": 1751853894112, "stop": 1751853894392, "duration": 280}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "function_wait", "time": {"start": 1751853893360, "stop": 1751853894111, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "capture_logs_for_allure", "time": {"start": 1751853893359, "stop": 1751853893360, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "clear_extract", "time": {"start": 1751853884946, "stop": 1751853885997, "duration": 1051}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "_session_faker", "time": {"start": 1751853884907, "stop": 1751853884946, "duration": 39}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "print_info", "time": {"start": 1751853894111, "stop": 1751853894111, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "f80f7630a06cb7b6", "name": "接口地址", "source": "f80f7630a06cb7b6.json", "type": "application/json", "size": 44}, {"uid": "4149996515cf8dc1", "name": "接口名称", "source": "4149996515cf8dc1.json", "type": "application/json", "size": 15}, {"uid": "3074748e8fbd56d8", "name": "请求方式", "source": "3074748e8fbd56d8.json", "type": "application/json", "size": 4}, {"uid": "7b5fcd7c8ef81a9", "name": "请求头", "source": "7b5fcd7c8ef81a9.json", "type": "application/json", "size": 122}, {"uid": "799f9c082de2956d", "name": "<PERSON><PERSON>", "source": "799f9c082de2956d.json", "type": "application/json", "size": 12}, {"uid": "101a12c4ad5bcff0", "name": "测试用例名称", "source": "101a12c4ad5bcff0.json", "type": "application/json", "size": 24}, {"uid": "a4b92e190a48788a", "name": "参数类型", "source": "a4b92e190a48788a.json", "type": "application/json", "size": 4}, {"uid": "854532695cf0a3d0", "name": "请求参数json格式", "source": "854532695cf0a3d0.json", "type": "application/json", "size": 179}, {"uid": "4b4387ae000d3e0d", "name": "请求参数实际入参", "source": "4b4387ae000d3e0d.json", "type": "application/json", "size": 189}, {"uid": "be9b151899d41157", "name": "接口实际响应信息", "source": "be9b151899d41157.json", "type": "application/json", "size": 3479}, {"uid": "d23a3c768aac1a3a", "name": "状态码断言结果：成功", "source": "d23a3c768aac1a3a.txt", "type": "text/plain", "size": 37}, {"uid": "f37b17191c36a763", "name": "相等断言结果：成功", "source": "f37b17191c36a763.json", "type": "application/json", "size": 53}, {"uid": "88e092455376a372", "name": "📋 测试执行日志", "source": "88e092455376a372.txt", "type": "text/plain", "size": 27582}], "parameters": [], "hasContent": true, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 13}, "afterStages": [{"name": "function_wait::0", "time": {"start": 1751853894393, "stop": 1751853895144, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "capture_logs_for_allure::0", "time": {"start": 1751853895144, "stop": 1751853895155, "duration": 11}, "status": "passed", "steps": [], "attachments": [{"uid": "dadef6ba969f6702", "name": "📋 测试执行日志 (完整记录)", "source": "dadef6ba969f6702.txt", "type": "text/plain", "size": 4779}], "parameters": [], "hasContent": true, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "print_info::0", "time": {"start": 1751853894392, "stop": 1751853894393, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "39132-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "9d984784ed856f2.json", "parameterValues": []}