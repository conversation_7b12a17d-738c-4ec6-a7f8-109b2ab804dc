{"uid": "44712ed9b2137610", "name": "**搜索**", "fullName": "testcase.play.test_play.TestPlay#test_play", "historyId": "80ea16c71eb34b6e85110c3f875859f1", "time": {"start": 1751853897749, "stop": 1751853898049, "duration": 300}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "function_wait", "time": {"start": 1751853896996, "stop": 1751853897747, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "capture_logs_for_allure", "time": {"start": 1751853896996, "stop": 1751853896996, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "clear_extract", "time": {"start": 1751853884946, "stop": 1751853885997, "duration": 1051}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "_session_faker", "time": {"start": 1751853884907, "stop": 1751853884946, "duration": 39}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "print_info", "time": {"start": 1751853897747, "stop": 1751853897749, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "da5ea5cc3e0aee83", "name": "接口地址", "source": "da5ea5cc3e0aee83.json", "type": "application/json", "size": 53}, {"uid": "48a91febfb3a4e56", "name": "接口名称", "source": "48a91febfb3a4e56.json", "type": "application/json", "size": 10}, {"uid": "1b0bd8f2ac61f694", "name": "请求方式", "source": "1b0bd8f2ac61f694.json", "type": "application/json", "size": 4}, {"uid": "75b2e7e9ecc162df", "name": "请求头", "source": "75b2e7e9ecc162df.json", "type": "application/json", "size": 122}, {"uid": "c0b0ca7c4b89c65c", "name": "<PERSON><PERSON>", "source": "c0b0ca7c4b89c65c.json", "type": "application/json", "size": 12}, {"uid": "39c72b166abac1fd", "name": "测试用例名称", "source": "39c72b166abac1fd.json", "type": "application/json", "size": 30}, {"uid": "5439a0eada61420", "name": "参数类型", "source": "5439a0eada61420.json", "type": "application/json", "size": 4}, {"uid": "ebda6ee12bdd9f02", "name": "请求参数json格式", "source": "ebda6ee12bdd9f02.json", "type": "application/json", "size": 201}, {"uid": "bbc9c06189b9beaa", "name": "请求参数实际入参", "source": "bbc9c06189b9beaa.json", "type": "application/json", "size": 211}, {"uid": "859de44f5cfd7e73", "name": "接口实际响应信息", "source": "859de44f5cfd7e73.json", "type": "application/json", "size": 22387}, {"uid": "7082e86ab70c47d8", "name": "状态码断言结果：成功", "source": "7082e86ab70c47d8.txt", "type": "text/plain", "size": 37}, {"uid": "4a7034bd29482987", "name": "相等断言结果：成功", "source": "4a7034bd29482987.json", "type": "application/json", "size": 53}, {"uid": "f3cad154aa42a4", "name": "📋 测试执行日志", "source": "f3cad154aa42a4.txt", "type": "text/plain", "size": 14921}], "parameters": [], "hasContent": true, "stepsCount": 0, "attachmentsCount": 13, "shouldDisplayMessage": false}, "afterStages": [{"name": "function_wait::0", "time": {"start": 1751853898050, "stop": 1751853898801, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "capture_logs_for_allure::0", "time": {"start": 1751853898801, "stop": 1751853898810, "duration": 9}, "status": "passed", "steps": [], "attachments": [{"uid": "4fb2e5da1020c103", "name": "📋 测试执行日志 (完整记录)", "source": "4fb2e5da1020c103.txt", "type": "text/plain", "size": 15160}], "parameters": [], "hasContent": true, "stepsCount": 0, "attachmentsCount": 1, "shouldDisplayMessage": false}, {"name": "print_info::0", "time": {"start": 1751853898050, "stop": 1751853898050, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}], "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "39132-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "44712ed9b2137610.json", "parameterValues": []}