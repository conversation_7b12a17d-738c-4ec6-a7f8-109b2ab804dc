{"uid": "e836910d0b215417", "name": "为你推荐", "fullName": "testcase.play.test_play.TestPlay#test_play", "historyId": "a03ba38a51bcc9af12332b9cb9ea6b41", "time": {"start": 1751853892274, "stop": 1751853892596, "duration": 322}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "function_wait", "time": {"start": 1751853891522, "stop": 1751853892274, "duration": 752}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "capture_logs_for_allure", "time": {"start": 1751853891522, "stop": 1751853891522, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "clear_extract", "time": {"start": 1751853884946, "stop": 1751853885997, "duration": 1051}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "_session_faker", "time": {"start": 1751853884907, "stop": 1751853884946, "duration": 39}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "print_info", "time": {"start": 1751853892274, "stop": 1751853892274, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "60dab2d8e8489d7", "name": "接口地址", "source": "60dab2d8e8489d7.json", "type": "application/json", "size": 54}, {"uid": "86182ecdbfed08e6", "name": "接口名称", "source": "86182ecdbfed08e6.json", "type": "application/json", "size": 12}, {"uid": "d3faac06907bfd4b", "name": "请求方式", "source": "d3faac06907bfd4b.json", "type": "application/json", "size": 4}, {"uid": "bdeec395b519db7f", "name": "请求头", "source": "bdeec395b519db7f.json", "type": "application/json", "size": 122}, {"uid": "17a7b2380eec9176", "name": "<PERSON><PERSON>", "source": "17a7b2380eec9176.json", "type": "application/json", "size": 12}, {"uid": "87cd210d10f1fce1", "name": "测试用例名称", "source": "87cd210d10f1fce1.json", "type": "application/json", "size": 30}, {"uid": "6955c691a48b2957", "name": "参数类型", "source": "6955c691a48b2957.json", "type": "application/json", "size": 4}, {"uid": "8ab0dc11be13b6c2", "name": "请求参数json格式", "source": "8ab0dc11be13b6c2.json", "type": "application/json", "size": 214}, {"uid": "2ac363ac0632a44d", "name": "请求参数实际入参", "source": "2ac363ac0632a44d.json", "type": "application/json", "size": 224}, {"uid": "335917c0dc45d19f", "name": "接口实际响应信息", "source": "335917c0dc45d19f.json", "type": "application/json", "size": 44012}, {"uid": "c940113d79875879", "name": "状态码断言结果：成功", "source": "c940113d79875879.txt", "type": "text/plain", "size": 37}, {"uid": "94f8c3108f39efaf", "name": "相等断言结果：成功", "source": "94f8c3108f39efaf.json", "type": "application/json", "size": 53}, {"uid": "896e363170a27f65", "name": "📋 测试执行日志", "source": "896e363170a27f65.txt", "type": "text/plain", "size": 14945}], "parameters": [], "hasContent": true, "stepsCount": 0, "attachmentsCount": 13, "shouldDisplayMessage": false}, "afterStages": [{"name": "function_wait::0", "time": {"start": 1751853892598, "stop": 1751853893349, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "capture_logs_for_allure::0", "time": {"start": 1751853893349, "stop": 1751853893358, "duration": 9}, "status": "passed", "steps": [], "attachments": [{"uid": "1134ee2dc9dc2507", "name": "📋 测试执行日志 (完整记录)", "source": "1134ee2dc9dc2507.txt", "type": "text/plain", "size": 27582}], "parameters": [], "hasContent": true, "stepsCount": 0, "attachmentsCount": 1, "shouldDisplayMessage": false}, {"name": "print_info::0", "time": {"start": 1751853892597, "stop": 1751853892597, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}], "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "39132-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "e836910d0b215417.json", "parameterValues": []}