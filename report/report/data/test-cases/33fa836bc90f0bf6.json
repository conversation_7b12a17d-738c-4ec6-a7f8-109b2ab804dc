{"uid": "33fa836bc90f0bf6", "name": "近期特惠", "fullName": "testcase.play.test_play.TestPlay#test_play", "historyId": "dcf03e580545918eaa3c4bf9273130d9", "time": {"start": 1751852995665, "stop": 1751852995983, "duration": 318}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "clear_extract", "time": {"start": 1751852990929, "stop": 1751852991206, "duration": 277}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "function_wait", "time": {"start": 1751852994914, "stop": 1751852995665, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "_session_faker", "time": {"start": 1751852990879, "stop": 1751852990927, "duration": 48}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "capture_logs_for_allure", "time": {"start": 1751852994914, "stop": 1751852994914, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "print_info", "time": {"start": 1751852995665, "stop": 1751852995665, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "440f642014e5f7a6", "name": "接口地址", "source": "440f642014e5f7a6.json", "type": "application/json", "size": 54}, {"uid": "91e8d067c282cc27", "name": "接口名称", "source": "91e8d067c282cc27.json", "type": "application/json", "size": 12}, {"uid": "6c1a05adddc32b83", "name": "请求方式", "source": "6c1a05adddc32b83.json", "type": "application/json", "size": 4}, {"uid": "508e71067ecb9068", "name": "请求头", "source": "508e71067ecb9068.json", "type": "application/json", "size": 122}, {"uid": "244981abbb16869c", "name": "<PERSON><PERSON>", "source": "244981abbb16869c.json", "type": "application/json", "size": 12}, {"uid": "5d20fddc0917cbb1", "name": "测试用例名称", "source": "5d20fddc0917cbb1.json", "type": "application/json", "size": 30}, {"uid": "bde5dceeb13907aa", "name": "参数类型", "source": "bde5dceeb13907aa.json", "type": "application/json", "size": 4}, {"uid": "f7e4478b2491b52", "name": "请求参数json格式", "source": "f7e4478b2491b52.json", "type": "application/json", "size": 214}, {"uid": "2bd54e3be7e91c11", "name": "请求参数实际入参", "source": "2bd54e3be7e91c11.json", "type": "application/json", "size": 224}, {"uid": "8d8f1fc4a6793ad9", "name": "接口实际响应信息", "source": "8d8f1fc4a6793ad9.json", "type": "application/json", "size": 22188}, {"uid": "19621528c38f7133", "name": "状态码断言结果：成功", "source": "19621528c38f7133.txt", "type": "text/plain", "size": 37}, {"uid": "232a6d21d60129b9", "name": "相等断言结果：成功", "source": "232a6d21d60129b9.json", "type": "application/json", "size": 53}, {"uid": "353ab981682bcf94", "name": "📋 测试执行日志", "source": "353ab981682bcf94.txt", "type": "text/plain", "size": 14716}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 13, "hasContent": true, "stepsCount": 0}, "afterStages": [{"name": "function_wait::0", "time": {"start": 1751852995985, "stop": 1751852996736, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "capture_logs_for_allure::0", "time": {"start": 1751852996737, "stop": 1751852996749, "duration": 12}, "status": "passed", "steps": [], "attachments": [{"uid": "50ba7d414b5cf2f6", "name": "📋 测试执行日志 (完整记录)", "source": "50ba7d414b5cf2f6.txt", "type": "text/plain", "size": 14715}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 1, "hasContent": true, "stepsCount": 0}, {"name": "print_info::0", "time": {"start": 1751852995984, "stop": 1751852995984, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}], "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "37476-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [{"name": "api_info", "value": "{'baseInfo': {'module': '演出票', 'api_name': '近期特惠', 'host': '${get_host(play)}', 'url': '/api/show/info/home_near_show_list', 'method': 'post', 'headers': '${get_headers(app)}'}, 'testCase': {'case_name': '获取近期特惠演出列表', 'validation': [{'code': 200}, {'eq': {'code': 0}}], 'extract': 0, 'extract_list': np.float64(0.0), 'data': {'page': 1, 'pageSize': 20, 'channelCode': '${appKey()}', 'token': '${get_extract_data(token)}'}}}"}], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "33fa836bc90f0bf6.json", "parameterValues": ["{'baseInfo': {'module': '演出票', 'api_name': '近期特惠', 'host': '${get_host(play)}', 'url': '/api/show/info/home_near_show_list', 'method': 'post', 'headers': '${get_headers(app)}'}, 'testCase': {'case_name': '获取近期特惠演出列表', 'validation': [{'code': 200}, {'eq': {'code': 0}}], 'extract': 0, 'extract_list': np.float64(0.0), 'data': {'page': 1, 'pageSize': 20, 'channelCode': '${appKey()}', 'token': '${get_extract_data(token)}'}}}"]}