{"uid": "534e0a29aa75e0da", "name": "首页广告位", "fullName": "testcase.play.test_play.TestPlay#test_play", "historyId": "998f7fb53b18ea3045f71922b591e307", "time": {"start": 1751853894112, "stop": 1751853894392, "duration": 280}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "function_wait", "time": {"start": 1751853893360, "stop": 1751853894111, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "capture_logs_for_allure", "time": {"start": 1751853893359, "stop": 1751853893360, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "clear_extract", "time": {"start": 1751853884946, "stop": 1751853885997, "duration": 1051}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "_session_faker", "time": {"start": 1751853884907, "stop": 1751853884946, "duration": 39}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "print_info", "time": {"start": 1751853894111, "stop": 1751853894111, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "c9dc4d101ba5bc54", "name": "接口地址", "source": "c9dc4d101ba5bc54.json", "type": "application/json", "size": 44}, {"uid": "fea1ca7def1fe8ca", "name": "接口名称", "source": "fea1ca7def1fe8ca.json", "type": "application/json", "size": 15}, {"uid": "c865074d29c481ef", "name": "请求方式", "source": "c865074d29c481ef.json", "type": "application/json", "size": 4}, {"uid": "474c72ae3a5c12a", "name": "请求头", "source": "474c72ae3a5c12a.json", "type": "application/json", "size": 122}, {"uid": "330d5a9c0f9685ac", "name": "<PERSON><PERSON>", "source": "330d5a9c0f9685ac.json", "type": "application/json", "size": 12}, {"uid": "a34edf2c38f9dc59", "name": "测试用例名称", "source": "a34edf2c38f9dc59.json", "type": "application/json", "size": 24}, {"uid": "4d03d06904ecdd52", "name": "参数类型", "source": "4d03d06904ecdd52.json", "type": "application/json", "size": 4}, {"uid": "7fc77fb5cf5eb952", "name": "请求参数json格式", "source": "7fc77fb5cf5eb952.json", "type": "application/json", "size": 179}, {"uid": "ee5c3093cd1ea5da", "name": "请求参数实际入参", "source": "ee5c3093cd1ea5da.json", "type": "application/json", "size": 189}, {"uid": "d4b9f8c32d5492aa", "name": "接口实际响应信息", "source": "d4b9f8c32d5492aa.json", "type": "application/json", "size": 3479}, {"uid": "6adfab88a2f41e18", "name": "状态码断言结果：成功", "source": "6adfab88a2f41e18.txt", "type": "text/plain", "size": 37}, {"uid": "dedbb3a56aa660c0", "name": "相等断言结果：成功", "source": "dedbb3a56aa660c0.json", "type": "application/json", "size": 53}, {"uid": "cdb711ee5c9bc453", "name": "📋 测试执行日志", "source": "cdb711ee5c9bc453.txt", "type": "text/plain", "size": 27582}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentsCount": 13, "shouldDisplayMessage": false}, "afterStages": [{"name": "function_wait::0", "time": {"start": 1751853894393, "stop": 1751853895144, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "capture_logs_for_allure::0", "time": {"start": 1751853895144, "stop": 1751853895155, "duration": 11}, "status": "passed", "steps": [], "attachments": [{"uid": "1272bdfa256a2dec", "name": "📋 测试执行日志 (完整记录)", "source": "1272bdfa256a2dec.txt", "type": "text/plain", "size": 4779}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentsCount": 1, "shouldDisplayMessage": false}, {"name": "print_info::0", "time": {"start": 1751853894392, "stop": 1751853894393, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}], "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "39132-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "534e0a29aa75e0da.json", "parameterValues": []}