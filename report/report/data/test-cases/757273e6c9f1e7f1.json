{"uid": "757273e6c9f1e7f1", "name": "取消订单", "fullName": "testcase.play.test_play.TestPlay#test_play", "historyId": "097386a9d43be40d3c28c1874a6e169b", "time": {"start": 1751853910547, "stop": 1751853912006, "duration": 1459}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "print_info", "time": {"start": 1751853910547, "stop": 1751853910547, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "clear_extract", "time": {"start": 1751853884946, "stop": 1751853885997, "duration": 1051}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "_session_faker", "time": {"start": 1751853884907, "stop": 1751853884946, "duration": 39}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "capture_logs_for_allure", "time": {"start": 1751853909796, "stop": 1751853909796, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "function_wait", "time": {"start": 1751853909796, "stop": 1751853910547, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "74ecf19751e9971e", "name": "接口地址", "source": "74ecf19751e9971e.json", "type": "application/json", "size": 42}, {"uid": "f603ba112c0bb3a7", "name": "接口名称", "source": "f603ba112c0bb3a7.json", "type": "application/json", "size": 12}, {"uid": "565618011a7bc50a", "name": "请求方式", "source": "565618011a7bc50a.json", "type": "application/json", "size": 4}, {"uid": "5031465254262f6b", "name": "请求头", "source": "5031465254262f6b.json", "type": "application/json", "size": 122}, {"uid": "17c39ed5ab8f1ef2", "name": "<PERSON><PERSON>", "source": "17c39ed5ab8f1ef2.json", "type": "application/json", "size": 12}, {"uid": "96e22fcc0f2b557f", "name": "测试用例名称", "source": "96e22fcc0f2b557f.json", "type": "application/json", "size": 12}, {"uid": "21a1f4afc7da6fa6", "name": "参数类型", "source": "21a1f4afc7da6fa6.json", "type": "application/json", "size": 4}, {"uid": "4dd66be5328f2a41", "name": "请求参数json格式", "source": "4dd66be5328f2a41.json", "type": "application/json", "size": 217}, {"uid": "35757f8ff8be23e7", "name": "请求参数实际入参", "source": "35757f8ff8be23e7.json", "type": "application/json", "size": 227}, {"uid": "537da6decdbd9e3b", "name": "接口实际响应信息", "source": "537da6decdbd9e3b.json", "type": "application/json", "size": 48}, {"uid": "c1c9d2e879945fe9", "name": "状态码断言结果：成功", "source": "c1c9d2e879945fe9.txt", "type": "text/plain", "size": 37}, {"uid": "310aab51d3c461d6", "name": "相等断言结果：成功", "source": "310aab51d3c461d6.json", "type": "application/json", "size": 53}, {"uid": "13a57637b2021209", "name": "📋 测试执行日志", "source": "13a57637b2021209.txt", "type": "text/plain", "size": 2968}], "parameters": [], "hasContent": true, "stepsCount": 0, "attachmentsCount": 13, "shouldDisplayMessage": false}, "afterStages": [{"name": "print_info::0", "time": {"start": 1751853912007, "stop": 1751853912007, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "capture_logs_for_allure::0", "time": {"start": 1751853912759, "stop": 1751853912769, "duration": 10}, "status": "passed", "steps": [], "attachments": [{"uid": "bce3e254196bec95", "name": "📋 测试执行日志 (完整记录)", "source": "bce3e254196bec95.txt", "type": "text/plain", "size": 2307}], "parameters": [], "hasContent": true, "stepsCount": 0, "attachmentsCount": 1, "shouldDisplayMessage": false}, {"name": "function_wait::0", "time": {"start": 1751853912008, "stop": 1751853912759, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}], "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "39132-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "757273e6c9f1e7f1.json", "parameterValues": []}