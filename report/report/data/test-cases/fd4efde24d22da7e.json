{"uid": "fd4efde24d22da7e", "name": "演唱会分类", "fullName": "testcase.play.test_play.TestPlay#test_play", "historyId": "47b52c96aa0c1d59c25f4bc63959c980", "time": {"start": 1751851575127, "stop": 1751851575443, "duration": 316}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "_session_faker", "time": {"start": 1751851564550, "stop": 1751851564591, "duration": 41}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "capture_logs_for_allure", "time": {"start": 1751851574375, "stop": 1751851574375, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "print_info", "time": {"start": 1751851575126, "stop": 1751851575126, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "clear_extract", "time": {"start": 1751851564591, "stop": 1751851564922, "duration": 331}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "function_wait", "time": {"start": 1751851574375, "stop": 1751851575126, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "45a50276e177ab1f", "name": "接口地址", "source": "45a50276e177ab1f.json", "type": "application/json", "size": 53}, {"uid": "b11c9de56e1a8a22", "name": "接口名称", "source": "b11c9de56e1a8a22.json", "type": "application/json", "size": 15}, {"uid": "6b514dbbbad5137", "name": "请求方式", "source": "6b514dbbbad5137.json", "type": "application/json", "size": 4}, {"uid": "2b9997aa1951698c", "name": "请求头", "source": "2b9997aa1951698c.json", "type": "application/json", "size": 122}, {"uid": "f39940725379f1ee", "name": "<PERSON><PERSON>", "source": "f39940725379f1ee.json", "type": "application/json", "size": 12}, {"uid": "5e93abe2d58b0046", "name": "测试用例名称", "source": "5e93abe2d58b0046.json", "type": "application/json", "size": 25}, {"uid": "9e82fabab95116b", "name": "参数类型", "source": "9e82fabab95116b.json", "type": "application/json", "size": 4}, {"uid": "5565f6632898653a", "name": "请求参数json格式", "source": "5565f6632898653a.json", "type": "application/json", "size": 207}, {"uid": "aea50a0f47823ea2", "name": "请求参数实际入参", "source": "aea50a0f47823ea2.json", "type": "application/json", "size": 217}, {"uid": "2321cd3e8fe43f3b", "name": "接口实际响应信息", "source": "2321cd3e8fe43f3b.json", "type": "application/json", "size": 22018}, {"uid": "1b73bfa61ed806cd", "name": "状态码断言结果：成功", "source": "1b73bfa61ed806cd.txt", "type": "text/plain", "size": 37}, {"uid": "99e246cb6a2802fd", "name": "相等断言结果：成功", "source": "99e246cb6a2802fd.json", "type": "application/json", "size": 53}, {"uid": "364da64bac1bcb14", "name": "📋 测试执行日志", "source": "364da64bac1bcb14.txt", "type": "text/plain", "size": 4549}], "parameters": [], "hasContent": true, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 13}, "afterStages": [{"name": "capture_logs_for_allure::0", "time": {"start": 1751851576196, "stop": 1751851576219, "duration": 23}, "status": "passed", "steps": [], "attachments": [{"uid": "328183c2b1df9faa", "name": "📋 测试执行日志 (完整记录)", "source": "328183c2b1df9faa.txt", "type": "text/plain", "size": 14539}], "parameters": [], "hasContent": true, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "print_info::0", "time": {"start": 1751851575444, "stop": 1751851575444, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "function_wait::0", "time": {"start": 1751851575444, "stop": 1751851576195, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "C01_演出模块流程"}, {"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "34968-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "fd4efde24d22da7e.json", "parameterValues": []}