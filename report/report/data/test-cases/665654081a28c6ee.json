{"uid": "665654081a28c6ee", "name": "演出分类", "fullName": "testcase.play.test_play.TestPlay#test_play", "historyId": "c36ab200ccb8723de7119149ea6c3088", "time": {"start": 1751853230164, "stop": 1751853230535, "duration": 371}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "print_info", "time": {"start": 1751853230163, "stop": 1751853230163, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "capture_logs_for_allure", "time": {"start": 1751853229413, "stop": 1751853229413, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "_session_faker", "time": {"start": 1751853229094, "stop": 1751853229142, "duration": 48}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "clear_extract", "time": {"start": 1751853229143, "stop": 1751853229413, "duration": 270}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "function_wait", "time": {"start": 1751853229413, "stop": 1751853230163, "duration": 750}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "ca42daa3887eb6b3", "name": "接口地址", "source": "ca42daa3887eb6b3.json", "type": "application/json", "size": 48}, {"uid": "1e9ac16de281fa1b", "name": "接口名称", "source": "1e9ac16de281fa1b.json", "type": "application/json", "size": 12}, {"uid": "d7a9f7ef2f4bc6a0", "name": "请求方式", "source": "d7a9f7ef2f4bc6a0.json", "type": "application/json", "size": 4}, {"uid": "e4fffeaad6d53aca", "name": "请求头", "source": "e4fffeaad6d53aca.json", "type": "application/json", "size": 122}, {"uid": "b4b485cf75102f5f", "name": "<PERSON><PERSON>", "source": "b4b485cf75102f5f.json", "type": "application/json", "size": 12}, {"uid": "6d19c51f843f5e22", "name": "测试用例名称", "source": "6d19c51f843f5e22.json", "type": "application/json", "size": 24}, {"uid": "dd06411dc2e23f4d", "name": "参数类型", "source": "dd06411dc2e23f4d.json", "type": "application/json", "size": 4}, {"uid": "630228f22b390d20", "name": "请求参数json格式", "source": "630228f22b390d20.json", "type": "application/json", "size": 204}, {"uid": "3c25c51eac77da7a", "name": "请求参数实际入参", "source": "3c25c51eac77da7a.json", "type": "application/json", "size": 214}, {"uid": "c9e463baacf84a0e", "name": "接口实际响应信息", "source": "c9e463baacf84a0e.json", "type": "application/json", "size": 3148}, {"uid": "e1613d9345698917", "name": "状态码断言结果：成功", "source": "e1613d9345698917.txt", "type": "text/plain", "size": 37}, {"uid": "ed29429e3dbf836e", "name": "相等断言结果：成功", "source": "ed29429e3dbf836e.json", "type": "application/json", "size": 53}, {"uid": "586df2aaae6133e6", "name": "📋 测试执行日志", "source": "586df2aaae6133e6.txt", "type": "text/plain", "size": 11711}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 13, "hasContent": true, "stepsCount": 0}, "afterStages": [{"name": "print_info::0", "time": {"start": 1751853230536, "stop": 1751853230537, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "capture_logs_for_allure::0", "time": {"start": 1751853231289, "stop": 1751853231303, "duration": 14}, "status": "passed", "steps": [], "attachments": [{"uid": "8510227473925a12", "name": "📋 测试执行日志 (完整记录)", "source": "8510227473925a12.txt", "type": "text/plain", "size": 4525}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 1, "hasContent": true, "stepsCount": 0}, {"name": "function_wait::0", "time": {"start": 1751853230537, "stop": 1751853231288, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}], "labels": [{"name": "story", "value": "C01_演出模块流程"}, {"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "28812-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "665654081a28c6ee.json", "parameterValues": []}