{"uid": "851d23b72b2a5541", "name": "取消订单", "fullName": "testcase.play.test_play.TestPlay#test_play", "historyId": "097386a9d43be40d3c28c1874a6e169b", "time": {"start": 1751851590380, "stop": 1751851591654, "duration": 1274}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "_session_faker", "time": {"start": 1751851564550, "stop": 1751851564591, "duration": 41}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "print_info", "time": {"start": 1751851590379, "stop": 1751851590379, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "clear_extract", "time": {"start": 1751851564591, "stop": 1751851564922, "duration": 331}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "function_wait", "time": {"start": 1751851589629, "stop": 1751851590379, "duration": 750}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "capture_logs_for_allure", "time": {"start": 1751851589629, "stop": 1751851589629, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "b45507fb8482ea06", "name": "接口地址", "source": "b45507fb8482ea06.json", "type": "application/json", "size": 42}, {"uid": "804f4f7e98e77d3c", "name": "接口名称", "source": "804f4f7e98e77d3c.json", "type": "application/json", "size": 12}, {"uid": "7de1cc10e0b33823", "name": "请求方式", "source": "7de1cc10e0b33823.json", "type": "application/json", "size": 4}, {"uid": "55d6fc5864319319", "name": "请求头", "source": "55d6fc5864319319.json", "type": "application/json", "size": 122}, {"uid": "eb150d1a393d6d66", "name": "<PERSON><PERSON>", "source": "eb150d1a393d6d66.json", "type": "application/json", "size": 12}, {"uid": "3ea797b005410114", "name": "测试用例名称", "source": "3ea797b005410114.json", "type": "application/json", "size": 12}, {"uid": "23a02938eb842937", "name": "参数类型", "source": "23a02938eb842937.json", "type": "application/json", "size": 4}, {"uid": "e2acd607b4d18579", "name": "请求参数json格式", "source": "e2acd607b4d18579.json", "type": "application/json", "size": 217}, {"uid": "7cf3efea3531b49a", "name": "请求参数实际入参", "source": "7cf3efea3531b49a.json", "type": "application/json", "size": 227}, {"uid": "edf1b3682131154f", "name": "接口实际响应信息", "source": "edf1b3682131154f.json", "type": "application/json", "size": 48}, {"uid": "bc5937dd2b6cb623", "name": "状态码断言结果：成功", "source": "bc5937dd2b6cb623.txt", "type": "text/plain", "size": 37}, {"uid": "7238e50e95b0d047", "name": "相等断言结果：成功", "source": "7238e50e95b0d047.json", "type": "application/json", "size": 53}, {"uid": "f166845290a1f5d5", "name": "📋 测试执行日志", "source": "f166845290a1f5d5.txt", "type": "text/plain", "size": 2439}], "parameters": [], "hasContent": true, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 13}, "afterStages": [{"name": "print_info::0", "time": {"start": 1751851591655, "stop": 1751851591655, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "function_wait::0", "time": {"start": 1751851591655, "stop": 1751851592406, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "capture_logs_for_allure::0", "time": {"start": 1751851592407, "stop": 1751851592415, "duration": 8}, "status": "passed", "steps": [], "attachments": [{"uid": "fc2854099af7dfab", "name": "📋 测试执行日志 (完整记录)", "source": "fc2854099af7dfab.txt", "type": "text/plain", "size": 2076}], "parameters": [], "hasContent": true, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 1}], "labels": [{"name": "story", "value": "C01_演出模块流程"}, {"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "34968-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "851d23b72b2a5541.json", "parameterValues": []}