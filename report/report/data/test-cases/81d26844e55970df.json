{"uid": "81d26844e55970df", "name": "项目下场次票品列表", "fullName": "testcase.play.test_play.TestPlay#test_play", "historyId": "3287c72f85d7169cf4925a7d71668b28", "time": {"start": 1751853903726, "stop": 1751853904261, "duration": 535}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "capture_logs_for_allure", "time": {"start": 1751853902975, "stop": 1751853902975, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "clear_extract", "time": {"start": 1751853884946, "stop": 1751853885997, "duration": 1051}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "print_info", "time": {"start": 1751853903725, "stop": 1751853903726, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "_session_faker", "time": {"start": 1751853884907, "stop": 1751853884946, "duration": 39}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "function_wait", "time": {"start": 1751853902975, "stop": 1751853903725, "duration": 750}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "1cddc1f065c63a7a", "name": "接口地址", "source": "1cddc1f065c63a7a.json", "type": "application/json", "size": 52}, {"uid": "f95ba9aefb69845f", "name": "接口名称", "source": "f95ba9aefb69845f.json", "type": "application/json", "size": 27}, {"uid": "b2a9df73ba4f2f5f", "name": "请求方式", "source": "b2a9df73ba4f2f5f.json", "type": "application/json", "size": 3}, {"uid": "cbf4ab3a5eb5e654", "name": "请求头", "source": "cbf4ab3a5eb5e654.json", "type": "application/json", "size": 122}, {"uid": "ec13e4f117e2189d", "name": "<PERSON><PERSON>", "source": "ec13e4f117e2189d.json", "type": "application/json", "size": 12}, {"uid": "872d745d04f40f2d", "name": "测试用例名称", "source": "872d745d04f40f2d.json", "type": "application/json", "size": 39}, {"uid": "b0c714c4121779ce", "name": "参数类型", "source": "b0c714c4121779ce.json", "type": "application/json", "size": 6}, {"uid": "d21f83ee21f13d18", "name": "请求参数json格式", "source": "d21f83ee21f13d18.json", "type": "application/json", "size": 199}, {"uid": "b7bba51657c533fd", "name": "请求参数实际入参", "source": "b7bba51657c533fd.json", "type": "application/json", "size": 209}, {"uid": "cbdb153f2dfd90e1", "name": "接口实际响应信息", "source": "cbdb153f2dfd90e1.json", "type": "application/json", "size": 7483}, {"uid": "e01db544204322af", "name": "状态码断言结果：成功", "source": "e01db544204322af.txt", "type": "text/plain", "size": 37}, {"uid": "a5eb024261a4c119", "name": "相等断言结果：成功", "source": "a5eb024261a4c119.json", "type": "application/json", "size": 53}, {"uid": "a5ccd9a9eec4d0f3", "name": "📋 测试执行日志", "source": "a5ccd9a9eec4d0f3.txt", "type": "text/plain", "size": 3123}], "parameters": [], "hasContent": true, "stepsCount": 0, "attachmentsCount": 13, "shouldDisplayMessage": false}, "afterStages": [{"name": "capture_logs_for_allure::0", "time": {"start": 1751853905014, "stop": 1751853905023, "duration": 9}, "status": "passed", "steps": [], "attachments": [{"uid": "2d3b3f05a4b20e3f", "name": "📋 测试执行日志 (完整记录)", "source": "2d3b3f05a4b20e3f.txt", "type": "text/plain", "size": 5242}], "parameters": [], "hasContent": true, "stepsCount": 0, "attachmentsCount": 1, "shouldDisplayMessage": false}, {"name": "print_info::0", "time": {"start": 1751853904262, "stop": 1751853904262, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "function_wait::0", "time": {"start": 1751853904263, "stop": 1751853905014, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}], "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "39132-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "81d26844e55970df.json", "parameterValues": []}