{"uid": "9ce5adb4ce605eec", "name": "今日必抢", "fullName": "testcase.play.test_play.TestPlay#test_play", "historyId": "240ea0a77309ba41fd9d13d8f2320d63", "time": {"start": 1751853729788, "stop": 1751853731128, "duration": 1340}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "_session_faker", "time": {"start": 1751853726731, "stop": 1751853726779, "duration": 48}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "hasContent": false, "stepsCount": 0, "attachmentsCount": 0}, {"name": "function_wait", "time": {"start": 1751853729037, "stop": 1751853729788, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "hasContent": false, "stepsCount": 0, "attachmentsCount": 0}, {"name": "clear_extract", "time": {"start": 1751853726779, "stop": 1751853727168, "duration": 389}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "hasContent": false, "stepsCount": 0, "attachmentsCount": 0}, {"name": "print_info", "time": {"start": 1751853729788, "stop": 1751853729788, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "hasContent": false, "stepsCount": 0, "attachmentsCount": 0}, {"name": "capture_logs_for_allure", "time": {"start": 1751853729037, "stop": 1751853729037, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "hasContent": false, "stepsCount": 0, "attachmentsCount": 0}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "a805ae941081ba5c", "name": "接口地址", "source": "a805ae941081ba5c.json", "type": "application/json", "size": 55}, {"uid": "4c412adcd3adb9b3", "name": "接口名称", "source": "4c412adcd3adb9b3.json", "type": "application/json", "size": 12}, {"uid": "92010b553b695c6c", "name": "请求方式", "source": "92010b553b695c6c.json", "type": "application/json", "size": 4}, {"uid": "1b2400db906a5292", "name": "请求头", "source": "1b2400db906a5292.json", "type": "application/json", "size": 122}, {"uid": "a09dedaf44a4b9f", "name": "<PERSON><PERSON>", "source": "a09dedaf44a4b9f.json", "type": "application/json", "size": 12}, {"uid": "88084efecf153c7c", "name": "测试用例名称", "source": "88084efecf153c7c.json", "type": "application/json", "size": 30}, {"uid": "e7097d2ca676273", "name": "参数类型", "source": "e7097d2ca676273.json", "type": "application/json", "size": 4}, {"uid": "c6b4d3bcde8aebf6", "name": "请求参数json格式", "source": "c6b4d3bcde8aebf6.json", "type": "application/json", "size": 214}, {"uid": "49737e800123f21d", "name": "请求参数实际入参", "source": "49737e800123f21d.json", "type": "application/json", "size": 224}, {"uid": "5a1351c13cc10df", "name": "接口实际响应信息", "source": "5a1351c13cc10df.json", "type": "application/json", "size": 22188}, {"uid": "b7fabfe999664689", "name": "状态码断言结果：成功", "source": "b7fabfe999664689.txt", "type": "text/plain", "size": 37}, {"uid": "dd811ea5f88ad678", "name": "相等断言结果：成功", "source": "dd811ea5f88ad678.json", "type": "application/json", "size": 53}, {"uid": "1f8654d147a7acf7", "name": "📋 测试执行日志", "source": "1f8654d147a7acf7.txt", "type": "text/plain", "size": 4097}], "parameters": [], "shouldDisplayMessage": false, "hasContent": true, "stepsCount": 0, "attachmentsCount": 13}, "afterStages": [{"name": "function_wait::0", "time": {"start": 1751853731130, "stop": 1751853731881, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "hasContent": false, "stepsCount": 0, "attachmentsCount": 0}, {"name": "print_info::0", "time": {"start": 1751853731129, "stop": 1751853731129, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "hasContent": false, "stepsCount": 0, "attachmentsCount": 0}, {"name": "capture_logs_for_allure::0", "time": {"start": 1751853731881, "stop": 1751853731892, "duration": 11}, "status": "passed", "steps": [], "attachments": [{"uid": "1ea469fee6888e70", "name": "📋 测试执行日志 (完整记录)", "source": "1ea469fee6888e70.txt", "type": "text/plain", "size": 14716}], "parameters": [], "shouldDisplayMessage": false, "hasContent": true, "stepsCount": 0, "attachmentsCount": 1}], "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "39072-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [{"name": "api_info", "value": "{'baseInfo': {'module': '演出票', 'api_name': '今日必抢', 'host': '${get_host(play)}', 'url': '/api/show/info/home_today_sale_list', 'method': 'post', 'headers': '${get_headers(app)}'}, 'testCase': {'case_name': '获取今日必抢演出列表', 'validation': [{'code': 200}, {'eq': {'code': 0}}], 'extract': {'extract_auto': 'home_today_sale_list'}, 'extract_list': np.float64(0.0), 'data': {'page': 1, 'pageSize': 20, 'channelCode': '${appKey()}', 'token': '${get_extract_data(token)}'}}}"}], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "9ce5adb4ce605eec.json", "parameterValues": ["{'baseInfo': {'module': '演出票', 'api_name': '今日必抢', 'host': '${get_host(play)}', 'url': '/api/show/info/home_today_sale_list', 'method': 'post', 'headers': '${get_headers(app)}'}, 'testCase': {'case_name': '获取今日必抢演出列表', 'validation': [{'code': 200}, {'eq': {'code': 0}}], 'extract': {'extract_auto': 'home_today_sale_list'}, 'extract_list': np.float64(0.0), 'data': {'page': 1, 'pageSize': 20, 'channelCode': '${appKey()}', 'token': '${get_extract_data(token)}'}}}"]}