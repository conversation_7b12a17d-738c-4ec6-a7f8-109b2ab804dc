{"uid": "27441bbaaece18d1", "name": "查询影讯接口", "fullName": "testcase.demo_test.test_demo.TestFilmOrder#test_film_order", "historyId": "e9c2ea8fd9c81240c2b50d2a9668daf2", "time": {"start": 1751853913524, "stop": 1751853913725, "duration": 201}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "capture_logs_for_allure", "time": {"start": 1751853912773, "stop": 1751853912773, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "function_wait", "time": {"start": 1751853912773, "stop": 1751853913524, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "clear_extract", "time": {"start": 1751853884946, "stop": 1751853885997, "duration": 1051}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "_session_faker", "time": {"start": 1751853884907, "stop": 1751853884946, "duration": 39}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "print_info", "time": {"start": 1751853913524, "stop": 1751853913524, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "ca77b20bfbf79b11", "name": "接口地址", "source": "ca77b20bfbf79b11.json", "type": "application/json", "size": 48}, {"uid": "2efbe2224d7deba6", "name": "接口名称", "source": "2efbe2224d7deba6.json", "type": "application/json", "size": 18}, {"uid": "1d35efbd2d0c5c33", "name": "请求方式", "source": "1d35efbd2d0c5c33.json", "type": "application/json", "size": 4}, {"uid": "ec7f8b402777c307", "name": "请求头", "source": "ec7f8b402777c307.json", "type": "application/json", "size": 15}, {"uid": "518d0c09768f8e47", "name": "<PERSON><PERSON>", "source": "518d0c09768f8e47.json", "type": "application/json", "size": 12}, {"uid": "d3b4e4394b673ae4", "name": "测试用例名称", "source": "d3b4e4394b673ae4.json", "type": "application/json", "size": 27}, {"uid": "83921a8fe8ec3eee", "name": "参数类型", "source": "83921a8fe8ec3eee.json", "type": "application/json", "size": 4}, {"uid": "9969e279d23b45f2", "name": "请求参数json格式", "source": "9969e279d23b45f2.json", "type": "application/json", "size": 493}, {"uid": "9236b86d82061ae0", "name": "请求参数实际入参", "source": "9236b86d82061ae0.json", "type": "application/json", "size": 436}, {"uid": "f7e9ae4fa07d24fa", "name": "接口实际响应信息", "source": "f7e9ae4fa07d24fa.json", "type": "application/json", "size": 52017}, {"uid": "9627b62218f187fe", "name": "状态码断言结果：成功", "source": "9627b62218f187fe.txt", "type": "text/plain", "size": 37}, {"uid": "9c6d14c301ffdadf", "name": "相等断言结果：成功", "source": "9c6d14c301ffdadf.json", "type": "application/json", "size": 53}, {"uid": "b47ad02c2d9f0f4d", "name": "相等断言结果：成功", "source": "b47ad02c2d9f0f4d.json", "type": "application/json", "size": 77}, {"uid": "eb06068e37a8e3b0", "name": "📋 测试执行日志", "source": "eb06068e37a8e3b0.txt", "type": "text/plain", "size": 2307}], "parameters": [], "hasContent": true, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 14}, "afterStages": [{"name": "capture_logs_for_allure::0", "time": {"start": 1751853914477, "stop": 1751853914488, "duration": 11}, "status": "passed", "steps": [], "attachments": [{"uid": "9c6a67e65cf322e8", "name": "📋 测试执行日志 (完整记录)", "source": "9c6a67e65cf322e8.txt", "type": "text/plain", "size": 30345}], "parameters": [], "hasContent": true, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "function_wait::0", "time": {"start": 1751853913727, "stop": 1751853914477, "duration": 750}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "print_info::0", "time": {"start": 1751853913726, "stop": 1751853913726, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "C02_电影下单流程"}, {"name": "feature", "value": "M02_电影订票模块接口测试"}, {"name": "parentSuite", "value": "testcase.demo_test"}, {"name": "suite", "value": "test_demo"}, {"name": "subSuite", "value": "TestFilmOrder"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "39132-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.demo_test.test_demo"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "27441bbaaece18d1.json", "parameterValues": []}