{"uid": "1e2cd764a486cb90", "name": "取消订单", "fullName": "testcase.play.test_play.TestPlay#test_play", "historyId": "097386a9d43be40d3c28c1874a6e169b", "time": {"start": 1751853910547, "stop": 1751853912006, "duration": 1459}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "print_info", "time": {"start": 1751853910547, "stop": 1751853910547, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "clear_extract", "time": {"start": 1751853884946, "stop": 1751853885997, "duration": 1051}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "_session_faker", "time": {"start": 1751853884907, "stop": 1751853884946, "duration": 39}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "capture_logs_for_allure", "time": {"start": 1751853909796, "stop": 1751853909796, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "function_wait", "time": {"start": 1751853909796, "stop": 1751853910547, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "35d76e9818e70454", "name": "接口地址", "source": "35d76e9818e70454.json", "type": "application/json", "size": 42}, {"uid": "cc4a0503e7dcbc26", "name": "接口名称", "source": "cc4a0503e7dcbc26.json", "type": "application/json", "size": 12}, {"uid": "bf3277c9c3fe8e8d", "name": "请求方式", "source": "bf3277c9c3fe8e8d.json", "type": "application/json", "size": 4}, {"uid": "e810705f40a32f2d", "name": "请求头", "source": "e810705f40a32f2d.json", "type": "application/json", "size": 122}, {"uid": "3513742becf2a176", "name": "<PERSON><PERSON>", "source": "3513742becf2a176.json", "type": "application/json", "size": 12}, {"uid": "448396eaff9eb1b6", "name": "测试用例名称", "source": "448396eaff9eb1b6.json", "type": "application/json", "size": 12}, {"uid": "bc675ab80c85564a", "name": "参数类型", "source": "bc675ab80c85564a.json", "type": "application/json", "size": 4}, {"uid": "dbaacac624f3e1f9", "name": "请求参数json格式", "source": "dbaacac624f3e1f9.json", "type": "application/json", "size": 217}, {"uid": "9b953c5ee0efe762", "name": "请求参数实际入参", "source": "9b953c5ee0efe762.json", "type": "application/json", "size": 227}, {"uid": "5fbd0283c22a9589", "name": "接口实际响应信息", "source": "5fbd0283c22a9589.json", "type": "application/json", "size": 48}, {"uid": "8541e95195b53763", "name": "状态码断言结果：成功", "source": "8541e95195b53763.txt", "type": "text/plain", "size": 37}, {"uid": "160a0b64faf98dba", "name": "相等断言结果：成功", "source": "160a0b64faf98dba.json", "type": "application/json", "size": 53}, {"uid": "e6800928a96b91a0", "name": "📋 测试执行日志", "source": "e6800928a96b91a0.txt", "type": "text/plain", "size": 2968}], "parameters": [], "hasContent": true, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 13}, "afterStages": [{"name": "print_info::0", "time": {"start": 1751853912007, "stop": 1751853912007, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "capture_logs_for_allure::0", "time": {"start": 1751853912759, "stop": 1751853912769, "duration": 10}, "status": "passed", "steps": [], "attachments": [{"uid": "8758aba8fd8e417c", "name": "📋 测试执行日志 (完整记录)", "source": "8758aba8fd8e417c.txt", "type": "text/plain", "size": 2307}], "parameters": [], "hasContent": true, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "function_wait::0", "time": {"start": 1751853912008, "stop": 1751853912759, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "39132-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "1e2cd764a486cb90.json", "parameterValues": []}