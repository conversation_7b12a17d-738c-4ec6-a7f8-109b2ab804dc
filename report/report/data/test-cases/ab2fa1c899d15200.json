{"uid": "ab2fa1c899d15200", "name": "今日必抢", "fullName": "testcase.play.test_play.TestPlay#test_play", "historyId": "09729bd43bb947d1ebe3c16a80dc6e25", "time": {"start": 1751852458787, "stop": 1751852459086, "duration": 299}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "print_info", "time": {"start": 1751852458787, "stop": 1751852458787, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "capture_logs_for_allure", "time": {"start": 1751852458036, "stop": 1751852458036, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "_session_faker", "time": {"start": 1751852455733, "stop": 1751852455863, "duration": 130}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "clear_extract", "time": {"start": 1751852455863, "stop": 1751852456123, "duration": 260}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "function_wait", "time": {"start": 1751852458036, "stop": 1751852458787, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "e6014272bddc8e8b", "name": "接口地址", "source": "e6014272bddc8e8b.json", "type": "application/json", "size": 55}, {"uid": "aebf037b5fd9b80e", "name": "接口名称", "source": "aebf037b5fd9b80e.json", "type": "application/json", "size": 12}, {"uid": "ff242be21e145208", "name": "请求方式", "source": "ff242be21e145208.json", "type": "application/json", "size": 4}, {"uid": "a2c41c053f588331", "name": "请求头", "source": "a2c41c053f588331.json", "type": "application/json", "size": 122}, {"uid": "1b67261f684bcd53", "name": "<PERSON><PERSON>", "source": "1b67261f684bcd53.json", "type": "application/json", "size": 12}, {"uid": "733ff577dcf1502", "name": "测试用例名称", "source": "733ff577dcf1502.json", "type": "application/json", "size": 30}, {"uid": "4edb705023520b12", "name": "参数类型", "source": "4edb705023520b12.json", "type": "application/json", "size": 4}, {"uid": "d08df6c7892cf1de", "name": "请求参数json格式", "source": "d08df6c7892cf1de.json", "type": "application/json", "size": 214}, {"uid": "18a64bb157600436", "name": "请求参数实际入参", "source": "18a64bb157600436.json", "type": "application/json", "size": 224}, {"uid": "2edb0d33da95b136", "name": "接口实际响应信息", "source": "2edb0d33da95b136.json", "type": "application/json", "size": 22188}, {"uid": "96570277a2ac4697", "name": "状态码断言结果：成功", "source": "96570277a2ac4697.txt", "type": "text/plain", "size": 37}, {"uid": "8c753f5047552c38", "name": "相等断言结果：成功", "source": "8c753f5047552c38.json", "type": "application/json", "size": 53}, {"uid": "8154eee5ef9dd0a", "name": "📋 测试执行日志", "source": "8154eee5ef9dd0a.txt", "type": "text/plain", "size": 4497}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 13, "hasContent": true, "stepsCount": 0}, "afterStages": [{"name": "print_info::0", "time": {"start": 1751852459087, "stop": 1751852459087, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "capture_logs_for_allure::0", "time": {"start": 1751852459839, "stop": 1751852459850, "duration": 11}, "status": "passed", "steps": [], "attachments": [{"uid": "9726528d31480776", "name": "📋 测试执行日志 (完整记录)", "source": "9726528d31480776.txt", "type": "text/plain", "size": 14976}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 1, "hasContent": true, "stepsCount": 0}, {"name": "function_wait::0", "time": {"start": 1751852459088, "stop": 1751852459838, "duration": 750}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}], "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "38092-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "ab2fa1c899d15200.json", "parameterValues": []}