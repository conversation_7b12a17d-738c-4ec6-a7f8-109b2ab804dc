{"uid": "6993b15567b9c7da", "name": "项目下场次票品列表", "fullName": "testcase.play.test_play.TestPlay#test_play", "historyId": "92afa2451c3a3109d55af2cbc713b516", "time": {"start": 1751853008988, "stop": 1751853009562, "duration": 574}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "function_wait", "time": {"start": 1751853008235, "stop": 1751853008987, "duration": 752}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "clear_extract", "time": {"start": 1751852990929, "stop": 1751852991206, "duration": 277}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "_session_faker", "time": {"start": 1751852990879, "stop": 1751852990927, "duration": 48}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "print_info", "time": {"start": 1751853008987, "stop": 1751853008988, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "capture_logs_for_allure", "time": {"start": 1751853008235, "stop": 1751853008235, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "1072f0fa36c69692", "name": "接口地址", "source": "1072f0fa36c69692.json", "type": "application/json", "size": 52}, {"uid": "3834a62f8bca163e", "name": "接口名称", "source": "3834a62f8bca163e.json", "type": "application/json", "size": 27}, {"uid": "43726cdee95a9999", "name": "请求方式", "source": "43726cdee95a9999.json", "type": "application/json", "size": 3}, {"uid": "4109dfe0d1fa6615", "name": "请求头", "source": "4109dfe0d1fa6615.json", "type": "application/json", "size": 122}, {"uid": "4c3753914db4f046", "name": "<PERSON><PERSON>", "source": "4c3753914db4f046.json", "type": "application/json", "size": 12}, {"uid": "7c91118a88ebf1c9", "name": "测试用例名称", "source": "7c91118a88ebf1c9.json", "type": "application/json", "size": 39}, {"uid": "613a09abc18de2fe", "name": "参数类型", "source": "613a09abc18de2fe.json", "type": "application/json", "size": 6}, {"uid": "1d9fae6b5442a914", "name": "请求参数json格式", "source": "1d9fae6b5442a914.json", "type": "application/json", "size": 199}, {"uid": "72a96cd51252d1b9", "name": "请求参数实际入参", "source": "72a96cd51252d1b9.json", "type": "application/json", "size": 209}, {"uid": "c265cfd5673a8c18", "name": "接口实际响应信息", "source": "c265cfd5673a8c18.json", "type": "application/json", "size": 7483}, {"uid": "8b4fa2de55c4dd70", "name": "状态码断言结果：成功", "source": "8b4fa2de55c4dd70.txt", "type": "text/plain", "size": 37}, {"uid": "1f0d2fcc4bb3c3c", "name": "相等断言结果：成功", "source": "1f0d2fcc4bb3c3c.json", "type": "application/json", "size": 53}, {"uid": "9cf13b7e0389cef1", "name": "📋 测试执行日志", "source": "9cf13b7e0389cef1.txt", "type": "text/plain", "size": 2892}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 13, "hasContent": true, "stepsCount": 0}, "afterStages": [{"name": "function_wait::0", "time": {"start": 1751853009563, "stop": 1751853010314, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "print_info::0", "time": {"start": 1751853009562, "stop": 1751853009563, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "capture_logs_for_allure::0", "time": {"start": 1751853010314, "stop": 1751853010326, "duration": 12}, "status": "passed", "steps": [], "attachments": [{"uid": "f39fcb9bfd41c781", "name": "📋 测试执行日志 (完整记录)", "source": "f39fcb9bfd41c781.txt", "type": "text/plain", "size": 4692}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 1, "hasContent": true, "stepsCount": 0}], "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "37476-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [{"name": "api_info", "value": "{'baseInfo': {'module': '演出票', 'api_name': '项目下场次票品列表', 'host': '${get_host(play)}', 'url': '/api/show/info/show_ticket_units', 'method': 'get', 'headers': '${get_headers(app)}'}, 'testCase': {'case_name': '获取项目下场次票品列表信息', 'validation': [{'code': 200}, {'eq': {'code': 0}}], 'extract': {'extract_auto': 'show_ticket_units'}, 'extract_list': np.float64(0.0), 'params': {'showId': '${get_extract_data(showInfo,showId)}', 'channelCode': '${appKey()}', 'token': '${get_extract_data(token)}'}}}"}], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "6993b15567b9c7da.json", "parameterValues": ["{'baseInfo': {'module': '演出票', 'api_name': '项目下场次票品列表', 'host': '${get_host(play)}', 'url': '/api/show/info/show_ticket_units', 'method': 'get', 'headers': '${get_headers(app)}'}, 'testCase': {'case_name': '获取项目下场次票品列表信息', 'validation': [{'code': 200}, {'eq': {'code': 0}}], 'extract': {'extract_auto': 'show_ticket_units'}, 'extract_list': np.float64(0.0), 'params': {'showId': '${get_extract_data(showInfo,showId)}', 'channelCode': '${appKey()}', 'token': '${get_extract_data(token)}'}}}"]}