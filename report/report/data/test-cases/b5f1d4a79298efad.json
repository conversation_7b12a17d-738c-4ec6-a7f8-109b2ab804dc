{"uid": "b5f1d4a79298efad", "name": "查询影讯接口", "fullName": "testcase.demo_test.test_demo.TestFilmOrder#test_film_order", "historyId": "e9c2ea8fd9c81240c2b50d2a9668daf2", "time": {"start": 1751866444384, "stop": 1751866444761, "duration": 377}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "capture_logs_for_allure", "time": {"start": 1751866443632, "stop": 1751866443632, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "clear_extract", "time": {"start": 1751866415419, "stop": 1751866415726, "duration": 307}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "_session_faker", "time": {"start": 1751866415384, "stop": 1751866415419, "duration": 35}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "print_info", "time": {"start": 1751866444383, "stop": 1751866444383, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "function_wait", "time": {"start": 1751866443632, "stop": 1751866444383, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "12abfc2e07aae3ed", "name": "接口地址", "source": "12abfc2e07aae3ed.json", "type": "application/json", "size": 48}, {"uid": "ff522f4357c15ff7", "name": "接口名称", "source": "ff522f4357c15ff7.json", "type": "application/json", "size": 18}, {"uid": "1771a92c3acb0181", "name": "请求方式", "source": "1771a92c3acb0181.json", "type": "application/json", "size": 4}, {"uid": "1faa596e191b2605", "name": "请求头", "source": "1faa596e191b2605.json", "type": "application/json", "size": 15}, {"uid": "ef156e201bbae655", "name": "<PERSON><PERSON>", "source": "ef156e201bbae655.json", "type": "application/json", "size": 12}, {"uid": "b4ab3ffedfaef91d", "name": "测试用例名称", "source": "b4ab3ffedfaef91d.json", "type": "application/json", "size": 27}, {"uid": "50d8987e45176af2", "name": "参数类型", "source": "50d8987e45176af2.json", "type": "application/json", "size": 4}, {"uid": "6dc538f4ff8b8703", "name": "请求参数json格式", "source": "6dc538f4ff8b8703.json", "type": "application/json", "size": 493}, {"uid": "6d8c3da5a092b4e1", "name": "请求参数实际入参", "source": "6d8c3da5a092b4e1.json", "type": "application/json", "size": 436}, {"uid": "9f7b94494224d07b", "name": "接口实际响应信息", "source": "9f7b94494224d07b.json", "type": "application/json", "size": 52017}, {"uid": "be34d6d6970fc5ef", "name": "状态码断言结果：成功", "source": "be34d6d6970fc5ef.txt", "type": "text/plain", "size": 37}, {"uid": "837d16a4b758f0c2", "name": "相等断言结果：成功", "source": "837d16a4b758f0c2.json", "type": "application/json", "size": 53}, {"uid": "869b5056b6282136", "name": "相等断言结果：成功", "source": "869b5056b6282136.json", "type": "application/json", "size": 77}, {"uid": "29e110e6e66b010", "name": "📋 测试执行日志", "source": "29e110e6e66b010.txt", "type": "text/plain", "size": 2076}], "parameters": [], "hasContent": true, "stepsCount": 0, "attachmentsCount": 14, "shouldDisplayMessage": false}, "afterStages": [{"name": "capture_logs_for_allure::0", "time": {"start": 1751866445516, "stop": 1751866445525, "duration": 9}, "status": "passed", "steps": [], "attachments": [{"uid": "c331c69c4fc01c9c", "name": "📋 测试执行日志 (完整记录)", "source": "c331c69c4fc01c9c.txt", "type": "text/plain", "size": 29804}], "parameters": [], "hasContent": true, "stepsCount": 0, "attachmentsCount": 1, "shouldDisplayMessage": false}, {"name": "print_info::0", "time": {"start": 1751866444764, "stop": 1751866444764, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "function_wait::0", "time": {"start": 1751866444764, "stop": 1751866445515, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}], "labels": [{"name": "feature", "value": "M02_电影订票模块接口测试"}, {"name": "story", "value": "C02_电影下单流程"}, {"name": "parentSuite", "value": "testcase.demo_test"}, {"name": "suite", "value": "test_demo"}, {"name": "subSuite", "value": "TestFilmOrder"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "16848-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.demo_test.test_demo"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "b5f1d4a79298efad.json", "parameterValues": []}