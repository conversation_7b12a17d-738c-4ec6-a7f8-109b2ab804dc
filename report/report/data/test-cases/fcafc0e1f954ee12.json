{"uid": "fcafc0e1f954ee12", "name": "项目下场次票品列表", "fullName": "testcase.play.test_play.TestPlay#test_play", "historyId": "92afa2451c3a3109d55af2cbc713b516", "time": {"start": 1751853746300, "stop": 1751853746875, "duration": 575}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "print_info", "time": {"start": 1751853746300, "stop": 1751853746300, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "hasContent": false, "stepsCount": 0, "attachmentsCount": 0}, {"name": "_session_faker", "time": {"start": 1751853726731, "stop": 1751853726779, "duration": 48}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "hasContent": false, "stepsCount": 0, "attachmentsCount": 0}, {"name": "clear_extract", "time": {"start": 1751853726779, "stop": 1751853727168, "duration": 389}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "hasContent": false, "stepsCount": 0, "attachmentsCount": 0}, {"name": "function_wait", "time": {"start": 1751853745548, "stop": 1751853746300, "duration": 752}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "hasContent": false, "stepsCount": 0, "attachmentsCount": 0}, {"name": "capture_logs_for_allure", "time": {"start": 1751853745548, "stop": 1751853745548, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "hasContent": false, "stepsCount": 0, "attachmentsCount": 0}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "cae7ef2ec26a9940", "name": "接口地址", "source": "cae7ef2ec26a9940.json", "type": "application/json", "size": 52}, {"uid": "72312888d32b8311", "name": "接口名称", "source": "72312888d32b8311.json", "type": "application/json", "size": 27}, {"uid": "1cf24bcb09fe3a7e", "name": "请求方式", "source": "1cf24bcb09fe3a7e.json", "type": "application/json", "size": 3}, {"uid": "c62877e0d6441106", "name": "请求头", "source": "c62877e0d6441106.json", "type": "application/json", "size": 122}, {"uid": "23ff3d7e87ded8f4", "name": "<PERSON><PERSON>", "source": "23ff3d7e87ded8f4.json", "type": "application/json", "size": 12}, {"uid": "6c850d43c4aa7e8a", "name": "测试用例名称", "source": "6c850d43c4aa7e8a.json", "type": "application/json", "size": 39}, {"uid": "f8f20cd6c1591de6", "name": "参数类型", "source": "f8f20cd6c1591de6.json", "type": "application/json", "size": 6}, {"uid": "67dd039b19310643", "name": "请求参数json格式", "source": "67dd039b19310643.json", "type": "application/json", "size": 199}, {"uid": "8e4bc851c149ef17", "name": "请求参数实际入参", "source": "8e4bc851c149ef17.json", "type": "application/json", "size": 209}, {"uid": "6450efc513eb71e", "name": "接口实际响应信息", "source": "6450efc513eb71e.json", "type": "application/json", "size": 7483}, {"uid": "ef31ee29f7bce60e", "name": "状态码断言结果：成功", "source": "ef31ee29f7bce60e.txt", "type": "text/plain", "size": 37}, {"uid": "1b3813309afd200a", "name": "相等断言结果：成功", "source": "1b3813309afd200a.json", "type": "application/json", "size": 53}, {"uid": "21c020ea45e4e6da", "name": "📋 测试执行日志", "source": "21c020ea45e4e6da.txt", "type": "text/plain", "size": 2892}], "parameters": [], "shouldDisplayMessage": false, "hasContent": true, "stepsCount": 0, "attachmentsCount": 13}, "afterStages": [{"name": "print_info::0", "time": {"start": 1751853746876, "stop": 1751853746876, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "hasContent": false, "stepsCount": 0, "attachmentsCount": 0}, {"name": "function_wait::0", "time": {"start": 1751853746876, "stop": 1751853747627, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "hasContent": false, "stepsCount": 0, "attachmentsCount": 0}, {"name": "capture_logs_for_allure::0", "time": {"start": 1751853747628, "stop": 1751853747636, "duration": 8}, "status": "passed", "steps": [], "attachments": [{"uid": "fcb2bdbe153619a4", "name": "📋 测试执行日志 (完整记录)", "source": "fcb2bdbe153619a4.txt", "type": "text/plain", "size": 4692}], "parameters": [], "shouldDisplayMessage": false, "hasContent": true, "stepsCount": 0, "attachmentsCount": 1}], "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "39072-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [{"name": "api_info", "value": "{'baseInfo': {'module': '演出票', 'api_name': '项目下场次票品列表', 'host': '${get_host(play)}', 'url': '/api/show/info/show_ticket_units', 'method': 'get', 'headers': '${get_headers(app)}'}, 'testCase': {'case_name': '获取项目下场次票品列表信息', 'validation': [{'code': 200}, {'eq': {'code': 0}}], 'extract': {'extract_auto': 'show_ticket_units'}, 'extract_list': np.float64(0.0), 'params': {'showId': '${get_extract_data(showInfo,showId)}', 'channelCode': '${appKey()}', 'token': '${get_extract_data(token)}'}}}"}], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "fcafc0e1f954ee12.json", "parameterValues": ["{'baseInfo': {'module': '演出票', 'api_name': '项目下场次票品列表', 'host': '${get_host(play)}', 'url': '/api/show/info/show_ticket_units', 'method': 'get', 'headers': '${get_headers(app)}'}, 'testCase': {'case_name': '获取项目下场次票品列表信息', 'validation': [{'code': 200}, {'eq': {'code': 0}}], 'extract': {'extract_auto': 'show_ticket_units'}, 'extract_list': np.float64(0.0), 'params': {'showId': '${get_extract_data(showInfo,showId)}', 'channelCode': '${appKey()}', 'token': '${get_extract_data(token)}'}}}"]}