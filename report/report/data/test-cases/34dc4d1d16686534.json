{"uid": "34dc4d1d16686534", "name": "根据场次获取取票方式", "fullName": "testcase.play.test_play.TestPlay#test_play", "historyId": "40f667e7e7d9255278cb56eb99290821", "time": {"start": 1751866435788, "stop": 1751866436826, "duration": 1038}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "clear_extract", "time": {"start": 1751866415419, "stop": 1751866415726, "duration": 307}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "print_info", "time": {"start": 1751866435787, "stop": 1751866435787, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "_session_faker", "time": {"start": 1751866415384, "stop": 1751866415419, "duration": 35}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "capture_logs_for_allure", "time": {"start": 1751866435036, "stop": 1751866435036, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "function_wait", "time": {"start": 1751866435036, "stop": 1751866435787, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "2b2b01fcc43fcc5c", "name": "接口地址", "source": "2b2b01fcc43fcc5c.json", "type": "application/json", "size": 57}, {"uid": "e6557d034e438f4e", "name": "接口名称", "source": "e6557d034e438f4e.json", "type": "application/json", "size": 30}, {"uid": "c8fd1fff4747fc4f", "name": "请求方式", "source": "c8fd1fff4747fc4f.json", "type": "application/json", "size": 3}, {"uid": "4bdf11f461c9587", "name": "请求头", "source": "4bdf11f461c9587.json", "type": "application/json", "size": 122}, {"uid": "4b97ac4bb73d68db", "name": "<PERSON><PERSON>", "source": "4b97ac4bb73d68db.json", "type": "application/json", "size": 12}, {"uid": "2dc8040e3939f408", "name": "测试用例名称", "source": "2dc8040e3939f408.json", "type": "application/json", "size": 30}, {"uid": "7c4c17fb2ebca1b6", "name": "参数类型", "source": "7c4c17fb2ebca1b6.json", "type": "application/json", "size": 6}, {"uid": "34525438c88e0485", "name": "请求参数json格式", "source": "34525438c88e0485.json", "type": "application/json", "size": 199}, {"uid": "2b159d72a91340a2", "name": "请求参数实际入参", "source": "2b159d72a91340a2.json", "type": "application/json", "size": 209}, {"uid": "514bff9ea9ce450a", "name": "接口实际响应信息", "source": "514bff9ea9ce450a.json", "type": "application/json", "size": 599}, {"uid": "801e1270307644cd", "name": "状态码断言结果：成功", "source": "801e1270307644cd.txt", "type": "text/plain", "size": 37}, {"uid": "34d1289e884e65f2", "name": "相等断言结果：成功", "source": "34d1289e884e65f2.json", "type": "application/json", "size": 53}, {"uid": "f327de7d4f589e89", "name": "📋 测试执行日志", "source": "f327de7d4f589e89.txt", "type": "text/plain", "size": 4692}], "parameters": [], "hasContent": true, "stepsCount": 0, "attachmentsCount": 13, "shouldDisplayMessage": false}, "afterStages": [{"name": "print_info::0", "time": {"start": 1751866436827, "stop": 1751866436827, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "capture_logs_for_allure::0", "time": {"start": 1751866437581, "stop": 1751866437593, "duration": 12}, "status": "passed", "steps": [], "attachments": [{"uid": "e109b6152663f7f1", "name": "📋 测试执行日志 (完整记录)", "source": "e109b6152663f7f1.txt", "type": "text/plain", "size": 2377}], "parameters": [], "hasContent": true, "stepsCount": 0, "attachmentsCount": 1, "shouldDisplayMessage": false}, {"name": "function_wait::0", "time": {"start": 1751866436829, "stop": 1751866437580, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}], "labels": [{"name": "story", "value": "C01_演出模块流程"}, {"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "16848-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "34dc4d1d16686534.json", "parameterValues": []}