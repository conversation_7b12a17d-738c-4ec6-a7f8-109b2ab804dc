{"uid": "d30d62ef829d62b3", "name": "**搜索**", "fullName": "testcase.play.test_play.TestPlay#test_play", "historyId": "80ea16c71eb34b6e85110c3f875859f1", "time": {"start": 1751853897749, "stop": 1751853898049, "duration": 300}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "function_wait", "time": {"start": 1751853896996, "stop": 1751853897747, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "capture_logs_for_allure", "time": {"start": 1751853896996, "stop": 1751853896996, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "clear_extract", "time": {"start": 1751853884946, "stop": 1751853885997, "duration": 1051}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "_session_faker", "time": {"start": 1751853884907, "stop": 1751853884946, "duration": 39}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "print_info", "time": {"start": 1751853897747, "stop": 1751853897749, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "f18b21957565a0ac", "name": "接口地址", "source": "f18b21957565a0ac.json", "type": "application/json", "size": 53}, {"uid": "26baf83d2bc116ef", "name": "接口名称", "source": "26baf83d2bc116ef.json", "type": "application/json", "size": 10}, {"uid": "5e915fd87fc85b4c", "name": "请求方式", "source": "5e915fd87fc85b4c.json", "type": "application/json", "size": 4}, {"uid": "b1ac1bc05efc359f", "name": "请求头", "source": "b1ac1bc05efc359f.json", "type": "application/json", "size": 122}, {"uid": "dd1d9c3da350c406", "name": "<PERSON><PERSON>", "source": "dd1d9c3da350c406.json", "type": "application/json", "size": 12}, {"uid": "9dde46992c152066", "name": "测试用例名称", "source": "9dde46992c152066.json", "type": "application/json", "size": 30}, {"uid": "7a4e044a935c1d91", "name": "参数类型", "source": "7a4e044a935c1d91.json", "type": "application/json", "size": 4}, {"uid": "6a867d5702eea7c0", "name": "请求参数json格式", "source": "6a867d5702eea7c0.json", "type": "application/json", "size": 201}, {"uid": "10f6d2f6501484a9", "name": "请求参数实际入参", "source": "10f6d2f6501484a9.json", "type": "application/json", "size": 211}, {"uid": "17154b767fa09087", "name": "接口实际响应信息", "source": "17154b767fa09087.json", "type": "application/json", "size": 22387}, {"uid": "7c028d03a2cd061d", "name": "状态码断言结果：成功", "source": "7c028d03a2cd061d.txt", "type": "text/plain", "size": 37}, {"uid": "ca02b11275185f7d", "name": "相等断言结果：成功", "source": "ca02b11275185f7d.json", "type": "application/json", "size": 53}, {"uid": "732f99fb825f6c48", "name": "📋 测试执行日志", "source": "732f99fb825f6c48.txt", "type": "text/plain", "size": 14921}], "parameters": [], "hasContent": true, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 13}, "afterStages": [{"name": "function_wait::0", "time": {"start": 1751853898050, "stop": 1751853898801, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "capture_logs_for_allure::0", "time": {"start": 1751853898801, "stop": 1751853898810, "duration": 9}, "status": "passed", "steps": [], "attachments": [{"uid": "e65d4726fb2105e6", "name": "📋 测试执行日志 (完整记录)", "source": "e65d4726fb2105e6.txt", "type": "text/plain", "size": 15160}], "parameters": [], "hasContent": true, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "print_info::0", "time": {"start": 1751853898050, "stop": 1751853898050, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "39132-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "d30d62ef829d62b3.json", "parameterValues": []}