{"uid": "ee908964caf50746", "name": "演出分类", "fullName": "testcase.play.test_play.TestPlay#test_play", "historyId": "c36ab200ccb8723de7119149ea6c3088", "time": {"start": 1751853886749, "stop": 1751853887119, "duration": 370}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "function_wait", "time": {"start": 1751853885997, "stop": 1751853886748, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "print_info", "time": {"start": 1751853886748, "stop": 1751853886748, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "clear_extract", "time": {"start": 1751853884946, "stop": 1751853885997, "duration": 1051}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "capture_logs_for_allure", "time": {"start": 1751853885997, "stop": 1751853885997, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "_session_faker", "time": {"start": 1751853884907, "stop": 1751853884946, "duration": 39}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "d2b185e462dd593e", "name": "接口地址", "source": "d2b185e462dd593e.json", "type": "application/json", "size": 48}, {"uid": "b9e8382c54381fc3", "name": "接口名称", "source": "b9e8382c54381fc3.json", "type": "application/json", "size": 12}, {"uid": "c4b5ad7238c07340", "name": "请求方式", "source": "c4b5ad7238c07340.json", "type": "application/json", "size": 4}, {"uid": "5f45846963784b06", "name": "请求头", "source": "5f45846963784b06.json", "type": "application/json", "size": 122}, {"uid": "80dda4819d142ec3", "name": "<PERSON><PERSON>", "source": "80dda4819d142ec3.json", "type": "application/json", "size": 12}, {"uid": "d981210ac3e72d29", "name": "测试用例名称", "source": "d981210ac3e72d29.json", "type": "application/json", "size": 24}, {"uid": "34f757e8836453da", "name": "参数类型", "source": "34f757e8836453da.json", "type": "application/json", "size": 4}, {"uid": "f82b09327147e474", "name": "请求参数json格式", "source": "f82b09327147e474.json", "type": "application/json", "size": 204}, {"uid": "75e012710b9be996", "name": "请求参数实际入参", "source": "75e012710b9be996.json", "type": "application/json", "size": 214}, {"uid": "867bf0b70d16e0a7", "name": "接口实际响应信息", "source": "867bf0b70d16e0a7.json", "type": "application/json", "size": 3148}, {"uid": "d589827f43ca3652", "name": "状态码断言结果：成功", "source": "d589827f43ca3652.txt", "type": "text/plain", "size": 37}, {"uid": "e6572e2552b7264a", "name": "相等断言结果：成功", "source": "e6572e2552b7264a.json", "type": "application/json", "size": 53}, {"uid": "26eb8a64579f09c3", "name": "📋 测试执行日志", "source": "26eb8a64579f09c3.txt", "type": "text/plain", "size": 2076}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentsCount": 13, "shouldDisplayMessage": false}, "afterStages": [{"name": "function_wait::0", "time": {"start": 1751853887121, "stop": 1751853887872, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "print_info::0", "time": {"start": 1751853887121, "stop": 1751853887121, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "capture_logs_for_allure::0", "time": {"start": 1751853887872, "stop": 1751853887881, "duration": 9}, "status": "passed", "steps": [], "attachments": [{"uid": "57402a19f90b3a54", "name": "📋 测试执行日志 (完整记录)", "source": "57402a19f90b3a54.txt", "type": "text/plain", "size": 4327}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentsCount": 1, "shouldDisplayMessage": false}], "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "39132-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "ee908964caf50746.json", "parameterValues": []}