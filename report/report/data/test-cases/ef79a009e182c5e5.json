{"uid": "ef79a009e182c5e5", "name": "项目下场次票品列表", "fullName": "testcase.play.test_play.TestPlay#test_play", "historyId": "3287c72f85d7169cf4925a7d71668b28", "time": {"start": 1751853903726, "stop": 1751853904261, "duration": 535}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "capture_logs_for_allure", "time": {"start": 1751853902975, "stop": 1751853902975, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "clear_extract", "time": {"start": 1751853884946, "stop": 1751853885997, "duration": 1051}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "print_info", "time": {"start": 1751853903725, "stop": 1751853903726, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "_session_faker", "time": {"start": 1751853884907, "stop": 1751853884946, "duration": 39}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "function_wait", "time": {"start": 1751853902975, "stop": 1751853903725, "duration": 750}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "65774080a9b9bb24", "name": "接口地址", "source": "65774080a9b9bb24.json", "type": "application/json", "size": 52}, {"uid": "e6ba1b8f0d5d7964", "name": "接口名称", "source": "e6ba1b8f0d5d7964.json", "type": "application/json", "size": 27}, {"uid": "c4e55637adf77ada", "name": "请求方式", "source": "c4e55637adf77ada.json", "type": "application/json", "size": 3}, {"uid": "edcf6f352f69542d", "name": "请求头", "source": "edcf6f352f69542d.json", "type": "application/json", "size": 122}, {"uid": "f5fefc260a3239ea", "name": "<PERSON><PERSON>", "source": "f5fefc260a3239ea.json", "type": "application/json", "size": 12}, {"uid": "b574955ee6f9933a", "name": "测试用例名称", "source": "b574955ee6f9933a.json", "type": "application/json", "size": 39}, {"uid": "9db08a49f4cb2512", "name": "参数类型", "source": "9db08a49f4cb2512.json", "type": "application/json", "size": 6}, {"uid": "40a017a9a8b82db", "name": "请求参数json格式", "source": "40a017a9a8b82db.json", "type": "application/json", "size": 199}, {"uid": "86d5f84342ddd8a1", "name": "请求参数实际入参", "source": "86d5f84342ddd8a1.json", "type": "application/json", "size": 209}, {"uid": "61d8f9e8621fcf0b", "name": "接口实际响应信息", "source": "61d8f9e8621fcf0b.json", "type": "application/json", "size": 7483}, {"uid": "71d6a496b365137e", "name": "状态码断言结果：成功", "source": "71d6a496b365137e.txt", "type": "text/plain", "size": 37}, {"uid": "a6d952f67c25c5d8", "name": "相等断言结果：成功", "source": "a6d952f67c25c5d8.json", "type": "application/json", "size": 53}, {"uid": "33630d3b38d00b10", "name": "📋 测试执行日志", "source": "33630d3b38d00b10.txt", "type": "text/plain", "size": 3123}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentsCount": 13, "shouldDisplayMessage": false}, "afterStages": [{"name": "capture_logs_for_allure::0", "time": {"start": 1751853905014, "stop": 1751853905023, "duration": 9}, "status": "passed", "steps": [], "attachments": [{"uid": "3435d47e9f38fcd9", "name": "📋 测试执行日志 (完整记录)", "source": "3435d47e9f38fcd9.txt", "type": "text/plain", "size": 5242}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentsCount": 1, "shouldDisplayMessage": false}, {"name": "print_info::0", "time": {"start": 1751853904262, "stop": 1751853904262, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "function_wait::0", "time": {"start": 1751853904263, "stop": 1751853905014, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}], "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "39132-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "ef79a009e182c5e5.json", "parameterValues": []}