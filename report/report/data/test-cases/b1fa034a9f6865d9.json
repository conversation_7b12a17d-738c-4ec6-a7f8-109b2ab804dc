{"uid": "b1fa034a9f6865d9", "name": "**搜索**", "fullName": "testcase.play.test_play.TestPlay#test_play", "historyId": "932055e7a8948657a1bdaf6fe3b4e09c", "time": {"start": 1751853739998, "stop": 1751853740291, "duration": 293}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "print_info", "time": {"start": 1751853739997, "stop": 1751853739997, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "hasContent": false, "stepsCount": 0, "attachmentsCount": 0}, {"name": "_session_faker", "time": {"start": 1751853726731, "stop": 1751853726779, "duration": 48}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "hasContent": false, "stepsCount": 0, "attachmentsCount": 0}, {"name": "function_wait", "time": {"start": 1751853739247, "stop": 1751853739997, "duration": 750}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "hasContent": false, "stepsCount": 0, "attachmentsCount": 0}, {"name": "clear_extract", "time": {"start": 1751853726779, "stop": 1751853727168, "duration": 389}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "hasContent": false, "stepsCount": 0, "attachmentsCount": 0}, {"name": "capture_logs_for_allure", "time": {"start": 1751853739246, "stop": 1751853739247, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "hasContent": false, "stepsCount": 0, "attachmentsCount": 0}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "77a8fbae841c9710", "name": "接口地址", "source": "77a8fbae841c9710.json", "type": "application/json", "size": 53}, {"uid": "e17538ff56459672", "name": "接口名称", "source": "e17538ff56459672.json", "type": "application/json", "size": 10}, {"uid": "207c3bded09db4e6", "name": "请求方式", "source": "207c3bded09db4e6.json", "type": "application/json", "size": 4}, {"uid": "35d86ad928c8e60d", "name": "请求头", "source": "35d86ad928c8e60d.json", "type": "application/json", "size": 122}, {"uid": "874799ce51afee45", "name": "<PERSON><PERSON>", "source": "874799ce51afee45.json", "type": "application/json", "size": 12}, {"uid": "3cdf8acd649dadf1", "name": "测试用例名称", "source": "3cdf8acd649dadf1.json", "type": "application/json", "size": 30}, {"uid": "804b9034627d32ef", "name": "参数类型", "source": "804b9034627d32ef.json", "type": "application/json", "size": 4}, {"uid": "29fd3f47f87c51e", "name": "请求参数json格式", "source": "29fd3f47f87c51e.json", "type": "application/json", "size": 201}, {"uid": "265729a26a6c7613", "name": "请求参数实际入参", "source": "265729a26a6c7613.json", "type": "application/json", "size": 211}, {"uid": "5c2c6fe9218f0617", "name": "接口实际响应信息", "source": "5c2c6fe9218f0617.json", "type": "application/json", "size": 22387}, {"uid": "70089a3d89c659e", "name": "状态码断言结果：成功", "source": "70089a3d89c659e.txt", "type": "text/plain", "size": 37}, {"uid": "bd426e818aeb0d5a", "name": "相等断言结果：成功", "source": "bd426e818aeb0d5a.json", "type": "application/json", "size": 53}, {"uid": "4ce518ebf1b35a2f", "name": "📋 测试执行日志", "source": "4ce518ebf1b35a2f.txt", "type": "text/plain", "size": 14539}], "parameters": [], "shouldDisplayMessage": false, "hasContent": true, "stepsCount": 0, "attachmentsCount": 13}, "afterStages": [{"name": "print_info::0", "time": {"start": 1751853740291, "stop": 1751853740291, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "hasContent": false, "stepsCount": 0, "attachmentsCount": 0}, {"name": "function_wait::0", "time": {"start": 1751853740292, "stop": 1751853741043, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "hasContent": false, "stepsCount": 0, "attachmentsCount": 0}, {"name": "capture_logs_for_allure::0", "time": {"start": 1751853741043, "stop": 1751853741053, "duration": 10}, "status": "passed", "steps": [], "attachments": [{"uid": "8a225c507b9f715b", "name": "📋 测试执行日志 (完整记录)", "source": "8a225c507b9f715b.txt", "type": "text/plain", "size": 14929}], "parameters": [], "shouldDisplayMessage": false, "hasContent": true, "stepsCount": 0, "attachmentsCount": 1}], "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "39072-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [{"name": "api_info", "value": "{'baseInfo': {'module': '演出票', 'api_name': '**搜索**', 'host': '${get_host(play)}', 'url': '/api/show/info/performance_search', 'method': 'post', 'headers': '${get_headers(app)}'}, 'testCase': {'case_name': '按演出名称关键字搜索', 'validation': [{'code': 200}, {'eq': {'code': 0}}], 'extract': 0, 'extract_list': np.float64(0.0), 'data': {'keyword': '演', 'channelCode': '${appKey()}', 'token': '${get_extract_data(token)}'}}}"}], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "b1fa034a9f6865d9.json", "parameterValues": ["{'baseInfo': {'module': '演出票', 'api_name': '**搜索**', 'host': '${get_host(play)}', 'url': '/api/show/info/performance_search', 'method': 'post', 'headers': '${get_headers(app)}'}, 'testCase': {'case_name': '按演出名称关键字搜索', 'validation': [{'code': 200}, {'eq': {'code': 0}}], 'extract': 0, 'extract_list': np.float64(0.0), 'data': {'keyword': '演', 'channelCode': '${appKey()}', 'token': '${get_extract_data(token)}'}}}"]}