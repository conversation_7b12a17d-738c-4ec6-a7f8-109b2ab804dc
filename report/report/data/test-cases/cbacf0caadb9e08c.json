{"uid": "cbacf0caadb9e08c", "name": "演唱会分类", "fullName": "testcase.play.test_play.TestPlay#test_play", "historyId": "b8a74dca691f81270d24ebc4399d791b", "time": {"start": 1751853001175, "stop": 1751853001499, "duration": 324}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "clear_extract", "time": {"start": 1751852990929, "stop": 1751852991206, "duration": 277}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "_session_faker", "time": {"start": 1751852990879, "stop": 1751852990927, "duration": 48}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "capture_logs_for_allure", "time": {"start": 1751853000421, "stop": 1751853000421, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "print_info", "time": {"start": 1751853001172, "stop": 1751853001173, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "function_wait", "time": {"start": 1751853000421, "stop": 1751853001172, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "ab1a5ab3d62261b", "name": "接口地址", "source": "ab1a5ab3d62261b.json", "type": "application/json", "size": 53}, {"uid": "c9d6db72e33bd399", "name": "接口名称", "source": "c9d6db72e33bd399.json", "type": "application/json", "size": 15}, {"uid": "793b493a8dcdcd13", "name": "请求方式", "source": "793b493a8dcdcd13.json", "type": "application/json", "size": 4}, {"uid": "e89d61eda9a20695", "name": "请求头", "source": "e89d61eda9a20695.json", "type": "application/json", "size": 122}, {"uid": "889f6f98eada199a", "name": "<PERSON><PERSON>", "source": "889f6f98eada199a.json", "type": "application/json", "size": 12}, {"uid": "6ac068c64214f6c3", "name": "测试用例名称", "source": "6ac068c64214f6c3.json", "type": "application/json", "size": 25}, {"uid": "995708767f0adc41", "name": "参数类型", "source": "995708767f0adc41.json", "type": "application/json", "size": 4}, {"uid": "25c8836cd3df768", "name": "请求参数json格式", "source": "25c8836cd3df768.json", "type": "application/json", "size": 207}, {"uid": "417fd129a430add5", "name": "请求参数实际入参", "source": "417fd129a430add5.json", "type": "application/json", "size": 217}, {"uid": "4d39aebfb06f14fb", "name": "接口实际响应信息", "source": "4d39aebfb06f14fb.json", "type": "application/json", "size": 22018}, {"uid": "1c891caebbce7276", "name": "状态码断言结果：成功", "source": "1c891caebbce7276.txt", "type": "text/plain", "size": 37}, {"uid": "cf1fe8f292b01755", "name": "相等断言结果：成功", "source": "cf1fe8f292b01755.json", "type": "application/json", "size": 53}, {"uid": "63e9e8731f3b4112", "name": "📋 测试执行日志", "source": "63e9e8731f3b4112.txt", "type": "text/plain", "size": 4549}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 13, "hasContent": true, "stepsCount": 0}, "afterStages": [{"name": "capture_logs_for_allure::0", "time": {"start": 1751853002253, "stop": 1751853002274, "duration": 21}, "status": "passed", "steps": [], "attachments": [{"uid": "12c3d2805079c77e", "name": "📋 测试执行日志 (完整记录)", "source": "12c3d2805079c77e.txt", "type": "text/plain", "size": 14539}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 1, "hasContent": true, "stepsCount": 0}, {"name": "print_info::0", "time": {"start": 1751853001500, "stop": 1751853001500, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "function_wait::0", "time": {"start": 1751853001502, "stop": 1751853002252, "duration": 750}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}], "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "37476-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [{"name": "api_info", "value": "{'baseInfo': {'module': '演出票', 'api_name': '演唱会分类', 'host': '${get_host(play)}', 'url': '/api/show/info/performance_search', 'method': 'post', 'headers': '${get_headers(app)}'}, 'testCase': {'case_name': '按类型搜索=演唱会', 'validation': [{'code': 200}, {'eq': {'code': 0}}], 'extract': 0, 'extract_list': np.float64(0.0), 'data': {'category_id': '10001', 'channelCode': '${appKey()}', 'token': '${get_extract_data(token)}'}}}"}], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "cbacf0caadb9e08c.json", "parameterValues": ["{'baseInfo': {'module': '演出票', 'api_name': '演唱会分类', 'host': '${get_host(play)}', 'url': '/api/show/info/performance_search', 'method': 'post', 'headers': '${get_headers(app)}'}, 'testCase': {'case_name': '按类型搜索=演唱会', 'validation': [{'code': 200}, {'eq': {'code': 0}}], 'extract': 0, 'extract_list': np.float64(0.0), 'data': {'category_id': '10001', 'channelCode': '${appKey()}', 'token': '${get_extract_data(token)}'}}}"]}