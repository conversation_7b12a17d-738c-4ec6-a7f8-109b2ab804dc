{"uid": "c24dab9da4508323", "name": "近期特惠", "fullName": "testcase.play.test_play.TestPlay#test_play", "historyId": "b9dc0ab2757f5b153151bfbd4cc293dd", "time": {"start": 1751853890455, "stop": 1751853890758, "duration": 303}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "function_wait", "time": {"start": 1751853889705, "stop": 1751853890455, "duration": 750}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "clear_extract", "time": {"start": 1751853884946, "stop": 1751853885997, "duration": 1051}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "capture_logs_for_allure", "time": {"start": 1751853889705, "stop": 1751853889705, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "_session_faker", "time": {"start": 1751853884907, "stop": 1751853884946, "duration": 39}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "print_info", "time": {"start": 1751853890455, "stop": 1751853890455, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "66a2a8b304015479", "name": "接口地址", "source": "66a2a8b304015479.json", "type": "application/json", "size": 54}, {"uid": "a74fa018335a1338", "name": "接口名称", "source": "a74fa018335a1338.json", "type": "application/json", "size": 12}, {"uid": "b77a4f350fccb9ff", "name": "请求方式", "source": "b77a4f350fccb9ff.json", "type": "application/json", "size": 4}, {"uid": "16c5b39f79497206", "name": "请求头", "source": "16c5b39f79497206.json", "type": "application/json", "size": 122}, {"uid": "f180a984316e9328", "name": "<PERSON><PERSON>", "source": "f180a984316e9328.json", "type": "application/json", "size": 12}, {"uid": "6ce4a393dd4b09e5", "name": "测试用例名称", "source": "6ce4a393dd4b09e5.json", "type": "application/json", "size": 30}, {"uid": "ed3df6f00854d4dc", "name": "参数类型", "source": "ed3df6f00854d4dc.json", "type": "application/json", "size": 4}, {"uid": "2aa08d0b86228994", "name": "请求参数json格式", "source": "2aa08d0b86228994.json", "type": "application/json", "size": 214}, {"uid": "819305c505d4fc26", "name": "请求参数实际入参", "source": "819305c505d4fc26.json", "type": "application/json", "size": 224}, {"uid": "7d3ddd3696e7e2f6", "name": "接口实际响应信息", "source": "7d3ddd3696e7e2f6.json", "type": "application/json", "size": 22188}, {"uid": "c83d58311cab5e9f", "name": "状态码断言结果：成功", "source": "c83d58311cab5e9f.txt", "type": "text/plain", "size": 37}, {"uid": "575e7e335753eaaa", "name": "相等断言结果：成功", "source": "575e7e335753eaaa.json", "type": "application/json", "size": 53}, {"uid": "5239a8c235a06c67", "name": "📋 测试执行日志", "source": "5239a8c235a06c67.txt", "type": "text/plain", "size": 15249}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentsCount": 13, "shouldDisplayMessage": false}, "afterStages": [{"name": "function_wait::0", "time": {"start": 1751853890760, "stop": 1751853891511, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "capture_logs_for_allure::0", "time": {"start": 1751853891511, "stop": 1751853891520, "duration": 9}, "status": "passed", "steps": [], "attachments": [{"uid": "2c4fac43419e976b", "name": "📋 测试执行日志 (完整记录)", "source": "2c4fac43419e976b.txt", "type": "text/plain", "size": 14945}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentsCount": 1, "shouldDisplayMessage": false}, {"name": "print_info::0", "time": {"start": 1751853890759, "stop": 1751853890760, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}], "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "39132-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "c24dab9da4508323.json", "parameterValues": []}