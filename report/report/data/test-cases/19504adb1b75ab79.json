{"uid": "19504adb1b75ab79", "name": "下单", "fullName": "testcase.play.test_play.TestPlay#test_play", "historyId": "df78d83b2c7879c3a2ecdde06190a07c", "time": {"start": 1751853907768, "stop": 1751853909033, "duration": 1265}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "function_wait", "time": {"start": 1751853907017, "stop": 1751853907767, "duration": 750}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "clear_extract", "time": {"start": 1751853884946, "stop": 1751853885997, "duration": 1051}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "print_info", "time": {"start": 1751853907767, "stop": 1751853907767, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "capture_logs_for_allure", "time": {"start": 1751853907017, "stop": 1751853907017, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "_session_faker", "time": {"start": 1751853884907, "stop": 1751853884946, "duration": 39}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "22ee57504bfc740", "name": "接口地址", "source": "22ee57504bfc740.json", "type": "application/json", "size": 42}, {"uid": "297e7b9bb6eac53c", "name": "接口名称", "source": "297e7b9bb6eac53c.json", "type": "application/json", "size": 6}, {"uid": "a528db431fc0f99a", "name": "请求方式", "source": "a528db431fc0f99a.json", "type": "application/json", "size": 4}, {"uid": "8bd707f4ad1d067f", "name": "请求头", "source": "8bd707f4ad1d067f.json", "type": "application/json", "size": 122}, {"uid": "d18766737f7f2c3a", "name": "<PERSON><PERSON>", "source": "d18766737f7f2c3a.json", "type": "application/json", "size": 12}, {"uid": "8892b64e822b75a9", "name": "测试用例名称", "source": "8892b64e822b75a9.json", "type": "application/json", "size": 12}, {"uid": "dcd9e572f63ee8b7", "name": "参数类型", "source": "dcd9e572f63ee8b7.json", "type": "application/json", "size": 4}, {"uid": "fd49ad3439006b1b", "name": "请求参数json格式", "source": "fd49ad3439006b1b.json", "type": "application/json", "size": 615}, {"uid": "32af9906b95b5a91", "name": "请求参数实际入参", "source": "32af9906b95b5a91.json", "type": "application/json", "size": 625}, {"uid": "f8a8cb502daa2e67", "name": "接口实际响应信息", "source": "f8a8cb502daa2e67.json", "type": "application/json", "size": 94}, {"uid": "667e3790ebc2a5f3", "name": "状态码断言结果：成功", "source": "667e3790ebc2a5f3.txt", "type": "text/plain", "size": 37}, {"uid": "c5a3db98f2d33771", "name": "相等断言结果：成功", "source": "c5a3db98f2d33771.json", "type": "application/json", "size": 53}, {"uid": "97045a484da7c6e7", "name": "📋 测试执行日志", "source": "97045a484da7c6e7.txt", "type": "text/plain", "size": 2608}], "parameters": [], "hasContent": true, "stepsCount": 0, "attachmentsCount": 13, "shouldDisplayMessage": false}, "afterStages": [{"name": "function_wait::0", "time": {"start": 1751853909034, "stop": 1751853909784, "duration": 750}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "print_info::0", "time": {"start": 1751853909033, "stop": 1751853909033, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "capture_logs_for_allure::0", "time": {"start": 1751853909784, "stop": 1751853909794, "duration": 10}, "status": "passed", "steps": [], "attachments": [{"uid": "af329c77c55776a5", "name": "📋 测试执行日志 (完整记录)", "source": "af329c77c55776a5.txt", "type": "text/plain", "size": 2968}], "parameters": [], "hasContent": true, "stepsCount": 0, "attachmentsCount": 1, "shouldDisplayMessage": false}], "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "39132-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "19504adb1b75ab79.json", "parameterValues": []}