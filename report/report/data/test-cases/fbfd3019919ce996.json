{"uid": "fbfd3019919ce996", "name": "演唱会分类", "fullName": "testcase.play.test_play.TestPlay#test_play", "historyId": "47b52c96aa0c1d59c25f4bc63959c980", "time": {"start": 1751853895908, "stop": 1751853896233, "duration": 325}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "print_info", "time": {"start": 1751853895908, "stop": 1751853895908, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "function_wait", "time": {"start": 1751853895158, "stop": 1751853895908, "duration": 750}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "capture_logs_for_allure", "time": {"start": 1751853895157, "stop": 1751853895158, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "clear_extract", "time": {"start": 1751853884946, "stop": 1751853885997, "duration": 1051}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "_session_faker", "time": {"start": 1751853884907, "stop": 1751853884946, "duration": 39}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "8fc28a769f8e7407", "name": "接口地址", "source": "8fc28a769f8e7407.json", "type": "application/json", "size": 53}, {"uid": "f8fb331be580f93", "name": "接口名称", "source": "f8fb331be580f93.json", "type": "application/json", "size": 15}, {"uid": "b3bc88d96dfaf796", "name": "请求方式", "source": "b3bc88d96dfaf796.json", "type": "application/json", "size": 4}, {"uid": "f36c13bfbc1e4f84", "name": "请求头", "source": "f36c13bfbc1e4f84.json", "type": "application/json", "size": 122}, {"uid": "324ab9f4d5546c55", "name": "<PERSON><PERSON>", "source": "324ab9f4d5546c55.json", "type": "application/json", "size": 12}, {"uid": "e647106787e281bc", "name": "测试用例名称", "source": "e647106787e281bc.json", "type": "application/json", "size": 25}, {"uid": "4eaf129a373358dd", "name": "参数类型", "source": "4eaf129a373358dd.json", "type": "application/json", "size": 4}, {"uid": "92d591be5421c328", "name": "请求参数json格式", "source": "92d591be5421c328.json", "type": "application/json", "size": 207}, {"uid": "6904dfd89fea7699", "name": "请求参数实际入参", "source": "6904dfd89fea7699.json", "type": "application/json", "size": 217}, {"uid": "1c8d74c706f74956", "name": "接口实际响应信息", "source": "1c8d74c706f74956.json", "type": "application/json", "size": 22018}, {"uid": "37279fdc2af3c318", "name": "状态码断言结果：成功", "source": "37279fdc2af3c318.txt", "type": "text/plain", "size": 37}, {"uid": "2f4ecbc390d3c681", "name": "相等断言结果：成功", "source": "2f4ecbc390d3c681.json", "type": "application/json", "size": 53}, {"uid": "b4beb866d5288eb9", "name": "📋 测试执行日志", "source": "b4beb866d5288eb9.txt", "type": "text/plain", "size": 4779}], "parameters": [], "hasContent": true, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 13}, "afterStages": [{"name": "print_info::0", "time": {"start": 1751853896234, "stop": 1751853896234, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "function_wait::0", "time": {"start": 1751853896235, "stop": 1751853896985, "duration": 750}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "capture_logs_for_allure::0", "time": {"start": 1751853896985, "stop": 1751853896995, "duration": 10}, "status": "passed", "steps": [], "attachments": [{"uid": "fa81430671049a36", "name": "📋 测试执行日志 (完整记录)", "source": "fa81430671049a36.txt", "type": "text/plain", "size": 14921}], "parameters": [], "hasContent": true, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 1}], "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "39132-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "fbfd3019919ce996.json", "parameterValues": []}