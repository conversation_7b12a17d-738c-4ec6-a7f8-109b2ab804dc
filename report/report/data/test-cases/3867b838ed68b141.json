{"uid": "3867b838ed68b141", "name": "演出项目详情", "fullName": "testcase.play.test_play.TestPlay#test_play", "historyId": "0457c216d6185e839b1ab99163c7bbc6", "time": {"start": 1751853899563, "stop": 1751853900106, "duration": 543}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "capture_logs_for_allure", "time": {"start": 1751853898812, "stop": 1751853898812, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "print_info", "time": {"start": 1751853899563, "stop": 1751853899563, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "clear_extract", "time": {"start": 1751853884946, "stop": 1751853885997, "duration": 1051}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "_session_faker", "time": {"start": 1751853884907, "stop": 1751853884946, "duration": 39}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "function_wait", "time": {"start": 1751853898812, "stop": 1751853899563, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "b838d0fd921a0001", "name": "接口地址", "source": "b838d0fd921a0001.json", "type": "application/json", "size": 51}, {"uid": "49ba685b1c552395", "name": "接口名称", "source": "49ba685b1c552395.json", "type": "application/json", "size": 18}, {"uid": "f1299323c5a1d1ad", "name": "请求方式", "source": "f1299323c5a1d1ad.json", "type": "application/json", "size": 3}, {"uid": "e02e84c48ea80a95", "name": "请求头", "source": "e02e84c48ea80a95.json", "type": "application/json", "size": 122}, {"uid": "4531993faf08ab21", "name": "<PERSON><PERSON>", "source": "4531993faf08ab21.json", "type": "application/json", "size": 12}, {"uid": "4ea92c6c98073fe9", "name": "测试用例名称", "source": "4ea92c6c98073fe9.json", "type": "application/json", "size": 30}, {"uid": "206c732137073de9", "name": "参数类型", "source": "206c732137073de9.json", "type": "application/json", "size": 6}, {"uid": "c33ca64b6d75585e", "name": "请求参数json格式", "source": "c33ca64b6d75585e.json", "type": "application/json", "size": 206}, {"uid": "752142d96c46492a", "name": "请求参数实际入参", "source": "752142d96c46492a.json", "type": "application/json", "size": 216}, {"uid": "b94a5abcb4389990", "name": "接口实际响应信息", "source": "b94a5abcb4389990.json", "type": "application/json", "size": 4313}, {"uid": "eb5e67effda495e", "name": "状态码断言结果：成功", "source": "eb5e67effda495e.txt", "type": "text/plain", "size": 37}, {"uid": "a0b8ec754b4232c2", "name": "相等断言结果：成功", "source": "a0b8ec754b4232c2.json", "type": "application/json", "size": 53}, {"uid": "a69f03d8a6e348e1", "name": "📋 测试执行日志", "source": "a69f03d8a6e348e1.txt", "type": "text/plain", "size": 15160}], "parameters": [], "hasContent": true, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 13}, "afterStages": [{"name": "capture_logs_for_allure::0", "time": {"start": 1751853900859, "stop": 1751853900875, "duration": 16}, "status": "passed", "steps": [], "attachments": [{"uid": "81445314c9abe43b", "name": "📋 测试执行日志 (完整记录)", "source": "81445314c9abe43b.txt", "type": "text/plain", "size": 6363}], "parameters": [], "hasContent": true, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "print_info::0", "time": {"start": 1751853900107, "stop": 1751853900107, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "function_wait::0", "time": {"start": 1751853900108, "stop": 1751853900859, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "39132-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "3867b838ed68b141.json", "parameterValues": []}