{"uid": "28b129c1418628b9", "name": "近期特惠", "fullName": "testcase.play.test_play.TestPlay#test_play", "historyId": "b9dc0ab2757f5b153151bfbd4cc293dd", "time": {"start": 1751866420275, "stop": 1751866420583, "duration": 308}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "function_wait", "time": {"start": 1751866419524, "stop": 1751866420274, "duration": 750}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "clear_extract", "time": {"start": 1751866415419, "stop": 1751866415726, "duration": 307}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "_session_faker", "time": {"start": 1751866415384, "stop": 1751866415419, "duration": 35}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "capture_logs_for_allure", "time": {"start": 1751866419524, "stop": 1751866419524, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "print_info", "time": {"start": 1751866420274, "stop": 1751866420274, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "3338526e6c9d835c", "name": "接口地址", "source": "3338526e6c9d835c.json", "type": "application/json", "size": 54}, {"uid": "154da75c0456571", "name": "接口名称", "source": "154da75c0456571.json", "type": "application/json", "size": 12}, {"uid": "89579f58f32dcbb4", "name": "请求方式", "source": "89579f58f32dcbb4.json", "type": "application/json", "size": 4}, {"uid": "9d056e4d57d2a684", "name": "请求头", "source": "9d056e4d57d2a684.json", "type": "application/json", "size": 122}, {"uid": "1aa874f5cbb77f84", "name": "<PERSON><PERSON>", "source": "1aa874f5cbb77f84.json", "type": "application/json", "size": 12}, {"uid": "f56d3099964db434", "name": "测试用例名称", "source": "f56d3099964db434.json", "type": "application/json", "size": 30}, {"uid": "f91803ca2d9895e8", "name": "参数类型", "source": "f91803ca2d9895e8.json", "type": "application/json", "size": 4}, {"uid": "5b5ec9f284e4beb1", "name": "请求参数json格式", "source": "5b5ec9f284e4beb1.json", "type": "application/json", "size": 214}, {"uid": "89ed8fd43a3e10e4", "name": "请求参数实际入参", "source": "89ed8fd43a3e10e4.json", "type": "application/json", "size": 224}, {"uid": "41f0da886d2eda85", "name": "接口实际响应信息", "source": "41f0da886d2eda85.json", "type": "application/json", "size": 22188}, {"uid": "b41823393d6eabb7", "name": "状态码断言结果：成功", "source": "b41823393d6eabb7.txt", "type": "text/plain", "size": 37}, {"uid": "ab225fc53b77a7d", "name": "相等断言结果：成功", "source": "ab225fc53b77a7d.json", "type": "application/json", "size": 53}, {"uid": "fdab1c943fefc992", "name": "📋 测试执行日志", "source": "fdab1c943fefc992.txt", "type": "text/plain", "size": 14716}], "parameters": [], "hasContent": true, "stepsCount": 0, "attachmentsCount": 13, "shouldDisplayMessage": false}, "afterStages": [{"name": "function_wait::0", "time": {"start": 1751866420585, "stop": 1751866421336, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "capture_logs_for_allure::0", "time": {"start": 1751866421337, "stop": 1751866421350, "duration": 13}, "status": "passed", "steps": [], "attachments": [{"uid": "ef6052fe6c94f76a", "name": "📋 测试执行日志 (完整记录)", "source": "ef6052fe6c94f76a.txt", "type": "text/plain", "size": 14715}], "parameters": [], "hasContent": true, "stepsCount": 0, "attachmentsCount": 1, "shouldDisplayMessage": false}, {"name": "print_info::0", "time": {"start": 1751866420584, "stop": 1751866420584, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}], "labels": [{"name": "story", "value": "C01_演出模块流程"}, {"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "16848-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "28b129c1418628b9.json", "parameterValues": []}