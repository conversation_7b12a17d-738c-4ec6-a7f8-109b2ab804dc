{"uid": "e0397f48abac85a", "name": "根据场次获取取票方式", "fullName": "testcase.play.test_play.TestPlay#test_play", "historyId": "40f667e7e7d9255278cb56eb99290821", "time": {"start": 1751853905776, "stop": 1751853906248, "duration": 472}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "print_info", "time": {"start": 1751853905775, "stop": 1751853905775, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "clear_extract", "time": {"start": 1751853884946, "stop": 1751853885997, "duration": 1051}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "capture_logs_for_allure", "time": {"start": 1751853905025, "stop": 1751853905025, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "_session_faker", "time": {"start": 1751853884907, "stop": 1751853884946, "duration": 39}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "function_wait", "time": {"start": 1751853905025, "stop": 1751853905775, "duration": 750}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "30a3a7236f6b83aa", "name": "接口地址", "source": "30a3a7236f6b83aa.json", "type": "application/json", "size": 57}, {"uid": "9faaf89e26ec4050", "name": "接口名称", "source": "9faaf89e26ec4050.json", "type": "application/json", "size": 30}, {"uid": "20ee2f339c7a60ea", "name": "请求方式", "source": "20ee2f339c7a60ea.json", "type": "application/json", "size": 3}, {"uid": "4cc918322c2a763a", "name": "请求头", "source": "4cc918322c2a763a.json", "type": "application/json", "size": 122}, {"uid": "e7f1281b50c75852", "name": "<PERSON><PERSON>", "source": "e7f1281b50c75852.json", "type": "application/json", "size": 12}, {"uid": "1b2b26de861d6a0e", "name": "测试用例名称", "source": "1b2b26de861d6a0e.json", "type": "application/json", "size": 30}, {"uid": "c25cb683bbde1615", "name": "参数类型", "source": "c25cb683bbde1615.json", "type": "application/json", "size": 6}, {"uid": "936aa7b936c9510d", "name": "请求参数json格式", "source": "936aa7b936c9510d.json", "type": "application/json", "size": 199}, {"uid": "15ef6c41603b901e", "name": "请求参数实际入参", "source": "15ef6c41603b901e.json", "type": "application/json", "size": 209}, {"uid": "b0f2317d50543826", "name": "接口实际响应信息", "source": "b0f2317d50543826.json", "type": "application/json", "size": 599}, {"uid": "4870998c021a0ad3", "name": "状态码断言结果：成功", "source": "4870998c021a0ad3.txt", "type": "text/plain", "size": 37}, {"uid": "4ce8113db9e07942", "name": "相等断言结果：成功", "source": "4ce8113db9e07942.json", "type": "application/json", "size": 53}, {"uid": "151d05aeba99ca8a", "name": "📋 测试执行日志", "source": "151d05aeba99ca8a.txt", "type": "text/plain", "size": 5242}], "parameters": [], "hasContent": true, "stepsCount": 0, "attachmentsCount": 13, "shouldDisplayMessage": false}, "afterStages": [{"name": "print_info::0", "time": {"start": 1751853906249, "stop": 1751853906250, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "capture_logs_for_allure::0", "time": {"start": 1751853907001, "stop": 1751853907015, "duration": 14}, "status": "passed", "steps": [], "attachments": [{"uid": "d43ca9b7ca7c6388", "name": "📋 测试执行日志 (完整记录)", "source": "d43ca9b7ca7c6388.txt", "type": "text/plain", "size": 2608}], "parameters": [], "hasContent": true, "stepsCount": 0, "attachmentsCount": 1, "shouldDisplayMessage": false}, {"name": "function_wait::0", "time": {"start": 1751853906250, "stop": 1751853907001, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}], "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "39132-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "e0397f48abac85a.json", "parameterValues": []}