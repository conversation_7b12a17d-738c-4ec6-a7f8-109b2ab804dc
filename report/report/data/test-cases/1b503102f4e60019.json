{"uid": "1b503102f4e60019", "name": "查询影讯接口", "fullName": "testcase.demo_test.test_demo.TestFilmOrder#test_film_order", "historyId": "e9c2ea8fd9c81240c2b50d2a9668daf2", "time": {"start": 1751853913524, "stop": 1751853913725, "duration": 201}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "capture_logs_for_allure", "time": {"start": 1751853912773, "stop": 1751853912773, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "function_wait", "time": {"start": 1751853912773, "stop": 1751853913524, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "clear_extract", "time": {"start": 1751853884946, "stop": 1751853885997, "duration": 1051}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "_session_faker", "time": {"start": 1751853884907, "stop": 1751853884946, "duration": 39}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "print_info", "time": {"start": 1751853913524, "stop": 1751853913524, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "fd242b5d3d410236", "name": "接口地址", "source": "fd242b5d3d410236.json", "type": "application/json", "size": 48}, {"uid": "34b14f5c2f4f3ff8", "name": "接口名称", "source": "34b14f5c2f4f3ff8.json", "type": "application/json", "size": 18}, {"uid": "9407ad006baff3ce", "name": "请求方式", "source": "9407ad006baff3ce.json", "type": "application/json", "size": 4}, {"uid": "d397f44854d6419e", "name": "请求头", "source": "d397f44854d6419e.json", "type": "application/json", "size": 15}, {"uid": "bd9e6272b808fc27", "name": "<PERSON><PERSON>", "source": "bd9e6272b808fc27.json", "type": "application/json", "size": 12}, {"uid": "cdec75eecec6be8e", "name": "测试用例名称", "source": "cdec75eecec6be8e.json", "type": "application/json", "size": 27}, {"uid": "a32937129ada4bb3", "name": "参数类型", "source": "a32937129ada4bb3.json", "type": "application/json", "size": 4}, {"uid": "d72453250db8cce5", "name": "请求参数json格式", "source": "d72453250db8cce5.json", "type": "application/json", "size": 493}, {"uid": "e728c26d91d04293", "name": "请求参数实际入参", "source": "e728c26d91d04293.json", "type": "application/json", "size": 436}, {"uid": "e521f1158709e09a", "name": "接口实际响应信息", "source": "e521f1158709e09a.json", "type": "application/json", "size": 52017}, {"uid": "416f233f029dc470", "name": "状态码断言结果：成功", "source": "416f233f029dc470.txt", "type": "text/plain", "size": 37}, {"uid": "2922ccac29695cad", "name": "相等断言结果：成功", "source": "2922ccac29695cad.json", "type": "application/json", "size": 53}, {"uid": "ec6ade64029820ae", "name": "相等断言结果：成功", "source": "ec6ade64029820ae.json", "type": "application/json", "size": 77}, {"uid": "cc4d8058b48fce8d", "name": "📋 测试执行日志", "source": "cc4d8058b48fce8d.txt", "type": "text/plain", "size": 2307}], "parameters": [], "hasContent": true, "stepsCount": 0, "attachmentsCount": 14, "shouldDisplayMessage": false}, "afterStages": [{"name": "capture_logs_for_allure::0", "time": {"start": 1751853914477, "stop": 1751853914488, "duration": 11}, "status": "passed", "steps": [], "attachments": [{"uid": "ce04bf9f51b68b01", "name": "📋 测试执行日志 (完整记录)", "source": "ce04bf9f51b68b01.txt", "type": "text/plain", "size": 30345}], "parameters": [], "hasContent": true, "stepsCount": 0, "attachmentsCount": 1, "shouldDisplayMessage": false}, {"name": "function_wait::0", "time": {"start": 1751853913727, "stop": 1751853914477, "duration": 750}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "print_info::0", "time": {"start": 1751853913726, "stop": 1751853913726, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}], "labels": [{"name": "story", "value": "C02_电影下单流程"}, {"name": "feature", "value": "M02_电影订票模块接口测试"}, {"name": "parentSuite", "value": "testcase.demo_test"}, {"name": "suite", "value": "test_demo"}, {"name": "subSuite", "value": "TestFilmOrder"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "39132-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.demo_test.test_demo"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "1b503102f4e60019.json", "parameterValues": []}