{"uid": "4b021e78ad015362", "name": "首页广告位", "fullName": "testcase.play.test_play.TestPlay#test_play", "historyId": "998f7fb53b18ea3045f71922b591e307", "time": {"start": 1751866423947, "stop": 1751866424279, "duration": 332}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "print_info", "time": {"start": 1751866423947, "stop": 1751866423947, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "clear_extract", "time": {"start": 1751866415419, "stop": 1751866415726, "duration": 307}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "_session_faker", "time": {"start": 1751866415384, "stop": 1751866415419, "duration": 35}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "capture_logs_for_allure", "time": {"start": 1751866423196, "stop": 1751866423196, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "function_wait", "time": {"start": 1751866423196, "stop": 1751866423947, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "7cbf6da06bd8e192", "name": "接口地址", "source": "7cbf6da06bd8e192.json", "type": "application/json", "size": 44}, {"uid": "e046b8f170cf3f27", "name": "接口名称", "source": "e046b8f170cf3f27.json", "type": "application/json", "size": 15}, {"uid": "b286eaa6013e9441", "name": "请求方式", "source": "b286eaa6013e9441.json", "type": "application/json", "size": 4}, {"uid": "4d3f051aef837280", "name": "请求头", "source": "4d3f051aef837280.json", "type": "application/json", "size": 122}, {"uid": "90aedaebabf2324d", "name": "<PERSON><PERSON>", "source": "90aedaebabf2324d.json", "type": "application/json", "size": 12}, {"uid": "7e8f1365a7294cae", "name": "测试用例名称", "source": "7e8f1365a7294cae.json", "type": "application/json", "size": 24}, {"uid": "cb11c8e04d3384bc", "name": "参数类型", "source": "cb11c8e04d3384bc.json", "type": "application/json", "size": 4}, {"uid": "e66d66878d1f0ad0", "name": "请求参数json格式", "source": "e66d66878d1f0ad0.json", "type": "application/json", "size": 179}, {"uid": "cd98be34018495df", "name": "请求参数实际入参", "source": "cd98be34018495df.json", "type": "application/json", "size": 189}, {"uid": "b92b220ee5092c90", "name": "接口实际响应信息", "source": "b92b220ee5092c90.json", "type": "application/json", "size": 3479}, {"uid": "7c88a2aafa1ccdb6", "name": "状态码断言结果：成功", "source": "7c88a2aafa1ccdb6.txt", "type": "text/plain", "size": 37}, {"uid": "33e97834512b5a0e", "name": "相等断言结果：成功", "source": "33e97834512b5a0e.json", "type": "application/json", "size": 53}, {"uid": "78d0dc2ca4f474ea", "name": "📋 测试执行日志", "source": "78d0dc2ca4f474ea.txt", "type": "text/plain", "size": 27049}], "parameters": [], "hasContent": true, "stepsCount": 0, "attachmentsCount": 13, "shouldDisplayMessage": false}, "afterStages": [{"name": "print_info::0", "time": {"start": 1751866424279, "stop": 1751866424280, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "capture_logs_for_allure::0", "time": {"start": 1751866425032, "stop": 1751866425050, "duration": 18}, "status": "passed", "steps": [], "attachments": [{"uid": "11a3e6ee1db115d", "name": "📋 测试执行日志 (完整记录)", "source": "11a3e6ee1db115d.txt", "type": "text/plain", "size": 4549}], "parameters": [], "hasContent": true, "stepsCount": 0, "attachmentsCount": 1, "shouldDisplayMessage": false}, {"name": "function_wait::0", "time": {"start": 1751866424280, "stop": 1751866425031, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}], "labels": [{"name": "story", "value": "C01_演出模块流程"}, {"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "16848-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "4b021e78ad015362.json", "parameterValues": []}