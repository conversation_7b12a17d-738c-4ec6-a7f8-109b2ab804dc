{"uid": "d3a6ce14a08a2c26", "name": "**搜索**", "fullName": "testcase.play.test_play.TestPlay#test_play", "historyId": "80ea16c71eb34b6e85110c3f875859f1", "time": {"start": 1751852468344, "stop": 1751852468676, "duration": 332}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "_session_faker", "time": {"start": 1751852455733, "stop": 1751852455863, "duration": 130}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "function_wait", "time": {"start": 1751852467593, "stop": 1751852468344, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "print_info", "time": {"start": 1751852468344, "stop": 1751852468344, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "clear_extract", "time": {"start": 1751852455863, "stop": 1751852456123, "duration": 260}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "capture_logs_for_allure", "time": {"start": 1751852467593, "stop": 1751852467593, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "6cb66460d24bf444", "name": "接口地址", "source": "6cb66460d24bf444.json", "type": "application/json", "size": 53}, {"uid": "6a6abced70e1fb32", "name": "接口名称", "source": "6a6abced70e1fb32.json", "type": "application/json", "size": 10}, {"uid": "a8dfb5b896442762", "name": "请求方式", "source": "a8dfb5b896442762.json", "type": "application/json", "size": 4}, {"uid": "975f0ec1dba6f23b", "name": "请求头", "source": "975f0ec1dba6f23b.json", "type": "application/json", "size": 122}, {"uid": "d7d4bbeb2a7eb8c2", "name": "<PERSON><PERSON>", "source": "d7d4bbeb2a7eb8c2.json", "type": "application/json", "size": 12}, {"uid": "bc8ccf354a0dcc77", "name": "测试用例名称", "source": "bc8ccf354a0dcc77.json", "type": "application/json", "size": 30}, {"uid": "7989d052358b6439", "name": "参数类型", "source": "7989d052358b6439.json", "type": "application/json", "size": 4}, {"uid": "ac284b7297060335", "name": "请求参数json格式", "source": "ac284b7297060335.json", "type": "application/json", "size": 201}, {"uid": "577ea48cbcbb1f66", "name": "请求参数实际入参", "source": "577ea48cbcbb1f66.json", "type": "application/json", "size": 211}, {"uid": "de1b6bbf356e04d4", "name": "接口实际响应信息", "source": "de1b6bbf356e04d4.json", "type": "application/json", "size": 22387}, {"uid": "dcd1af9114d181a8", "name": "状态码断言结果：成功", "source": "dcd1af9114d181a8.txt", "type": "text/plain", "size": 37}, {"uid": "b330a9cc7d2d6587", "name": "相等断言结果：成功", "source": "b330a9cc7d2d6587.json", "type": "application/json", "size": 53}, {"uid": "b4afef1604efd10c", "name": "📋 测试执行日志", "source": "b4afef1604efd10c.txt", "type": "text/plain", "size": 14687}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 13, "hasContent": true, "stepsCount": 0}, "afterStages": [{"name": "function_wait::0", "time": {"start": 1751852468677, "stop": 1751852469429, "duration": 752}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "print_info::0", "time": {"start": 1751852468677, "stop": 1751852468677, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "capture_logs_for_allure::0", "time": {"start": 1751852469429, "stop": 1751852469448, "duration": 19}, "status": "passed", "steps": [], "attachments": [{"uid": "926c906e892c2fa2", "name": "📋 测试执行日志 (完整记录)", "source": "926c906e892c2fa2.txt", "type": "text/plain", "size": 15511}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 1, "hasContent": true, "stepsCount": 0}], "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "38092-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "d3a6ce14a08a2c26.json", "parameterValues": []}