{"uid": "d5dd43a600cd3bde", "name": "为你推荐", "fullName": "testcase.play.test_play.TestPlay#test_play", "historyId": "a03ba38a51bcc9af12332b9cb9ea6b41", "time": {"start": 1751851571438, "stop": 1751851571750, "duration": 312}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "_session_faker", "time": {"start": 1751851564550, "stop": 1751851564591, "duration": 41}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "print_info", "time": {"start": 1751851571438, "stop": 1751851571438, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "clear_extract", "time": {"start": 1751851564591, "stop": 1751851564922, "duration": 331}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "function_wait", "time": {"start": 1751851570688, "stop": 1751851571438, "duration": 750}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "capture_logs_for_allure", "time": {"start": 1751851570688, "stop": 1751851570688, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "5b8887bba0e29e5f", "name": "接口地址", "source": "5b8887bba0e29e5f.json", "type": "application/json", "size": 54}, {"uid": "db184aa279953617", "name": "接口名称", "source": "db184aa279953617.json", "type": "application/json", "size": 12}, {"uid": "a98cfc0a2afbdff1", "name": "请求方式", "source": "a98cfc0a2afbdff1.json", "type": "application/json", "size": 4}, {"uid": "dc5b1f41a43a2f50", "name": "请求头", "source": "dc5b1f41a43a2f50.json", "type": "application/json", "size": 122}, {"uid": "aba8720e5e66b04f", "name": "<PERSON><PERSON>", "source": "aba8720e5e66b04f.json", "type": "application/json", "size": 12}, {"uid": "f8e2e1b4fba45ac7", "name": "测试用例名称", "source": "f8e2e1b4fba45ac7.json", "type": "application/json", "size": 30}, {"uid": "f27382a8754297b1", "name": "参数类型", "source": "f27382a8754297b1.json", "type": "application/json", "size": 4}, {"uid": "cd27cdd2548c730e", "name": "请求参数json格式", "source": "cd27cdd2548c730e.json", "type": "application/json", "size": 214}, {"uid": "f6c72ea2b93ff42b", "name": "请求参数实际入参", "source": "f6c72ea2b93ff42b.json", "type": "application/json", "size": 224}, {"uid": "55fb871af1bfc89f", "name": "接口实际响应信息", "source": "55fb871af1bfc89f.json", "type": "application/json", "size": 44012}, {"uid": "f6583de774623639", "name": "状态码断言结果：成功", "source": "f6583de774623639.txt", "type": "text/plain", "size": 37}, {"uid": "adef66fa6da513ff", "name": "相等断言结果：成功", "source": "adef66fa6da513ff.json", "type": "application/json", "size": 53}, {"uid": "9231a4ac86e73ca8", "name": "📋 测试执行日志", "source": "9231a4ac86e73ca8.txt", "type": "text/plain", "size": 14715}], "parameters": [], "hasContent": true, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 13}, "afterStages": [{"name": "print_info::0", "time": {"start": 1751851571751, "stop": 1751851571751, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "function_wait::0", "time": {"start": 1751851571751, "stop": 1751851572502, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "capture_logs_for_allure::0", "time": {"start": 1751851572503, "stop": 1751851572519, "duration": 16}, "status": "passed", "steps": [], "attachments": [{"uid": "f96a83b0407896b2", "name": "📋 测试执行日志 (完整记录)", "source": "f96a83b0407896b2.txt", "type": "text/plain", "size": 27049}], "parameters": [], "hasContent": true, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 1}], "labels": [{"name": "story", "value": "C01_演出模块流程"}, {"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "34968-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "d5dd43a600cd3bde.json", "parameterValues": []}