{"uid": "5d67f8fbe23c1f60", "name": "首页广告位", "fullName": "testcase.play.test_play.TestPlay#test_play", "historyId": "998f7fb53b18ea3045f71922b591e307", "time": {"start": 1751853237445, "stop": 1751853237734, "duration": 289}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "_session_faker", "time": {"start": 1751853229094, "stop": 1751853229142, "duration": 48}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "capture_logs_for_allure", "time": {"start": 1751853236694, "stop": 1751853236694, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "clear_extract", "time": {"start": 1751853229143, "stop": 1751853229413, "duration": 270}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "function_wait", "time": {"start": 1751853236694, "stop": 1751853237445, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "print_info", "time": {"start": 1751853237445, "stop": 1751853237445, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "5256e76947264fc9", "name": "接口地址", "source": "5256e76947264fc9.json", "type": "application/json", "size": 44}, {"uid": "b1ef5544c3719487", "name": "接口名称", "source": "b1ef5544c3719487.json", "type": "application/json", "size": 15}, {"uid": "f2d2528aeaebb2d6", "name": "请求方式", "source": "f2d2528aeaebb2d6.json", "type": "application/json", "size": 4}, {"uid": "58140c21348f4cdb", "name": "请求头", "source": "58140c21348f4cdb.json", "type": "application/json", "size": 122}, {"uid": "1f30f779ff578ed1", "name": "<PERSON><PERSON>", "source": "1f30f779ff578ed1.json", "type": "application/json", "size": 12}, {"uid": "df9fa94f29798228", "name": "测试用例名称", "source": "df9fa94f29798228.json", "type": "application/json", "size": 24}, {"uid": "277ab9a6cb37f08", "name": "参数类型", "source": "277ab9a6cb37f08.json", "type": "application/json", "size": 4}, {"uid": "7ede04d88507f226", "name": "请求参数json格式", "source": "7ede04d88507f226.json", "type": "application/json", "size": 179}, {"uid": "7f5610712d48ec33", "name": "请求参数实际入参", "source": "7f5610712d48ec33.json", "type": "application/json", "size": 189}, {"uid": "9211a9784165a36b", "name": "接口实际响应信息", "source": "9211a9784165a36b.json", "type": "application/json", "size": 3479}, {"uid": "74ef58dcf18edfbb", "name": "状态码断言结果：成功", "source": "74ef58dcf18edfbb.txt", "type": "text/plain", "size": 37}, {"uid": "6980d30ed367309a", "name": "相等断言结果：成功", "source": "6980d30ed367309a.json", "type": "application/json", "size": 53}, {"uid": "ce4f139a382f4d71", "name": "📋 测试执行日志", "source": "ce4f139a382f4d71.txt", "type": "text/plain", "size": 27530}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 13, "hasContent": true, "stepsCount": 0}, "afterStages": [{"name": "capture_logs_for_allure::0", "time": {"start": 1751853238487, "stop": 1751853238497, "duration": 10}, "status": "passed", "steps": [], "attachments": [{"uid": "5e4640745ce81b9d", "name": "📋 测试执行日志 (完整记录)", "source": "5e4640745ce81b9d.txt", "type": "text/plain", "size": 4956}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 1, "hasContent": true, "stepsCount": 0}, {"name": "function_wait::0", "time": {"start": 1751853237736, "stop": 1751853238487, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "print_info::0", "time": {"start": 1751853237735, "stop": 1751853237735, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}], "labels": [{"name": "story", "value": "C01_演出模块流程"}, {"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "28812-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "5d67f8fbe23c1f60.json", "parameterValues": []}