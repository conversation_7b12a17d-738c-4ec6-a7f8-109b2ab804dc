{"uid": "870dbd37daaf40c8", "name": "首页广告位", "fullName": "testcase.play.test_play.TestPlay#test_play", "historyId": "998f7fb53b18ea3045f71922b591e307", "time": {"start": 1751852464680, "stop": 1751852464960, "duration": 280}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "function_wait", "time": {"start": 1751852463930, "stop": 1751852464680, "duration": 750}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "print_info", "time": {"start": 1751852464680, "stop": 1751852464680, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "_session_faker", "time": {"start": 1751852455733, "stop": 1751852455863, "duration": 130}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "capture_logs_for_allure", "time": {"start": 1751852463930, "stop": 1751852463930, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "clear_extract", "time": {"start": 1751852455863, "stop": 1751852456123, "duration": 260}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "55ae58faf86a0a1c", "name": "接口地址", "source": "55ae58faf86a0a1c.json", "type": "application/json", "size": 44}, {"uid": "29606e8fae8b9bc7", "name": "接口名称", "source": "29606e8fae8b9bc7.json", "type": "application/json", "size": 15}, {"uid": "f515c9a329f11520", "name": "请求方式", "source": "f515c9a329f11520.json", "type": "application/json", "size": 4}, {"uid": "98efd95751b07b59", "name": "请求头", "source": "98efd95751b07b59.json", "type": "application/json", "size": 122}, {"uid": "be2da5e7c62708e1", "name": "<PERSON><PERSON>", "source": "be2da5e7c62708e1.json", "type": "application/json", "size": 12}, {"uid": "a9adc1ed829d9225", "name": "测试用例名称", "source": "a9adc1ed829d9225.json", "type": "application/json", "size": 24}, {"uid": "f747b620bae9f720", "name": "参数类型", "source": "f747b620bae9f720.json", "type": "application/json", "size": 4}, {"uid": "d5419a4c54ac05c2", "name": "请求参数json格式", "source": "d5419a4c54ac05c2.json", "type": "application/json", "size": 179}, {"uid": "976dff6734719115", "name": "请求参数实际入参", "source": "976dff6734719115.json", "type": "application/json", "size": 189}, {"uid": "475b3aad80364d5", "name": "接口实际响应信息", "source": "475b3aad80364d5.json", "type": "application/json", "size": 3479}, {"uid": "1651f93903922b9a", "name": "状态码断言结果：成功", "source": "1651f93903922b9a.txt", "type": "text/plain", "size": 37}, {"uid": "8c7aa0204920c15e", "name": "相等断言结果：成功", "source": "8c7aa0204920c15e.json", "type": "application/json", "size": 53}, {"uid": "b8fc186fb507c776", "name": "📋 测试执行日志", "source": "b8fc186fb507c776.txt", "type": "text/plain", "size": 27309}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 13, "hasContent": true, "stepsCount": 0}, "afterStages": [{"name": "function_wait::0", "time": {"start": 1751852464961, "stop": 1751852465712, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "print_info::0", "time": {"start": 1751852464961, "stop": 1751852464961, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "capture_logs_for_allure::0", "time": {"start": 1751852465712, "stop": 1751852465726, "duration": 14}, "status": "passed", "steps": [], "attachments": [{"uid": "38793d38f8f0b15d", "name": "📋 测试执行日志 (完整记录)", "source": "38793d38f8f0b15d.txt", "type": "text/plain", "size": 5129}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 1, "hasContent": true, "stepsCount": 0}], "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "38092-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "870dbd37daaf40c8.json", "parameterValues": []}