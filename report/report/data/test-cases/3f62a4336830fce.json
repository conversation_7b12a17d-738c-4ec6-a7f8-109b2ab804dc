{"uid": "3f62a4336830fce", "name": "首页广告位", "fullName": "testcase.play.test_play.TestPlay#test_play", "historyId": "16615fd56de330c5e7df9e74f610a0a1", "time": {"start": 1751853736318, "stop": 1751853736640, "duration": 322}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "capture_logs_for_allure", "time": {"start": 1751853735565, "stop": 1751853735565, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "hasContent": false, "stepsCount": 0, "attachmentsCount": 0}, {"name": "function_wait", "time": {"start": 1751853735565, "stop": 1751853736316, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "hasContent": false, "stepsCount": 0, "attachmentsCount": 0}, {"name": "_session_faker", "time": {"start": 1751853726731, "stop": 1751853726779, "duration": 48}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "hasContent": false, "stepsCount": 0, "attachmentsCount": 0}, {"name": "clear_extract", "time": {"start": 1751853726779, "stop": 1751853727168, "duration": 389}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "hasContent": false, "stepsCount": 0, "attachmentsCount": 0}, {"name": "print_info", "time": {"start": 1751853736316, "stop": 1751853736316, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "hasContent": false, "stepsCount": 0, "attachmentsCount": 0}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "35b6769796427cef", "name": "接口地址", "source": "35b6769796427cef.json", "type": "application/json", "size": 44}, {"uid": "303429640c0f63d7", "name": "接口名称", "source": "303429640c0f63d7.json", "type": "application/json", "size": 15}, {"uid": "289d293ca3d5ac07", "name": "请求方式", "source": "289d293ca3d5ac07.json", "type": "application/json", "size": 4}, {"uid": "e47400b467756a08", "name": "请求头", "source": "e47400b467756a08.json", "type": "application/json", "size": 122}, {"uid": "c496a4f63ae51341", "name": "<PERSON><PERSON>", "source": "c496a4f63ae51341.json", "type": "application/json", "size": 12}, {"uid": "6f6af05732278c6", "name": "测试用例名称", "source": "6f6af05732278c6.json", "type": "application/json", "size": 24}, {"uid": "65f2d572c06d3f54", "name": "参数类型", "source": "65f2d572c06d3f54.json", "type": "application/json", "size": 4}, {"uid": "c9b7a284132eacb1", "name": "请求参数json格式", "source": "c9b7a284132eacb1.json", "type": "application/json", "size": 179}, {"uid": "bcaef09b3d30074e", "name": "请求参数实际入参", "source": "bcaef09b3d30074e.json", "type": "application/json", "size": 189}, {"uid": "2af11a0743963990", "name": "接口实际响应信息", "source": "2af11a0743963990.json", "type": "application/json", "size": 3479}, {"uid": "73624337106bc874", "name": "状态码断言结果：成功", "source": "73624337106bc874.txt", "type": "text/plain", "size": 37}, {"uid": "2d7a1cecaea8e7ee", "name": "相等断言结果：成功", "source": "2d7a1cecaea8e7ee.json", "type": "application/json", "size": 53}, {"uid": "1dad3d6b442cfee9", "name": "📋 测试执行日志", "source": "1dad3d6b442cfee9.txt", "type": "text/plain", "size": 27049}], "parameters": [], "shouldDisplayMessage": false, "hasContent": true, "stepsCount": 0, "attachmentsCount": 13}, "afterStages": [{"name": "capture_logs_for_allure::0", "time": {"start": 1751853737395, "stop": 1751853737413, "duration": 18}, "status": "passed", "steps": [], "attachments": [{"uid": "35aba44ff8e01289", "name": "📋 测试执行日志 (完整记录)", "source": "35aba44ff8e01289.txt", "type": "text/plain", "size": 4549}], "parameters": [], "shouldDisplayMessage": false, "hasContent": true, "stepsCount": 0, "attachmentsCount": 1}, {"name": "function_wait::0", "time": {"start": 1751853736644, "stop": 1751853737394, "duration": 750}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "hasContent": false, "stepsCount": 0, "attachmentsCount": 0}, {"name": "print_info::0", "time": {"start": 1751853736643, "stop": 1751853736643, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "hasContent": false, "stepsCount": 0, "attachmentsCount": 0}], "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "39072-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [{"name": "api_info", "value": "{'baseInfo': {'module': '演出票', 'api_name': '首页广告位', 'host': '${get_host(play)}', 'url': '/api/show/ad/select_list', 'method': 'post', 'headers': '${get_headers(app)}'}, 'testCase': {'case_name': '获取首页广告列表', 'validation': [{'code': 200}, {'eq': {'code': 0}}], 'extract': 0, 'extract_list': np.float64(0.0), 'data': {'channelCode': '${appKey()}', 'token': '${get_extract_data(token)}'}}}"}], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "3f62a4336830fce.json", "parameterValues": ["{'baseInfo': {'module': '演出票', 'api_name': '首页广告位', 'host': '${get_host(play)}', 'url': '/api/show/ad/select_list', 'method': 'post', 'headers': '${get_headers(app)}'}, 'testCase': {'case_name': '获取首页广告列表', 'validation': [{'code': 200}, {'eq': {'code': 0}}], 'extract': 0, 'extract_list': np.float64(0.0), 'data': {'channelCode': '${appKey()}', 'token': '${get_extract_data(token)}'}}}"]}