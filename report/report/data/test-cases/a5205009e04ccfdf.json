{"uid": "a5205009e04ccfdf", "name": "今日必抢", "fullName": "testcase.play.test_play.TestPlay#test_play", "historyId": "09729bd43bb947d1ebe3c16a80dc6e25", "time": {"start": 1751853888634, "stop": 1751853888938, "duration": 304}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "function_wait", "time": {"start": 1751853887883, "stop": 1751853888634, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "print_info", "time": {"start": 1751853888634, "stop": 1751853888634, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "capture_logs_for_allure", "time": {"start": 1751853887883, "stop": 1751853887883, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "clear_extract", "time": {"start": 1751853884946, "stop": 1751853885997, "duration": 1051}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "_session_faker", "time": {"start": 1751853884907, "stop": 1751853884946, "duration": 39}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "59aef756fa4093c4", "name": "接口地址", "source": "59aef756fa4093c4.json", "type": "application/json", "size": 55}, {"uid": "133b9d07cb7bfade", "name": "接口名称", "source": "133b9d07cb7bfade.json", "type": "application/json", "size": 12}, {"uid": "98c123525d99cb9", "name": "请求方式", "source": "98c123525d99cb9.json", "type": "application/json", "size": 4}, {"uid": "61bc3be21dfb7ede", "name": "请求头", "source": "61bc3be21dfb7ede.json", "type": "application/json", "size": 122}, {"uid": "c40830528a661dd9", "name": "<PERSON><PERSON>", "source": "c40830528a661dd9.json", "type": "application/json", "size": 12}, {"uid": "734be34656e74084", "name": "测试用例名称", "source": "734be34656e74084.json", "type": "application/json", "size": 30}, {"uid": "9d7152981f09e006", "name": "参数类型", "source": "9d7152981f09e006.json", "type": "application/json", "size": 4}, {"uid": "4af5cb1dc6f554c9", "name": "请求参数json格式", "source": "4af5cb1dc6f554c9.json", "type": "application/json", "size": 214}, {"uid": "9a71ac8603720a92", "name": "请求参数实际入参", "source": "9a71ac8603720a92.json", "type": "application/json", "size": 224}, {"uid": "3781b58161d5285a", "name": "接口实际响应信息", "source": "3781b58161d5285a.json", "type": "application/json", "size": 22188}, {"uid": "b2e5ae045a86d7f7", "name": "状态码断言结果：成功", "source": "b2e5ae045a86d7f7.txt", "type": "text/plain", "size": 37}, {"uid": "906996a83a7f7cea", "name": "相等断言结果：成功", "source": "906996a83a7f7cea.json", "type": "application/json", "size": 53}, {"uid": "231a37d08b20f5f1", "name": "📋 测试执行日志", "source": "231a37d08b20f5f1.txt", "type": "text/plain", "size": 4327}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentsCount": 13, "shouldDisplayMessage": false}, "afterStages": [{"name": "function_wait::0", "time": {"start": 1751853888939, "stop": 1751853889690, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "print_info::0", "time": {"start": 1751853888938, "stop": 1751853888939, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "capture_logs_for_allure::0", "time": {"start": 1751853889690, "stop": 1751853889699, "duration": 9}, "status": "passed", "steps": [], "attachments": [{"uid": "dadd7cea1987856d", "name": "📋 测试执行日志 (完整记录)", "source": "dadd7cea1987856d.txt", "type": "text/plain", "size": 15249}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentsCount": 1, "shouldDisplayMessage": false}], "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "39132-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "a5205009e04ccfdf.json", "parameterValues": []}