{"uid": "c2602fe8fb592166", "name": "演唱会分类", "fullName": "testcase.play.test_play.TestPlay#test_play", "historyId": "47b52c96aa0c1d59c25f4bc63959c980", "time": {"start": 1751852466479, "stop": 1751852466828, "duration": 349}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "capture_logs_for_allure", "time": {"start": 1751852465728, "stop": 1751852465728, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "_session_faker", "time": {"start": 1751852455733, "stop": 1751852455863, "duration": 130}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "function_wait", "time": {"start": 1751852465728, "stop": 1751852466479, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "print_info", "time": {"start": 1751852466479, "stop": 1751852466479, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "clear_extract", "time": {"start": 1751852455863, "stop": 1751852456123, "duration": 260}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "3548d50a1345efd", "name": "接口地址", "source": "3548d50a1345efd.json", "type": "application/json", "size": 53}, {"uid": "a4dbf23a0dd44cc4", "name": "接口名称", "source": "a4dbf23a0dd44cc4.json", "type": "application/json", "size": 15}, {"uid": "7782ee4a1af9822", "name": "请求方式", "source": "7782ee4a1af9822.json", "type": "application/json", "size": 4}, {"uid": "6bcf41a152ca2ac0", "name": "请求头", "source": "6bcf41a152ca2ac0.json", "type": "application/json", "size": 122}, {"uid": "baac68ddbaa275b6", "name": "<PERSON><PERSON>", "source": "baac68ddbaa275b6.json", "type": "application/json", "size": 12}, {"uid": "191e4ac65432aca6", "name": "测试用例名称", "source": "191e4ac65432aca6.json", "type": "application/json", "size": 25}, {"uid": "f63bb948e99a3885", "name": "参数类型", "source": "f63bb948e99a3885.json", "type": "application/json", "size": 4}, {"uid": "98285a3aad3a7bdf", "name": "请求参数json格式", "source": "98285a3aad3a7bdf.json", "type": "application/json", "size": 207}, {"uid": "5f2e8acac1a88780", "name": "请求参数实际入参", "source": "5f2e8acac1a88780.json", "type": "application/json", "size": 217}, {"uid": "b4e02fb9c7a77483", "name": "接口实际响应信息", "source": "b4e02fb9c7a77483.json", "type": "application/json", "size": 22018}, {"uid": "3ac729ef06ccc308", "name": "状态码断言结果：成功", "source": "3ac729ef06ccc308.txt", "type": "text/plain", "size": 37}, {"uid": "436980d22303e9db", "name": "相等断言结果：成功", "source": "436980d22303e9db.json", "type": "application/json", "size": 53}, {"uid": "9a7c732850671138", "name": "📋 测试执行日志", "source": "9a7c732850671138.txt", "type": "text/plain", "size": 5008}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 13, "hasContent": true, "stepsCount": 0}, "afterStages": [{"name": "capture_logs_for_allure::0", "time": {"start": 1751852467581, "stop": 1751852467592, "duration": 11}, "status": "passed", "steps": [], "attachments": [{"uid": "845715a0756f2ba8", "name": "📋 测试执行日志 (完整记录)", "source": "845715a0756f2ba8.txt", "type": "text/plain", "size": 14795}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 1, "hasContent": true, "stepsCount": 0}, {"name": "function_wait::0", "time": {"start": 1751852466830, "stop": 1751852467581, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "print_info::0", "time": {"start": 1751852466829, "stop": 1751852466829, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}], "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "38092-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "c2602fe8fb592166.json", "parameterValues": []}