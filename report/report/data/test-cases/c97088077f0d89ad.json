{"uid": "c97088077f0d89ad", "name": "演出场次列表", "fullName": "testcase.play.test_play.TestPlay#test_play", "historyId": "ce6724bce5907ddc1a7c9b8a022c9530", "time": {"start": 1751853901628, "stop": 1751853902213, "duration": 585}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "print_info", "time": {"start": 1751853901628, "stop": 1751853901628, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "function_wait", "time": {"start": 1751853900877, "stop": 1751853901628, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "capture_logs_for_allure", "time": {"start": 1751853900877, "stop": 1751853900877, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "clear_extract", "time": {"start": 1751853884946, "stop": 1751853885997, "duration": 1051}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "_session_faker", "time": {"start": 1751853884907, "stop": 1751853884946, "duration": 39}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "eecc253778f633c4", "name": "接口地址", "source": "eecc253778f633c4.json", "type": "application/json", "size": 52}, {"uid": "918a82d548810dea", "name": "接口名称", "source": "918a82d548810dea.json", "type": "application/json", "size": 18}, {"uid": "9aba84067f949502", "name": "请求方式", "source": "9aba84067f949502.json", "type": "application/json", "size": 3}, {"uid": "6630b657c7a0a77c", "name": "请求头", "source": "6630b657c7a0a77c.json", "type": "application/json", "size": 122}, {"uid": "3e1ab1dfa03eea92", "name": "<PERSON><PERSON>", "source": "3e1ab1dfa03eea92.json", "type": "application/json", "size": 12}, {"uid": "fe3208c94ef12dc0", "name": "测试用例名称", "source": "fe3208c94ef12dc0.json", "type": "application/json", "size": 30}, {"uid": "f34aa783fd060ed5", "name": "参数类型", "source": "f34aa783fd060ed5.json", "type": "application/json", "size": 6}, {"uid": "dfdf67c70182c63c", "name": "请求参数json格式", "source": "dfdf67c70182c63c.json", "type": "application/json", "size": 206}, {"uid": "be6877f772814700", "name": "请求参数实际入参", "source": "be6877f772814700.json", "type": "application/json", "size": 216}, {"uid": "6be41b6a901cc861", "name": "接口实际响应信息", "source": "6be41b6a901cc861.json", "type": "application/json", "size": 1528}, {"uid": "518c100df66489f1", "name": "状态码断言结果：成功", "source": "518c100df66489f1.txt", "type": "text/plain", "size": 37}, {"uid": "d5d9f677cef597ac", "name": "相等断言结果：成功", "source": "d5d9f677cef597ac.json", "type": "application/json", "size": 53}, {"uid": "e22bff4f07db3a00", "name": "📋 测试执行日志", "source": "e22bff4f07db3a00.txt", "type": "text/plain", "size": 6363}], "parameters": [], "hasContent": true, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 13}, "afterStages": [{"name": "print_info::0", "time": {"start": 1751853902214, "stop": 1751853902214, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "function_wait::0", "time": {"start": 1751853902215, "stop": 1751853902965, "duration": 750}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "capture_logs_for_allure::0", "time": {"start": 1751853902966, "stop": 1751853902973, "duration": 7}, "status": "passed", "steps": [], "attachments": [{"uid": "fd2e578c4b51f4ba", "name": "📋 测试执行日志 (完整记录)", "source": "fd2e578c4b51f4ba.txt", "type": "text/plain", "size": 3123}], "parameters": [], "hasContent": true, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 1}], "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "39132-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "c97088077f0d89ad.json", "parameterValues": []}