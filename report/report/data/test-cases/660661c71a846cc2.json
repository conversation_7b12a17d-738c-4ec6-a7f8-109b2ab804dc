{"uid": "660661c71a846cc2", "name": "演出场次列表", "fullName": "testcase.play.test_play.TestPlay#test_play", "historyId": "ce6724bce5907ddc1a7c9b8a022c9530", "time": {"start": 1751853901628, "stop": 1751853902213, "duration": 585}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "print_info", "time": {"start": 1751853901628, "stop": 1751853901628, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "function_wait", "time": {"start": 1751853900877, "stop": 1751853901628, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "capture_logs_for_allure", "time": {"start": 1751853900877, "stop": 1751853900877, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "clear_extract", "time": {"start": 1751853884946, "stop": 1751853885997, "duration": 1051}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "_session_faker", "time": {"start": 1751853884907, "stop": 1751853884946, "duration": 39}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "a67ed2f7bab3ed4", "name": "接口地址", "source": "a67ed2f7bab3ed4.json", "type": "application/json", "size": 52}, {"uid": "39c4f998b7d06c7e", "name": "接口名称", "source": "39c4f998b7d06c7e.json", "type": "application/json", "size": 18}, {"uid": "57d1b56b91c120ac", "name": "请求方式", "source": "57d1b56b91c120ac.json", "type": "application/json", "size": 3}, {"uid": "6119e67e2f299ba5", "name": "请求头", "source": "6119e67e2f299ba5.json", "type": "application/json", "size": 122}, {"uid": "9d7e16933755fbfe", "name": "<PERSON><PERSON>", "source": "9d7e16933755fbfe.json", "type": "application/json", "size": 12}, {"uid": "12c9a43cdc007eef", "name": "测试用例名称", "source": "12c9a43cdc007eef.json", "type": "application/json", "size": 30}, {"uid": "fcb7dc8821794125", "name": "参数类型", "source": "fcb7dc8821794125.json", "type": "application/json", "size": 6}, {"uid": "b6aad560916aa628", "name": "请求参数json格式", "source": "b6aad560916aa628.json", "type": "application/json", "size": 206}, {"uid": "9e2672277ca3b578", "name": "请求参数实际入参", "source": "9e2672277ca3b578.json", "type": "application/json", "size": 216}, {"uid": "4cb823d750646cab", "name": "接口实际响应信息", "source": "4cb823d750646cab.json", "type": "application/json", "size": 1528}, {"uid": "8c90a2cbe6277728", "name": "状态码断言结果：成功", "source": "8c90a2cbe6277728.txt", "type": "text/plain", "size": 37}, {"uid": "67adb6dd32a1951e", "name": "相等断言结果：成功", "source": "67adb6dd32a1951e.json", "type": "application/json", "size": 53}, {"uid": "f163010e5aedd12b", "name": "📋 测试执行日志", "source": "f163010e5aedd12b.txt", "type": "text/plain", "size": 6363}], "parameters": [], "hasContent": true, "stepsCount": 0, "attachmentsCount": 13, "shouldDisplayMessage": false}, "afterStages": [{"name": "print_info::0", "time": {"start": 1751853902214, "stop": 1751853902214, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "function_wait::0", "time": {"start": 1751853902215, "stop": 1751853902965, "duration": 750}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "capture_logs_for_allure::0", "time": {"start": 1751853902966, "stop": 1751853902973, "duration": 7}, "status": "passed", "steps": [], "attachments": [{"uid": "3cf297124154442a", "name": "📋 测试执行日志 (完整记录)", "source": "3cf297124154442a.txt", "type": "text/plain", "size": 3123}], "parameters": [], "hasContent": true, "stepsCount": 0, "attachmentsCount": 1, "shouldDisplayMessage": false}], "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "39132-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "660661c71a846cc2.json", "parameterValues": []}