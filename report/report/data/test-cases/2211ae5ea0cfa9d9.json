{"uid": "2211ae5ea0cfa9d9", "name": "取消订单", "fullName": "testcase.play.test_play.TestPlay#test_play", "historyId": "86ddc9742985e7678bcca10364db8520", "time": {"start": 1751853016051, "stop": 1751853018081, "duration": 2030}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "capture_logs_for_allure", "time": {"start": 1751853015299, "stop": 1751853015299, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "clear_extract", "time": {"start": 1751852990929, "stop": 1751852991206, "duration": 277}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "_session_faker", "time": {"start": 1751852990879, "stop": 1751852990927, "duration": 48}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "print_info", "time": {"start": 1751853016050, "stop": 1751853016050, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "function_wait", "time": {"start": 1751853015299, "stop": 1751853016050, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "b67c503c42b9bc6f", "name": "接口地址", "source": "b67c503c42b9bc6f.json", "type": "application/json", "size": 42}, {"uid": "84083bf9e4f91d08", "name": "接口名称", "source": "84083bf9e4f91d08.json", "type": "application/json", "size": 12}, {"uid": "4da3cd79b7161b7c", "name": "请求方式", "source": "4da3cd79b7161b7c.json", "type": "application/json", "size": 4}, {"uid": "a555c66fcfdd882d", "name": "请求头", "source": "a555c66fcfdd882d.json", "type": "application/json", "size": 122}, {"uid": "83869b339c816fad", "name": "<PERSON><PERSON>", "source": "83869b339c816fad.json", "type": "application/json", "size": 12}, {"uid": "b8871707fc8d845c", "name": "测试用例名称", "source": "b8871707fc8d845c.json", "type": "application/json", "size": 12}, {"uid": "45df1dff844da2a1", "name": "参数类型", "source": "45df1dff844da2a1.json", "type": "application/json", "size": 4}, {"uid": "f5e4e0779d792c16", "name": "请求参数json格式", "source": "f5e4e0779d792c16.json", "type": "application/json", "size": 217}, {"uid": "e7a9cd5ad8307782", "name": "请求参数实际入参", "source": "e7a9cd5ad8307782.json", "type": "application/json", "size": 227}, {"uid": "78c44dbb5103a34b", "name": "接口实际响应信息", "source": "78c44dbb5103a34b.json", "type": "application/json", "size": 48}, {"uid": "d4c0f181af7d9951", "name": "状态码断言结果：成功", "source": "d4c0f181af7d9951.txt", "type": "text/plain", "size": 37}, {"uid": "c3401f1a8af5903f", "name": "相等断言结果：成功", "source": "c3401f1a8af5903f.json", "type": "application/json", "size": 53}, {"uid": "9e102dc037de2282", "name": "📋 测试执行日志", "source": "9e102dc037de2282.txt", "type": "text/plain", "size": 2439}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 13, "hasContent": true, "stepsCount": 0}, "afterStages": [{"name": "capture_logs_for_allure::0", "time": {"start": 1751853018833, "stop": 1751853018848, "duration": 15}, "status": "passed", "steps": [], "attachments": [{"uid": "4f4af8b027a7a352", "name": "📋 测试执行日志 (完整记录)", "source": "4f4af8b027a7a352.txt", "type": "text/plain", "size": 2076}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 1, "hasContent": true, "stepsCount": 0}, {"name": "print_info::0", "time": {"start": 1751853018082, "stop": 1751853018082, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "function_wait::0", "time": {"start": 1751853018082, "stop": 1751853018833, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}], "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "37476-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [{"name": "api_info", "value": "{'baseInfo': {'module': '演出票', 'api_name': '取消订单', 'host': '${get_host(play)}', 'url': '/api/show/order/cancel', 'method': 'post', 'headers': '${get_headers(app)}'}, 'testCase': {'case_name': '取消订单', 'validation': [{'code': 200}, {'eq': {'code': 0}}], 'extract': 0, 'extract_list': np.float64(0.0), 'data': {'orderId': '${get_extract_data(orderId)}', 'channelCode': '${appKey()}', 'token': '${get_extract_data(token)}'}}}"}], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "2211ae5ea0cfa9d9.json", "parameterValues": ["{'baseInfo': {'module': '演出票', 'api_name': '取消订单', 'host': '${get_host(play)}', 'url': '/api/show/order/cancel', 'method': 'post', 'headers': '${get_headers(app)}'}, 'testCase': {'case_name': '取消订单', 'validation': [{'code': 200}, {'eq': {'code': 0}}], 'extract': 0, 'extract_list': np.float64(0.0), 'data': {'orderId': '${get_extract_data(orderId)}', 'channelCode': '${appKey()}', 'token': '${get_extract_data(token)}'}}}"]}