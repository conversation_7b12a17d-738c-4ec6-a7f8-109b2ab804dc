{"uid": "db738251e34276b", "name": "取消订单", "fullName": "testcase.play.test_play.TestPlay#test_play", "historyId": "097386a9d43be40d3c28c1874a6e169b", "time": {"start": 1751853910547, "stop": 1751853912006, "duration": 1459}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "print_info", "time": {"start": 1751853910547, "stop": 1751853910547, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "clear_extract", "time": {"start": 1751853884946, "stop": 1751853885997, "duration": 1051}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "_session_faker", "time": {"start": 1751853884907, "stop": 1751853884946, "duration": 39}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "capture_logs_for_allure", "time": {"start": 1751853909796, "stop": 1751853909796, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "function_wait", "time": {"start": 1751853909796, "stop": 1751853910547, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "e22e2b41b8c9230c", "name": "接口地址", "source": "e22e2b41b8c9230c.json", "type": "application/json", "size": 42}, {"uid": "d729917a51398d69", "name": "接口名称", "source": "d729917a51398d69.json", "type": "application/json", "size": 12}, {"uid": "d4aa1635b1ef3db4", "name": "请求方式", "source": "d4aa1635b1ef3db4.json", "type": "application/json", "size": 4}, {"uid": "6534ba31c2fae4ba", "name": "请求头", "source": "6534ba31c2fae4ba.json", "type": "application/json", "size": 122}, {"uid": "ab4b03612e71b9da", "name": "<PERSON><PERSON>", "source": "ab4b03612e71b9da.json", "type": "application/json", "size": 12}, {"uid": "b1f6037d90be46c6", "name": "测试用例名称", "source": "b1f6037d90be46c6.json", "type": "application/json", "size": 12}, {"uid": "c281b971cc7f218e", "name": "参数类型", "source": "c281b971cc7f218e.json", "type": "application/json", "size": 4}, {"uid": "52e92122ce024ddb", "name": "请求参数json格式", "source": "52e92122ce024ddb.json", "type": "application/json", "size": 217}, {"uid": "4748ebbc3247229e", "name": "请求参数实际入参", "source": "4748ebbc3247229e.json", "type": "application/json", "size": 227}, {"uid": "d375f183c0dc44a7", "name": "接口实际响应信息", "source": "d375f183c0dc44a7.json", "type": "application/json", "size": 48}, {"uid": "f22a0460ce0ea2d2", "name": "状态码断言结果：成功", "source": "f22a0460ce0ea2d2.txt", "type": "text/plain", "size": 37}, {"uid": "fe585eefb6459fa7", "name": "相等断言结果：成功", "source": "fe585eefb6459fa7.json", "type": "application/json", "size": 53}, {"uid": "4d5ca72cece517a8", "name": "📋 测试执行日志", "source": "4d5ca72cece517a8.txt", "type": "text/plain", "size": 2968}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentsCount": 13, "shouldDisplayMessage": false}, "afterStages": [{"name": "print_info::0", "time": {"start": 1751853912007, "stop": 1751853912007, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "capture_logs_for_allure::0", "time": {"start": 1751853912759, "stop": 1751853912769, "duration": 10}, "status": "passed", "steps": [], "attachments": [{"uid": "a73f9a019d9f9865", "name": "📋 测试执行日志 (完整记录)", "source": "a73f9a019d9f9865.txt", "type": "text/plain", "size": 2307}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentsCount": 1, "shouldDisplayMessage": false}, {"name": "function_wait::0", "time": {"start": 1751853912008, "stop": 1751853912759, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}], "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "39132-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "db738251e34276b.json", "parameterValues": []}