{"uid": "3f86c03840c1c535", "name": "为你推荐", "fullName": "testcase.play.test_play.TestPlay#test_play", "historyId": "93b5e0aac89859d5ee60ab661699f09c", "time": {"start": 1751852997502, "stop": 1751852997812, "duration": 310}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "function_wait", "time": {"start": 1751852996751, "stop": 1751852997502, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "clear_extract", "time": {"start": 1751852990929, "stop": 1751852991206, "duration": 277}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "print_info", "time": {"start": 1751852997502, "stop": 1751852997502, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "_session_faker", "time": {"start": 1751852990879, "stop": 1751852990927, "duration": 48}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "capture_logs_for_allure", "time": {"start": 1751852996751, "stop": 1751852996751, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "79fe29408ea69e69", "name": "接口地址", "source": "79fe29408ea69e69.json", "type": "application/json", "size": 54}, {"uid": "c43519ef9251dbc6", "name": "接口名称", "source": "c43519ef9251dbc6.json", "type": "application/json", "size": 12}, {"uid": "978df30b02754a32", "name": "请求方式", "source": "978df30b02754a32.json", "type": "application/json", "size": 4}, {"uid": "8fe14d5f93566299", "name": "请求头", "source": "8fe14d5f93566299.json", "type": "application/json", "size": 122}, {"uid": "6d6a9c369f06594b", "name": "<PERSON><PERSON>", "source": "6d6a9c369f06594b.json", "type": "application/json", "size": 12}, {"uid": "c3d5af3dd27ede46", "name": "测试用例名称", "source": "c3d5af3dd27ede46.json", "type": "application/json", "size": 30}, {"uid": "55e853460755ec77", "name": "参数类型", "source": "55e853460755ec77.json", "type": "application/json", "size": 4}, {"uid": "d3645cd067cdbf3f", "name": "请求参数json格式", "source": "d3645cd067cdbf3f.json", "type": "application/json", "size": 214}, {"uid": "dd9b013576d0a090", "name": "请求参数实际入参", "source": "dd9b013576d0a090.json", "type": "application/json", "size": 224}, {"uid": "6a51f14ce9c73199", "name": "接口实际响应信息", "source": "6a51f14ce9c73199.json", "type": "application/json", "size": 44012}, {"uid": "3fa82ba8910a06c9", "name": "状态码断言结果：成功", "source": "3fa82ba8910a06c9.txt", "type": "text/plain", "size": 37}, {"uid": "567da922e156fde6", "name": "相等断言结果：成功", "source": "567da922e156fde6.json", "type": "application/json", "size": 53}, {"uid": "b1a53dcd1367e4ac", "name": "📋 测试执行日志", "source": "b1a53dcd1367e4ac.txt", "type": "text/plain", "size": 14715}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 13, "hasContent": true, "stepsCount": 0}, "afterStages": [{"name": "function_wait::0", "time": {"start": 1751852997814, "stop": 1751852998565, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "print_info::0", "time": {"start": 1751852997813, "stop": 1751852997813, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "capture_logs_for_allure::0", "time": {"start": 1751852998565, "stop": 1751852998577, "duration": 12}, "status": "passed", "steps": [], "attachments": [{"uid": "a2e1495a74c571e2", "name": "📋 测试执行日志 (完整记录)", "source": "a2e1495a74c571e2.txt", "type": "text/plain", "size": 27049}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 1, "hasContent": true, "stepsCount": 0}], "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "37476-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [{"name": "api_info", "value": "{'baseInfo': {'module': '演出票', 'api_name': '为你推荐', 'host': '${get_host(play)}', 'url': '/api/show/info/home_recommend_list', 'method': 'post', 'headers': '${get_headers(app)}'}, 'testCase': {'case_name': '获取为你推荐演出列表', 'validation': [{'code': 200}, {'eq': {'code': 0}}], 'extract': 0, 'extract_list': np.float64(0.0), 'data': {'page': 1, 'pageSize': 20, 'channelCode': '${appKey()}', 'token': '${get_extract_data(token)}'}}}"}], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "3f86c03840c1c535.json", "parameterValues": ["{'baseInfo': {'module': '演出票', 'api_name': '为你推荐', 'host': '${get_host(play)}', 'url': '/api/show/info/home_recommend_list', 'method': 'post', 'headers': '${get_headers(app)}'}, 'testCase': {'case_name': '获取为你推荐演出列表', 'validation': [{'code': 200}, {'eq': {'code': 0}}], 'extract': 0, 'extract_list': np.float64(0.0), 'data': {'page': 1, 'pageSize': 20, 'channelCode': '${appKey()}', 'token': '${get_extract_data(token)}'}}}"]}