{"uid": "97761f466f042b38", "name": "演唱会分类", "fullName": "testcase.play.test_play.TestPlay#test_play", "historyId": "47b52c96aa0c1d59c25f4bc63959c980", "time": {"start": 1751853895908, "stop": 1751853896233, "duration": 325}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "print_info", "time": {"start": 1751853895908, "stop": 1751853895908, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "function_wait", "time": {"start": 1751853895158, "stop": 1751853895908, "duration": 750}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "capture_logs_for_allure", "time": {"start": 1751853895157, "stop": 1751853895158, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "clear_extract", "time": {"start": 1751853884946, "stop": 1751853885997, "duration": 1051}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "_session_faker", "time": {"start": 1751853884907, "stop": 1751853884946, "duration": 39}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "b830f634014aba44", "name": "接口地址", "source": "b830f634014aba44.json", "type": "application/json", "size": 53}, {"uid": "24297d21512902da", "name": "接口名称", "source": "24297d21512902da.json", "type": "application/json", "size": 15}, {"uid": "b0b52b1603e0a5ed", "name": "请求方式", "source": "b0b52b1603e0a5ed.json", "type": "application/json", "size": 4}, {"uid": "39f1fb94922bfac8", "name": "请求头", "source": "39f1fb94922bfac8.json", "type": "application/json", "size": 122}, {"uid": "270f6e588c712193", "name": "<PERSON><PERSON>", "source": "270f6e588c712193.json", "type": "application/json", "size": 12}, {"uid": "d3f33a702ad7e1a8", "name": "测试用例名称", "source": "d3f33a702ad7e1a8.json", "type": "application/json", "size": 25}, {"uid": "dab7877bdf27a2c2", "name": "参数类型", "source": "dab7877bdf27a2c2.json", "type": "application/json", "size": 4}, {"uid": "5edaac5dbce8837a", "name": "请求参数json格式", "source": "5edaac5dbce8837a.json", "type": "application/json", "size": 207}, {"uid": "8352282763dd3758", "name": "请求参数实际入参", "source": "8352282763dd3758.json", "type": "application/json", "size": 217}, {"uid": "34b004c8a799e680", "name": "接口实际响应信息", "source": "34b004c8a799e680.json", "type": "application/json", "size": 22018}, {"uid": "496f5a5bf40cf9f7", "name": "状态码断言结果：成功", "source": "496f5a5bf40cf9f7.txt", "type": "text/plain", "size": 37}, {"uid": "52a2f063093442b6", "name": "相等断言结果：成功", "source": "52a2f063093442b6.json", "type": "application/json", "size": 53}, {"uid": "96b71c7919d5928e", "name": "📋 测试执行日志", "source": "96b71c7919d5928e.txt", "type": "text/plain", "size": 4779}], "parameters": [], "hasContent": true, "stepsCount": 0, "attachmentsCount": 13, "shouldDisplayMessage": false}, "afterStages": [{"name": "print_info::0", "time": {"start": 1751853896234, "stop": 1751853896234, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "function_wait::0", "time": {"start": 1751853896235, "stop": 1751853896985, "duration": 750}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "capture_logs_for_allure::0", "time": {"start": 1751853896985, "stop": 1751853896995, "duration": 10}, "status": "passed", "steps": [], "attachments": [{"uid": "2b9f6d595a2943e5", "name": "📋 测试执行日志 (完整记录)", "source": "2b9f6d595a2943e5.txt", "type": "text/plain", "size": 14921}], "parameters": [], "hasContent": true, "stepsCount": 0, "attachmentsCount": 1, "shouldDisplayMessage": false}], "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "39132-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "97761f466f042b38.json", "parameterValues": []}