{"uid": "31e808295ecfade1", "name": "下单", "fullName": "testcase.play.test_play.TestPlay#test_play", "historyId": "340c0bd5adb900e5cd7660f86f67f4fa", "time": {"start": 1751853013098, "stop": 1751853014532, "duration": 1434}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "function_wait", "time": {"start": 1751853012347, "stop": 1751853013097, "duration": 750}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "clear_extract", "time": {"start": 1751852990929, "stop": 1751852991206, "duration": 277}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "_session_faker", "time": {"start": 1751852990879, "stop": 1751852990927, "duration": 48}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "capture_logs_for_allure", "time": {"start": 1751853012347, "stop": 1751853012347, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "print_info", "time": {"start": 1751853013097, "stop": 1751853013098, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "967331eecbbe6faa", "name": "接口地址", "source": "967331eecbbe6faa.json", "type": "application/json", "size": 42}, {"uid": "5b61af65b45ee770", "name": "接口名称", "source": "5b61af65b45ee770.json", "type": "application/json", "size": 6}, {"uid": "6989ee69723615c8", "name": "请求方式", "source": "6989ee69723615c8.json", "type": "application/json", "size": 4}, {"uid": "59fedd483f262d0b", "name": "请求头", "source": "59fedd483f262d0b.json", "type": "application/json", "size": 122}, {"uid": "f31cf63361104c68", "name": "<PERSON><PERSON>", "source": "f31cf63361104c68.json", "type": "application/json", "size": 12}, {"uid": "6e39656e2b6754dc", "name": "测试用例名称", "source": "6e39656e2b6754dc.json", "type": "application/json", "size": 12}, {"uid": "badafe95c0f5fb03", "name": "参数类型", "source": "badafe95c0f5fb03.json", "type": "application/json", "size": 4}, {"uid": "ace552b15a3e95a", "name": "请求参数json格式", "source": "ace552b15a3e95a.json", "type": "application/json", "size": 615}, {"uid": "a9a634d0031691e2", "name": "请求参数实际入参", "source": "a9a634d0031691e2.json", "type": "application/json", "size": 625}, {"uid": "aabf339b9a20c8f0", "name": "接口实际响应信息", "source": "aabf339b9a20c8f0.json", "type": "application/json", "size": 94}, {"uid": "b3fddb3aeec145f4", "name": "状态码断言结果：成功", "source": "b3fddb3aeec145f4.txt", "type": "text/plain", "size": 37}, {"uid": "b8bf2c56ecb5f7db", "name": "相等断言结果：成功", "source": "b8bf2c56ecb5f7db.json", "type": "application/json", "size": 53}, {"uid": "dbb8a2f412b95a88", "name": "📋 测试执行日志", "source": "dbb8a2f412b95a88.txt", "type": "text/plain", "size": 2377}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 13, "hasContent": true, "stepsCount": 0}, "afterStages": [{"name": "function_wait::0", "time": {"start": 1751853014533, "stop": 1751853015284, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "capture_logs_for_allure::0", "time": {"start": 1751853015284, "stop": 1751853015297, "duration": 13}, "status": "passed", "steps": [], "attachments": [{"uid": "4c2312cea403992d", "name": "📋 测试执行日志 (完整记录)", "source": "4c2312cea403992d.txt", "type": "text/plain", "size": 2439}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 1, "hasContent": true, "stepsCount": 0}, {"name": "print_info::0", "time": {"start": 1751853014533, "stop": 1751853014533, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}], "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "37476-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [{"name": "api_info", "value": "{'baseInfo': {'module': '演出票', 'api_name': '下单', 'host': '${get_host(play)}', 'url': '/api/show/order/create', 'method': 'post', 'headers': '${get_headers(app)}'}, 'testCase': {'case_name': '创建订单', 'validation': [{'code': 200}, {'eq': {'code': 0}}], 'extract': {'orderId': '$..orderId'}, 'extract_list': np.float64(0.0), 'data': {'recipientMobile': '${get_extract_data(login_info,mobile)}', 'recipientName': '购票人', 'recipientIdNo': '', 'fetchTicketWayId': '${get_extract_data(fetchTicketWayId)}', 'recipientAddressId': '', 'fetchTicketWayType': '${get_extract_data(fetchType)}', 'performanceId': '${get_extract_data(performance_id)}', 'showId': '${get_extract_data(showInfo,showId)}', 'salesPlanId': '${get_extract_data(ticketInfo,ticketUnitId)}', 'salesPlanCount': '1', 'totalTicketPrice': '${get_extract_data(ticketInfo,price)}', 'deliveryPrice': '0', 'orderPrice': '${get_extract_data(ticketInfo,price)}', 'realNameIds': '', 'seatRequest': '', 'channelCode': '${appKey()}', 'merchant_code': '', 'token': '${get_extract_data(token)}'}}}"}], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "31e808295ecfade1.json", "parameterValues": ["{'baseInfo': {'module': '演出票', 'api_name': '下单', 'host': '${get_host(play)}', 'url': '/api/show/order/create', 'method': 'post', 'headers': '${get_headers(app)}'}, 'testCase': {'case_name': '创建订单', 'validation': [{'code': 200}, {'eq': {'code': 0}}], 'extract': {'orderId': '$..orderId'}, 'extract_list': np.float64(0.0), 'data': {'recipientMobile': '${get_extract_data(login_info,mobile)}', 'recipientName': '购票人', 'recipientIdNo': '', 'fetchTicketWayId': '${get_extract_data(fetchTicketWayId)}', 'recipientAddressId': '', 'fetchTicketWayType': '${get_extract_data(fetchType)}', 'performanceId': '${get_extract_data(performance_id)}', 'showId': '${get_extract_data(showInfo,showId)}', 'salesPlanId': '${get_extract_data(ticketInfo,ticketUnitId)}', 'salesPlanCount': '1', 'totalTicketPrice': '${get_extract_data(ticketInfo,price)}', 'deliveryPrice': '0', 'orderPrice': '${get_extract_data(ticketInfo,price)}', 'realNameIds': '', 'seatRequest': '', 'channelCode': '${appKey()}', 'merchant_code': '', 'token': '${get_extract_data(token)}'}}}"]}