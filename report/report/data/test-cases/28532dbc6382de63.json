{"uid": "28532dbc6382de63", "name": "取消订单", "fullName": "testcase.play.test_play.TestPlay#test_play", "historyId": "097386a9d43be40d3c28c1874a6e169b", "time": {"start": 1751852481196, "stop": 1751852482456, "duration": 1260}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "print_info", "time": {"start": 1751852481196, "stop": 1751852481196, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "_session_faker", "time": {"start": 1751852455733, "stop": 1751852455863, "duration": 130}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "capture_logs_for_allure", "time": {"start": 1751852480445, "stop": 1751852480445, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "clear_extract", "time": {"start": 1751852455863, "stop": 1751852456123, "duration": 260}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "function_wait", "time": {"start": 1751852480445, "stop": 1751852481196, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "d5a98723ef039413", "name": "接口地址", "source": "d5a98723ef039413.json", "type": "application/json", "size": 42}, {"uid": "6d7acfe69876014d", "name": "接口名称", "source": "6d7acfe69876014d.json", "type": "application/json", "size": 12}, {"uid": "a4d8e7fa20efd8f5", "name": "请求方式", "source": "a4d8e7fa20efd8f5.json", "type": "application/json", "size": 4}, {"uid": "107d97c386c63e0e", "name": "请求头", "source": "107d97c386c63e0e.json", "type": "application/json", "size": 122}, {"uid": "d1a049ae78524bbd", "name": "<PERSON><PERSON>", "source": "d1a049ae78524bbd.json", "type": "application/json", "size": 12}, {"uid": "33fbfe63bfb57735", "name": "测试用例名称", "source": "33fbfe63bfb57735.json", "type": "application/json", "size": 12}, {"uid": "7f7b40acdd31ae0b", "name": "参数类型", "source": "7f7b40acdd31ae0b.json", "type": "application/json", "size": 4}, {"uid": "4dddff549928a176", "name": "请求参数json格式", "source": "4dddff549928a176.json", "type": "application/json", "size": 217}, {"uid": "b1247173ef5cb515", "name": "请求参数实际入参", "source": "b1247173ef5cb515.json", "type": "application/json", "size": 227}, {"uid": "55c27c8603303002", "name": "接口实际响应信息", "source": "55c27c8603303002.json", "type": "application/json", "size": 48}, {"uid": "c2c0a8a3e84e85a", "name": "状态码断言结果：成功", "source": "c2c0a8a3e84e85a.txt", "type": "text/plain", "size": 37}, {"uid": "c5f11a4f605e9f0", "name": "相等断言结果：成功", "source": "c5f11a4f605e9f0.json", "type": "application/json", "size": 53}, {"uid": "fc1512782c532318", "name": "📋 测试执行日志", "source": "fc1512782c532318.txt", "type": "text/plain", "size": 2682}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 13, "hasContent": true, "stepsCount": 0}, "afterStages": [{"name": "print_info::0", "time": {"start": 1751852482457, "stop": 1751852482457, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "capture_logs_for_allure::0", "time": {"start": 1751852483209, "stop": 1751852483224, "duration": 15}, "status": "passed", "steps": [], "attachments": [{"uid": "3b7405d0a3c07077", "name": "📋 测试执行日志 (完整记录)", "source": "3b7405d0a3c07077.txt", "type": "text/plain", "size": 2659}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 1, "hasContent": true, "stepsCount": 0}, {"name": "function_wait::0", "time": {"start": 1751852482457, "stop": 1751852483208, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}], "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "38092-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "28532dbc6382de63.json", "parameterValues": []}