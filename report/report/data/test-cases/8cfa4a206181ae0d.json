{"uid": "8cfa4a206181ae0d", "name": "为你推荐", "fullName": "testcase.play.test_play.TestPlay#test_play", "historyId": "93b5e0aac89859d5ee60ab661699f09c", "time": {"start": 1751853734481, "stop": 1751853734798, "duration": 317}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "print_info", "time": {"start": 1751853734481, "stop": 1751853734481, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "hasContent": false, "stepsCount": 0, "attachmentsCount": 0}, {"name": "_session_faker", "time": {"start": 1751853726731, "stop": 1751853726779, "duration": 48}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "hasContent": false, "stepsCount": 0, "attachmentsCount": 0}, {"name": "function_wait", "time": {"start": 1751853733731, "stop": 1751853734481, "duration": 750}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "hasContent": false, "stepsCount": 0, "attachmentsCount": 0}, {"name": "clear_extract", "time": {"start": 1751853726779, "stop": 1751853727168, "duration": 389}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "hasContent": false, "stepsCount": 0, "attachmentsCount": 0}, {"name": "capture_logs_for_allure", "time": {"start": 1751853733731, "stop": 1751853733731, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "hasContent": false, "stepsCount": 0, "attachmentsCount": 0}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "d5b8c3aaa1082c1c", "name": "接口地址", "source": "d5b8c3aaa1082c1c.json", "type": "application/json", "size": 54}, {"uid": "b0e241863aa99ae0", "name": "接口名称", "source": "b0e241863aa99ae0.json", "type": "application/json", "size": 12}, {"uid": "6e620677a0b7ad03", "name": "请求方式", "source": "6e620677a0b7ad03.json", "type": "application/json", "size": 4}, {"uid": "8e0634778584c105", "name": "请求头", "source": "8e0634778584c105.json", "type": "application/json", "size": 122}, {"uid": "f196acb53137e28a", "name": "<PERSON><PERSON>", "source": "f196acb53137e28a.json", "type": "application/json", "size": 12}, {"uid": "e1fb8730d3689c07", "name": "测试用例名称", "source": "e1fb8730d3689c07.json", "type": "application/json", "size": 30}, {"uid": "ce4fc47cd4973de4", "name": "参数类型", "source": "ce4fc47cd4973de4.json", "type": "application/json", "size": 4}, {"uid": "b3ec5af7cbad59b7", "name": "请求参数json格式", "source": "b3ec5af7cbad59b7.json", "type": "application/json", "size": 214}, {"uid": "8b00b1f0e7d99bdb", "name": "请求参数实际入参", "source": "8b00b1f0e7d99bdb.json", "type": "application/json", "size": 224}, {"uid": "d66770168e37bfaf", "name": "接口实际响应信息", "source": "d66770168e37bfaf.json", "type": "application/json", "size": 44012}, {"uid": "87a9dcd7abbf2290", "name": "状态码断言结果：成功", "source": "87a9dcd7abbf2290.txt", "type": "text/plain", "size": 37}, {"uid": "b491718231e308f8", "name": "相等断言结果：成功", "source": "b491718231e308f8.json", "type": "application/json", "size": 53}, {"uid": "9a72ee87d9ae1b49", "name": "📋 测试执行日志", "source": "9a72ee87d9ae1b49.txt", "type": "text/plain", "size": 14715}], "parameters": [], "shouldDisplayMessage": false, "hasContent": true, "stepsCount": 0, "attachmentsCount": 13}, "afterStages": [{"name": "print_info::0", "time": {"start": 1751853734799, "stop": 1751853734799, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "hasContent": false, "stepsCount": 0, "attachmentsCount": 0}, {"name": "function_wait::0", "time": {"start": 1751853734800, "stop": 1751853735551, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "hasContent": false, "stepsCount": 0, "attachmentsCount": 0}, {"name": "capture_logs_for_allure::0", "time": {"start": 1751853735552, "stop": 1751853735563, "duration": 11}, "status": "passed", "steps": [], "attachments": [{"uid": "9e1144ee2fbb549b", "name": "📋 测试执行日志 (完整记录)", "source": "9e1144ee2fbb549b.txt", "type": "text/plain", "size": 27049}], "parameters": [], "shouldDisplayMessage": false, "hasContent": true, "stepsCount": 0, "attachmentsCount": 1}], "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "39072-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [{"name": "api_info", "value": "{'baseInfo': {'module': '演出票', 'api_name': '为你推荐', 'host': '${get_host(play)}', 'url': '/api/show/info/home_recommend_list', 'method': 'post', 'headers': '${get_headers(app)}'}, 'testCase': {'case_name': '获取为你推荐演出列表', 'validation': [{'code': 200}, {'eq': {'code': 0}}], 'extract': 0, 'extract_list': np.float64(0.0), 'data': {'page': 1, 'pageSize': 20, 'channelCode': '${appKey()}', 'token': '${get_extract_data(token)}'}}}"}], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "8cfa4a206181ae0d.json", "parameterValues": ["{'baseInfo': {'module': '演出票', 'api_name': '为你推荐', 'host': '${get_host(play)}', 'url': '/api/show/info/home_recommend_list', 'method': 'post', 'headers': '${get_headers(app)}'}, 'testCase': {'case_name': '获取为你推荐演出列表', 'validation': [{'code': 200}, {'eq': {'code': 0}}], 'extract': 0, 'extract_list': np.float64(0.0), 'data': {'page': 1, 'pageSize': 20, 'channelCode': '${appKey()}', 'token': '${get_extract_data(token)}'}}}"]}