{"uid": "9b262513cd80826b", "name": "**搜索**", "fullName": "testcase.play.test_play.TestPlay#test_play", "historyId": "80ea16c71eb34b6e85110c3f875859f1", "time": {"start": 1751866427644, "stop": 1751866427947, "duration": 303}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "capture_logs_for_allure", "time": {"start": 1751866426892, "stop": 1751866426892, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "clear_extract", "time": {"start": 1751866415419, "stop": 1751866415726, "duration": 307}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "_session_faker", "time": {"start": 1751866415384, "stop": 1751866415419, "duration": 35}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "function_wait", "time": {"start": 1751866426892, "stop": 1751866427643, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "print_info", "time": {"start": 1751866427643, "stop": 1751866427643, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "cce6b95f77ce85f5", "name": "接口地址", "source": "cce6b95f77ce85f5.json", "type": "application/json", "size": 53}, {"uid": "f4f7f8990ffbf923", "name": "接口名称", "source": "f4f7f8990ffbf923.json", "type": "application/json", "size": 10}, {"uid": "2499122ebe77efc7", "name": "请求方式", "source": "2499122ebe77efc7.json", "type": "application/json", "size": 4}, {"uid": "214bc744427210fe", "name": "请求头", "source": "214bc744427210fe.json", "type": "application/json", "size": 122}, {"uid": "2cdaf3f0107cd2d5", "name": "<PERSON><PERSON>", "source": "2cdaf3f0107cd2d5.json", "type": "application/json", "size": 12}, {"uid": "8d77e0d18ee45fa8", "name": "测试用例名称", "source": "8d77e0d18ee45fa8.json", "type": "application/json", "size": 30}, {"uid": "24c89dead8724ec6", "name": "参数类型", "source": "24c89dead8724ec6.json", "type": "application/json", "size": 4}, {"uid": "1197f99ade9a5b02", "name": "请求参数json格式", "source": "1197f99ade9a5b02.json", "type": "application/json", "size": 201}, {"uid": "55ce169a7edf083b", "name": "请求参数实际入参", "source": "55ce169a7edf083b.json", "type": "application/json", "size": 211}, {"uid": "c1bf018678ff38f1", "name": "接口实际响应信息", "source": "c1bf018678ff38f1.json", "type": "application/json", "size": 22387}, {"uid": "20a8dffb709fa74c", "name": "状态码断言结果：成功", "source": "20a8dffb709fa74c.txt", "type": "text/plain", "size": 37}, {"uid": "ae05895d21bff8d4", "name": "相等断言结果：成功", "source": "ae05895d21bff8d4.json", "type": "application/json", "size": 53}, {"uid": "76b7e6671b12b722", "name": "📋 测试执行日志", "source": "76b7e6671b12b722.txt", "type": "text/plain", "size": 14539}], "parameters": [], "hasContent": true, "stepsCount": 0, "attachmentsCount": 13, "shouldDisplayMessage": false}, "afterStages": [{"name": "capture_logs_for_allure::0", "time": {"start": 1751866428699, "stop": 1751866428715, "duration": 16}, "status": "passed", "steps": [], "attachments": [{"uid": "bf00586928b7fd52", "name": "📋 测试执行日志 (完整记录)", "source": "bf00586928b7fd52.txt", "type": "text/plain", "size": 14929}], "parameters": [], "hasContent": true, "stepsCount": 0, "attachmentsCount": 1, "shouldDisplayMessage": false}, {"name": "function_wait::0", "time": {"start": 1751866427948, "stop": 1751866428698, "duration": 750}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "print_info::0", "time": {"start": 1751866427947, "stop": 1751866427947, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}], "labels": [{"name": "story", "value": "C01_演出模块流程"}, {"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "16848-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "9b262513cd80826b.json", "parameterValues": []}