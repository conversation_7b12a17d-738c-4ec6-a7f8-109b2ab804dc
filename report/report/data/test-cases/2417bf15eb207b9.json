{"uid": "2417bf15eb207b9", "name": "下单", "fullName": "testcase.play.test_play.TestPlay#test_play", "historyId": "340c0bd5adb900e5cd7660f86f67f4fa", "time": {"start": 1751853750394, "stop": 1751853751641, "duration": 1247}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "_session_faker", "time": {"start": 1751853726731, "stop": 1751853726779, "duration": 48}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "hasContent": false, "stepsCount": 0, "attachmentsCount": 0}, {"name": "clear_extract", "time": {"start": 1751853726779, "stop": 1751853727168, "duration": 389}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "hasContent": false, "stepsCount": 0, "attachmentsCount": 0}, {"name": "print_info", "time": {"start": 1751853750394, "stop": 1751853750394, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "hasContent": false, "stepsCount": 0, "attachmentsCount": 0}, {"name": "function_wait", "time": {"start": 1751853749644, "stop": 1751853750394, "duration": 750}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "hasContent": false, "stepsCount": 0, "attachmentsCount": 0}, {"name": "capture_logs_for_allure", "time": {"start": 1751853749644, "stop": 1751853749644, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "hasContent": false, "stepsCount": 0, "attachmentsCount": 0}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "45c95a2fc45a6a77", "name": "接口地址", "source": "45c95a2fc45a6a77.json", "type": "application/json", "size": 42}, {"uid": "e31a45c3bed92fa7", "name": "接口名称", "source": "e31a45c3bed92fa7.json", "type": "application/json", "size": 6}, {"uid": "5664a09b0bc8d4ed", "name": "请求方式", "source": "5664a09b0bc8d4ed.json", "type": "application/json", "size": 4}, {"uid": "4d1cba9df5089f14", "name": "请求头", "source": "4d1cba9df5089f14.json", "type": "application/json", "size": 122}, {"uid": "68aea438280bb426", "name": "<PERSON><PERSON>", "source": "68aea438280bb426.json", "type": "application/json", "size": 12}, {"uid": "843860f689fd2d81", "name": "测试用例名称", "source": "843860f689fd2d81.json", "type": "application/json", "size": 12}, {"uid": "c658a6d482ba1eee", "name": "参数类型", "source": "c658a6d482ba1eee.json", "type": "application/json", "size": 4}, {"uid": "97f0086ae0caeaf2", "name": "请求参数json格式", "source": "97f0086ae0caeaf2.json", "type": "application/json", "size": 615}, {"uid": "a2bcbf02623d6ea0", "name": "请求参数实际入参", "source": "a2bcbf02623d6ea0.json", "type": "application/json", "size": 625}, {"uid": "5a7a20a427826d58", "name": "接口实际响应信息", "source": "5a7a20a427826d58.json", "type": "application/json", "size": 94}, {"uid": "5d359ee90fabb613", "name": "状态码断言结果：成功", "source": "5d359ee90fabb613.txt", "type": "text/plain", "size": 37}, {"uid": "6a4c5245f74af583", "name": "相等断言结果：成功", "source": "6a4c5245f74af583.json", "type": "application/json", "size": 53}, {"uid": "adc1eff0f82d46d7", "name": "📋 测试执行日志", "source": "adc1eff0f82d46d7.txt", "type": "text/plain", "size": 2377}], "parameters": [], "shouldDisplayMessage": false, "hasContent": true, "stepsCount": 0, "attachmentsCount": 13}, "afterStages": [{"name": "print_info::0", "time": {"start": 1751853751642, "stop": 1751853751642, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "hasContent": false, "stepsCount": 0, "attachmentsCount": 0}, {"name": "function_wait::0", "time": {"start": 1751853751642, "stop": 1751853752393, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "hasContent": false, "stepsCount": 0, "attachmentsCount": 0}, {"name": "capture_logs_for_allure::0", "time": {"start": 1751853752393, "stop": 1751853752402, "duration": 9}, "status": "passed", "steps": [], "attachments": [{"uid": "6dd3294b36d9754", "name": "📋 测试执行日志 (完整记录)", "source": "6dd3294b36d9754.txt", "type": "text/plain", "size": 2439}], "parameters": [], "shouldDisplayMessage": false, "hasContent": true, "stepsCount": 0, "attachmentsCount": 1}], "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "39072-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [{"name": "api_info", "value": "{'baseInfo': {'module': '演出票', 'api_name': '下单', 'host': '${get_host(play)}', 'url': '/api/show/order/create', 'method': 'post', 'headers': '${get_headers(app)}'}, 'testCase': {'case_name': '创建订单', 'validation': [{'code': 200}, {'eq': {'code': 0}}], 'extract': {'orderId': '$..orderId'}, 'extract_list': np.float64(0.0), 'data': {'recipientMobile': '${get_extract_data(login_info,mobile)}', 'recipientName': '购票人', 'recipientIdNo': '', 'fetchTicketWayId': '${get_extract_data(fetchTicketWayId)}', 'recipientAddressId': '', 'fetchTicketWayType': '${get_extract_data(fetchType)}', 'performanceId': '${get_extract_data(performance_id)}', 'showId': '${get_extract_data(showInfo,showId)}', 'salesPlanId': '${get_extract_data(ticketInfo,ticketUnitId)}', 'salesPlanCount': '1', 'totalTicketPrice': '${get_extract_data(ticketInfo,price)}', 'deliveryPrice': '0', 'orderPrice': '${get_extract_data(ticketInfo,price)}', 'realNameIds': '', 'seatRequest': '', 'channelCode': '${appKey()}', 'merchant_code': '', 'token': '${get_extract_data(token)}'}}}"}], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "2417bf15eb207b9.json", "parameterValues": ["{'baseInfo': {'module': '演出票', 'api_name': '下单', 'host': '${get_host(play)}', 'url': '/api/show/order/create', 'method': 'post', 'headers': '${get_headers(app)}'}, 'testCase': {'case_name': '创建订单', 'validation': [{'code': 200}, {'eq': {'code': 0}}], 'extract': {'orderId': '$..orderId'}, 'extract_list': np.float64(0.0), 'data': {'recipientMobile': '${get_extract_data(login_info,mobile)}', 'recipientName': '购票人', 'recipientIdNo': '', 'fetchTicketWayId': '${get_extract_data(fetchTicketWayId)}', 'recipientAddressId': '', 'fetchTicketWayType': '${get_extract_data(fetchType)}', 'performanceId': '${get_extract_data(performance_id)}', 'showId': '${get_extract_data(showInfo,showId)}', 'salesPlanId': '${get_extract_data(ticketInfo,ticketUnitId)}', 'salesPlanCount': '1', 'totalTicketPrice': '${get_extract_data(ticketInfo,price)}', 'deliveryPrice': '0', 'orderPrice': '${get_extract_data(ticketInfo,price)}', 'realNameIds': '', 'seatRequest': '', 'channelCode': '${appKey()}', 'merchant_code': '', 'token': '${get_extract_data(token)}'}}}"]}