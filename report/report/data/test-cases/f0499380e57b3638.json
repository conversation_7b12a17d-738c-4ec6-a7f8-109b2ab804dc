{"uid": "f0499380e57b3638", "name": "下单", "fullName": "testcase.play.test_play.TestPlay#test_play", "historyId": "df78d83b2c7879c3a2ecdde06190a07c", "time": {"start": 1751851587216, "stop": 1751851588859, "duration": 1643}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "_session_faker", "time": {"start": 1751851564550, "stop": 1751851564591, "duration": 41}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "function_wait", "time": {"start": 1751851586465, "stop": 1751851587215, "duration": 750}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "print_info", "time": {"start": 1751851587215, "stop": 1751851587215, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "capture_logs_for_allure", "time": {"start": 1751851586465, "stop": 1751851586465, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "clear_extract", "time": {"start": 1751851564591, "stop": 1751851564922, "duration": 331}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "ecf4da68adcb6570", "name": "接口地址", "source": "ecf4da68adcb6570.json", "type": "application/json", "size": 42}, {"uid": "8353f737714ac84b", "name": "接口名称", "source": "8353f737714ac84b.json", "type": "application/json", "size": 6}, {"uid": "1d014ca581362707", "name": "请求方式", "source": "1d014ca581362707.json", "type": "application/json", "size": 4}, {"uid": "aa03173dfdc8e936", "name": "请求头", "source": "aa03173dfdc8e936.json", "type": "application/json", "size": 122}, {"uid": "b6f506dd7ad980f2", "name": "<PERSON><PERSON>", "source": "b6f506dd7ad980f2.json", "type": "application/json", "size": 12}, {"uid": "a38ec1b456d23367", "name": "测试用例名称", "source": "a38ec1b456d23367.json", "type": "application/json", "size": 12}, {"uid": "ca313a3a482e0df5", "name": "参数类型", "source": "ca313a3a482e0df5.json", "type": "application/json", "size": 4}, {"uid": "ddba0920147b09d4", "name": "请求参数json格式", "source": "ddba0920147b09d4.json", "type": "application/json", "size": 615}, {"uid": "6b7af029d0b49cf7", "name": "请求参数实际入参", "source": "6b7af029d0b49cf7.json", "type": "application/json", "size": 625}, {"uid": "5f6dbb759369e261", "name": "接口实际响应信息", "source": "5f6dbb759369e261.json", "type": "application/json", "size": 94}, {"uid": "7233b70213a3714a", "name": "状态码断言结果：成功", "source": "7233b70213a3714a.txt", "type": "text/plain", "size": 37}, {"uid": "d0b7bb34a84d118c", "name": "相等断言结果：成功", "source": "d0b7bb34a84d118c.json", "type": "application/json", "size": 53}, {"uid": "f79304f7c1121d56", "name": "📋 测试执行日志", "source": "f79304f7c1121d56.txt", "type": "text/plain", "size": 2377}], "parameters": [], "hasContent": true, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 13}, "afterStages": [{"name": "function_wait::0", "time": {"start": 1751851588861, "stop": 1751851589611, "duration": 750}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "print_info::0", "time": {"start": 1751851588860, "stop": 1751851588860, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "capture_logs_for_allure::0", "time": {"start": 1751851589612, "stop": 1751851589627, "duration": 15}, "status": "passed", "steps": [], "attachments": [{"uid": "1764904727309e96", "name": "📋 测试执行日志 (完整记录)", "source": "1764904727309e96.txt", "type": "text/plain", "size": 2439}], "parameters": [], "hasContent": true, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 1}], "labels": [{"name": "story", "value": "C01_演出模块流程"}, {"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "34968-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "f0499380e57b3638.json", "parameterValues": []}