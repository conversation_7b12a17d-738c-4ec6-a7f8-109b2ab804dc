{"uid": "3638184b23db18e3", "name": "演出项目详情", "fullName": "testcase.play.test_play.TestPlay#test_play", "historyId": "0457c216d6185e839b1ab99163c7bbc6", "time": {"start": 1751852470200, "stop": 1751852470748, "duration": 548}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "print_info", "time": {"start": 1751852470200, "stop": 1751852470200, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "function_wait", "time": {"start": 1751852469450, "stop": 1751852470200, "duration": 750}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "_session_faker", "time": {"start": 1751852455733, "stop": 1751852455863, "duration": 130}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "clear_extract", "time": {"start": 1751852455863, "stop": 1751852456123, "duration": 260}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "capture_logs_for_allure", "time": {"start": 1751852469450, "stop": 1751852469450, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "f905603b1d5ac5f7", "name": "接口地址", "source": "f905603b1d5ac5f7.json", "type": "application/json", "size": 51}, {"uid": "6cf2a5b2085fb5cc", "name": "接口名称", "source": "6cf2a5b2085fb5cc.json", "type": "application/json", "size": 18}, {"uid": "15f1c48d606a36f5", "name": "请求方式", "source": "15f1c48d606a36f5.json", "type": "application/json", "size": 3}, {"uid": "538446dbb1ff7e8e", "name": "请求头", "source": "538446dbb1ff7e8e.json", "type": "application/json", "size": 122}, {"uid": "5ad5a11243963249", "name": "<PERSON><PERSON>", "source": "5ad5a11243963249.json", "type": "application/json", "size": 12}, {"uid": "ad0b75403d863fd6", "name": "测试用例名称", "source": "ad0b75403d863fd6.json", "type": "application/json", "size": 30}, {"uid": "43fc2869b95a7ee8", "name": "参数类型", "source": "43fc2869b95a7ee8.json", "type": "application/json", "size": 6}, {"uid": "fbbc2f8abe602cea", "name": "请求参数json格式", "source": "fbbc2f8abe602cea.json", "type": "application/json", "size": 206}, {"uid": "3f2d2aed8ae9c64e", "name": "请求参数实际入参", "source": "3f2d2aed8ae9c64e.json", "type": "application/json", "size": 216}, {"uid": "761d57774be553c2", "name": "接口实际响应信息", "source": "761d57774be553c2.json", "type": "application/json", "size": 4313}, {"uid": "bc5a55a406bb8938", "name": "状态码断言结果：成功", "source": "bc5a55a406bb8938.txt", "type": "text/plain", "size": 37}, {"uid": "5941cf1dcebfe705", "name": "相等断言结果：成功", "source": "5941cf1dcebfe705.json", "type": "application/json", "size": 53}, {"uid": "17d5fd93f3f7cb1d", "name": "📋 测试执行日志", "source": "17d5fd93f3f7cb1d.txt", "type": "text/plain", "size": 15390}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 13, "hasContent": true, "stepsCount": 0}, "afterStages": [{"name": "print_info::0", "time": {"start": 1751852470748, "stop": 1751852470748, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "function_wait::0", "time": {"start": 1751852470749, "stop": 1751852471500, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "capture_logs_for_allure::0", "time": {"start": 1751852471500, "stop": 1751852471511, "duration": 11}, "status": "passed", "steps": [], "attachments": [{"uid": "4bff3246c8096db4", "name": "📋 测试执行日志 (完整记录)", "source": "4bff3246c8096db4.txt", "type": "text/plain", "size": 6083}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 1, "hasContent": true, "stepsCount": 0}], "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "38092-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "3638184b23db18e3.json", "parameterValues": []}