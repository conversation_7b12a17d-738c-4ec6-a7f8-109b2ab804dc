{"uid": "29e2132b326e61dd", "name": "演出分类", "fullName": "testcase.play.test_play.TestPlay#test_play", "historyId": "c36ab200ccb8723de7119149ea6c3088", "time": {"start": 1751853886749, "stop": 1751853887119, "duration": 370}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "function_wait", "time": {"start": 1751853885997, "stop": 1751853886748, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "print_info", "time": {"start": 1751853886748, "stop": 1751853886748, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "clear_extract", "time": {"start": 1751853884946, "stop": 1751853885997, "duration": 1051}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "capture_logs_for_allure", "time": {"start": 1751853885997, "stop": 1751853885997, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "_session_faker", "time": {"start": 1751853884907, "stop": 1751853884946, "duration": 39}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "ca63331a35839c5c", "name": "接口地址", "source": "ca63331a35839c5c.json", "type": "application/json", "size": 48}, {"uid": "298ab4e62d7a71d8", "name": "接口名称", "source": "298ab4e62d7a71d8.json", "type": "application/json", "size": 12}, {"uid": "84baae0d58c6bff9", "name": "请求方式", "source": "84baae0d58c6bff9.json", "type": "application/json", "size": 4}, {"uid": "fcff11fa812d10da", "name": "请求头", "source": "fcff11fa812d10da.json", "type": "application/json", "size": 122}, {"uid": "e784239daa3c554", "name": "<PERSON><PERSON>", "source": "e784239daa3c554.json", "type": "application/json", "size": 12}, {"uid": "494512749678124f", "name": "测试用例名称", "source": "494512749678124f.json", "type": "application/json", "size": 24}, {"uid": "c97d52d23012896c", "name": "参数类型", "source": "c97d52d23012896c.json", "type": "application/json", "size": 4}, {"uid": "b893b3314aec8cd0", "name": "请求参数json格式", "source": "b893b3314aec8cd0.json", "type": "application/json", "size": 204}, {"uid": "eef1b816a5d6f625", "name": "请求参数实际入参", "source": "eef1b816a5d6f625.json", "type": "application/json", "size": 214}, {"uid": "1a6d25fb90dde5b", "name": "接口实际响应信息", "source": "1a6d25fb90dde5b.json", "type": "application/json", "size": 3148}, {"uid": "c0a175c2b0f87010", "name": "状态码断言结果：成功", "source": "c0a175c2b0f87010.txt", "type": "text/plain", "size": 37}, {"uid": "ea5c4563309b995", "name": "相等断言结果：成功", "source": "ea5c4563309b995.json", "type": "application/json", "size": 53}, {"uid": "dbd04492b4ea1f4c", "name": "📋 测试执行日志", "source": "dbd04492b4ea1f4c.txt", "type": "text/plain", "size": 2076}], "parameters": [], "hasContent": true, "stepsCount": 0, "attachmentsCount": 13, "shouldDisplayMessage": false}, "afterStages": [{"name": "function_wait::0", "time": {"start": 1751853887121, "stop": 1751853887872, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "print_info::0", "time": {"start": 1751853887121, "stop": 1751853887121, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "capture_logs_for_allure::0", "time": {"start": 1751853887872, "stop": 1751853887881, "duration": 9}, "status": "passed", "steps": [], "attachments": [{"uid": "979390c565a935e3", "name": "📋 测试执行日志 (完整记录)", "source": "979390c565a935e3.txt", "type": "text/plain", "size": 4327}], "parameters": [], "hasContent": true, "stepsCount": 0, "attachmentsCount": 1, "shouldDisplayMessage": false}], "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "39132-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "29e2132b326e61dd.json", "parameterValues": []}