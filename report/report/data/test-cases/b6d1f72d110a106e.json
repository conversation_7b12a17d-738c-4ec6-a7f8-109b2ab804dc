{"uid": "b6d1f72d110a106e", "name": "取消订单", "fullName": "testcase.play.test_play.TestPlay#test_play", "historyId": "86ddc9742985e7678bcca10364db8520", "time": {"start": 1751853753154, "stop": 1751853754353, "duration": 1199}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "_session_faker", "time": {"start": 1751853726731, "stop": 1751853726779, "duration": 48}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "hasContent": false, "stepsCount": 0, "attachmentsCount": 0}, {"name": "function_wait", "time": {"start": 1751853752403, "stop": 1751853753154, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "hasContent": false, "stepsCount": 0, "attachmentsCount": 0}, {"name": "print_info", "time": {"start": 1751853753154, "stop": 1751853753154, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "hasContent": false, "stepsCount": 0, "attachmentsCount": 0}, {"name": "clear_extract", "time": {"start": 1751853726779, "stop": 1751853727168, "duration": 389}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "hasContent": false, "stepsCount": 0, "attachmentsCount": 0}, {"name": "capture_logs_for_allure", "time": {"start": 1751853752403, "stop": 1751853752403, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "hasContent": false, "stepsCount": 0, "attachmentsCount": 0}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "5ec405df0936b6b1", "name": "接口地址", "source": "5ec405df0936b6b1.json", "type": "application/json", "size": 42}, {"uid": "e21736a7c644be94", "name": "接口名称", "source": "e21736a7c644be94.json", "type": "application/json", "size": 12}, {"uid": "781c4d769893ff87", "name": "请求方式", "source": "781c4d769893ff87.json", "type": "application/json", "size": 4}, {"uid": "8fc0a53cdc764c50", "name": "请求头", "source": "8fc0a53cdc764c50.json", "type": "application/json", "size": 122}, {"uid": "1bad84334e80059b", "name": "<PERSON><PERSON>", "source": "1bad84334e80059b.json", "type": "application/json", "size": 12}, {"uid": "cf35a665026c5d9d", "name": "测试用例名称", "source": "cf35a665026c5d9d.json", "type": "application/json", "size": 12}, {"uid": "f30d63bd168a4f54", "name": "参数类型", "source": "f30d63bd168a4f54.json", "type": "application/json", "size": 4}, {"uid": "f7f8af4222880dfe", "name": "请求参数json格式", "source": "f7f8af4222880dfe.json", "type": "application/json", "size": 217}, {"uid": "78defe80f49e27fd", "name": "请求参数实际入参", "source": "78defe80f49e27fd.json", "type": "application/json", "size": 227}, {"uid": "587d7488e9ba8090", "name": "接口实际响应信息", "source": "587d7488e9ba8090.json", "type": "application/json", "size": 48}, {"uid": "29e79c7619591ee", "name": "状态码断言结果：成功", "source": "29e79c7619591ee.txt", "type": "text/plain", "size": 37}, {"uid": "63549d63d7877829", "name": "相等断言结果：成功", "source": "63549d63d7877829.json", "type": "application/json", "size": 53}, {"uid": "b1dfbd2b86fd444", "name": "📋 测试执行日志", "source": "b1dfbd2b86fd444.txt", "type": "text/plain", "size": 2439}], "parameters": [], "shouldDisplayMessage": false, "hasContent": true, "stepsCount": 0, "attachmentsCount": 13}, "afterStages": [{"name": "function_wait::0", "time": {"start": 1751853754354, "stop": 1751853755105, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "hasContent": false, "stepsCount": 0, "attachmentsCount": 0}, {"name": "print_info::0", "time": {"start": 1751853754354, "stop": 1751853754354, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "hasContent": false, "stepsCount": 0, "attachmentsCount": 0}, {"name": "capture_logs_for_allure::0", "time": {"start": 1751853755105, "stop": 1751853755122, "duration": 17}, "status": "passed", "steps": [], "attachments": [{"uid": "f281309a850b56ee", "name": "📋 测试执行日志 (完整记录)", "source": "f281309a850b56ee.txt", "type": "text/plain", "size": 2076}], "parameters": [], "shouldDisplayMessage": false, "hasContent": true, "stepsCount": 0, "attachmentsCount": 1}], "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "39072-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [{"name": "api_info", "value": "{'baseInfo': {'module': '演出票', 'api_name': '取消订单', 'host': '${get_host(play)}', 'url': '/api/show/order/cancel', 'method': 'post', 'headers': '${get_headers(app)}'}, 'testCase': {'case_name': '取消订单', 'validation': [{'code': 200}, {'eq': {'code': 0}}], 'extract': 0, 'extract_list': np.float64(0.0), 'data': {'orderId': '${get_extract_data(orderId)}', 'channelCode': '${appKey()}', 'token': '${get_extract_data(token)}'}}}"}], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "b6d1f72d110a106e.json", "parameterValues": ["{'baseInfo': {'module': '演出票', 'api_name': '取消订单', 'host': '${get_host(play)}', 'url': '/api/show/order/cancel', 'method': 'post', 'headers': '${get_headers(app)}'}, 'testCase': {'case_name': '取消订单', 'validation': [{'code': 200}, {'eq': {'code': 0}}], 'extract': 0, 'extract_list': np.float64(0.0), 'data': {'orderId': '${get_extract_data(orderId)}', 'channelCode': '${appKey()}', 'token': '${get_extract_data(token)}'}}}"]}