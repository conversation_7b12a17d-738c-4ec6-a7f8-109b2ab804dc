{"uid": "5e28f8b8fdb93f8", "name": "为你推荐", "fullName": "testcase.play.test_play.TestPlay#test_play", "historyId": "a03ba38a51bcc9af12332b9cb9ea6b41", "time": {"start": 1751852462852, "stop": 1751852463165, "duration": 313}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "function_wait", "time": {"start": 1751852462102, "stop": 1751852462852, "duration": 750}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "_session_faker", "time": {"start": 1751852455733, "stop": 1751852455863, "duration": 130}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "print_info", "time": {"start": 1751852462852, "stop": 1751852462852, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "capture_logs_for_allure", "time": {"start": 1751852462102, "stop": 1751852462102, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "clear_extract", "time": {"start": 1751852455863, "stop": 1751852456123, "duration": 260}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "3da5beca5793eaa8", "name": "接口地址", "source": "3da5beca5793eaa8.json", "type": "application/json", "size": 54}, {"uid": "e5031d8750ac3648", "name": "接口名称", "source": "e5031d8750ac3648.json", "type": "application/json", "size": 12}, {"uid": "4b3ef2a0de32f90b", "name": "请求方式", "source": "4b3ef2a0de32f90b.json", "type": "application/json", "size": 4}, {"uid": "ef023039f30c3e95", "name": "请求头", "source": "ef023039f30c3e95.json", "type": "application/json", "size": 122}, {"uid": "5a7a4981f1c16270", "name": "<PERSON><PERSON>", "source": "5a7a4981f1c16270.json", "type": "application/json", "size": 12}, {"uid": "92ef6ea03826d20e", "name": "测试用例名称", "source": "92ef6ea03826d20e.json", "type": "application/json", "size": 30}, {"uid": "87795087e4428008", "name": "参数类型", "source": "87795087e4428008.json", "type": "application/json", "size": 4}, {"uid": "6b75a36cdf8a403b", "name": "请求参数json格式", "source": "6b75a36cdf8a403b.json", "type": "application/json", "size": 214}, {"uid": "643d1f4d5af29691", "name": "请求参数实际入参", "source": "643d1f4d5af29691.json", "type": "application/json", "size": 224}, {"uid": "c888389ca60bf5c3", "name": "接口实际响应信息", "source": "c888389ca60bf5c3.json", "type": "application/json", "size": 44012}, {"uid": "414b68ccdd572ab4", "name": "状态码断言结果：成功", "source": "414b68ccdd572ab4.txt", "type": "text/plain", "size": 37}, {"uid": "28ceab68fd377201", "name": "相等断言结果：成功", "source": "28ceab68fd377201.json", "type": "application/json", "size": 53}, {"uid": "7ae38dcc7af25de5", "name": "📋 测试执行日志", "source": "7ae38dcc7af25de5.txt", "type": "text/plain", "size": 15174}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 13, "hasContent": true, "stepsCount": 0}, "afterStages": [{"name": "function_wait::0", "time": {"start": 1751852463166, "stop": 1751852463917, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "print_info::0", "time": {"start": 1751852463165, "stop": 1751852463166, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "capture_logs_for_allure::0", "time": {"start": 1751852463917, "stop": 1751852463928, "duration": 11}, "status": "passed", "steps": [], "attachments": [{"uid": "5518eadf4158359b", "name": "📋 测试执行日志 (完整记录)", "source": "5518eadf4158359b.txt", "type": "text/plain", "size": 27309}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 1, "hasContent": true, "stepsCount": 0}], "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "38092-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "5e28f8b8fdb93f8.json", "parameterValues": []}