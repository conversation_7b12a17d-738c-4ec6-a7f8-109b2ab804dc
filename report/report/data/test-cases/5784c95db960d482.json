{"uid": "5784c95db960d482", "name": "演出分类", "fullName": "testcase.play.test_play.TestPlay#test_play", "historyId": "c36ab200ccb8723de7119149ea6c3088", "time": {"start": 1751853886749, "stop": 1751853887119, "duration": 370}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "function_wait", "time": {"start": 1751853885997, "stop": 1751853886748, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "print_info", "time": {"start": 1751853886748, "stop": 1751853886748, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "clear_extract", "time": {"start": 1751853884946, "stop": 1751853885997, "duration": 1051}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "capture_logs_for_allure", "time": {"start": 1751853885997, "stop": 1751853885997, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "_session_faker", "time": {"start": 1751853884907, "stop": 1751853884946, "duration": 39}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "44aba512cceb7702", "name": "接口地址", "source": "44aba512cceb7702.json", "type": "application/json", "size": 48}, {"uid": "17acb355e33d115d", "name": "接口名称", "source": "17acb355e33d115d.json", "type": "application/json", "size": 12}, {"uid": "387f93991b2a8f51", "name": "请求方式", "source": "387f93991b2a8f51.json", "type": "application/json", "size": 4}, {"uid": "21935c8554155f4f", "name": "请求头", "source": "21935c8554155f4f.json", "type": "application/json", "size": 122}, {"uid": "78a0fb592191080", "name": "<PERSON><PERSON>", "source": "78a0fb592191080.json", "type": "application/json", "size": 12}, {"uid": "25c4a43c78b1db8f", "name": "测试用例名称", "source": "25c4a43c78b1db8f.json", "type": "application/json", "size": 24}, {"uid": "34bb8efbadeadca7", "name": "参数类型", "source": "34bb8efbadeadca7.json", "type": "application/json", "size": 4}, {"uid": "4fa11fac681168e4", "name": "请求参数json格式", "source": "4fa11fac681168e4.json", "type": "application/json", "size": 204}, {"uid": "fae81f1e78a56325", "name": "请求参数实际入参", "source": "fae81f1e78a56325.json", "type": "application/json", "size": 214}, {"uid": "4138c42294de37c9", "name": "接口实际响应信息", "source": "4138c42294de37c9.json", "type": "application/json", "size": 3148}, {"uid": "13acb07ff79d0a78", "name": "状态码断言结果：成功", "source": "13acb07ff79d0a78.txt", "type": "text/plain", "size": 37}, {"uid": "a104118e01d62ce5", "name": "相等断言结果：成功", "source": "a104118e01d62ce5.json", "type": "application/json", "size": 53}, {"uid": "b8a66b6b2c757817", "name": "📋 测试执行日志", "source": "b8a66b6b2c757817.txt", "type": "text/plain", "size": 2076}], "parameters": [], "hasContent": true, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 13}, "afterStages": [{"name": "function_wait::0", "time": {"start": 1751853887121, "stop": 1751853887872, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "print_info::0", "time": {"start": 1751853887121, "stop": 1751853887121, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "capture_logs_for_allure::0", "time": {"start": 1751853887872, "stop": 1751853887881, "duration": 9}, "status": "passed", "steps": [], "attachments": [{"uid": "296e0da8732ea354", "name": "📋 测试执行日志 (完整记录)", "source": "296e0da8732ea354.txt", "type": "text/plain", "size": 4327}], "parameters": [], "hasContent": true, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 1}], "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "39132-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "5784c95db960d482.json", "parameterValues": []}