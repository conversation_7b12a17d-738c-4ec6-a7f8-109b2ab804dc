{"uid": "9c49aeb77298e52c", "name": "根据场次获取取票方式", "fullName": "testcase.play.test_play.TestPlay#test_play", "historyId": "169108c6786ad08586d73174286032ec", "time": {"start": 1751853748389, "stop": 1751853748879, "duration": 490}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "function_wait", "time": {"start": 1751853747638, "stop": 1751853748389, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "hasContent": false, "stepsCount": 0, "attachmentsCount": 0}, {"name": "_session_faker", "time": {"start": 1751853726731, "stop": 1751853726779, "duration": 48}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "hasContent": false, "stepsCount": 0, "attachmentsCount": 0}, {"name": "clear_extract", "time": {"start": 1751853726779, "stop": 1751853727168, "duration": 389}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "hasContent": false, "stepsCount": 0, "attachmentsCount": 0}, {"name": "capture_logs_for_allure", "time": {"start": 1751853747638, "stop": 1751853747638, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "hasContent": false, "stepsCount": 0, "attachmentsCount": 0}, {"name": "print_info", "time": {"start": 1751853748389, "stop": 1751853748389, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "hasContent": false, "stepsCount": 0, "attachmentsCount": 0}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "a91e935ff4f1197", "name": "接口地址", "source": "a91e935ff4f1197.json", "type": "application/json", "size": 57}, {"uid": "3c4b83d21356a69a", "name": "接口名称", "source": "3c4b83d21356a69a.json", "type": "application/json", "size": 30}, {"uid": "3e72f87f7d5eac5c", "name": "请求方式", "source": "3e72f87f7d5eac5c.json", "type": "application/json", "size": 3}, {"uid": "bc5ba7c7396a7c0", "name": "请求头", "source": "bc5ba7c7396a7c0.json", "type": "application/json", "size": 122}, {"uid": "62550ee8b6fb78b4", "name": "<PERSON><PERSON>", "source": "62550ee8b6fb78b4.json", "type": "application/json", "size": 12}, {"uid": "65a3dcb293276842", "name": "测试用例名称", "source": "65a3dcb293276842.json", "type": "application/json", "size": 30}, {"uid": "c73fd3d9f1793f07", "name": "参数类型", "source": "c73fd3d9f1793f07.json", "type": "application/json", "size": 6}, {"uid": "b616d3153c11036e", "name": "请求参数json格式", "source": "b616d3153c11036e.json", "type": "application/json", "size": 199}, {"uid": "c39479f2da508920", "name": "请求参数实际入参", "source": "c39479f2da508920.json", "type": "application/json", "size": 209}, {"uid": "3ea8650df4948fb", "name": "接口实际响应信息", "source": "3ea8650df4948fb.json", "type": "application/json", "size": 599}, {"uid": "fc689296dbd0c165", "name": "状态码断言结果：成功", "source": "fc689296dbd0c165.txt", "type": "text/plain", "size": 37}, {"uid": "ff90dbe4f936e49c", "name": "相等断言结果：成功", "source": "ff90dbe4f936e49c.json", "type": "application/json", "size": 53}, {"uid": "a7ab30d97360bfe2", "name": "📋 测试执行日志", "source": "a7ab30d97360bfe2.txt", "type": "text/plain", "size": 4692}], "parameters": [], "shouldDisplayMessage": false, "hasContent": true, "stepsCount": 0, "attachmentsCount": 13}, "afterStages": [{"name": "function_wait::0", "time": {"start": 1751853748880, "stop": 1751853749631, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "hasContent": false, "stepsCount": 0, "attachmentsCount": 0}, {"name": "capture_logs_for_allure::0", "time": {"start": 1751853749631, "stop": 1751853749642, "duration": 11}, "status": "passed", "steps": [], "attachments": [{"uid": "54995921a3507147", "name": "📋 测试执行日志 (完整记录)", "source": "54995921a3507147.txt", "type": "text/plain", "size": 2377}], "parameters": [], "shouldDisplayMessage": false, "hasContent": true, "stepsCount": 0, "attachmentsCount": 1}, {"name": "print_info::0", "time": {"start": 1751853748880, "stop": 1751853748880, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "hasContent": false, "stepsCount": 0, "attachmentsCount": 0}], "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "39072-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [{"name": "api_info", "value": "{'baseInfo': {'module': '演出票', 'api_name': '根据场次获取取票方式', 'host': '${get_host(play)}', 'url': '/api/show/info/show_fetch_ticket_ways', 'method': 'get', 'headers': '${get_headers(app)}'}, 'testCase': {'case_name': '获取该场次的取票方式', 'validation': [{'code': 200}, {'eq': {'code': 0}}], 'extract': {'fetchTicketWayId': '$..fetchTicketWayId', 'fetchType': '$..fetchType'}, 'extract_list': np.float64(0.0), 'params': {'showId': '${get_extract_data(showInfo,showId)}', 'channelCode': '${appKey()}', 'token': '${get_extract_data(token)}'}}}"}], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "9c49aeb77298e52c.json", "parameterValues": ["{'baseInfo': {'module': '演出票', 'api_name': '根据场次获取取票方式', 'host': '${get_host(play)}', 'url': '/api/show/info/show_fetch_ticket_ways', 'method': 'get', 'headers': '${get_headers(app)}'}, 'testCase': {'case_name': '获取该场次的取票方式', 'validation': [{'code': 200}, {'eq': {'code': 0}}], 'extract': {'fetchTicketWayId': '$..fetchTicketWayId', 'fetchType': '$..fetchType'}, 'extract_list': np.float64(0.0), 'params': {'showId': '${get_extract_data(showInfo,showId)}', 'channelCode': '${appKey()}', 'token': '${get_extract_data(token)}'}}}"]}