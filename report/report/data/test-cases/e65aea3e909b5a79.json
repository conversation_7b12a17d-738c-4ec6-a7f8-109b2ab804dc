{"uid": "e65aea3e909b5a79", "name": "演出项目详情", "fullName": "testcase.play.test_play.TestPlay#test_play", "historyId": "f1d7da39199bd96c94383323f4027e2f", "time": {"start": 1751853741806, "stop": 1751853742686, "duration": 880}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "print_info", "time": {"start": 1751853741805, "stop": 1751853741805, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "hasContent": false, "stepsCount": 0, "attachmentsCount": 0}, {"name": "capture_logs_for_allure", "time": {"start": 1751853741054, "stop": 1751853741055, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "hasContent": false, "stepsCount": 0, "attachmentsCount": 0}, {"name": "_session_faker", "time": {"start": 1751853726731, "stop": 1751853726779, "duration": 48}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "hasContent": false, "stepsCount": 0, "attachmentsCount": 0}, {"name": "function_wait", "time": {"start": 1751853741055, "stop": 1751853741805, "duration": 750}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "hasContent": false, "stepsCount": 0, "attachmentsCount": 0}, {"name": "clear_extract", "time": {"start": 1751853726779, "stop": 1751853727168, "duration": 389}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "hasContent": false, "stepsCount": 0, "attachmentsCount": 0}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "a4aa14dafd3b9cf1", "name": "接口地址", "source": "a4aa14dafd3b9cf1.json", "type": "application/json", "size": 51}, {"uid": "ac3ebf1f16ea69fc", "name": "接口名称", "source": "ac3ebf1f16ea69fc.json", "type": "application/json", "size": 18}, {"uid": "f9eb781dc36629e4", "name": "请求方式", "source": "f9eb781dc36629e4.json", "type": "application/json", "size": 3}, {"uid": "d2113b14366db8fd", "name": "请求头", "source": "d2113b14366db8fd.json", "type": "application/json", "size": 122}, {"uid": "cd4e66a27fd397ab", "name": "<PERSON><PERSON>", "source": "cd4e66a27fd397ab.json", "type": "application/json", "size": 12}, {"uid": "b2f8eac6fb1629cd", "name": "测试用例名称", "source": "b2f8eac6fb1629cd.json", "type": "application/json", "size": 30}, {"uid": "e961d8d8c2f770fd", "name": "参数类型", "source": "e961d8d8c2f770fd.json", "type": "application/json", "size": 6}, {"uid": "ca226e36cffc48de", "name": "请求参数json格式", "source": "ca226e36cffc48de.json", "type": "application/json", "size": 206}, {"uid": "a5aaebc65af1515d", "name": "请求参数实际入参", "source": "a5aaebc65af1515d.json", "type": "application/json", "size": 216}, {"uid": "d4772db19f69ae9", "name": "接口实际响应信息", "source": "d4772db19f69ae9.json", "type": "application/json", "size": 4313}, {"uid": "c2b4f6666dbfd3ad", "name": "状态码断言结果：成功", "source": "c2b4f6666dbfd3ad.txt", "type": "text/plain", "size": 37}, {"uid": "f61356060aa0b339", "name": "相等断言结果：成功", "source": "f61356060aa0b339.json", "type": "application/json", "size": 53}, {"uid": "abcca313458e1136", "name": "📋 测试执行日志", "source": "abcca313458e1136.txt", "type": "text/plain", "size": 14929}], "parameters": [], "shouldDisplayMessage": false, "hasContent": true, "stepsCount": 0, "attachmentsCount": 13}, "afterStages": [{"name": "print_info::0", "time": {"start": 1751853742687, "stop": 1751853742688, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "hasContent": false, "stepsCount": 0, "attachmentsCount": 0}, {"name": "capture_logs_for_allure::0", "time": {"start": 1751853743439, "stop": 1751853743449, "duration": 10}, "status": "passed", "steps": [], "attachments": [{"uid": "d8b5006e75bd47a", "name": "📋 测试执行日志 (完整记录)", "source": "d8b5006e75bd47a.txt", "type": "text/plain", "size": 5822}], "parameters": [], "shouldDisplayMessage": false, "hasContent": true, "stepsCount": 0, "attachmentsCount": 1}, {"name": "function_wait::0", "time": {"start": 1751853742688, "stop": 1751853743439, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "hasContent": false, "stepsCount": 0, "attachmentsCount": 0}], "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "39072-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [{"name": "api_info", "value": "{'baseInfo': {'module': '演出票', 'api_name': '演出项目详情', 'host': '${get_host(play)}', 'url': '/api/show/info/performance_info', 'method': 'get', 'headers': '${get_headers(app)}'}, 'testCase': {'case_name': '获取演出项目详情信息', 'validation': [{'code': 200}, {'eq': {'code': 0}}], 'extract': 0, 'extract_list': np.float64(0.0), 'params': {'performanceId': '${get_extract_data(performance_id)}', 'channelCode': '${appKey()}', 'token': '${get_extract_data(token)}'}}}"}], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "e65aea3e909b5a79.json", "parameterValues": ["{'baseInfo': {'module': '演出票', 'api_name': '演出项目详情', 'host': '${get_host(play)}', 'url': '/api/show/info/performance_info', 'method': 'get', 'headers': '${get_headers(app)}'}, 'testCase': {'case_name': '获取演出项目详情信息', 'validation': [{'code': 200}, {'eq': {'code': 0}}], 'extract': 0, 'extract_list': np.float64(0.0), 'params': {'performanceId': '${get_extract_data(performance_id)}', 'channelCode': '${appKey()}', 'token': '${get_extract_data(token)}'}}}"]}