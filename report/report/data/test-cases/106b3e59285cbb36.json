{"uid": "106b3e59285cbb36", "name": "演出分类", "fullName": "testcase.play.test_play.TestPlay#test_play", "historyId": "c36ab200ccb8723de7119149ea6c3088", "time": {"start": 1751866416482, "stop": 1751866416894, "duration": 412}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "capture_logs_for_allure", "time": {"start": 1751866415726, "stop": 1751866415726, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "function_wait", "time": {"start": 1751866415726, "stop": 1751866416477, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "clear_extract", "time": {"start": 1751866415419, "stop": 1751866415726, "duration": 307}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "print_info", "time": {"start": 1751866416477, "stop": 1751866416477, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "_session_faker", "time": {"start": 1751866415384, "stop": 1751866415419, "duration": 35}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "509ae4e6b09d5f81", "name": "接口地址", "source": "509ae4e6b09d5f81.json", "type": "application/json", "size": 48}, {"uid": "8f553fc9232a6bc5", "name": "接口名称", "source": "8f553fc9232a6bc5.json", "type": "application/json", "size": 12}, {"uid": "c92d6162c2b93761", "name": "请求方式", "source": "c92d6162c2b93761.json", "type": "application/json", "size": 4}, {"uid": "fde2a720276269bc", "name": "请求头", "source": "fde2a720276269bc.json", "type": "application/json", "size": 122}, {"uid": "acd1a6f4461437bc", "name": "<PERSON><PERSON>", "source": "acd1a6f4461437bc.json", "type": "application/json", "size": 12}, {"uid": "f1fd881c7d4d98ae", "name": "测试用例名称", "source": "f1fd881c7d4d98ae.json", "type": "application/json", "size": 24}, {"uid": "cc3d71be586d1b65", "name": "参数类型", "source": "cc3d71be586d1b65.json", "type": "application/json", "size": 4}, {"uid": "cd168ca2f685003b", "name": "请求参数json格式", "source": "cd168ca2f685003b.json", "type": "application/json", "size": 204}, {"uid": "38ab7b5bf46e7be2", "name": "请求参数实际入参", "source": "38ab7b5bf46e7be2.json", "type": "application/json", "size": 214}, {"uid": "4c5dd15712f5844", "name": "接口实际响应信息", "source": "4c5dd15712f5844.json", "type": "application/json", "size": 3148}, {"uid": "525b1b3788231887", "name": "状态码断言结果：成功", "source": "525b1b3788231887.txt", "type": "text/plain", "size": 37}, {"uid": "8d132e5deb851d55", "name": "相等断言结果：成功", "source": "8d132e5deb851d55.json", "type": "application/json", "size": 53}, {"uid": "15bc26ada46d3d7d", "name": "📋 测试执行日志", "source": "15bc26ada46d3d7d.txt", "type": "text/plain", "size": 182244}], "parameters": [], "hasContent": true, "stepsCount": 0, "attachmentsCount": 13, "shouldDisplayMessage": false}, "afterStages": [{"name": "capture_logs_for_allure::0", "time": {"start": 1751866417647, "stop": 1751866417656, "duration": 9}, "status": "passed", "steps": [], "attachments": [{"uid": "4cde880e5d3efb8", "name": "📋 测试执行日志 (完整记录)", "source": "4cde880e5d3efb8.txt", "type": "text/plain", "size": 4097}], "parameters": [], "hasContent": true, "stepsCount": 0, "attachmentsCount": 1, "shouldDisplayMessage": false}, {"name": "function_wait::0", "time": {"start": 1751866416896, "stop": 1751866417647, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "print_info::0", "time": {"start": 1751866416895, "stop": 1751866416896, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}], "labels": [{"name": "story", "value": "C01_演出模块流程"}, {"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "16848-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "106b3e59285cbb36.json", "parameterValues": []}