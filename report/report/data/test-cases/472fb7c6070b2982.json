{"uid": "472fb7c6070b2982", "name": "近期特惠", "fullName": "testcase.play.test_play.TestPlay#test_play", "historyId": "b9dc0ab2757f5b153151bfbd4cc293dd", "time": {"start": 1751853233896, "stop": 1751853234106, "duration": 210}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "_session_faker", "time": {"start": 1751853229094, "stop": 1751853229142, "duration": 48}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "print_info", "time": {"start": 1751853233895, "stop": 1751853233895, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "clear_extract", "time": {"start": 1751853229143, "stop": 1751853229413, "duration": 270}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "capture_logs_for_allure", "time": {"start": 1751853233145, "stop": 1751853233145, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "function_wait", "time": {"start": 1751853233145, "stop": 1751853233895, "duration": 750}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "807ca83e209c1dbb", "name": "接口地址", "source": "807ca83e209c1dbb.json", "type": "application/json", "size": 54}, {"uid": "b6bdf89644fc736", "name": "接口名称", "source": "b6bdf89644fc736.json", "type": "application/json", "size": 12}, {"uid": "133e697f0b6531db", "name": "请求方式", "source": "133e697f0b6531db.json", "type": "application/json", "size": 4}, {"uid": "90d51d5799efd99d", "name": "请求头", "source": "90d51d5799efd99d.json", "type": "application/json", "size": 122}, {"uid": "b0bb2b26037b847a", "name": "<PERSON><PERSON>", "source": "b0bb2b26037b847a.json", "type": "application/json", "size": 12}, {"uid": "45458ba2b08aa219", "name": "测试用例名称", "source": "45458ba2b08aa219.json", "type": "application/json", "size": 30}, {"uid": "5e0a2d9072364ec0", "name": "参数类型", "source": "5e0a2d9072364ec0.json", "type": "application/json", "size": 4}, {"uid": "ea58194af2378be0", "name": "请求参数json格式", "source": "ea58194af2378be0.json", "type": "application/json", "size": 214}, {"uid": "7e07b1727dc5336e", "name": "请求参数实际入参", "source": "7e07b1727dc5336e.json", "type": "application/json", "size": 224}, {"uid": "48336e2ac4c028da", "name": "接口实际响应信息", "source": "48336e2ac4c028da.json", "type": "application/json", "size": 22188}, {"uid": "9fe7c9f999ff1ae4", "name": "状态码断言结果：成功", "source": "9fe7c9f999ff1ae4.txt", "type": "text/plain", "size": 37}, {"uid": "82e265e1419c36b4", "name": "相等断言结果：成功", "source": "82e265e1419c36b4.json", "type": "application/json", "size": 53}, {"uid": "20bcafc50515c0a", "name": "📋 测试执行日志", "source": "20bcafc50515c0a.txt", "type": "text/plain", "size": 15197}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 13, "hasContent": true, "stepsCount": 0}, "afterStages": [{"name": "print_info::0", "time": {"start": 1751853234107, "stop": 1751853234107, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "capture_logs_for_allure::0", "time": {"start": 1751853234857, "stop": 1751853234868, "duration": 11}, "status": "passed", "steps": [], "attachments": [{"uid": "beb7a5a2412f6e59", "name": "📋 测试执行日志 (完整记录)", "source": "beb7a5a2412f6e59.txt", "type": "text/plain", "size": 15149}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 1, "hasContent": true, "stepsCount": 0}, {"name": "function_wait::0", "time": {"start": 1751853234107, "stop": 1751853234857, "duration": 750}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}], "labels": [{"name": "story", "value": "C01_演出模块流程"}, {"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "28812-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "472fb7c6070b2982.json", "parameterValues": []}