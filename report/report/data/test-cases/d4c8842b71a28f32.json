{"uid": "d4c8842b71a28f32", "name": "演出分类", "fullName": "testcase.play.test_play.TestPlay#test_play", "historyId": "8670a6529c6811d89314564f4152b621", "time": {"start": 1751852991959, "stop": 1751852992309, "duration": 350}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "function_wait", "time": {"start": 1751852991206, "stop": 1751852991957, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "print_info", "time": {"start": 1751852991957, "stop": 1751852991958, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "clear_extract", "time": {"start": 1751852990929, "stop": 1751852991206, "duration": 277}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "_session_faker", "time": {"start": 1751852990879, "stop": 1751852990927, "duration": 48}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "capture_logs_for_allure", "time": {"start": 1751852991206, "stop": 1751852991206, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "71e67b06c09bfe91", "name": "接口地址", "source": "71e67b06c09bfe91.json", "type": "application/json", "size": 48}, {"uid": "88d3f7a288ef7f52", "name": "接口名称", "source": "88d3f7a288ef7f52.json", "type": "application/json", "size": 12}, {"uid": "15fb59e32039b7f6", "name": "请求方式", "source": "15fb59e32039b7f6.json", "type": "application/json", "size": 4}, {"uid": "5e25dd685dd11ba8", "name": "请求头", "source": "5e25dd685dd11ba8.json", "type": "application/json", "size": 122}, {"uid": "345dea70be09609f", "name": "<PERSON><PERSON>", "source": "345dea70be09609f.json", "type": "application/json", "size": 12}, {"uid": "5cd93230d34d020a", "name": "测试用例名称", "source": "5cd93230d34d020a.json", "type": "application/json", "size": 24}, {"uid": "703edf71fe1377a2", "name": "参数类型", "source": "703edf71fe1377a2.json", "type": "application/json", "size": 4}, {"uid": "dae158aa5c2f85c7", "name": "请求参数json格式", "source": "dae158aa5c2f85c7.json", "type": "application/json", "size": 204}, {"uid": "b424f202a0c4d447", "name": "请求参数实际入参", "source": "b424f202a0c4d447.json", "type": "application/json", "size": 214}, {"uid": "aca5dc361d517359", "name": "接口实际响应信息", "source": "aca5dc361d517359.json", "type": "application/json", "size": 3148}, {"uid": "76a600a962ad7513", "name": "状态码断言结果：成功", "source": "76a600a962ad7513.txt", "type": "text/plain", "size": 37}, {"uid": "497a81fb9c34915b", "name": "相等断言结果：成功", "source": "497a81fb9c34915b.json", "type": "application/json", "size": 53}, {"uid": "680fd5fd94f1b110", "name": "📋 测试执行日志", "source": "680fd5fd94f1b110.txt", "type": "text/plain", "size": 123997}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 13, "hasContent": true, "stepsCount": 0}, "afterStages": [{"name": "function_wait::0", "time": {"start": 1751852992312, "stop": 1751852993063, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "print_info::0", "time": {"start": 1751852992312, "stop": 1751852992312, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "capture_logs_for_allure::0", "time": {"start": 1751852993063, "stop": 1751852993076, "duration": 13}, "status": "passed", "steps": [], "attachments": [{"uid": "cf934e47f4339040", "name": "📋 测试执行日志 (完整记录)", "source": "cf934e47f4339040.txt", "type": "text/plain", "size": 4097}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 1, "hasContent": true, "stepsCount": 0}], "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "37476-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [{"name": "api_info", "value": "{'baseInfo': {'module': '演出票', 'api_name': '演出分类', 'host': '${get_host(play)}', 'url': '/api/show/info/category_list', 'method': 'post', 'headers': '${get_headers(app)}'}, 'testCase': {'case_name': '获取猫眼演出分类', 'validation': [{'code': 200}, {'eq': {'code': 0}}], 'extract': 0, 'extract_list': np.float64(0.0), 'data': {'channelCode': '${appKey()}', 'merchant_code': '', 'token': '${get_extract_data(token)}'}}}"}], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "d4c8842b71a28f32.json", "parameterValues": ["{'baseInfo': {'module': '演出票', 'api_name': '演出分类', 'host': '${get_host(play)}', 'url': '/api/show/info/category_list', 'method': 'post', 'headers': '${get_headers(app)}'}, 'testCase': {'case_name': '获取猫眼演出分类', 'validation': [{'code': 200}, {'eq': {'code': 0}}], 'extract': 0, 'extract_list': np.float64(0.0), 'data': {'channelCode': '${appKey()}', 'merchant_code': '', 'token': '${get_extract_data(token)}'}}}"]}