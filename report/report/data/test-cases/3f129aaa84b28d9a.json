{"uid": "3f129aaa84b28d9a", "name": "演出项目详情", "fullName": "testcase.play.test_play.TestPlay#test_play", "historyId": "0457c216d6185e839b1ab99163c7bbc6", "time": {"start": 1751851578842, "stop": 1751851579441, "duration": 599}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "_session_faker", "time": {"start": 1751851564550, "stop": 1751851564591, "duration": 41}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "print_info", "time": {"start": 1751851578842, "stop": 1751851578842, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "clear_extract", "time": {"start": 1751851564591, "stop": 1751851564922, "duration": 331}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "capture_logs_for_allure", "time": {"start": 1751851578090, "stop": 1751851578090, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "function_wait", "time": {"start": 1751851578090, "stop": 1751851578842, "duration": 752}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "368655604298179a", "name": "接口地址", "source": "368655604298179a.json", "type": "application/json", "size": 51}, {"uid": "233d52ecbc7df21f", "name": "接口名称", "source": "233d52ecbc7df21f.json", "type": "application/json", "size": 18}, {"uid": "daa995c7c848f97a", "name": "请求方式", "source": "daa995c7c848f97a.json", "type": "application/json", "size": 3}, {"uid": "80c742dbadfa232b", "name": "请求头", "source": "80c742dbadfa232b.json", "type": "application/json", "size": 122}, {"uid": "de1c2bc90c2b79db", "name": "<PERSON><PERSON>", "source": "de1c2bc90c2b79db.json", "type": "application/json", "size": 12}, {"uid": "4377e83cb08f498", "name": "测试用例名称", "source": "4377e83cb08f498.json", "type": "application/json", "size": 30}, {"uid": "634101640f82da8", "name": "参数类型", "source": "634101640f82da8.json", "type": "application/json", "size": 6}, {"uid": "4cd7575d7a90acba", "name": "请求参数json格式", "source": "4cd7575d7a90acba.json", "type": "application/json", "size": 206}, {"uid": "28d980f94cc1cb79", "name": "请求参数实际入参", "source": "28d980f94cc1cb79.json", "type": "application/json", "size": 216}, {"uid": "774f3da8230d14da", "name": "接口实际响应信息", "source": "774f3da8230d14da.json", "type": "application/json", "size": 4313}, {"uid": "b88872d674dc2a9a", "name": "状态码断言结果：成功", "source": "b88872d674dc2a9a.txt", "type": "text/plain", "size": 37}, {"uid": "f219180e50c2dd01", "name": "相等断言结果：成功", "source": "f219180e50c2dd01.json", "type": "application/json", "size": 53}, {"uid": "5721e7b64fe23349", "name": "📋 测试执行日志", "source": "5721e7b64fe23349.txt", "type": "text/plain", "size": 14929}], "parameters": [], "hasContent": true, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 13}, "afterStages": [{"name": "print_info::0", "time": {"start": 1751851579442, "stop": 1751851579442, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "capture_logs_for_allure::0", "time": {"start": 1751851580195, "stop": 1751851580208, "duration": 13}, "status": "passed", "steps": [], "attachments": [{"uid": "8d198503e78418c8", "name": "📋 测试执行日志 (完整记录)", "source": "8d198503e78418c8.txt", "type": "text/plain", "size": 5822}], "parameters": [], "hasContent": true, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "function_wait::0", "time": {"start": 1751851579443, "stop": 1751851580194, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "story", "value": "C01_演出模块流程"}, {"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "34968-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "3f129aaa84b28d9a.json", "parameterValues": []}