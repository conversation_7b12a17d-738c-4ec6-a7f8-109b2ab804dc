{"uid": "31cb2c9906e43271", "name": "下单", "fullName": "testcase.play.test_play.TestPlay#test_play", "historyId": "df78d83b2c7879c3a2ecdde06190a07c", "time": {"start": 1751852478401, "stop": 1751852479673, "duration": 1272}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "_session_faker", "time": {"start": 1751852455733, "stop": 1751852455863, "duration": 130}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "print_info", "time": {"start": 1751852478399, "stop": 1751852478400, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "function_wait", "time": {"start": 1751852477647, "stop": 1751852478398, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "clear_extract", "time": {"start": 1751852455863, "stop": 1751852456123, "duration": 260}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "capture_logs_for_allure", "time": {"start": 1751852477647, "stop": 1751852477647, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "476742ec1c20a6f8", "name": "接口地址", "source": "476742ec1c20a6f8.json", "type": "application/json", "size": 42}, {"uid": "b584862399020ac8", "name": "接口名称", "source": "b584862399020ac8.json", "type": "application/json", "size": 6}, {"uid": "3ee8138594ae938a", "name": "请求方式", "source": "3ee8138594ae938a.json", "type": "application/json", "size": 4}, {"uid": "1dea26b8571c6275", "name": "请求头", "source": "1dea26b8571c6275.json", "type": "application/json", "size": 122}, {"uid": "a9b420e54c0a8ff2", "name": "<PERSON><PERSON>", "source": "a9b420e54c0a8ff2.json", "type": "application/json", "size": 12}, {"uid": "73a54dea29c145c2", "name": "测试用例名称", "source": "73a54dea29c145c2.json", "type": "application/json", "size": 12}, {"uid": "9c069e75f690e5af", "name": "参数类型", "source": "9c069e75f690e5af.json", "type": "application/json", "size": 4}, {"uid": "f5ffc8ea75ed5267", "name": "请求参数json格式", "source": "f5ffc8ea75ed5267.json", "type": "application/json", "size": 615}, {"uid": "17f3558b1f5cf046", "name": "请求参数实际入参", "source": "17f3558b1f5cf046.json", "type": "application/json", "size": 625}, {"uid": "78f2a441bef3de6e", "name": "接口实际响应信息", "source": "78f2a441bef3de6e.json", "type": "application/json", "size": 94}, {"uid": "8b6c692ca06a09c2", "name": "状态码断言结果：成功", "source": "8b6c692ca06a09c2.txt", "type": "text/plain", "size": 37}, {"uid": "5e3c3bcde7219dc1", "name": "相等断言结果：成功", "source": "5e3c3bcde7219dc1.json", "type": "application/json", "size": 53}, {"uid": "3287c14613cddb8a", "name": "📋 测试执行日志", "source": "3287c14613cddb8a.txt", "type": "text/plain", "size": 2839}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 13, "hasContent": true, "stepsCount": 0}, "afterStages": [{"name": "print_info::0", "time": {"start": 1751852479673, "stop": 1751852479674, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "function_wait::0", "time": {"start": 1751852479674, "stop": 1751852480425, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "capture_logs_for_allure::0", "time": {"start": 1751852480427, "stop": 1751852480444, "duration": 17}, "status": "passed", "steps": [], "attachments": [{"uid": "dc449347199cc244", "name": "📋 测试执行日志 (完整记录)", "source": "dc449347199cc244.txt", "type": "text/plain", "size": 2682}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 1, "hasContent": true, "stepsCount": 0}], "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "38092-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "31cb2c9906e43271.json", "parameterValues": []}