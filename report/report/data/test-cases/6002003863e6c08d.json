{"uid": "6002003863e6c08d", "name": "演出分类", "fullName": "testcase.play.test_play.TestPlay#test_play", "historyId": "8670a6529c6811d89314564f4152b621", "time": {"start": 1751853727920, "stop": 1751853728267, "duration": 347}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "_session_faker", "time": {"start": 1751853726731, "stop": 1751853726779, "duration": 48}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "hasContent": false, "stepsCount": 0, "attachmentsCount": 0}, {"name": "clear_extract", "time": {"start": 1751853726779, "stop": 1751853727168, "duration": 389}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "hasContent": false, "stepsCount": 0, "attachmentsCount": 0}, {"name": "function_wait", "time": {"start": 1751853727168, "stop": 1751853727919, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "hasContent": false, "stepsCount": 0, "attachmentsCount": 0}, {"name": "capture_logs_for_allure", "time": {"start": 1751853727168, "stop": 1751853727168, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "hasContent": false, "stepsCount": 0, "attachmentsCount": 0}, {"name": "print_info", "time": {"start": 1751853727919, "stop": 1751853727919, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "hasContent": false, "stepsCount": 0, "attachmentsCount": 0}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "ac956cc2edc82ede", "name": "接口地址", "source": "ac956cc2edc82ede.json", "type": "application/json", "size": 48}, {"uid": "cc93235ea8028768", "name": "接口名称", "source": "cc93235ea8028768.json", "type": "application/json", "size": 12}, {"uid": "762a123ebd6a1de0", "name": "请求方式", "source": "762a123ebd6a1de0.json", "type": "application/json", "size": 4}, {"uid": "4747bd33b5f074dc", "name": "请求头", "source": "4747bd33b5f074dc.json", "type": "application/json", "size": 122}, {"uid": "fbf63519de09d648", "name": "<PERSON><PERSON>", "source": "fbf63519de09d648.json", "type": "application/json", "size": 12}, {"uid": "55dc09a477c3402d", "name": "测试用例名称", "source": "55dc09a477c3402d.json", "type": "application/json", "size": 24}, {"uid": "3d68484b8d0d157", "name": "参数类型", "source": "3d68484b8d0d157.json", "type": "application/json", "size": 4}, {"uid": "c2f0e785908bb5d0", "name": "请求参数json格式", "source": "c2f0e785908bb5d0.json", "type": "application/json", "size": 204}, {"uid": "33a50e7797e5053a", "name": "请求参数实际入参", "source": "33a50e7797e5053a.json", "type": "application/json", "size": 214}, {"uid": "1a915ff438f9f0a1", "name": "接口实际响应信息", "source": "1a915ff438f9f0a1.json", "type": "application/json", "size": 3148}, {"uid": "f59cdbabc0936f47", "name": "状态码断言结果：成功", "source": "f59cdbabc0936f47.txt", "type": "text/plain", "size": 37}, {"uid": "42be5baa1d1aaa39", "name": "相等断言结果：成功", "source": "42be5baa1d1aaa39.json", "type": "application/json", "size": 53}, {"uid": "64c77cbf32502b22", "name": "📋 测试执行日志", "source": "64c77cbf32502b22.txt", "type": "text/plain", "size": 7387}], "parameters": [], "shouldDisplayMessage": false, "hasContent": true, "stepsCount": 0, "attachmentsCount": 13}, "afterStages": [{"name": "function_wait::0", "time": {"start": 1751853728270, "stop": 1751853729021, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "hasContent": false, "stepsCount": 0, "attachmentsCount": 0}, {"name": "capture_logs_for_allure::0", "time": {"start": 1751853729021, "stop": 1751853729035, "duration": 14}, "status": "passed", "steps": [], "attachments": [{"uid": "561a4c72563c1e83", "name": "📋 测试执行日志 (完整记录)", "source": "561a4c72563c1e83.txt", "type": "text/plain", "size": 4097}], "parameters": [], "shouldDisplayMessage": false, "hasContent": true, "stepsCount": 0, "attachmentsCount": 1}, {"name": "print_info::0", "time": {"start": 1751853728270, "stop": 1751853728270, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "hasContent": false, "stepsCount": 0, "attachmentsCount": 0}], "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "39072-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [{"name": "api_info", "value": "{'baseInfo': {'module': '演出票', 'api_name': '演出分类', 'host': '${get_host(play)}', 'url': '/api/show/info/category_list', 'method': 'post', 'headers': '${get_headers(app)}'}, 'testCase': {'case_name': '获取猫眼演出分类', 'validation': [{'code': 200}, {'eq': {'code': 0}}], 'extract': 0, 'extract_list': np.float64(0.0), 'data': {'channelCode': '${appKey()}', 'merchant_code': '', 'token': '${get_extract_data(token)}'}}}"}], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "6002003863e6c08d.json", "parameterValues": ["{'baseInfo': {'module': '演出票', 'api_name': '演出分类', 'host': '${get_host(play)}', 'url': '/api/show/info/category_list', 'method': 'post', 'headers': '${get_headers(app)}'}, 'testCase': {'case_name': '获取猫眼演出分类', 'validation': [{'code': 200}, {'eq': {'code': 0}}], 'extract': 0, 'extract_list': np.float64(0.0), 'data': {'channelCode': '${appKey()}', 'merchant_code': '', 'token': '${get_extract_data(token)}'}}}"]}