{"uid": "b46a7426e2ddfc25", "name": "为你推荐", "fullName": "testcase.play.test_play.TestPlay#test_play", "historyId": "a03ba38a51bcc9af12332b9cb9ea6b41", "time": {"start": 1751866422104, "stop": 1751866422426, "duration": 322}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "print_info", "time": {"start": 1751866422103, "stop": 1751866422103, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "clear_extract", "time": {"start": 1751866415419, "stop": 1751866415726, "duration": 307}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "_session_faker", "time": {"start": 1751866415384, "stop": 1751866415419, "duration": 35}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "capture_logs_for_allure", "time": {"start": 1751866421353, "stop": 1751866421353, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "function_wait", "time": {"start": 1751866421353, "stop": 1751866422103, "duration": 750}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "f48ab6adbc9170f4", "name": "接口地址", "source": "f48ab6adbc9170f4.json", "type": "application/json", "size": 54}, {"uid": "f0f2141c635d7786", "name": "接口名称", "source": "f0f2141c635d7786.json", "type": "application/json", "size": 12}, {"uid": "6418addbd501c864", "name": "请求方式", "source": "6418addbd501c864.json", "type": "application/json", "size": 4}, {"uid": "8928da39f9b8d", "name": "请求头", "source": "8928da39f9b8d.json", "type": "application/json", "size": 122}, {"uid": "24331c75b6446416", "name": "<PERSON><PERSON>", "source": "24331c75b6446416.json", "type": "application/json", "size": 12}, {"uid": "fc8d92222c1a1005", "name": "测试用例名称", "source": "fc8d92222c1a1005.json", "type": "application/json", "size": 30}, {"uid": "56b4b439ef58b2b0", "name": "参数类型", "source": "56b4b439ef58b2b0.json", "type": "application/json", "size": 4}, {"uid": "df4bdc059d0cbb72", "name": "请求参数json格式", "source": "df4bdc059d0cbb72.json", "type": "application/json", "size": 214}, {"uid": "89b26e73d5dafa4b", "name": "请求参数实际入参", "source": "89b26e73d5dafa4b.json", "type": "application/json", "size": 224}, {"uid": "db3db64b3b42577d", "name": "接口实际响应信息", "source": "db3db64b3b42577d.json", "type": "application/json", "size": 44012}, {"uid": "8b728b976a911818", "name": "状态码断言结果：成功", "source": "8b728b976a911818.txt", "type": "text/plain", "size": 37}, {"uid": "319e495e59159038", "name": "相等断言结果：成功", "source": "319e495e59159038.json", "type": "application/json", "size": 53}, {"uid": "2aeb08b741493293", "name": "📋 测试执行日志", "source": "2aeb08b741493293.txt", "type": "text/plain", "size": 14715}], "parameters": [], "hasContent": true, "stepsCount": 0, "attachmentsCount": 13, "shouldDisplayMessage": false}, "afterStages": [{"name": "print_info::0", "time": {"start": 1751866422426, "stop": 1751866422426, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "capture_logs_for_allure::0", "time": {"start": 1751866423179, "stop": 1751866423195, "duration": 16}, "status": "passed", "steps": [], "attachments": [{"uid": "682c7d57745ca8b3", "name": "📋 测试执行日志 (完整记录)", "source": "682c7d57745ca8b3.txt", "type": "text/plain", "size": 27049}], "parameters": [], "hasContent": true, "stepsCount": 0, "attachmentsCount": 1, "shouldDisplayMessage": false}, {"name": "function_wait::0", "time": {"start": 1751866422427, "stop": 1751866423177, "duration": 750}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}], "labels": [{"name": "story", "value": "C01_演出模块流程"}, {"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "16848-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "b46a7426e2ddfc25.json", "parameterValues": []}