{"uid": "a5e23ccf78bc6188", "name": "为你推荐", "fullName": "testcase.play.test_play.TestPlay#test_play", "historyId": "a03ba38a51bcc9af12332b9cb9ea6b41", "time": {"start": 1751853892274, "stop": 1751853892596, "duration": 322}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "function_wait", "time": {"start": 1751853891522, "stop": 1751853892274, "duration": 752}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "capture_logs_for_allure", "time": {"start": 1751853891522, "stop": 1751853891522, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "clear_extract", "time": {"start": 1751853884946, "stop": 1751853885997, "duration": 1051}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "_session_faker", "time": {"start": 1751853884907, "stop": 1751853884946, "duration": 39}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "print_info", "time": {"start": 1751853892274, "stop": 1751853892274, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "24f695638cc1b246", "name": "接口地址", "source": "24f695638cc1b246.json", "type": "application/json", "size": 54}, {"uid": "35ef03577a6e04d4", "name": "接口名称", "source": "35ef03577a6e04d4.json", "type": "application/json", "size": 12}, {"uid": "f48345586c8bd6d0", "name": "请求方式", "source": "f48345586c8bd6d0.json", "type": "application/json", "size": 4}, {"uid": "cde83c7bc3e87f67", "name": "请求头", "source": "cde83c7bc3e87f67.json", "type": "application/json", "size": 122}, {"uid": "16143e55715c8b75", "name": "<PERSON><PERSON>", "source": "16143e55715c8b75.json", "type": "application/json", "size": 12}, {"uid": "8fe66e3ced711bf1", "name": "测试用例名称", "source": "8fe66e3ced711bf1.json", "type": "application/json", "size": 30}, {"uid": "954b06b4c0cb986f", "name": "参数类型", "source": "954b06b4c0cb986f.json", "type": "application/json", "size": 4}, {"uid": "9e7f349a4f0e948f", "name": "请求参数json格式", "source": "9e7f349a4f0e948f.json", "type": "application/json", "size": 214}, {"uid": "8a0e29b5d98caed4", "name": "请求参数实际入参", "source": "8a0e29b5d98caed4.json", "type": "application/json", "size": 224}, {"uid": "1cda8f34c53267d8", "name": "接口实际响应信息", "source": "1cda8f34c53267d8.json", "type": "application/json", "size": 44012}, {"uid": "affd878d8ad6766c", "name": "状态码断言结果：成功", "source": "affd878d8ad6766c.txt", "type": "text/plain", "size": 37}, {"uid": "b3a77ee8a12b4eca", "name": "相等断言结果：成功", "source": "b3a77ee8a12b4eca.json", "type": "application/json", "size": 53}, {"uid": "f200943ec280883f", "name": "📋 测试执行日志", "source": "f200943ec280883f.txt", "type": "text/plain", "size": 14945}], "parameters": [], "hasContent": true, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 13}, "afterStages": [{"name": "function_wait::0", "time": {"start": 1751853892598, "stop": 1751853893349, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "capture_logs_for_allure::0", "time": {"start": 1751853893349, "stop": 1751853893358, "duration": 9}, "status": "passed", "steps": [], "attachments": [{"uid": "5516ff3bf1ab1a27", "name": "📋 测试执行日志 (完整记录)", "source": "5516ff3bf1ab1a27.txt", "type": "text/plain", "size": 27582}], "parameters": [], "hasContent": true, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "print_info::0", "time": {"start": 1751853892597, "stop": 1751853892597, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "39132-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "a5e23ccf78bc6188.json", "parameterValues": []}