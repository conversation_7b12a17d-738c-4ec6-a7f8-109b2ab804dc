{"uid": "af5ac0c1145865e9", "name": "首页广告位", "fullName": "testcase.play.test_play.TestPlay#test_play", "historyId": "998f7fb53b18ea3045f71922b591e307", "time": {"start": 1751853894112, "stop": 1751853894392, "duration": 280}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "function_wait", "time": {"start": 1751853893360, "stop": 1751853894111, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "capture_logs_for_allure", "time": {"start": 1751853893359, "stop": 1751853893360, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "clear_extract", "time": {"start": 1751853884946, "stop": 1751853885997, "duration": 1051}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "_session_faker", "time": {"start": 1751853884907, "stop": 1751853884946, "duration": 39}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "print_info", "time": {"start": 1751853894111, "stop": 1751853894111, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "248ee5f6fc2fa5f9", "name": "接口地址", "source": "248ee5f6fc2fa5f9.json", "type": "application/json", "size": 44}, {"uid": "9f4a3376db017009", "name": "接口名称", "source": "9f4a3376db017009.json", "type": "application/json", "size": 15}, {"uid": "2800c7d7947d381f", "name": "请求方式", "source": "2800c7d7947d381f.json", "type": "application/json", "size": 4}, {"uid": "6ecc53c1d3bc62ac", "name": "请求头", "source": "6ecc53c1d3bc62ac.json", "type": "application/json", "size": 122}, {"uid": "4718c55558dbf0ee", "name": "<PERSON><PERSON>", "source": "4718c55558dbf0ee.json", "type": "application/json", "size": 12}, {"uid": "c5d6210cc0c1d181", "name": "测试用例名称", "source": "c5d6210cc0c1d181.json", "type": "application/json", "size": 24}, {"uid": "546ddac6aa07a1fb", "name": "参数类型", "source": "546ddac6aa07a1fb.json", "type": "application/json", "size": 4}, {"uid": "1a554c01670f2b2f", "name": "请求参数json格式", "source": "1a554c01670f2b2f.json", "type": "application/json", "size": 179}, {"uid": "10e9f10ce9c1e05a", "name": "请求参数实际入参", "source": "10e9f10ce9c1e05a.json", "type": "application/json", "size": 189}, {"uid": "52f87a3e956a4fc1", "name": "接口实际响应信息", "source": "52f87a3e956a4fc1.json", "type": "application/json", "size": 3479}, {"uid": "a0dc5f8ce69e78e5", "name": "状态码断言结果：成功", "source": "a0dc5f8ce69e78e5.txt", "type": "text/plain", "size": 37}, {"uid": "906a0657b9ab14a1", "name": "相等断言结果：成功", "source": "906a0657b9ab14a1.json", "type": "application/json", "size": 53}, {"uid": "d068a4a2cbab78a", "name": "📋 测试执行日志", "source": "d068a4a2cbab78a.txt", "type": "text/plain", "size": 27582}], "parameters": [], "hasContent": true, "stepsCount": 0, "attachmentsCount": 13, "shouldDisplayMessage": false}, "afterStages": [{"name": "function_wait::0", "time": {"start": 1751853894393, "stop": 1751853895144, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "capture_logs_for_allure::0", "time": {"start": 1751853895144, "stop": 1751853895155, "duration": 11}, "status": "passed", "steps": [], "attachments": [{"uid": "996378cc8f166bb0", "name": "📋 测试执行日志 (完整记录)", "source": "996378cc8f166bb0.txt", "type": "text/plain", "size": 4779}], "parameters": [], "hasContent": true, "stepsCount": 0, "attachmentsCount": 1, "shouldDisplayMessage": false}, {"name": "print_info::0", "time": {"start": 1751853894392, "stop": 1751853894393, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}], "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "39132-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "af5ac0c1145865e9.json", "parameterValues": []}