{"uid": "2f6023e06fa39570", "name": "演出场次列表", "fullName": "testcase.play.test_play.TestPlay#test_play", "historyId": "ce6724bce5907ddc1a7c9b8a022c9530", "time": {"start": 1751852472264, "stop": 1751852472789, "duration": 525}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "_session_faker", "time": {"start": 1751852455733, "stop": 1751852455863, "duration": 130}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "function_wait", "time": {"start": 1751852471513, "stop": 1751852472264, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "print_info", "time": {"start": 1751852472264, "stop": 1751852472264, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "clear_extract", "time": {"start": 1751852455863, "stop": 1751852456123, "duration": 260}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "capture_logs_for_allure", "time": {"start": 1751852471513, "stop": 1751852471513, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "16518c8177155c14", "name": "接口地址", "source": "16518c8177155c14.json", "type": "application/json", "size": 52}, {"uid": "5cdae13bc06b66ea", "name": "接口名称", "source": "5cdae13bc06b66ea.json", "type": "application/json", "size": 18}, {"uid": "58fa9957044e526", "name": "请求方式", "source": "58fa9957044e526.json", "type": "application/json", "size": 3}, {"uid": "90511e1acd7a46", "name": "请求头", "source": "90511e1acd7a46.json", "type": "application/json", "size": 122}, {"uid": "19f5044fe06aa2ad", "name": "<PERSON><PERSON>", "source": "19f5044fe06aa2ad.json", "type": "application/json", "size": 12}, {"uid": "ff22d825a56fffae", "name": "测试用例名称", "source": "ff22d825a56fffae.json", "type": "application/json", "size": 30}, {"uid": "daeea214123f4cf0", "name": "参数类型", "source": "daeea214123f4cf0.json", "type": "application/json", "size": 6}, {"uid": "bf4aad49b7251e19", "name": "请求参数json格式", "source": "bf4aad49b7251e19.json", "type": "application/json", "size": 206}, {"uid": "d5874ae818281f51", "name": "请求参数实际入参", "source": "d5874ae818281f51.json", "type": "application/json", "size": 216}, {"uid": "5f9882256672ba6e", "name": "接口实际响应信息", "source": "5f9882256672ba6e.json", "type": "application/json", "size": 1528}, {"uid": "8224ea9a606fce36", "name": "状态码断言结果：成功", "source": "8224ea9a606fce36.txt", "type": "text/plain", "size": 37}, {"uid": "f28e942680f1928d", "name": "相等断言结果：成功", "source": "f28e942680f1928d.json", "type": "application/json", "size": 53}, {"uid": "eb687c040ed244ca", "name": "📋 测试执行日志", "source": "eb687c040ed244ca.txt", "type": "text/plain", "size": 6083}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 13, "hasContent": true, "stepsCount": 0}, "afterStages": [{"name": "function_wait::0", "time": {"start": 1751852472790, "stop": 1751852473541, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "print_info::0", "time": {"start": 1751852472790, "stop": 1751852472790, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "capture_logs_for_allure::0", "time": {"start": 1751852473541, "stop": 1751852473551, "duration": 10}, "status": "passed", "steps": [], "attachments": [{"uid": "2836b3cd2a46a396", "name": "📋 测试执行日志 (完整记录)", "source": "2836b3cd2a46a396.txt", "type": "text/plain", "size": 3474}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 1, "hasContent": true, "stepsCount": 0}], "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "38092-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "2f6023e06fa39570.json", "parameterValues": []}