{"uid": "ec7c0e203e89fa16", "name": "演唱会分类", "fullName": "testcase.play.test_play.TestPlay#test_play", "historyId": "b8a74dca691f81270d24ebc4399d791b", "time": {"start": 1751853738165, "stop": 1751853738463, "duration": 298}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "function_wait", "time": {"start": 1751853737415, "stop": 1751853738165, "duration": 750}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "hasContent": false, "stepsCount": 0, "attachmentsCount": 0}, {"name": "capture_logs_for_allure", "time": {"start": 1751853737415, "stop": 1751853737415, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "hasContent": false, "stepsCount": 0, "attachmentsCount": 0}, {"name": "_session_faker", "time": {"start": 1751853726731, "stop": 1751853726779, "duration": 48}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "hasContent": false, "stepsCount": 0, "attachmentsCount": 0}, {"name": "clear_extract", "time": {"start": 1751853726779, "stop": 1751853727168, "duration": 389}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "hasContent": false, "stepsCount": 0, "attachmentsCount": 0}, {"name": "print_info", "time": {"start": 1751853738165, "stop": 1751853738165, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "hasContent": false, "stepsCount": 0, "attachmentsCount": 0}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "be1e872d19d7a641", "name": "接口地址", "source": "be1e872d19d7a641.json", "type": "application/json", "size": 53}, {"uid": "7856d4b3c52276e3", "name": "接口名称", "source": "7856d4b3c52276e3.json", "type": "application/json", "size": 15}, {"uid": "da1252fee341d3d4", "name": "请求方式", "source": "da1252fee341d3d4.json", "type": "application/json", "size": 4}, {"uid": "a364e4d9880ef80f", "name": "请求头", "source": "a364e4d9880ef80f.json", "type": "application/json", "size": 122}, {"uid": "a12b04a44ca3421b", "name": "<PERSON><PERSON>", "source": "a12b04a44ca3421b.json", "type": "application/json", "size": 12}, {"uid": "5dcbb646e05cb699", "name": "测试用例名称", "source": "5dcbb646e05cb699.json", "type": "application/json", "size": 25}, {"uid": "e3d808ebe4968bd5", "name": "参数类型", "source": "e3d808ebe4968bd5.json", "type": "application/json", "size": 4}, {"uid": "2f6441f510f9002f", "name": "请求参数json格式", "source": "2f6441f510f9002f.json", "type": "application/json", "size": 207}, {"uid": "efb5e086f0d8d86b", "name": "请求参数实际入参", "source": "efb5e086f0d8d86b.json", "type": "application/json", "size": 217}, {"uid": "e65c11c0f3ef3025", "name": "接口实际响应信息", "source": "e65c11c0f3ef3025.json", "type": "application/json", "size": 22018}, {"uid": "dd054ea30e5f4ee0", "name": "状态码断言结果：成功", "source": "dd054ea30e5f4ee0.txt", "type": "text/plain", "size": 37}, {"uid": "3c88c7306c39b0c3", "name": "相等断言结果：成功", "source": "3c88c7306c39b0c3.json", "type": "application/json", "size": 53}, {"uid": "12c7c8dad5f13065", "name": "📋 测试执行日志", "source": "12c7c8dad5f13065.txt", "type": "text/plain", "size": 4549}], "parameters": [], "shouldDisplayMessage": false, "hasContent": true, "stepsCount": 0, "attachmentsCount": 13}, "afterStages": [{"name": "function_wait::0", "time": {"start": 1751853738464, "stop": 1751853739216, "duration": 752}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "hasContent": false, "stepsCount": 0, "attachmentsCount": 0}, {"name": "capture_logs_for_allure::0", "time": {"start": 1751853739218, "stop": 1751853739245, "duration": 27}, "status": "passed", "steps": [], "attachments": [{"uid": "3c1d56a71503bde1", "name": "📋 测试执行日志 (完整记录)", "source": "3c1d56a71503bde1.txt", "type": "text/plain", "size": 14539}], "parameters": [], "shouldDisplayMessage": false, "hasContent": true, "stepsCount": 0, "attachmentsCount": 1}, {"name": "print_info::0", "time": {"start": 1751853738464, "stop": 1751853738464, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "hasContent": false, "stepsCount": 0, "attachmentsCount": 0}], "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "39072-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [{"name": "api_info", "value": "{'baseInfo': {'module': '演出票', 'api_name': '演唱会分类', 'host': '${get_host(play)}', 'url': '/api/show/info/performance_search', 'method': 'post', 'headers': '${get_headers(app)}'}, 'testCase': {'case_name': '按类型搜索=演唱会', 'validation': [{'code': 200}, {'eq': {'code': 0}}], 'extract': 0, 'extract_list': np.float64(0.0), 'data': {'category_id': '10001', 'channelCode': '${appKey()}', 'token': '${get_extract_data(token)}'}}}"}], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "ec7c0e203e89fa16.json", "parameterValues": ["{'baseInfo': {'module': '演出票', 'api_name': '演唱会分类', 'host': '${get_host(play)}', 'url': '/api/show/info/performance_search', 'method': 'post', 'headers': '${get_headers(app)}'}, 'testCase': {'case_name': '按类型搜索=演唱会', 'validation': [{'code': 200}, {'eq': {'code': 0}}], 'extract': 0, 'extract_list': np.float64(0.0), 'data': {'category_id': '10001', 'channelCode': '${appKey()}', 'token': '${get_extract_data(token)}'}}}"]}