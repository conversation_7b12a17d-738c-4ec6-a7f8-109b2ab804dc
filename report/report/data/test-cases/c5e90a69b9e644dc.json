{"uid": "c5e90a69b9e644dc", "name": "近期特惠", "fullName": "testcase.play.test_play.TestPlay#test_play", "historyId": "dcf03e580545918eaa3c4bf9273130d9", "time": {"start": 1751853732644, "stop": 1751853732967, "duration": 323}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "capture_logs_for_allure", "time": {"start": 1751853731893, "stop": 1751853731893, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "hasContent": false, "stepsCount": 0, "attachmentsCount": 0}, {"name": "_session_faker", "time": {"start": 1751853726731, "stop": 1751853726779, "duration": 48}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "hasContent": false, "stepsCount": 0, "attachmentsCount": 0}, {"name": "print_info", "time": {"start": 1751853732644, "stop": 1751853732644, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "hasContent": false, "stepsCount": 0, "attachmentsCount": 0}, {"name": "clear_extract", "time": {"start": 1751853726779, "stop": 1751853727168, "duration": 389}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "hasContent": false, "stepsCount": 0, "attachmentsCount": 0}, {"name": "function_wait", "time": {"start": 1751853731894, "stop": 1751853732644, "duration": 750}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "hasContent": false, "stepsCount": 0, "attachmentsCount": 0}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "263d8044657b15ff", "name": "接口地址", "source": "263d8044657b15ff.json", "type": "application/json", "size": 54}, {"uid": "e644f914174b69bb", "name": "接口名称", "source": "e644f914174b69bb.json", "type": "application/json", "size": 12}, {"uid": "fa924548e620be7c", "name": "请求方式", "source": "fa924548e620be7c.json", "type": "application/json", "size": 4}, {"uid": "cbb8608519671581", "name": "请求头", "source": "cbb8608519671581.json", "type": "application/json", "size": 122}, {"uid": "b0ec71498754f8ec", "name": "<PERSON><PERSON>", "source": "b0ec71498754f8ec.json", "type": "application/json", "size": 12}, {"uid": "fd7c801ff5e019f5", "name": "测试用例名称", "source": "fd7c801ff5e019f5.json", "type": "application/json", "size": 30}, {"uid": "346ae0164dc26a7a", "name": "参数类型", "source": "346ae0164dc26a7a.json", "type": "application/json", "size": 4}, {"uid": "27f052f752d41749", "name": "请求参数json格式", "source": "27f052f752d41749.json", "type": "application/json", "size": 214}, {"uid": "a20d660d82d61f68", "name": "请求参数实际入参", "source": "a20d660d82d61f68.json", "type": "application/json", "size": 224}, {"uid": "10edbd757e9583b4", "name": "接口实际响应信息", "source": "10edbd757e9583b4.json", "type": "application/json", "size": 22188}, {"uid": "648e55ff205783fa", "name": "状态码断言结果：成功", "source": "648e55ff205783fa.txt", "type": "text/plain", "size": 37}, {"uid": "4109d9691cc892f", "name": "相等断言结果：成功", "source": "4109d9691cc892f.json", "type": "application/json", "size": 53}, {"uid": "5b3ca901fd58ab04", "name": "📋 测试执行日志", "source": "5b3ca901fd58ab04.txt", "type": "text/plain", "size": 14716}], "parameters": [], "shouldDisplayMessage": false, "hasContent": true, "stepsCount": 0, "attachmentsCount": 13}, "afterStages": [{"name": "capture_logs_for_allure::0", "time": {"start": 1751853733720, "stop": 1751853733729, "duration": 9}, "status": "passed", "steps": [], "attachments": [{"uid": "b30665026d879c54", "name": "📋 测试执行日志 (完整记录)", "source": "b30665026d879c54.txt", "type": "text/plain", "size": 14715}], "parameters": [], "shouldDisplayMessage": false, "hasContent": true, "stepsCount": 0, "attachmentsCount": 1}, {"name": "print_info::0", "time": {"start": 1751853732968, "stop": 1751853732969, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "hasContent": false, "stepsCount": 0, "attachmentsCount": 0}, {"name": "function_wait::0", "time": {"start": 1751853732969, "stop": 1751853733720, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "hasContent": false, "stepsCount": 0, "attachmentsCount": 0}], "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "39072-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [{"name": "api_info", "value": "{'baseInfo': {'module': '演出票', 'api_name': '近期特惠', 'host': '${get_host(play)}', 'url': '/api/show/info/home_near_show_list', 'method': 'post', 'headers': '${get_headers(app)}'}, 'testCase': {'case_name': '获取近期特惠演出列表', 'validation': [{'code': 200}, {'eq': {'code': 0}}], 'extract': 0, 'extract_list': np.float64(0.0), 'data': {'page': 1, 'pageSize': 20, 'channelCode': '${appKey()}', 'token': '${get_extract_data(token)}'}}}"}], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "c5e90a69b9e644dc.json", "parameterValues": ["{'baseInfo': {'module': '演出票', 'api_name': '近期特惠', 'host': '${get_host(play)}', 'url': '/api/show/info/home_near_show_list', 'method': 'post', 'headers': '${get_headers(app)}'}, 'testCase': {'case_name': '获取近期特惠演出列表', 'validation': [{'code': 200}, {'eq': {'code': 0}}], 'extract': 0, 'extract_list': np.float64(0.0), 'data': {'page': 1, 'pageSize': 20, 'channelCode': '${appKey()}', 'token': '${get_extract_data(token)}'}}}"]}