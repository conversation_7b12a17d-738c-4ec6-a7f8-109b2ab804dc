{"uid": "9f3333ce3c6ea387", "name": "项目下场次票品列表", "fullName": "testcase.play.test_play.TestPlay#test_play", "historyId": "3287c72f85d7169cf4925a7d71668b28", "time": {"start": 1751866433661, "stop": 1751866434254, "duration": 593}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "print_info", "time": {"start": 1751866433660, "stop": 1751866433660, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "clear_extract", "time": {"start": 1751866415419, "stop": 1751866415726, "duration": 307}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "capture_logs_for_allure", "time": {"start": 1751866432909, "stop": 1751866432909, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "_session_faker", "time": {"start": 1751866415384, "stop": 1751866415419, "duration": 35}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "function_wait", "time": {"start": 1751866432909, "stop": 1751866433660, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "9537325f20d80154", "name": "接口地址", "source": "9537325f20d80154.json", "type": "application/json", "size": 52}, {"uid": "3391bf546c007952", "name": "接口名称", "source": "3391bf546c007952.json", "type": "application/json", "size": 27}, {"uid": "a6f20fe039c01021", "name": "请求方式", "source": "a6f20fe039c01021.json", "type": "application/json", "size": 3}, {"uid": "a271107eb01ee26", "name": "请求头", "source": "a271107eb01ee26.json", "type": "application/json", "size": 122}, {"uid": "7d64b07c0ff65397", "name": "<PERSON><PERSON>", "source": "7d64b07c0ff65397.json", "type": "application/json", "size": 12}, {"uid": "e2190c315389e950", "name": "测试用例名称", "source": "e2190c315389e950.json", "type": "application/json", "size": 39}, {"uid": "cb848715e2296dca", "name": "参数类型", "source": "cb848715e2296dca.json", "type": "application/json", "size": 6}, {"uid": "6a7f19685d8dd3bd", "name": "请求参数json格式", "source": "6a7f19685d8dd3bd.json", "type": "application/json", "size": 199}, {"uid": "8fd9ea05c5461013", "name": "请求参数实际入参", "source": "8fd9ea05c5461013.json", "type": "application/json", "size": 209}, {"uid": "6d3e854d1430659", "name": "接口实际响应信息", "source": "6d3e854d1430659.json", "type": "application/json", "size": 7483}, {"uid": "8f9bab3be06cb4c6", "name": "状态码断言结果：成功", "source": "8f9bab3be06cb4c6.txt", "type": "text/plain", "size": 37}, {"uid": "861d8a423bd63bbe", "name": "相等断言结果：成功", "source": "861d8a423bd63bbe.json", "type": "application/json", "size": 53}, {"uid": "b935f59162bc9fe2", "name": "📋 测试执行日志", "source": "b935f59162bc9fe2.txt", "type": "text/plain", "size": 2892}], "parameters": [], "hasContent": true, "stepsCount": 0, "attachmentsCount": 13, "shouldDisplayMessage": false}, "afterStages": [{"name": "print_info::0", "time": {"start": 1751866434256, "stop": 1751866434256, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "capture_logs_for_allure::0", "time": {"start": 1751866435009, "stop": 1751866435032, "duration": 23}, "status": "passed", "steps": [], "attachments": [{"uid": "fc853d6ba0112dd7", "name": "📋 测试执行日志 (完整记录)", "source": "fc853d6ba0112dd7.txt", "type": "text/plain", "size": 4692}], "parameters": [], "hasContent": true, "stepsCount": 0, "attachmentsCount": 1, "shouldDisplayMessage": false}, {"name": "function_wait::0", "time": {"start": 1751866434256, "stop": 1751866435007, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}], "labels": [{"name": "story", "value": "C01_演出模块流程"}, {"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "16848-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "9f3333ce3c6ea387.json", "parameterValues": []}