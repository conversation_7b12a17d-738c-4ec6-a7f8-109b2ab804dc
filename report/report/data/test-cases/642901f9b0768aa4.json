{"uid": "642901f9b0768aa4", "name": "根据场次获取取票方式", "fullName": "testcase.play.test_play.TestPlay#test_play", "historyId": "40f667e7e7d9255278cb56eb99290821", "time": {"start": 1751853905776, "stop": 1751853906248, "duration": 472}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "print_info", "time": {"start": 1751853905775, "stop": 1751853905775, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "clear_extract", "time": {"start": 1751853884946, "stop": 1751853885997, "duration": 1051}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "capture_logs_for_allure", "time": {"start": 1751853905025, "stop": 1751853905025, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "_session_faker", "time": {"start": 1751853884907, "stop": 1751853884946, "duration": 39}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "function_wait", "time": {"start": 1751853905025, "stop": 1751853905775, "duration": 750}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "301bc699b29e1b6", "name": "接口地址", "source": "301bc699b29e1b6.json", "type": "application/json", "size": 57}, {"uid": "fb5673ac6657e27a", "name": "接口名称", "source": "fb5673ac6657e27a.json", "type": "application/json", "size": 30}, {"uid": "2f5e2735efe38949", "name": "请求方式", "source": "2f5e2735efe38949.json", "type": "application/json", "size": 3}, {"uid": "67f5546081bbd544", "name": "请求头", "source": "67f5546081bbd544.json", "type": "application/json", "size": 122}, {"uid": "4b86676dfa099b2d", "name": "<PERSON><PERSON>", "source": "4b86676dfa099b2d.json", "type": "application/json", "size": 12}, {"uid": "765513bc095c53ed", "name": "测试用例名称", "source": "765513bc095c53ed.json", "type": "application/json", "size": 30}, {"uid": "91199572e88ae534", "name": "参数类型", "source": "91199572e88ae534.json", "type": "application/json", "size": 6}, {"uid": "c0be4b119a9ba3bc", "name": "请求参数json格式", "source": "c0be4b119a9ba3bc.json", "type": "application/json", "size": 199}, {"uid": "3c935285ea2c6d3f", "name": "请求参数实际入参", "source": "3c935285ea2c6d3f.json", "type": "application/json", "size": 209}, {"uid": "3ef8502150c5aaa5", "name": "接口实际响应信息", "source": "3ef8502150c5aaa5.json", "type": "application/json", "size": 599}, {"uid": "fe03b9b475530f60", "name": "状态码断言结果：成功", "source": "fe03b9b475530f60.txt", "type": "text/plain", "size": 37}, {"uid": "daf52c7bdcf3143e", "name": "相等断言结果：成功", "source": "daf52c7bdcf3143e.json", "type": "application/json", "size": 53}, {"uid": "445d6368ef4a503", "name": "📋 测试执行日志", "source": "445d6368ef4a503.txt", "type": "text/plain", "size": 5242}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentsCount": 13, "shouldDisplayMessage": false}, "afterStages": [{"name": "print_info::0", "time": {"start": 1751853906249, "stop": 1751853906250, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "capture_logs_for_allure::0", "time": {"start": 1751853907001, "stop": 1751853907015, "duration": 14}, "status": "passed", "steps": [], "attachments": [{"uid": "cdd8607e98deb1b4", "name": "📋 测试执行日志 (完整记录)", "source": "cdd8607e98deb1b4.txt", "type": "text/plain", "size": 2608}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentsCount": 1, "shouldDisplayMessage": false}, {"name": "function_wait::0", "time": {"start": 1751853906250, "stop": 1751853907001, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}], "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "39132-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "642901f9b0768aa4.json", "parameterValues": []}