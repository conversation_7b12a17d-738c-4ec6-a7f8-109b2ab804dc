{"uid": "630053f790a508f", "name": "近期特惠", "fullName": "testcase.play.test_play.TestPlay#test_play", "historyId": "b9dc0ab2757f5b153151bfbd4cc293dd", "time": {"start": 1751852460603, "stop": 1751852461345, "duration": 742}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "capture_logs_for_allure", "time": {"start": 1751852459852, "stop": 1751852459852, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "print_info", "time": {"start": 1751852460603, "stop": 1751852460603, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "_session_faker", "time": {"start": 1751852455733, "stop": 1751852455863, "duration": 130}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "function_wait", "time": {"start": 1751852459852, "stop": 1751852460603, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "clear_extract", "time": {"start": 1751852455863, "stop": 1751852456123, "duration": 260}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "aaf33b5c8e500890", "name": "接口地址", "source": "aaf33b5c8e500890.json", "type": "application/json", "size": 54}, {"uid": "bc502306595a74e3", "name": "接口名称", "source": "bc502306595a74e3.json", "type": "application/json", "size": 12}, {"uid": "1b1b2f76fd88f29", "name": "请求方式", "source": "1b1b2f76fd88f29.json", "type": "application/json", "size": 4}, {"uid": "bdb96eee5b316f3f", "name": "请求头", "source": "bdb96eee5b316f3f.json", "type": "application/json", "size": 122}, {"uid": "4d4ae2459be9e1e", "name": "<PERSON><PERSON>", "source": "4d4ae2459be9e1e.json", "type": "application/json", "size": 12}, {"uid": "454cbcd45ce64109", "name": "测试用例名称", "source": "454cbcd45ce64109.json", "type": "application/json", "size": 30}, {"uid": "a955ee04f11649e4", "name": "参数类型", "source": "a955ee04f11649e4.json", "type": "application/json", "size": 4}, {"uid": "c1f9bf6eb51a0823", "name": "请求参数json格式", "source": "c1f9bf6eb51a0823.json", "type": "application/json", "size": 214}, {"uid": "903e62a52129cef5", "name": "请求参数实际入参", "source": "903e62a52129cef5.json", "type": "application/json", "size": 224}, {"uid": "23810b8914718e24", "name": "接口实际响应信息", "source": "23810b8914718e24.json", "type": "application/json", "size": 22188}, {"uid": "e1ba401c3253ac94", "name": "状态码断言结果：成功", "source": "e1ba401c3253ac94.txt", "type": "text/plain", "size": 37}, {"uid": "3378e0851adedb18", "name": "相等断言结果：成功", "source": "3378e0851adedb18.json", "type": "application/json", "size": 53}, {"uid": "787c5865d812fd84", "name": "📋 测试执行日志", "source": "787c5865d812fd84.txt", "type": "text/plain", "size": 14976}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 13, "hasContent": true, "stepsCount": 0}, "afterStages": [{"name": "capture_logs_for_allure::0", "time": {"start": 1751852462097, "stop": 1751852462098, "duration": 1}, "status": "passed", "steps": [], "attachments": [{"uid": "7f9013c0c2ecc185", "name": "📋 测试执行日志 (完整记录)", "source": "7f9013c0c2ecc185.txt", "type": "text/plain", "size": 15295}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 1, "hasContent": true, "stepsCount": 0}, {"name": "print_info::0", "time": {"start": 1751852461345, "stop": 1751852461346, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "function_wait::0", "time": {"start": 1751852461346, "stop": 1751852462097, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}], "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "38092-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "630053f790a508f.json", "parameterValues": []}