{"uid": "b3b0e81dd2545627", "name": "今日必抢", "fullName": "testcase.play.test_play.TestPlay#test_play", "historyId": "09729bd43bb947d1ebe3c16a80dc6e25", "time": {"start": 1751853232057, "stop": 1751853232377, "duration": 320}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "print_info", "time": {"start": 1751853232056, "stop": 1751853232056, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "capture_logs_for_allure", "time": {"start": 1751853231305, "stop": 1751853231305, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "_session_faker", "time": {"start": 1751853229094, "stop": 1751853229142, "duration": 48}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "clear_extract", "time": {"start": 1751853229143, "stop": 1751853229413, "duration": 270}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "function_wait", "time": {"start": 1751853231305, "stop": 1751853232056, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "2dca88c39b8cf4c1", "name": "接口地址", "source": "2dca88c39b8cf4c1.json", "type": "application/json", "size": 55}, {"uid": "5096f67275fc39ee", "name": "接口名称", "source": "5096f67275fc39ee.json", "type": "application/json", "size": 12}, {"uid": "d37a02d3b9807014", "name": "请求方式", "source": "d37a02d3b9807014.json", "type": "application/json", "size": 4}, {"uid": "a1b4a713039634c7", "name": "请求头", "source": "a1b4a713039634c7.json", "type": "application/json", "size": 122}, {"uid": "bd899e715d7f2a9f", "name": "<PERSON><PERSON>", "source": "bd899e715d7f2a9f.json", "type": "application/json", "size": 12}, {"uid": "88b5e8d8ec6f053f", "name": "测试用例名称", "source": "88b5e8d8ec6f053f.json", "type": "application/json", "size": 30}, {"uid": "96b2503c38fb7f33", "name": "参数类型", "source": "96b2503c38fb7f33.json", "type": "application/json", "size": 4}, {"uid": "ace68bfbb694b316", "name": "请求参数json格式", "source": "ace68bfbb694b316.json", "type": "application/json", "size": 214}, {"uid": "206af2dccd94946d", "name": "请求参数实际入参", "source": "206af2dccd94946d.json", "type": "application/json", "size": 224}, {"uid": "37f145dda8a9cf51", "name": "接口实际响应信息", "source": "37f145dda8a9cf51.json", "type": "application/json", "size": 22188}, {"uid": "36262e82f7974040", "name": "状态码断言结果：成功", "source": "36262e82f7974040.txt", "type": "text/plain", "size": 37}, {"uid": "7f2656f1b4e82ce5", "name": "相等断言结果：成功", "source": "7f2656f1b4e82ce5.json", "type": "application/json", "size": 53}, {"uid": "7ccdb80624cd56a6", "name": "📋 测试执行日志", "source": "7ccdb80624cd56a6.txt", "type": "text/plain", "size": 4525}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 13, "hasContent": true, "stepsCount": 0}, "afterStages": [{"name": "print_info::0", "time": {"start": 1751853232378, "stop": 1751853232378, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "capture_logs_for_allure::0", "time": {"start": 1751853233131, "stop": 1751853233143, "duration": 12}, "status": "passed", "steps": [], "attachments": [{"uid": "fa5582312b30804b", "name": "📋 测试执行日志 (完整记录)", "source": "fa5582312b30804b.txt", "type": "text/plain", "size": 15197}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 1, "hasContent": true, "stepsCount": 0}, {"name": "function_wait::0", "time": {"start": 1751853232378, "stop": 1751853233130, "duration": 752}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}], "labels": [{"name": "story", "value": "C01_演出模块流程"}, {"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "28812-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "b3b0e81dd2545627.json", "parameterValues": []}