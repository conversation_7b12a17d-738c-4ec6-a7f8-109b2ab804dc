{"uid": "55a09c5d371dccf7", "name": "今日必抢", "fullName": "testcase.play.test_play.TestPlay#test_play", "historyId": "09729bd43bb947d1ebe3c16a80dc6e25", "time": {"start": 1751853888634, "stop": 1751853888938, "duration": 304}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "function_wait", "time": {"start": 1751853887883, "stop": 1751853888634, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "print_info", "time": {"start": 1751853888634, "stop": 1751853888634, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "capture_logs_for_allure", "time": {"start": 1751853887883, "stop": 1751853887883, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "clear_extract", "time": {"start": 1751853884946, "stop": 1751853885997, "duration": 1051}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "_session_faker", "time": {"start": 1751853884907, "stop": 1751853884946, "duration": 39}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "527da655076aaef", "name": "接口地址", "source": "527da655076aaef.json", "type": "application/json", "size": 55}, {"uid": "be0f37d127f6e4ae", "name": "接口名称", "source": "be0f37d127f6e4ae.json", "type": "application/json", "size": 12}, {"uid": "5169bd18d29a1ea9", "name": "请求方式", "source": "5169bd18d29a1ea9.json", "type": "application/json", "size": 4}, {"uid": "d3520acc8d2fadc2", "name": "请求头", "source": "d3520acc8d2fadc2.json", "type": "application/json", "size": 122}, {"uid": "ee4e9321a10abae7", "name": "<PERSON><PERSON>", "source": "ee4e9321a10abae7.json", "type": "application/json", "size": 12}, {"uid": "dcbb64f2f202208a", "name": "测试用例名称", "source": "dcbb64f2f202208a.json", "type": "application/json", "size": 30}, {"uid": "9355eee9694edd66", "name": "参数类型", "source": "9355eee9694edd66.json", "type": "application/json", "size": 4}, {"uid": "a4cde7499e1ebedc", "name": "请求参数json格式", "source": "a4cde7499e1ebedc.json", "type": "application/json", "size": 214}, {"uid": "eca27cb7c4f1222f", "name": "请求参数实际入参", "source": "eca27cb7c4f1222f.json", "type": "application/json", "size": 224}, {"uid": "1645e7434965c8aa", "name": "接口实际响应信息", "source": "1645e7434965c8aa.json", "type": "application/json", "size": 22188}, {"uid": "92840ee1b94a1bd9", "name": "状态码断言结果：成功", "source": "92840ee1b94a1bd9.txt", "type": "text/plain", "size": 37}, {"uid": "34279d49d6c309a2", "name": "相等断言结果：成功", "source": "34279d49d6c309a2.json", "type": "application/json", "size": 53}, {"uid": "39b953a80a7d9156", "name": "📋 测试执行日志", "source": "39b953a80a7d9156.txt", "type": "text/plain", "size": 4327}], "parameters": [], "hasContent": true, "stepsCount": 0, "attachmentsCount": 13, "shouldDisplayMessage": false}, "afterStages": [{"name": "function_wait::0", "time": {"start": 1751853888939, "stop": 1751853889690, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "print_info::0", "time": {"start": 1751853888938, "stop": 1751853888939, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "capture_logs_for_allure::0", "time": {"start": 1751853889690, "stop": 1751853889699, "duration": 9}, "status": "passed", "steps": [], "attachments": [{"uid": "8ed68c4ca1b579cd", "name": "📋 测试执行日志 (完整记录)", "source": "8ed68c4ca1b579cd.txt", "type": "text/plain", "size": 15249}], "parameters": [], "hasContent": true, "stepsCount": 0, "attachmentsCount": 1, "shouldDisplayMessage": false}], "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "39132-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "55a09c5d371dccf7.json", "parameterValues": []}