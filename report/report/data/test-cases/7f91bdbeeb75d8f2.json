{"uid": "7f91bdbeeb75d8f2", "name": "**搜索**", "fullName": "testcase.play.test_play.TestPlay#test_play", "historyId": "80ea16c71eb34b6e85110c3f875859f1", "time": {"start": 1751853897749, "stop": 1751853898049, "duration": 300}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "function_wait", "time": {"start": 1751853896996, "stop": 1751853897747, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "capture_logs_for_allure", "time": {"start": 1751853896996, "stop": 1751853896996, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "clear_extract", "time": {"start": 1751853884946, "stop": 1751853885997, "duration": 1051}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "_session_faker", "time": {"start": 1751853884907, "stop": 1751853884946, "duration": 39}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "print_info", "time": {"start": 1751853897747, "stop": 1751853897749, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "8d41ab19545965d6", "name": "接口地址", "source": "8d41ab19545965d6.json", "type": "application/json", "size": 53}, {"uid": "a8395a95d292480", "name": "接口名称", "source": "a8395a95d292480.json", "type": "application/json", "size": 10}, {"uid": "5e1e7b3294e6bede", "name": "请求方式", "source": "5e1e7b3294e6bede.json", "type": "application/json", "size": 4}, {"uid": "5b9f141b1e346ae6", "name": "请求头", "source": "5b9f141b1e346ae6.json", "type": "application/json", "size": 122}, {"uid": "9fd3f14306e43f2f", "name": "<PERSON><PERSON>", "source": "9fd3f14306e43f2f.json", "type": "application/json", "size": 12}, {"uid": "a376f6a5317af3a5", "name": "测试用例名称", "source": "a376f6a5317af3a5.json", "type": "application/json", "size": 30}, {"uid": "8c2c90077c38ae9b", "name": "参数类型", "source": "8c2c90077c38ae9b.json", "type": "application/json", "size": 4}, {"uid": "c0db981558ffedc9", "name": "请求参数json格式", "source": "c0db981558ffedc9.json", "type": "application/json", "size": 201}, {"uid": "75d6d11e95a0f5b0", "name": "请求参数实际入参", "source": "75d6d11e95a0f5b0.json", "type": "application/json", "size": 211}, {"uid": "f5cc58b8ac2a680", "name": "接口实际响应信息", "source": "f5cc58b8ac2a680.json", "type": "application/json", "size": 22387}, {"uid": "f53c0bec251c8d16", "name": "状态码断言结果：成功", "source": "f53c0bec251c8d16.txt", "type": "text/plain", "size": 37}, {"uid": "b3d84383c3fca4ec", "name": "相等断言结果：成功", "source": "b3d84383c3fca4ec.json", "type": "application/json", "size": 53}, {"uid": "ee9dba5a7cf08d36", "name": "📋 测试执行日志", "source": "ee9dba5a7cf08d36.txt", "type": "text/plain", "size": 14921}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentsCount": 13, "shouldDisplayMessage": false}, "afterStages": [{"name": "function_wait::0", "time": {"start": 1751853898050, "stop": 1751853898801, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "capture_logs_for_allure::0", "time": {"start": 1751853898801, "stop": 1751853898810, "duration": 9}, "status": "passed", "steps": [], "attachments": [{"uid": "7497b07eae3bd0c2", "name": "📋 测试执行日志 (完整记录)", "source": "7497b07eae3bd0c2.txt", "type": "text/plain", "size": 15160}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentsCount": 1, "shouldDisplayMessage": false}, {"name": "print_info::0", "time": {"start": 1751853898050, "stop": 1751853898050, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}], "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "39132-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "7f91bdbeeb75d8f2.json", "parameterValues": []}