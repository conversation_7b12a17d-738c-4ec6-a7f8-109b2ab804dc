{"uid": "4f3e509b1795f4a0", "name": "下单", "fullName": "testcase.play.test_play.TestPlay#test_play", "historyId": "df78d83b2c7879c3a2ecdde06190a07c", "time": {"start": 1751866438347, "stop": 1751866440058, "duration": 1711}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "function_wait", "time": {"start": 1751866437595, "stop": 1751866438346, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "clear_extract", "time": {"start": 1751866415419, "stop": 1751866415726, "duration": 307}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "_session_faker", "time": {"start": 1751866415384, "stop": 1751866415419, "duration": 35}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "print_info", "time": {"start": 1751866438346, "stop": 1751866438347, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "capture_logs_for_allure", "time": {"start": 1751866437595, "stop": 1751866437595, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "6db5d12a601383f9", "name": "接口地址", "source": "6db5d12a601383f9.json", "type": "application/json", "size": 42}, {"uid": "af7f50545fd8eacb", "name": "接口名称", "source": "af7f50545fd8eacb.json", "type": "application/json", "size": 6}, {"uid": "bbbb5f5c353cec1f", "name": "请求方式", "source": "bbbb5f5c353cec1f.json", "type": "application/json", "size": 4}, {"uid": "9bd9bfe49f5f2c1f", "name": "请求头", "source": "9bd9bfe49f5f2c1f.json", "type": "application/json", "size": 122}, {"uid": "34bcab6e290b3889", "name": "<PERSON><PERSON>", "source": "34bcab6e290b3889.json", "type": "application/json", "size": 12}, {"uid": "2e5cb7ca52edb260", "name": "测试用例名称", "source": "2e5cb7ca52edb260.json", "type": "application/json", "size": 12}, {"uid": "f1c4f0494eead9f6", "name": "参数类型", "source": "f1c4f0494eead9f6.json", "type": "application/json", "size": 4}, {"uid": "fb11c7002ae37be", "name": "请求参数json格式", "source": "fb11c7002ae37be.json", "type": "application/json", "size": 615}, {"uid": "d2e32403410de10a", "name": "请求参数实际入参", "source": "d2e32403410de10a.json", "type": "application/json", "size": 625}, {"uid": "d86b0bd3c934b76e", "name": "接口实际响应信息", "source": "d86b0bd3c934b76e.json", "type": "application/json", "size": 94}, {"uid": "823942342ecc2193", "name": "状态码断言结果：成功", "source": "823942342ecc2193.txt", "type": "text/plain", "size": 37}, {"uid": "cfdedb6f5b9a362c", "name": "相等断言结果：成功", "source": "cfdedb6f5b9a362c.json", "type": "application/json", "size": 53}, {"uid": "8ab7ad3552d3a3f2", "name": "📋 测试执行日志", "source": "8ab7ad3552d3a3f2.txt", "type": "text/plain", "size": 2377}], "parameters": [], "hasContent": true, "stepsCount": 0, "attachmentsCount": 13, "shouldDisplayMessage": false}, "afterStages": [{"name": "function_wait::0", "time": {"start": 1751866440059, "stop": 1751866440810, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "print_info::0", "time": {"start": 1751866440058, "stop": 1751866440058, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "capture_logs_for_allure::0", "time": {"start": 1751866440814, "stop": 1751866440841, "duration": 27}, "status": "passed", "steps": [], "attachments": [{"uid": "9290e3de9248d60f", "name": "📋 测试执行日志 (完整记录)", "source": "9290e3de9248d60f.txt", "type": "text/plain", "size": 2439}], "parameters": [], "hasContent": true, "stepsCount": 0, "attachmentsCount": 1, "shouldDisplayMessage": false}], "labels": [{"name": "story", "value": "C01_演出模块流程"}, {"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "16848-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "4f3e509b1795f4a0.json", "parameterValues": []}