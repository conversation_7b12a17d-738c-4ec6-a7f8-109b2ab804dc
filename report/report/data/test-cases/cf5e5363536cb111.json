{"uid": "cf5e5363536cb111", "name": "今日必抢", "fullName": "testcase.play.test_play.TestPlay#test_play", "historyId": "240ea0a77309ba41fd9d13d8f2320d63", "time": {"start": 1751852993830, "stop": 1751852994144, "duration": 314}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "function_wait", "time": {"start": 1751852993079, "stop": 1751852993830, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "capture_logs_for_allure", "time": {"start": 1751852993079, "stop": 1751852993079, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "clear_extract", "time": {"start": 1751852990929, "stop": 1751852991206, "duration": 277}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "_session_faker", "time": {"start": 1751852990879, "stop": 1751852990927, "duration": 48}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "print_info", "time": {"start": 1751852993830, "stop": 1751852993830, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "965d310156899704", "name": "接口地址", "source": "965d310156899704.json", "type": "application/json", "size": 55}, {"uid": "a5ff703ea2a9d1c0", "name": "接口名称", "source": "a5ff703ea2a9d1c0.json", "type": "application/json", "size": 12}, {"uid": "d2feadec7d8a0d65", "name": "请求方式", "source": "d2feadec7d8a0d65.json", "type": "application/json", "size": 4}, {"uid": "e6b97bc9b099e4a7", "name": "请求头", "source": "e6b97bc9b099e4a7.json", "type": "application/json", "size": 122}, {"uid": "b8c69c1a56cd85a4", "name": "<PERSON><PERSON>", "source": "b8c69c1a56cd85a4.json", "type": "application/json", "size": 12}, {"uid": "9e4d607cafe43f76", "name": "测试用例名称", "source": "9e4d607cafe43f76.json", "type": "application/json", "size": 30}, {"uid": "744a8b4b8e60760", "name": "参数类型", "source": "744a8b4b8e60760.json", "type": "application/json", "size": 4}, {"uid": "8612c7cb245ca4ff", "name": "请求参数json格式", "source": "8612c7cb245ca4ff.json", "type": "application/json", "size": 214}, {"uid": "7e4a2885063b08cf", "name": "请求参数实际入参", "source": "7e4a2885063b08cf.json", "type": "application/json", "size": 224}, {"uid": "925b09f05a4e8c01", "name": "接口实际响应信息", "source": "925b09f05a4e8c01.json", "type": "application/json", "size": 22188}, {"uid": "5e0d0ce6dda621d3", "name": "状态码断言结果：成功", "source": "5e0d0ce6dda621d3.txt", "type": "text/plain", "size": 37}, {"uid": "c5e7e3642c3a7d29", "name": "相等断言结果：成功", "source": "c5e7e3642c3a7d29.json", "type": "application/json", "size": 53}, {"uid": "78b3c149e35cc250", "name": "📋 测试执行日志", "source": "78b3c149e35cc250.txt", "type": "text/plain", "size": 4097}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 13, "hasContent": true, "stepsCount": 0}, "afterStages": [{"name": "function_wait::0", "time": {"start": 1751852994146, "stop": 1751852994896, "duration": 750}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "capture_logs_for_allure::0", "time": {"start": 1751852994897, "stop": 1751852994912, "duration": 15}, "status": "passed", "steps": [], "attachments": [{"uid": "e260c703b9bc36dc", "name": "📋 测试执行日志 (完整记录)", "source": "e260c703b9bc36dc.txt", "type": "text/plain", "size": 14716}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 1, "hasContent": true, "stepsCount": 0}, {"name": "print_info::0", "time": {"start": 1751852994145, "stop": 1751852994145, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}], "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "37476-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [{"name": "api_info", "value": "{'baseInfo': {'module': '演出票', 'api_name': '今日必抢', 'host': '${get_host(play)}', 'url': '/api/show/info/home_today_sale_list', 'method': 'post', 'headers': '${get_headers(app)}'}, 'testCase': {'case_name': '获取今日必抢演出列表', 'validation': [{'code': 200}, {'eq': {'code': 0}}], 'extract': {'extract_auto': 'home_today_sale_list'}, 'extract_list': np.float64(0.0), 'data': {'page': 1, 'pageSize': 20, 'channelCode': '${appKey()}', 'token': '${get_extract_data(token)}'}}}"}], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "cf5e5363536cb111.json", "parameterValues": ["{'baseInfo': {'module': '演出票', 'api_name': '今日必抢', 'host': '${get_host(play)}', 'url': '/api/show/info/home_today_sale_list', 'method': 'post', 'headers': '${get_headers(app)}'}, 'testCase': {'case_name': '获取今日必抢演出列表', 'validation': [{'code': 200}, {'eq': {'code': 0}}], 'extract': {'extract_auto': 'home_today_sale_list'}, 'extract_list': np.float64(0.0), 'data': {'page': 1, 'pageSize': 20, 'channelCode': '${appKey()}', 'token': '${get_extract_data(token)}'}}}"]}