{"uid": "37b174fdfcced18e", "name": "下单", "fullName": "testcase.play.test_play.TestPlay#test_play", "historyId": "df78d83b2c7879c3a2ecdde06190a07c", "time": {"start": 1751853251105, "stop": 1751853252391, "duration": 1286}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "print_info", "time": {"start": 1751853251104, "stop": 1751853251104, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "function_wait", "time": {"start": 1751853250354, "stop": 1751853251104, "duration": 750}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "_session_faker", "time": {"start": 1751853229094, "stop": 1751853229142, "duration": 48}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "capture_logs_for_allure", "time": {"start": 1751853250353, "stop": 1751853250353, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "clear_extract", "time": {"start": 1751853229143, "stop": 1751853229413, "duration": 270}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "81831304406e2af3", "name": "接口地址", "source": "81831304406e2af3.json", "type": "application/json", "size": 42}, {"uid": "2b38858772ca81e4", "name": "接口名称", "source": "2b38858772ca81e4.json", "type": "application/json", "size": 6}, {"uid": "7979b58c06efa94", "name": "请求方式", "source": "7979b58c06efa94.json", "type": "application/json", "size": 4}, {"uid": "d7bc7f7e366ac39e", "name": "请求头", "source": "d7bc7f7e366ac39e.json", "type": "application/json", "size": 122}, {"uid": "459dd9cb9bd16aa2", "name": "<PERSON><PERSON>", "source": "459dd9cb9bd16aa2.json", "type": "application/json", "size": 12}, {"uid": "2193bcb44779d60", "name": "测试用例名称", "source": "2193bcb44779d60.json", "type": "application/json", "size": 12}, {"uid": "b937f47191d5e208", "name": "参数类型", "source": "b937f47191d5e208.json", "type": "application/json", "size": 4}, {"uid": "647a1c491702354c", "name": "请求参数json格式", "source": "647a1c491702354c.json", "type": "application/json", "size": 615}, {"uid": "cb9b769e9bb6487f", "name": "请求参数实际入参", "source": "cb9b769e9bb6487f.json", "type": "application/json", "size": 625}, {"uid": "3c04123fd8f362bc", "name": "接口实际响应信息", "source": "3c04123fd8f362bc.json", "type": "application/json", "size": 94}, {"uid": "4dc73374b3a8e6ef", "name": "状态码断言结果：成功", "source": "4dc73374b3a8e6ef.txt", "type": "text/plain", "size": 37}, {"uid": "e4d7071f48fe12d1", "name": "相等断言结果：成功", "source": "e4d7071f48fe12d1.json", "type": "application/json", "size": 53}, {"uid": "f8743ba5fca2dcf3", "name": "📋 测试执行日志", "source": "f8743ba5fca2dcf3.txt", "type": "text/plain", "size": 2801}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 13, "hasContent": true, "stepsCount": 0}, "afterStages": [{"name": "print_info::0", "time": {"start": 1751853252392, "stop": 1751853252392, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "function_wait::0", "time": {"start": 1751853252392, "stop": 1751853253143, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "capture_logs_for_allure::0", "time": {"start": 1751853253143, "stop": 1751853253155, "duration": 12}, "status": "passed", "steps": [], "attachments": [{"uid": "dbd627f6fbbb1fea", "name": "📋 测试执行日志 (完整记录)", "source": "dbd627f6fbbb1fea.txt", "type": "text/plain", "size": 2922}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 1, "hasContent": true, "stepsCount": 0}], "labels": [{"name": "story", "value": "C01_演出模块流程"}, {"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "28812-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "37b174fdfcced18e.json", "parameterValues": []}