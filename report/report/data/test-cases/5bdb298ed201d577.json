{"uid": "5bdb298ed201d577", "name": "演出场次列表", "fullName": "testcase.play.test_play.TestPlay#test_play", "historyId": "ce6724bce5907ddc1a7c9b8a022c9530", "time": {"start": 1751866431593, "stop": 1751866432144, "duration": 551}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "function_wait", "time": {"start": 1751866430842, "stop": 1751866431592, "duration": 750}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "clear_extract", "time": {"start": 1751866415419, "stop": 1751866415726, "duration": 307}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "_session_faker", "time": {"start": 1751866415384, "stop": 1751866415419, "duration": 35}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "print_info", "time": {"start": 1751866431592, "stop": 1751866431592, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "capture_logs_for_allure", "time": {"start": 1751866430841, "stop": 1751866430842, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "9037d94924ee215d", "name": "接口地址", "source": "9037d94924ee215d.json", "type": "application/json", "size": 52}, {"uid": "c724f378779d8e9f", "name": "接口名称", "source": "c724f378779d8e9f.json", "type": "application/json", "size": 18}, {"uid": "a2da9e88687bd869", "name": "请求方式", "source": "a2da9e88687bd869.json", "type": "application/json", "size": 3}, {"uid": "2c753aa9ff14ec11", "name": "请求头", "source": "2c753aa9ff14ec11.json", "type": "application/json", "size": 122}, {"uid": "955a0b7e0cd6d404", "name": "<PERSON><PERSON>", "source": "955a0b7e0cd6d404.json", "type": "application/json", "size": 12}, {"uid": "5ee49e24749da994", "name": "测试用例名称", "source": "5ee49e24749da994.json", "type": "application/json", "size": 30}, {"uid": "13b5627b2458ff00", "name": "参数类型", "source": "13b5627b2458ff00.json", "type": "application/json", "size": 6}, {"uid": "e9d347c59c418f93", "name": "请求参数json格式", "source": "e9d347c59c418f93.json", "type": "application/json", "size": 206}, {"uid": "a9c09745615f4a7", "name": "请求参数实际入参", "source": "a9c09745615f4a7.json", "type": "application/json", "size": 216}, {"uid": "dc93ca892f007bcb", "name": "接口实际响应信息", "source": "dc93ca892f007bcb.json", "type": "application/json", "size": 1528}, {"uid": "4ac8eb03611d2681", "name": "状态码断言结果：成功", "source": "4ac8eb03611d2681.txt", "type": "text/plain", "size": 37}, {"uid": "115c8d40633e4d35", "name": "相等断言结果：成功", "source": "115c8d40633e4d35.json", "type": "application/json", "size": 53}, {"uid": "64bd534adcd7d16", "name": "📋 测试执行日志", "source": "64bd534adcd7d16.txt", "type": "text/plain", "size": 5822}], "parameters": [], "hasContent": true, "stepsCount": 0, "attachmentsCount": 13, "shouldDisplayMessage": false}, "afterStages": [{"name": "function_wait::0", "time": {"start": 1751866432145, "stop": 1751866432896, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "print_info::0", "time": {"start": 1751866432144, "stop": 1751866432145, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "capture_logs_for_allure::0", "time": {"start": 1751866432896, "stop": 1751866432906, "duration": 10}, "status": "passed", "steps": [], "attachments": [{"uid": "44452fa07978b67b", "name": "📋 测试执行日志 (完整记录)", "source": "44452fa07978b67b.txt", "type": "text/plain", "size": 2892}], "parameters": [], "hasContent": true, "stepsCount": 0, "attachmentsCount": 1, "shouldDisplayMessage": false}], "labels": [{"name": "story", "value": "C01_演出模块流程"}, {"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "16848-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "5bdb298ed201d577.json", "parameterValues": []}