{"uid": "8a22b37c9cd603f3", "name": "为你推荐", "fullName": "testcase.play.test_play.TestPlay#test_play", "historyId": "a03ba38a51bcc9af12332b9cb9ea6b41", "time": {"start": 1751853235620, "stop": 1751853235932, "duration": 312}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "print_info", "time": {"start": 1751853235620, "stop": 1751853235620, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "function_wait", "time": {"start": 1751853234870, "stop": 1751853235620, "duration": 750}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "_session_faker", "time": {"start": 1751853229094, "stop": 1751853229142, "duration": 48}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "clear_extract", "time": {"start": 1751853229143, "stop": 1751853229413, "duration": 270}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "capture_logs_for_allure", "time": {"start": 1751853234870, "stop": 1751853234870, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "9a8aba19f5e96857", "name": "接口地址", "source": "9a8aba19f5e96857.json", "type": "application/json", "size": 54}, {"uid": "ff091f899e45cb9b", "name": "接口名称", "source": "ff091f899e45cb9b.json", "type": "application/json", "size": 12}, {"uid": "b25f3c87ea51afb7", "name": "请求方式", "source": "b25f3c87ea51afb7.json", "type": "application/json", "size": 4}, {"uid": "6fff2456f7dc635", "name": "请求头", "source": "6fff2456f7dc635.json", "type": "application/json", "size": 122}, {"uid": "5a2849914ac06ae", "name": "<PERSON><PERSON>", "source": "5a2849914ac06ae.json", "type": "application/json", "size": 12}, {"uid": "53274376c0cba591", "name": "测试用例名称", "source": "53274376c0cba591.json", "type": "application/json", "size": 30}, {"uid": "d9b15d6f6430bdd1", "name": "参数类型", "source": "d9b15d6f6430bdd1.json", "type": "application/json", "size": 4}, {"uid": "61e6cf799b0d212e", "name": "请求参数json格式", "source": "61e6cf799b0d212e.json", "type": "application/json", "size": 214}, {"uid": "23c5a6dbac7f1b1e", "name": "请求参数实际入参", "source": "23c5a6dbac7f1b1e.json", "type": "application/json", "size": 224}, {"uid": "c833a89ccd144857", "name": "接口实际响应信息", "source": "c833a89ccd144857.json", "type": "application/json", "size": 44012}, {"uid": "ed461942cdb5b93b", "name": "状态码断言结果：成功", "source": "ed461942cdb5b93b.txt", "type": "text/plain", "size": 37}, {"uid": "f4d28c69938e8487", "name": "相等断言结果：成功", "source": "f4d28c69938e8487.json", "type": "application/json", "size": 53}, {"uid": "f0494dfe3868fe4d", "name": "📋 测试执行日志", "source": "f0494dfe3868fe4d.txt", "type": "text/plain", "size": 15149}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 13, "hasContent": true, "stepsCount": 0}, "afterStages": [{"name": "print_info::0", "time": {"start": 1751853235933, "stop": 1751853235933, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "function_wait::0", "time": {"start": 1751853235934, "stop": 1751853236684, "duration": 750}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "capture_logs_for_allure::0", "time": {"start": 1751853236684, "stop": 1751853236692, "duration": 8}, "status": "passed", "steps": [], "attachments": [{"uid": "2751dfe7adac6807", "name": "📋 测试执行日志 (完整记录)", "source": "2751dfe7adac6807.txt", "type": "text/plain", "size": 27530}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 1, "hasContent": true, "stepsCount": 0}], "labels": [{"name": "story", "value": "C01_演出模块流程"}, {"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "28812-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "8a22b37c9cd603f3.json", "parameterValues": []}