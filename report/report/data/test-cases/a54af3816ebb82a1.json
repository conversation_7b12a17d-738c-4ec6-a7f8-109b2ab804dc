{"uid": "a54af3816ebb82a1", "name": "演唱会分类", "fullName": "testcase.play.test_play.TestPlay#test_play", "historyId": "47b52c96aa0c1d59c25f4bc63959c980", "time": {"start": 1751853239249, "stop": 1751853239571, "duration": 322}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "print_info", "time": {"start": 1751853239249, "stop": 1751853239249, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "capture_logs_for_allure", "time": {"start": 1751853238499, "stop": 1751853238499, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "function_wait", "time": {"start": 1751853238499, "stop": 1751853239249, "duration": 750}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "_session_faker", "time": {"start": 1751853229094, "stop": 1751853229142, "duration": 48}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "clear_extract", "time": {"start": 1751853229143, "stop": 1751853229413, "duration": 270}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "b9cef33db8ef4e80", "name": "接口地址", "source": "b9cef33db8ef4e80.json", "type": "application/json", "size": 53}, {"uid": "e2a6bcff28d1fa52", "name": "接口名称", "source": "e2a6bcff28d1fa52.json", "type": "application/json", "size": 15}, {"uid": "ae98db7bc298af30", "name": "请求方式", "source": "ae98db7bc298af30.json", "type": "application/json", "size": 4}, {"uid": "3c899aaebbdffcf7", "name": "请求头", "source": "3c899aaebbdffcf7.json", "type": "application/json", "size": 122}, {"uid": "86cd37a96c11b9b4", "name": "<PERSON><PERSON>", "source": "86cd37a96c11b9b4.json", "type": "application/json", "size": 12}, {"uid": "319f6edb6c93f78e", "name": "测试用例名称", "source": "319f6edb6c93f78e.json", "type": "application/json", "size": 25}, {"uid": "982a9c6624b71db5", "name": "参数类型", "source": "982a9c6624b71db5.json", "type": "application/json", "size": 4}, {"uid": "797d5a7c616f769c", "name": "请求参数json格式", "source": "797d5a7c616f769c.json", "type": "application/json", "size": 207}, {"uid": "2848bc6b7a01e34e", "name": "请求参数实际入参", "source": "2848bc6b7a01e34e.json", "type": "application/json", "size": 217}, {"uid": "724b4b3a318d5f98", "name": "接口实际响应信息", "source": "724b4b3a318d5f98.json", "type": "application/json", "size": 22018}, {"uid": "b6bcf5eb82f7b43c", "name": "状态码断言结果：成功", "source": "b6bcf5eb82f7b43c.txt", "type": "text/plain", "size": 37}, {"uid": "c17bc08af90341aa", "name": "相等断言结果：成功", "source": "c17bc08af90341aa.json", "type": "application/json", "size": 53}, {"uid": "ed1068a06bfb8c7d", "name": "📋 测试执行日志", "source": "ed1068a06bfb8c7d.txt", "type": "text/plain", "size": 4956}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 13, "hasContent": true, "stepsCount": 0}, "afterStages": [{"name": "print_info::0", "time": {"start": 1751853239572, "stop": 1751853239572, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "capture_logs_for_allure::0", "time": {"start": 1751853240323, "stop": 1751853240332, "duration": 9}, "status": "passed", "steps": [], "attachments": [{"uid": "456409b675c5a2c0", "name": "📋 测试执行日志 (完整记录)", "source": "456409b675c5a2c0.txt", "type": "text/plain", "size": 15022}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 1, "hasContent": true, "stepsCount": 0}, {"name": "function_wait::0", "time": {"start": 1751853239573, "stop": 1751853240323, "duration": 750}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}], "labels": [{"name": "story", "value": "C01_演出模块流程"}, {"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "28812-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "a54af3816ebb82a1.json", "parameterValues": []}