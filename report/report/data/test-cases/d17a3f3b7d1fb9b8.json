{"uid": "d17a3f3b7d1fb9b8", "name": "根据场次获取取票方式", "fullName": "testcase.play.test_play.TestPlay#test_play", "historyId": "40f667e7e7d9255278cb56eb99290821", "time": {"start": 1751852476394, "stop": 1751852476878, "duration": 484}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "function_wait", "time": {"start": 1751852475642, "stop": 1751852476393, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "print_info", "time": {"start": 1751852476393, "stop": 1751852476393, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "capture_logs_for_allure", "time": {"start": 1751852475642, "stop": 1751852475642, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "_session_faker", "time": {"start": 1751852455733, "stop": 1751852455863, "duration": 130}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "clear_extract", "time": {"start": 1751852455863, "stop": 1751852456123, "duration": 260}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "d5d51bccbbe3de82", "name": "接口地址", "source": "d5d51bccbbe3de82.json", "type": "application/json", "size": 57}, {"uid": "1697a61a7f21b4dd", "name": "接口名称", "source": "1697a61a7f21b4dd.json", "type": "application/json", "size": 30}, {"uid": "678ceb0ba97a91df", "name": "请求方式", "source": "678ceb0ba97a91df.json", "type": "application/json", "size": 3}, {"uid": "c3c5503f3f6925f4", "name": "请求头", "source": "c3c5503f3f6925f4.json", "type": "application/json", "size": 122}, {"uid": "87868b24e913dac9", "name": "<PERSON><PERSON>", "source": "87868b24e913dac9.json", "type": "application/json", "size": 12}, {"uid": "b8b90204d578346f", "name": "测试用例名称", "source": "b8b90204d578346f.json", "type": "application/json", "size": 30}, {"uid": "a95217da0b3855b4", "name": "参数类型", "source": "a95217da0b3855b4.json", "type": "application/json", "size": 6}, {"uid": "166a21992b1f931e", "name": "请求参数json格式", "source": "166a21992b1f931e.json", "type": "application/json", "size": 199}, {"uid": "ada36946e661c9b4", "name": "请求参数实际入参", "source": "ada36946e661c9b4.json", "type": "application/json", "size": 209}, {"uid": "ce1a84a86e2811c0", "name": "接口实际响应信息", "source": "ce1a84a86e2811c0.json", "type": "application/json", "size": 599}, {"uid": "8009d145009853e2", "name": "状态码断言结果：成功", "source": "8009d145009853e2.txt", "type": "text/plain", "size": 37}, {"uid": "8b221a58cf2e3235", "name": "相等断言结果：成功", "source": "8b221a58cf2e3235.json", "type": "application/json", "size": 53}, {"uid": "67a8dd913c882a06", "name": "📋 测试执行日志", "source": "67a8dd913c882a06.txt", "type": "text/plain", "size": 4962}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 13, "hasContent": true, "stepsCount": 0}, "afterStages": [{"name": "function_wait::0", "time": {"start": 1751852476880, "stop": 1751852477630, "duration": 750}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "print_info::0", "time": {"start": 1751852476879, "stop": 1751852476879, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "capture_logs_for_allure::0", "time": {"start": 1751852477630, "stop": 1751852477645, "duration": 15}, "status": "passed", "steps": [], "attachments": [{"uid": "a6d1c94b75fd2329", "name": "📋 测试执行日志 (完整记录)", "source": "a6d1c94b75fd2329.txt", "type": "text/plain", "size": 2960}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 1, "hasContent": true, "stepsCount": 0}], "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "38092-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "d17a3f3b7d1fb9b8.json", "parameterValues": []}