{"uid": "ebb6333e9f73176c", "name": "演出分类", "fullName": "testcase.play.test_play.TestPlay#test_play", "historyId": "c36ab200ccb8723de7119149ea6c3088", "time": {"start": 1751852456883, "stop": 1751852457271, "duration": 388}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "print_info", "time": {"start": 1751852456874, "stop": 1751852456874, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "function_wait", "time": {"start": 1751852456124, "stop": 1751852456874, "duration": 750}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "_session_faker", "time": {"start": 1751852455733, "stop": 1751852455863, "duration": 130}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "capture_logs_for_allure", "time": {"start": 1751852456123, "stop": 1751852456124, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "clear_extract", "time": {"start": 1751852455863, "stop": 1751852456123, "duration": 260}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "321de1ac8a5fb5cb", "name": "接口地址", "source": "321de1ac8a5fb5cb.json", "type": "application/json", "size": 48}, {"uid": "44e83014949089fa", "name": "接口名称", "source": "44e83014949089fa.json", "type": "application/json", "size": 12}, {"uid": "b81ca5e7e95c905d", "name": "请求方式", "source": "b81ca5e7e95c905d.json", "type": "application/json", "size": 4}, {"uid": "47773125d8817adb", "name": "请求头", "source": "47773125d8817adb.json", "type": "application/json", "size": 122}, {"uid": "b93556333782fbb", "name": "<PERSON><PERSON>", "source": "b93556333782fbb.json", "type": "application/json", "size": 12}, {"uid": "26f2098171438d12", "name": "测试用例名称", "source": "26f2098171438d12.json", "type": "application/json", "size": 24}, {"uid": "6e2e6872253aa382", "name": "参数类型", "source": "6e2e6872253aa382.json", "type": "application/json", "size": 4}, {"uid": "8bd4321659680bfa", "name": "请求参数json格式", "source": "8bd4321659680bfa.json", "type": "application/json", "size": 204}, {"uid": "dca42731aa97e8bd", "name": "请求参数实际入参", "source": "dca42731aa97e8bd.json", "type": "application/json", "size": 214}, {"uid": "84f608831c17d39f", "name": "接口实际响应信息", "source": "84f608831c17d39f.json", "type": "application/json", "size": 3148}, {"uid": "345525b1954fbba3", "name": "状态码断言结果：成功", "source": "345525b1954fbba3.txt", "type": "text/plain", "size": 37}, {"uid": "13ef742c76dc7014", "name": "相等断言结果：成功", "source": "13ef742c76dc7014.json", "type": "application/json", "size": 53}, {"uid": "166808ef9fd70287", "name": "📋 测试执行日志", "source": "166808ef9fd70287.txt", "type": "text/plain", "size": 2076}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 13, "hasContent": true, "stepsCount": 0}, "afterStages": [{"name": "print_info::0", "time": {"start": 1751852457272, "stop": 1751852457272, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "function_wait::0", "time": {"start": 1751852457273, "stop": 1751852458024, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "capture_logs_for_allure::0", "time": {"start": 1751852458025, "stop": 1751852458035, "duration": 10}, "status": "passed", "steps": [], "attachments": [{"uid": "1f7b031eca9c4e5d", "name": "📋 测试执行日志 (完整记录)", "source": "1f7b031eca9c4e5d.txt", "type": "text/plain", "size": 4618}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 1, "hasContent": true, "stepsCount": 0}], "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "38092-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "ebb6333e9f73176c.json", "parameterValues": []}