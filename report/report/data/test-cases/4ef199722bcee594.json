{"uid": "4ef199722bcee594", "name": "今日必抢", "fullName": "testcase.play.test_play.TestPlay#test_play", "historyId": "09729bd43bb947d1ebe3c16a80dc6e25", "time": {"start": 1751866418410, "stop": 1751866418751, "duration": 341}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "function_wait", "time": {"start": 1751866417658, "stop": 1751866418408, "duration": 750}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "clear_extract", "time": {"start": 1751866415419, "stop": 1751866415726, "duration": 307}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "capture_logs_for_allure", "time": {"start": 1751866417658, "stop": 1751866417658, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "_session_faker", "time": {"start": 1751866415384, "stop": 1751866415419, "duration": 35}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "print_info", "time": {"start": 1751866418408, "stop": 1751866418409, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "5c2d9ceb0f46d38f", "name": "接口地址", "source": "5c2d9ceb0f46d38f.json", "type": "application/json", "size": 55}, {"uid": "c1a58fa8f62065d5", "name": "接口名称", "source": "c1a58fa8f62065d5.json", "type": "application/json", "size": 12}, {"uid": "7c5faa902acd9041", "name": "请求方式", "source": "7c5faa902acd9041.json", "type": "application/json", "size": 4}, {"uid": "a4273a8a51671ba7", "name": "请求头", "source": "a4273a8a51671ba7.json", "type": "application/json", "size": 122}, {"uid": "7cdc5360aa90984", "name": "<PERSON><PERSON>", "source": "7cdc5360aa90984.json", "type": "application/json", "size": 12}, {"uid": "57b795b57c14ab80", "name": "测试用例名称", "source": "57b795b57c14ab80.json", "type": "application/json", "size": 30}, {"uid": "b990df4cb9248990", "name": "参数类型", "source": "b990df4cb9248990.json", "type": "application/json", "size": 4}, {"uid": "69335f8e3ef2a017", "name": "请求参数json格式", "source": "69335f8e3ef2a017.json", "type": "application/json", "size": 214}, {"uid": "bcd20dc0627560cd", "name": "请求参数实际入参", "source": "bcd20dc0627560cd.json", "type": "application/json", "size": 224}, {"uid": "f03d412bfed747dd", "name": "接口实际响应信息", "source": "f03d412bfed747dd.json", "type": "application/json", "size": 22188}, {"uid": "7fde20ab6737c790", "name": "状态码断言结果：成功", "source": "7fde20ab6737c790.txt", "type": "text/plain", "size": 37}, {"uid": "9cf1e1d209c46bc4", "name": "相等断言结果：成功", "source": "9cf1e1d209c46bc4.json", "type": "application/json", "size": 53}, {"uid": "335c2ebf3349a1df", "name": "📋 测试执行日志", "source": "335c2ebf3349a1df.txt", "type": "text/plain", "size": 4097}], "parameters": [], "hasContent": true, "stepsCount": 0, "attachmentsCount": 13, "shouldDisplayMessage": false}, "afterStages": [{"name": "function_wait::0", "time": {"start": 1751866418752, "stop": 1751866419503, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "capture_logs_for_allure::0", "time": {"start": 1751866419504, "stop": 1751866419521, "duration": 17}, "status": "passed", "steps": [], "attachments": [{"uid": "808c8a5ca399ec78", "name": "📋 测试执行日志 (完整记录)", "source": "808c8a5ca399ec78.txt", "type": "text/plain", "size": 14716}], "parameters": [], "hasContent": true, "stepsCount": 0, "attachmentsCount": 1, "shouldDisplayMessage": false}, {"name": "print_info::0", "time": {"start": 1751866418752, "stop": 1751866418752, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "attachmentsCount": 0, "shouldDisplayMessage": false}], "labels": [{"name": "story", "value": "C01_演出模块流程"}, {"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "16848-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "4ef199722bcee594.json", "parameterValues": []}