{"uid": "c13f5ecc533d9ff2", "name": "**搜索**", "fullName": "testcase.play.test_play.TestPlay#test_play", "historyId": "80ea16c71eb34b6e85110c3f875859f1", "time": {"start": 1751853241086, "stop": 1751853241389, "duration": 303}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "function_wait", "time": {"start": 1751853240335, "stop": 1751853241086, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "print_info", "time": {"start": 1751853241086, "stop": 1751853241086, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "capture_logs_for_allure", "time": {"start": 1751853240334, "stop": 1751853240334, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "_session_faker", "time": {"start": 1751853229094, "stop": 1751853229142, "duration": 48}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "clear_extract", "time": {"start": 1751853229143, "stop": 1751853229413, "duration": 270}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "aafdca848850f151", "name": "接口地址", "source": "aafdca848850f151.json", "type": "application/json", "size": 53}, {"uid": "3af74d33f8707345", "name": "接口名称", "source": "3af74d33f8707345.json", "type": "application/json", "size": 10}, {"uid": "8dcb75914e512569", "name": "请求方式", "source": "8dcb75914e512569.json", "type": "application/json", "size": 4}, {"uid": "c2a3bed5202a4f53", "name": "请求头", "source": "c2a3bed5202a4f53.json", "type": "application/json", "size": 122}, {"uid": "f16ad510a9d5e472", "name": "<PERSON><PERSON>", "source": "f16ad510a9d5e472.json", "type": "application/json", "size": 12}, {"uid": "264f94ef03a09792", "name": "测试用例名称", "source": "264f94ef03a09792.json", "type": "application/json", "size": 30}, {"uid": "dcf3e21e400526db", "name": "参数类型", "source": "dcf3e21e400526db.json", "type": "application/json", "size": 4}, {"uid": "ccc86915967954e0", "name": "请求参数json格式", "source": "ccc86915967954e0.json", "type": "application/json", "size": 201}, {"uid": "291329ea5be5c31c", "name": "请求参数实际入参", "source": "291329ea5be5c31c.json", "type": "application/json", "size": 211}, {"uid": "6c95b90d413d3465", "name": "接口实际响应信息", "source": "6c95b90d413d3465.json", "type": "application/json", "size": 22387}, {"uid": "6a1b462d7f9e76fd", "name": "状态码断言结果：成功", "source": "6a1b462d7f9e76fd.txt", "type": "text/plain", "size": 37}, {"uid": "cedcfdef03e9cdae", "name": "相等断言结果：成功", "source": "cedcfdef03e9cdae.json", "type": "application/json", "size": 53}, {"uid": "533bfd8aaeea38ed", "name": "📋 测试执行日志", "source": "533bfd8aaeea38ed.txt", "type": "text/plain", "size": 15022}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 13, "hasContent": true, "stepsCount": 0}, "afterStages": [{"name": "function_wait::0", "time": {"start": 1751853241391, "stop": 1751853242142, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "print_info::0", "time": {"start": 1751853241390, "stop": 1751853241390, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "capture_logs_for_allure::0", "time": {"start": 1751853242142, "stop": 1751853242153, "duration": 11}, "status": "passed", "steps": [], "attachments": [{"uid": "983823176e388c03", "name": "📋 测试执行日志 (完整记录)", "source": "983823176e388c03.txt", "type": "text/plain", "size": 15355}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 1, "hasContent": true, "stepsCount": 0}], "labels": [{"name": "story", "value": "C01_演出模块流程"}, {"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "28812-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "c13f5ecc533d9ff2.json", "parameterValues": []}