{"uid": "78d283124196e595", "name": "根据场次获取取票方式", "fullName": "testcase.play.test_play.TestPlay#test_play", "historyId": "40f667e7e7d9255278cb56eb99290821", "time": {"start": 1751853905776, "stop": 1751853906248, "duration": 472}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "print_info", "time": {"start": 1751853905775, "stop": 1751853905775, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "clear_extract", "time": {"start": 1751853884946, "stop": 1751853885997, "duration": 1051}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "capture_logs_for_allure", "time": {"start": 1751853905025, "stop": 1751853905025, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "_session_faker", "time": {"start": 1751853884907, "stop": 1751853884946, "duration": 39}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "function_wait", "time": {"start": 1751853905025, "stop": 1751853905775, "duration": 750}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "ffd78898b19721e6", "name": "接口地址", "source": "ffd78898b19721e6.json", "type": "application/json", "size": 57}, {"uid": "8ac637d36b072203", "name": "接口名称", "source": "8ac637d36b072203.json", "type": "application/json", "size": 30}, {"uid": "3aefa6aa4409e536", "name": "请求方式", "source": "3aefa6aa4409e536.json", "type": "application/json", "size": 3}, {"uid": "f4bc11e6bdf474ab", "name": "请求头", "source": "f4bc11e6bdf474ab.json", "type": "application/json", "size": 122}, {"uid": "c0596fddd00c4502", "name": "<PERSON><PERSON>", "source": "c0596fddd00c4502.json", "type": "application/json", "size": 12}, {"uid": "18afe9df9571e752", "name": "测试用例名称", "source": "18afe9df9571e752.json", "type": "application/json", "size": 30}, {"uid": "b0f5a312938857d", "name": "参数类型", "source": "b0f5a312938857d.json", "type": "application/json", "size": 6}, {"uid": "5b46f62c3a4b913d", "name": "请求参数json格式", "source": "5b46f62c3a4b913d.json", "type": "application/json", "size": 199}, {"uid": "1feba8e2b18bc236", "name": "请求参数实际入参", "source": "1feba8e2b18bc236.json", "type": "application/json", "size": 209}, {"uid": "546de21bcd501cee", "name": "接口实际响应信息", "source": "546de21bcd501cee.json", "type": "application/json", "size": 599}, {"uid": "73390e53cfb9753c", "name": "状态码断言结果：成功", "source": "73390e53cfb9753c.txt", "type": "text/plain", "size": 37}, {"uid": "990211d11773560e", "name": "相等断言结果：成功", "source": "990211d11773560e.json", "type": "application/json", "size": 53}, {"uid": "3271a0ef7ba6f70f", "name": "📋 测试执行日志", "source": "3271a0ef7ba6f70f.txt", "type": "text/plain", "size": 5242}], "parameters": [], "hasContent": true, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 13}, "afterStages": [{"name": "print_info::0", "time": {"start": 1751853906249, "stop": 1751853906250, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "capture_logs_for_allure::0", "time": {"start": 1751853907001, "stop": 1751853907015, "duration": 14}, "status": "passed", "steps": [], "attachments": [{"uid": "7c6d702edeef0d92", "name": "📋 测试执行日志 (完整记录)", "source": "7c6d702edeef0d92.txt", "type": "text/plain", "size": 2608}], "parameters": [], "hasContent": true, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 1}, {"name": "function_wait::0", "time": {"start": 1751853906250, "stop": 1751853907001, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "stepsCount": 0, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "39132-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "78d283124196e595.json", "parameterValues": []}