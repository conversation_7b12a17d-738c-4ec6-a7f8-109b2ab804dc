{"uid": "b84e2a2ee20b1f18", "name": "首页广告位", "fullName": "testcase.play.test_play.TestPlay#test_play", "historyId": "16615fd56de330c5e7df9e74f610a0a1", "time": {"start": 1751852999329, "stop": 1751852999638, "duration": 309}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "capture_logs_for_allure", "time": {"start": 1751852998578, "stop": 1751852998578, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "clear_extract", "time": {"start": 1751852990929, "stop": 1751852991206, "duration": 277}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "function_wait", "time": {"start": 1751852998579, "stop": 1751852999329, "duration": 750}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "_session_faker", "time": {"start": 1751852990879, "stop": 1751852990927, "duration": 48}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "print_info", "time": {"start": 1751852999329, "stop": 1751852999329, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "8a0f01e0d52ea09c", "name": "接口地址", "source": "8a0f01e0d52ea09c.json", "type": "application/json", "size": 44}, {"uid": "6ac53fa98a3412a1", "name": "接口名称", "source": "6ac53fa98a3412a1.json", "type": "application/json", "size": 15}, {"uid": "eb5e19d8db40e342", "name": "请求方式", "source": "eb5e19d8db40e342.json", "type": "application/json", "size": 4}, {"uid": "22c32ef223aac06a", "name": "请求头", "source": "22c32ef223aac06a.json", "type": "application/json", "size": 122}, {"uid": "b5d9402d4536f891", "name": "<PERSON><PERSON>", "source": "b5d9402d4536f891.json", "type": "application/json", "size": 12}, {"uid": "861c7786ef7ad2a", "name": "测试用例名称", "source": "861c7786ef7ad2a.json", "type": "application/json", "size": 24}, {"uid": "59bc3704b452ec08", "name": "参数类型", "source": "59bc3704b452ec08.json", "type": "application/json", "size": 4}, {"uid": "edaf0e4bb0bd6418", "name": "请求参数json格式", "source": "edaf0e4bb0bd6418.json", "type": "application/json", "size": 179}, {"uid": "ffbc3324e24e8614", "name": "请求参数实际入参", "source": "ffbc3324e24e8614.json", "type": "application/json", "size": 189}, {"uid": "21b858c335ab53ae", "name": "接口实际响应信息", "source": "21b858c335ab53ae.json", "type": "application/json", "size": 3479}, {"uid": "720035b69d502a35", "name": "状态码断言结果：成功", "source": "720035b69d502a35.txt", "type": "text/plain", "size": 37}, {"uid": "5c65d15149e58c0b", "name": "相等断言结果：成功", "source": "5c65d15149e58c0b.json", "type": "application/json", "size": 53}, {"uid": "70b33903853765ee", "name": "📋 测试执行日志", "source": "70b33903853765ee.txt", "type": "text/plain", "size": 27049}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 13, "hasContent": true, "stepsCount": 0}, "afterStages": [{"name": "capture_logs_for_allure::0", "time": {"start": 1751853000391, "stop": 1751853000416, "duration": 25}, "status": "passed", "steps": [], "attachments": [{"uid": "72dbf9bafebbb75f", "name": "📋 测试执行日志 (完整记录)", "source": "72dbf9bafebbb75f.txt", "type": "text/plain", "size": 4549}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 1, "hasContent": true, "stepsCount": 0}, {"name": "function_wait::0", "time": {"start": 1751852999639, "stop": 1751853000390, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "print_info::0", "time": {"start": 1751852999639, "stop": 1751852999639, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}], "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "37476-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [{"name": "api_info", "value": "{'baseInfo': {'module': '演出票', 'api_name': '首页广告位', 'host': '${get_host(play)}', 'url': '/api/show/ad/select_list', 'method': 'post', 'headers': '${get_headers(app)}'}, 'testCase': {'case_name': '获取首页广告列表', 'validation': [{'code': 200}, {'eq': {'code': 0}}], 'extract': 0, 'extract_list': np.float64(0.0), 'data': {'channelCode': '${appKey()}', 'token': '${get_extract_data(token)}'}}}"}], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "b84e2a2ee20b1f18.json", "parameterValues": ["{'baseInfo': {'module': '演出票', 'api_name': '首页广告位', 'host': '${get_host(play)}', 'url': '/api/show/ad/select_list', 'method': 'post', 'headers': '${get_headers(app)}'}, 'testCase': {'case_name': '获取首页广告列表', 'validation': [{'code': 200}, {'eq': {'code': 0}}], 'extract': 0, 'extract_list': np.float64(0.0), 'data': {'channelCode': '${appKey()}', 'token': '${get_extract_data(token)}'}}}"]}