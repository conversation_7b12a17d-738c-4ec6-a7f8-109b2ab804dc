{"uid": "de323df0390c0dbe", "name": "查询影讯接口", "fullName": "testcase.demo_test.test_demo.TestFilmOrder#test_film_order", "historyId": "e9c2ea8fd9c81240c2b50d2a9668daf2", "time": {"start": 1751853913524, "stop": 1751853913725, "duration": 201}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "capture_logs_for_allure", "time": {"start": 1751853912773, "stop": 1751853912773, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "function_wait", "time": {"start": 1751853912773, "stop": 1751853913524, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "clear_extract", "time": {"start": 1751853884946, "stop": 1751853885997, "duration": 1051}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "_session_faker", "time": {"start": 1751853884907, "stop": 1751853884946, "duration": 39}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "print_info", "time": {"start": 1751853913524, "stop": 1751853913524, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "580bd49446cea775", "name": "接口地址", "source": "580bd49446cea775.json", "type": "application/json", "size": 48}, {"uid": "f4e0dc5ea5fed7c6", "name": "接口名称", "source": "f4e0dc5ea5fed7c6.json", "type": "application/json", "size": 18}, {"uid": "2163531488c36aa7", "name": "请求方式", "source": "2163531488c36aa7.json", "type": "application/json", "size": 4}, {"uid": "85002a026412286b", "name": "请求头", "source": "85002a026412286b.json", "type": "application/json", "size": 15}, {"uid": "cf59f4010a4702ed", "name": "<PERSON><PERSON>", "source": "cf59f4010a4702ed.json", "type": "application/json", "size": 12}, {"uid": "603b8867f2a074e5", "name": "测试用例名称", "source": "603b8867f2a074e5.json", "type": "application/json", "size": 27}, {"uid": "1524a8a17f154736", "name": "参数类型", "source": "1524a8a17f154736.json", "type": "application/json", "size": 4}, {"uid": "64e8446e07ae3e3d", "name": "请求参数json格式", "source": "64e8446e07ae3e3d.json", "type": "application/json", "size": 493}, {"uid": "835b72bfbb65e688", "name": "请求参数实际入参", "source": "835b72bfbb65e688.json", "type": "application/json", "size": 436}, {"uid": "a921b87ccecda4b7", "name": "接口实际响应信息", "source": "a921b87ccecda4b7.json", "type": "application/json", "size": 52017}, {"uid": "81d18437d29cc986", "name": "状态码断言结果：成功", "source": "81d18437d29cc986.txt", "type": "text/plain", "size": 37}, {"uid": "855b54eae37bb10b", "name": "相等断言结果：成功", "source": "855b54eae37bb10b.json", "type": "application/json", "size": 53}, {"uid": "93d2aca196aa9f05", "name": "相等断言结果：成功", "source": "93d2aca196aa9f05.json", "type": "application/json", "size": 77}, {"uid": "7508e2e31760bd8b", "name": "📋 测试执行日志", "source": "7508e2e31760bd8b.txt", "type": "text/plain", "size": 2307}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentsCount": 14, "shouldDisplayMessage": false}, "afterStages": [{"name": "capture_logs_for_allure::0", "time": {"start": 1751853914477, "stop": 1751853914488, "duration": 11}, "status": "passed", "steps": [], "attachments": [{"uid": "9bea81905deb140b", "name": "📋 测试执行日志 (完整记录)", "source": "9bea81905deb140b.txt", "type": "text/plain", "size": 30345}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentsCount": 1, "shouldDisplayMessage": false}, {"name": "function_wait::0", "time": {"start": 1751853913727, "stop": 1751853914477, "duration": 750}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}, {"name": "print_info::0", "time": {"start": 1751853913726, "stop": 1751853913726, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentsCount": 0, "shouldDisplayMessage": false}], "labels": [{"name": "story", "value": "C02_电影下单流程"}, {"name": "feature", "value": "M02_电影订票模块接口测试"}, {"name": "parentSuite", "value": "testcase.demo_test"}, {"name": "suite", "value": "test_demo"}, {"name": "subSuite", "value": "TestFilmOrder"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "39132-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.demo_test.test_demo"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "de323df0390c0dbe.json", "parameterValues": []}