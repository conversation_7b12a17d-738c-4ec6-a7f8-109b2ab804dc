{"uid": "b95e44570ef0c6e", "name": "根据场次获取取票方式", "fullName": "testcase.play.test_play.TestPlay#test_play", "historyId": "40f667e7e7d9255278cb56eb99290821", "time": {"start": 1751853249103, "stop": 1751853249587, "duration": 484}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "print_info", "time": {"start": 1751853249103, "stop": 1751853249103, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "capture_logs_for_allure", "time": {"start": 1751853248353, "stop": 1751853248353, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "function_wait", "time": {"start": 1751853248353, "stop": 1751853249103, "duration": 750}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "_session_faker", "time": {"start": 1751853229094, "stop": 1751853229142, "duration": 48}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "clear_extract", "time": {"start": 1751853229143, "stop": 1751853229413, "duration": 270}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "3925211e134bf0d4", "name": "接口地址", "source": "3925211e134bf0d4.json", "type": "application/json", "size": 57}, {"uid": "2096dd377422d4a8", "name": "接口名称", "source": "2096dd377422d4a8.json", "type": "application/json", "size": 30}, {"uid": "dac42519e202a9fd", "name": "请求方式", "source": "dac42519e202a9fd.json", "type": "application/json", "size": 3}, {"uid": "d27577c6989d07f3", "name": "请求头", "source": "d27577c6989d07f3.json", "type": "application/json", "size": 122}, {"uid": "dc423b8d53cf0c31", "name": "<PERSON><PERSON>", "source": "dc423b8d53cf0c31.json", "type": "application/json", "size": 12}, {"uid": "181ed3a0e1bf66cf", "name": "测试用例名称", "source": "181ed3a0e1bf66cf.json", "type": "application/json", "size": 30}, {"uid": "c9547833519be3e0", "name": "参数类型", "source": "c9547833519be3e0.json", "type": "application/json", "size": 6}, {"uid": "4f2f7cad3b26e3d8", "name": "请求参数json格式", "source": "4f2f7cad3b26e3d8.json", "type": "application/json", "size": 199}, {"uid": "af51177fd0e5c0ce", "name": "请求参数实际入参", "source": "af51177fd0e5c0ce.json", "type": "application/json", "size": 209}, {"uid": "5b06b3e4172a0fb7", "name": "接口实际响应信息", "source": "5b06b3e4172a0fb7.json", "type": "application/json", "size": 599}, {"uid": "1a94d7e87993379a", "name": "状态码断言结果：成功", "source": "1a94d7e87993379a.txt", "type": "text/plain", "size": 37}, {"uid": "58a4a0e82235dfaf", "name": "相等断言结果：成功", "source": "58a4a0e82235dfaf.json", "type": "application/json", "size": 53}, {"uid": "c31d626923a30604", "name": "📋 测试执行日志", "source": "c31d626923a30604.txt", "type": "text/plain", "size": 5174}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 13, "hasContent": true, "stepsCount": 0}, "afterStages": [{"name": "print_info::0", "time": {"start": 1751853249587, "stop": 1751853249588, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "capture_logs_for_allure::0", "time": {"start": 1751853250339, "stop": 1751853250352, "duration": 13}, "status": "passed", "steps": [], "attachments": [{"uid": "b50cf089918ba6fe", "name": "📋 测试执行日志 (完整记录)", "source": "b50cf089918ba6fe.txt", "type": "text/plain", "size": 2801}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 1, "hasContent": true, "stepsCount": 0}, {"name": "function_wait::0", "time": {"start": 1751853249588, "stop": 1751853250339, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}], "labels": [{"name": "story", "value": "C01_演出模块流程"}, {"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "28812-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "b95e44570ef0c6e.json", "parameterValues": []}