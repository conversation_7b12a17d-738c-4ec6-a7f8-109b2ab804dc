{"uid": "14099c620de644e4", "name": "取消订单", "fullName": "testcase.play.test_play.TestPlay#test_play", "historyId": "097386a9d43be40d3c28c1874a6e169b", "time": {"start": 1751853253909, "stop": 1751853255152, "duration": 1243}, "status": "passed", "flaky": false, "newFailed": false, "beforeStages": [{"name": "_session_faker", "time": {"start": 1751853229094, "stop": 1751853229142, "duration": 48}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "clear_extract", "time": {"start": 1751853229143, "stop": 1751853229413, "duration": 270}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "function_wait", "time": {"start": 1751853253157, "stop": 1751853253908, "duration": 751}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "print_info", "time": {"start": 1751853253908, "stop": 1751853253909, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "capture_logs_for_allure", "time": {"start": 1751853253157, "stop": 1751853253157, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}], "testStage": {"status": "passed", "steps": [], "attachments": [{"uid": "956f9d1fdc9260c7", "name": "接口地址", "source": "956f9d1fdc9260c7.json", "type": "application/json", "size": 42}, {"uid": "e993b566c3dc192d", "name": "接口名称", "source": "e993b566c3dc192d.json", "type": "application/json", "size": 12}, {"uid": "65f9df821c8fa404", "name": "请求方式", "source": "65f9df821c8fa404.json", "type": "application/json", "size": 4}, {"uid": "3f74399aa09026e3", "name": "请求头", "source": "3f74399aa09026e3.json", "type": "application/json", "size": 122}, {"uid": "43dc97ebdd407e5f", "name": "<PERSON><PERSON>", "source": "43dc97ebdd407e5f.json", "type": "application/json", "size": 12}, {"uid": "e89311e3d44df44f", "name": "测试用例名称", "source": "e89311e3d44df44f.json", "type": "application/json", "size": 12}, {"uid": "468d715b1d84dffc", "name": "参数类型", "source": "468d715b1d84dffc.json", "type": "application/json", "size": 4}, {"uid": "92fca30a92efe9bf", "name": "请求参数json格式", "source": "92fca30a92efe9bf.json", "type": "application/json", "size": 217}, {"uid": "9dbe041fc1121985", "name": "请求参数实际入参", "source": "9dbe041fc1121985.json", "type": "application/json", "size": 227}, {"uid": "f9fed2decc4ab48e", "name": "接口实际响应信息", "source": "f9fed2decc4ab48e.json", "type": "application/json", "size": 48}, {"uid": "ba913fbb3335524e", "name": "状态码断言结果：成功", "source": "ba913fbb3335524e.txt", "type": "text/plain", "size": 37}, {"uid": "a0a09774310890dd", "name": "相等断言结果：成功", "source": "a0a09774310890dd.json", "type": "application/json", "size": 53}, {"uid": "4733e21a2a1cb0cb", "name": "📋 测试执行日志", "source": "4733e21a2a1cb0cb.txt", "type": "text/plain", "size": 2922}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 13, "hasContent": true, "stepsCount": 0}, "afterStages": [{"name": "function_wait::0", "time": {"start": 1751853255154, "stop": 1751853255904, "duration": 750}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "print_info::0", "time": {"start": 1751853255153, "stop": 1751853255153, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "hasContent": false, "stepsCount": 0}, {"name": "capture_logs_for_allure::0", "time": {"start": 1751853255904, "stop": 1751853255914, "duration": 10}, "status": "passed", "steps": [], "attachments": [{"uid": "68d256138741f225", "name": "📋 测试执行日志 (完整记录)", "source": "68d256138741f225.txt", "type": "text/plain", "size": 2518}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 1, "hasContent": true, "stepsCount": 0}], "labels": [{"name": "story", "value": "C01_演出模块流程"}, {"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "28812-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": []}, "source": "14099c620de644e4.json", "parameterValues": []}