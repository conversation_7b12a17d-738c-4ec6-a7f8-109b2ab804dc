INFO - 2025-07-07 13:33:43,946 - conftest.py:7 -[conftest:print_info] - ---------------接口测试开始---------------
INFO - 2025-07-07 13:33:43,969 - sendrequests.py:57 -[sendrequests:execute_api_request] - 接口名称：首页广告位
INFO - 2025-07-07 13:33:43,969 - sendrequests.py:58 -[sendrequests:execute_api_request] - 请求地址：http://*************/api/show/ad/select_list
INFO - 2025-07-07 13:33:43,969 - sendrequests.py:59 -[sendrequests:execute_api_request] - 请求方式：POST
INFO - 2025-07-07 13:33:43,969 - sendrequests.py:60 -[sendrequests:execute_api_request] - 请求头：{'Content-Type': 'application/x-www-form-urlencoded', 'Connection': 'Keep-Alive', 'Accept-Encoding': 'gzip'}
INFO - 2025-07-07 13:33:43,969 - sendrequests.py:61 -[sendrequests:execute_api_request] - 测试用例名：获取首页广告列表
INFO - 2025-07-07 13:33:43,969 - sendrequests.py:62 -[sendrequests:execute_api_request] - cookies值：None
INFO - 2025-07-07 13:33:43,969 - sendrequests.py:67 -[sendrequests:execute_api_request] - 参数类型：data
INFO - 2025-07-07 13:33:43,969 - sendrequests.py:69 -[sendrequests:execute_api_request] - 请求参数：{"channelCode": "*********", "token": "lrDqVLEc8jnKaORIKiJB+y6nN1Io3lnKNueVwmjECnPs3mUEcTEbKdyma0pUIUY8s2ItjSU2QLLYFxywf0Grw9Hfdp3svbaytqb6Sc0oYJ/tO8wd56XQTybpwRkS8Qt8,*********"}
INFO - 2025-07-07 13:33:44,258 - apiutils_business.py:179 -[apiutils_business:execute_test_cases] - 接口实际返回结果：{"code":0,"msg":"","data":[{"id":"31","name":"端午活动","position_type":"home_middle","image_url":"upload\/20250529\/102446xt4IfG0Kf0.jpg","link_type":"customize","link_value":"https:\/\/www.msn.cn\/zh-cn\/news\/other\/%E7%AB%AF%E5%8D%88%E6%B0%9B%E5%9B%B4%E6%84%9F%E6%8B%89%E6%BB%A1-%E8%B5%9B%E9%BE%99%E8%88%9F-%E7%B2%BD%E5%AD%90-%E9%A6%99%E5%8C%85%E9%83%BD%E6%9C%89%E4%BA%86%E6%96%B0%E8%8A%B1%E6%A0%B7\/ar-AA1Fz2TU?ocid=msedgntp&pc=CNNDDB","sort_value":"99","link_type_desc":"自定义","image_url_show":"http:\/\/*************\/resource\/play\/upload\/20250529\/102446xt4IfG0Kf0.jpg"},{"id":"32","name":"陈慧娴","position_type":"home_middle","image_url":"upload\/20250529\/105313SLTl4LjSBR.png","link_type":"performance","link_value":"34","sort_value":"67","link_type_desc":"演出项目","image_url_show":"http:\/\/*************\/resource\/play\/upload\/20250529\/105313SLTl4LjSBR.png"},{"id":"40","name":"【专用】深圳大运中心体育场","position_type":"home_middle","image_url":"upload\/20250529\/13382349PX4yl_Dk.png","link_type":"performance","link_value":"69","sort_value":"66","link_type_desc":"演出项目","image_url_show":"http:\/\/*************\/resource\/play\/upload\/20250529\/13382349PX4yl_Dk.png"},{"id":"45","name":"luowt","position_type":"home_middle","image_url":"upload\/20250630\/144818XuaAA54TKR.png","link_type":"performance","link_value":"106","sort_value":"23","link_type_desc":"演出项目","image_url_show":"http:\/\/*************\/resource\/play\/upload\/20250630\/144818XuaAA54TKR.png"},{"id":"42","name":"wk新建的没有推广时间的","position_type":"home_middle","image_url":"upload\/20250529\/1732116kcMEpIoAV.png","link_type":"customize","link_value":"http:\/\/www.baoLY.com","sort_value":"22","link_type_desc":"自定义","image_url_show":"http:\/\/*************\/resource\/play\/upload\/20250529\/1732116kcMEpIoAV.png"},{"id":"30","name":"【勿动】测试城市","position_type":"home_middle","image_url":"upload\/20250528\/174625-aUjLRKWr1.jpg","link_type":"category","link_value":"10002","sort_value":"0","link_type_desc":"演出分类","image_url_show":"http:\/\/*************\/resource\/play\/upload\/20250528\/174625-aUjLRKWr1.jpg"},{"id":"29","name":"【勿动】QQR测试","position_type":"home_middle","image_url":"upload\/20250528\/173340QP-ZIAp90D.png","link_type":"performance","link_value":"26","sort_value":"-1","link_type_desc":"演出项目","image_url_show":"http:\/\/*************\/resource\/play\/upload\/20250528\/173340QP-ZIAp90D.png"}]}
INFO - 2025-07-07 13:33:44,267 - assertion_utils.py:35 -[assertion_utils:status_code_assert] - 状态码断言成功：接口实际返回状态码 200 == 200
INFO - 2025-07-07 13:33:44,268 - assertion_utils.py:94 -[assertion_utils:equal_assert] - 相等断言成功：接口实际结果 {'code': 0} == 预期结果：{'code': 0}
INFO - 2025-07-07 13:33:44,268 - assertion_utils.py:206 -[assertion_utils:assert_result] - 测试成功
INFO - 2025-07-07 13:33:44,279 - conftest.py:9 -[conftest:print_info] - ---------------接口测试结束---------------