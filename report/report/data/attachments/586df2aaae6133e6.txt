INFO - 2025-07-07 09:53:46,273 - web_service.py:50 -[web_service:log_request_info] - HTTP请求 - POST http://*************:5000/project/play&demo_test - 客户端IP: *************
INFO - 2025-07-07 09:53:46,274 - web_service.py:426 -[web_service:execute_command_simple] - 开始执行命令: python run.py play demo_test
INFO - 2025-07-07 09:53:46,274 - web_service.py:80 -[web_service:log_response_info] - HTTP响应 - 状态码: 200 - 处理时间: 1.96ms
INFO - 2025-07-07 09:53:46,274 - web_service.py:427 -[web_service:execute_command_simple] - 当前工作目录: C:\vae\python_project\zy_ApiAuto
INFO - 2025-07-07 09:53:46,274 - web_service.py:86 -[web_service:log_response_info] - 响应类型: application/json
INFO - 2025-07-07 09:53:46,274 - web_service.py:428 -[web_service:execute_command_simple] - 项目根目录: C:\vae\python_project\zy_ApiAuto
INFO - 2025-07-07 09:53:46,274 - web_service.py:429 -[web_service:execute_command_simple] - Python可执行文件: C:\vae\python_project\zy_ApiAuto\.venv\Scripts\pythonw.exe
INFO - 2025-07-07 09:53:46,275 - web_service.py:430 -[web_service:execute_command_simple] - 环境变量PATH: C:\vae\python_project\zy_ApiAuto\.venv/Scripts;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\windows\system32;C:\windows;C:\windows\System32\Wbem;C:\windows\System32\WindowsPowerShell\v1.0\;C:\windows\System32\OpenSSH\;C:\Program Files\nodejs\;C:\Program Files\nodejs\node_global\node_modules;C:\Program Files\Java\jdk1.8.0_281\bin;C:\Program Files\Java\jdk1.8.0_281\jre;C:\Program Files\Git\cmd;C:\vae\python3\3.12.6;C:\vae\python3\3.12.6\Scripts\;C:\Program Files\allure-2.13.8\bin;C:\Program Files\apache-jmeter-5.4.3\bin;C:\Program Files\platform-tools_r31.0.3-windows\platform-tools;C:\Program Files\dotnet\;C:\Program Files\Docker\Docker\resources\bin;C:\vae\python3\3.12.6\Scripts\;C:\vae\python3\3.12.6\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Program Files\JetBrains\PyCharm 2024.3.5\bin;;C:\Program Files\nodejs\node_global;
INFO - 2025-07-07 09:53:46,275 - web_service.py:434 -[web_service:execute_command_simple] - run.py文件是否存在: True
INFO - 2025-07-07 09:53:46,274 - web_service.py:92 -[web_service:log_response_info] - 响应内容: {"batch_id": "batch_1751853226", "command": "python run.py play demo_test", "message": "开始批量执行 2 个测试用例", "start_time": "2025-07-07 09:53:46", "status_url": "/status/batch_1751853226", "testcases": ["play", "demo_test"]}
INFO - 2025-07-07 09:53:46,275 - web_service.py:439 -[web_service:execute_command_simple] - pytest版本: 8.0.2
INFO - 2025-07-07 09:53:46,275 - web_service.py:450 -[web_service:execute_command_simple] - 转换后的完整命令: "C:\vae\python_project\zy_ApiAuto\.venv\Scripts\pythonw.exe" "C:\vae\python_project\zy_ApiAuto\run.py" play demo_test
INFO - 2025-07-07 09:53:46,275 - web_service.py:454 -[web_service:execute_command_simple] - 准备启动subprocess进程...
INFO - 2025-07-07 09:53:46,286 - web_service.py:489 -[web_service:execute_command_simple] - subprocess进程已启动，PID: 17224
INFO - 2025-07-07 09:53:46,286 - web_service.py:503 -[web_service:execute_command_simple] - 开始读取进程输出...
INFO - 2025-07-07 09:53:46,948 - run.py:17 -[run:<module>] - Python版本: 3.12.6 (tags/v3.12.6:a4a2d2b, Sep  6 2024, 20:11:23) [MSC v.1940 64 bit (AMD64)]
INFO - 2025-07-07 09:53:46,948 - run.py:18 -[run:<module>] - Python可执行文件: C:\vae\python_project\zy_ApiAuto\.venv\Scripts\pythonw.exe
INFO - 2025-07-07 09:53:46,948 - run.py:19 -[run:<module>] - 当前工作目录: C:\vae\python_project\zy_ApiAuto
INFO - 2025-07-07 09:53:46,948 - run.py:20 -[run:<module>] - 脚本参数: ['C:\\vae\\python_project\\zy_ApiAuto\\run.py', 'play', 'demo_test']
INFO - 2025-07-07 09:53:46,948 - run.py:36 -[run:<module>] - 执行路径【['./testcase/play', './testcase/demo_test']】
INFO - 2025-07-07 09:53:46,948 - run.py:40 -[run:<module>] - 清理旧的报告文件...
INFO - 2025-07-07 09:53:47,975 - run.py:46 -[run:<module>] - 清理旧的测试结果文件...
INFO - 2025-07-07 09:53:47,997 - run.py:60 -[run:<module>] - pytest参数: ['./testcase/play', './testcase/demo_test', '--tb=short', '--strict-markers', '--disable-warnings']
INFO - 2025-07-07 09:53:47,997 - web_service.py:517 -[web_service:execute_command_simple] - [batch_1751853226] 第8行:  INFO - 2025-07-07 09:53:47,997 - run.py:60 -[run:<module>] - pytest参数: ['./testcase/play', './testcase/demo_test', '--tb=short', '--strict-markers', '--disable-warnings']
INFO - 2025-07-07 09:53:48,312 - web_service.py:517 -[web_service:execute_command_simple] - [batch_1751853226] 第10行: platform win32 -- Python 3.12.6, pytest-8.0.2, pluggy-1.5.0 -- C:\vae\python_project\zy_ApiAuto\.venv\Scripts\pythonw.exe
INFO - 2025-07-07 09:53:48,313 - web_service.py:517 -[web_service:execute_command_simple] - [batch_1751853226] 第11行: cachedir: .pytest_cache
INFO - 2025-07-07 09:53:48,313 - web_service.py:517 -[web_service:execute_command_simple] - [batch_1751853226] 第13行: configfile: pytest.ini
INFO - 2025-07-07 09:53:48,313 - web_service.py:517 -[web_service:execute_command_simple] - [batch_1751853226] 第14行: plugins: allure-pytest-2.9.41, Faker-37.1.0, rerunfailures-14.0
INFO - 2025-07-07 09:53:48,636 - zy_backend_login.py:57 -[zy_backend_login:login] - 登录成功！{'msg': '登录成功', 'uid': '1942039411989512192', 'code': '0', 'data': {'name': '管理员', 'roleName': '租户管理员', 'isDefaultPwd': '0', 'userType': '1'}}
INFO - 2025-07-07 09:53:49,171 - connectMysql.py:22 -[connectMysql:__init__] - 成功连接到数据库：数据库ip：*************
INFO - 2025-07-07 09:53:49,218 - connectMysql.py:43 -[connectMysql:insert] - 插入数据库SQL--INSERT INTO `wei_sms_code_log` (`id`, `phone`, `channel_code`, `code`, `code_status`, `type`, `create_by`, `create_date`, `update_by`, `update_date`) VALUES (598201583166054733, '19877292090', 'C10000027', '123456', 0, 1, NULL, '2025-07-07 09:53:49', NULL, '2025-07-07 09:53:49');
INFO - 2025-07-07 09:53:49,218 - connectMysql.py:44 -[connectMysql:insert] - 数据库数据插入成功
INFO - 2025-07-07 09:53:49,218 - web_service.py:517 -[web_service:execute_command_simple] - [batch_1751853226] 第20行:  INFO - 2025-07-07 09:53:49,218 - connectMysql.py:44 -[connectMysql:insert] - 数据库数据插入成功
INFO - 2025-07-07 09:53:49,400 - zy_app_login.py:103 -[zy_app_login:login] - 【++登录成功++】{'head': {'errCode': '0', 'errMsg': '', 'tradeId': 'login', 'timestamp': '20250707095412'}, 'uid': '1942039415697215488', 'body': {'birthday': '', 'lastTime': '20250707095014', 'isCurrentRegister': '0', 'zipCode': '', 'sign': '156fde85f143d4440e51aeb931c22eb6', 'imageNo': 0, 'points': '0', 'cityName': '', 'areaName': '', 'areaNo': '', 'imageUrl': '', 'provinceNo': '', 'email': '', 'QQ': '', 'cityNo': '', 'address': '', 'level': '2', 'nickName': '', 'sex': 2, 'mobile': '19877292090', 'MSN': '', 'identityCard': '', 'sessionId': '346518532528558681', 'userId': '346516136741756608', 'isMobileValid': 1, 'loginCount': 0, 'token': '0sVf/9TsF/vUNfiRV10Rd78j9/KxSzElenZLaHZPfBXYeSVK8p+YV6865yT2Gq5WkbHRxS9K/4EOw2l8VxrP9AfQjRHVXZ3Q2LRUpzjehTn5b5j2V/3NtNIx0Jw1sIdK', 'realName': '', 'createTime': '1751613674000', 'provinceName': '', 'username': ''}}
INFO - 2025-07-07 09:53:50,163 - conftest.py:7 -[conftest:print_info] - ---------------接口测试开始---------------
INFO - 2025-07-07 09:53:50,247 - sendrequests.py:57 -[sendrequests:execute_api_request] - 接口名称：演出分类
INFO - 2025-07-07 09:53:50,247 - sendrequests.py:58 -[sendrequests:execute_api_request] - 请求地址：http://*************/api/show/info/category_list
INFO - 2025-07-07 09:53:50,248 - sendrequests.py:59 -[sendrequests:execute_api_request] - 请求方式：POST
INFO - 2025-07-07 09:53:50,248 - sendrequests.py:60 -[sendrequests:execute_api_request] - 请求头：{'Content-Type': 'application/x-www-form-urlencoded', 'Connection': 'Keep-Alive', 'Accept-Encoding': 'gzip'}
INFO - 2025-07-07 09:53:50,248 - sendrequests.py:61 -[sendrequests:execute_api_request] - 测试用例名：获取猫眼演出分类
INFO - 2025-07-07 09:53:50,248 - sendrequests.py:62 -[sendrequests:execute_api_request] - cookies值：None
INFO - 2025-07-07 09:53:50,248 - sendrequests.py:67 -[sendrequests:execute_api_request] - 参数类型：data
INFO - 2025-07-07 09:53:50,248 - sendrequests.py:69 -[sendrequests:execute_api_request] - 请求参数：{"channelCode": "C10000027", "merchant_code": "", "token": "0sVf/9TsF/vUNfiRV10Rd78j9/KxSzElenZLaHZPfBXYeSVK8p+YV6865yT2Gq5WkbHRxS9K/4EOw2l8VxrP9AfQjRHVXZ3Q2LRUpzjehTn5b5j2V/3NtNIx0Jw1sIdK,C10000027"}
INFO - 2025-07-07 09:53:50,248 - web_service.py:517 -[web_service:execute_command_simple] - [batch_1751853226] 第30行:  INFO - 2025-07-07 09:53:50,248 - sendrequests.py:69 -[sendrequests:execute_api_request] - 请求参数：{"channelCode": "C10000027", "merchant_code": "", "token": "0sVf/9TsF/vUNfiRV10Rd78j9/KxSzElenZLaHZPfBXYeSVK8p+YV6865yT2Gq5WkbHRxS9K/4EOw2l8VxrP9AfQjRHVXZ3Q2LRUpzjehTn5b5j2V/3NtNIx0Jw1sIdK,C10000027"}
INFO - 2025-07-07 09:53:50,513 - apiutils_business.py:179 -[apiutils_business:execute_test_cases] - 接口实际返回结果：{"code":0,"msg":"","data":[{"id":"10001","name":"演唱会","image":"http:\/\/*************\/resource\/play\/upload\/20250421\/175345K0n_6qP6Pi.jpg","tag_list":["测试文案","演出演唱会12"]},{"id":"10002","name":"体育赛事","image":"http:\/\/*************\/resource\/play\/upload\/20250408\/144744oaSxDsD3Qu.png","tag_list":["孙颖莎","全红婵"]},{"id":"10003","name":"戏曲艺术","image":"http:\/\/*************\/resource\/play\/upload\/20250408\/1448079S7mHpY2QU.png","tag_list":[]},{"id":"10017","name":"Livehouse","image":"http:\/\/*************\/resource\/play\/upload\/20250408\/1449279xNEIQlg9D.png","tag_list":[]},{"id":"10005","name":"舞蹈芭蕾","image":"http:\/\/*************\/resource\/play\/upload\/20250408\/145113dfAkJoKwCg.png","tag_list":[]},{"id":"10006","name":"音乐会","image":"http:\/\/*************\/resource\/play\/upload\/20250408\/1448369SbU_7vykE.png","tag_list":[]},{"id":"10007","name":"亲子演出","image":"http:\/\/*************\/resource\/play\/upload\/20250408\/144825FTdAI7yPy_.png","tag_list":[]},{"id":"10010","name":"音乐节","image":"http:\/\/*************\/resource\/play\/upload\/20250422\/145418ZAvXkbQMhU.png","tag_list":[]},{"id":"10014","name":"沉浸剧场","image":"http:\/\/*************\/resource\/play\/upload\/20250408\/144959tWsxiYBDA-.png","tag_list":[]},{"id":"10016","name":"相声","image":"http:\/\/*************\/resource\/play\/upload\/20250408\/144939LP2Ruhsuki.png","tag_list":[]},{"id":"10015","name":"脱口秀","image":"http:\/\/*************\/resource\/play\/upload\/20250408\/144949GXhLPNXxUa.png","tag_list":[]},{"id":"10009","name":"休闲展览","image":"http:\/\/*************\/resource\/play\/upload\/20250408\/144907oWhMCZrrG_.png","tag_list":[]},{"id":"10004","name":"话剧音乐剧","image":"http:\/\/*************\/resource\/play\/upload\/20250408\/145102PT7jed5WkQ.png","tag_list":[]},{"id":"10008","name":"其他","image":"http:\/\/*************\/resource\/play\/upload\/20250331\/164501dyI8a3idSN.png","tag_list":["标签 一","标签二"]}]}
INFO - 2025-07-07 09:53:50,519 - assertion_utils.py:35 -[assertion_utils:status_code_assert] - 状态码断言成功：接口实际返回状态码 200 == 200
INFO - 2025-07-07 09:53:50,521 - assertion_utils.py:94 -[assertion_utils:equal_assert] - 相等断言成功：接口实际结果 {'code': 0} == 预期结果：{'code': 0}
INFO - 2025-07-07 09:53:50,521 - assertion_utils.py:206 -[assertion_utils:assert_result] - 测试成功