INFO - 2025-07-07 10:05:10,546 - conftest.py:7 -[conftest:print_info] - ---------------接口测试开始---------------
INFO - 2025-07-07 10:05:10,555 - sendrequests.py:57 -[sendrequests:execute_api_request] - 接口名称：取消订单
INFO - 2025-07-07 10:05:10,555 - sendrequests.py:58 -[sendrequests:execute_api_request] - 请求地址：http://*************/api/show/order/cancel
INFO - 2025-07-07 10:05:10,555 - sendrequests.py:59 -[sendrequests:execute_api_request] - 请求方式：POST
INFO - 2025-07-07 10:05:10,555 - sendrequests.py:60 -[sendrequests:execute_api_request] - 请求头：{'Content-Type': 'application/x-www-form-urlencoded', 'Connection': 'Keep-Alive', 'Accept-Encoding': 'gzip'}
INFO - 2025-07-07 10:05:10,555 - sendrequests.py:61 -[sendrequests:execute_api_request] - 测试用例名：取消订单
INFO - 2025-07-07 10:05:10,555 - sendrequests.py:62 -[sendrequests:execute_api_request] - cookies值：None
INFO - 2025-07-07 10:05:10,556 - sendrequests.py:67 -[sendrequests:execute_api_request] - 参数类型：data
INFO - 2025-07-07 10:05:10,556 - web_service.py:517 -[web_service:execute_command_simple] - [batch_1751853880] 第210行:  INFO - 2025-07-07 10:05:10,555 - sendrequests.py:62 -[sendrequests:execute_api_request] - cookies值：None
INFO - 2025-07-07 10:05:10,556 - sendrequests.py:69 -[sendrequests:execute_api_request] - 请求参数：{"orderId": "2025070710053194911", "channelCode": "C10000027", "token": "Qe6Ksfgh3qb79B03Du4KRxqmaA/hYC1+xsEsPwf8whRUz+WKDKmpzWT26buO2HqW0XOCuwkxwoYJamrcDoQPy0lJKXp3E5fBDtbroNS2GzraxiiEAdZ8BrEeHrFsgXGM,C10000027"}
INFO - 2025-07-07 10:05:11,993 - apiutils_business.py:179 -[apiutils_business:execute_test_cases] - 接口实际返回结果：{"code":0,"msg":"","data":[]}
INFO - 2025-07-07 10:05:11,996 - assertion_utils.py:35 -[assertion_utils:status_code_assert] - 状态码断言成功：接口实际返回状态码 200 == 200
INFO - 2025-07-07 10:05:11,997 - assertion_utils.py:94 -[assertion_utils:equal_assert] - 相等断言成功：接口实际结果 {'code': 0} == 预期结果：{'code': 0}
INFO - 2025-07-07 10:05:11,997 - assertion_utils.py:206 -[assertion_utils:assert_result] - 测试成功
INFO - 2025-07-07 10:05:12,006 - conftest.py:9 -[conftest:print_info] - ---------------接口测试结束---------------