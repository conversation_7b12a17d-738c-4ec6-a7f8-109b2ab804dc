INFO - 2025-07-07 09:54:07,021 - conftest.py:7 -[conftest:print_info] - ---------------接口测试开始---------------
INFO - 2025-07-07 09:54:07,029 - sendrequests.py:57 -[sendrequests:execute_api_request] - 接口名称：项目下场次票品列表
INFO - 2025-07-07 09:54:07,029 - sendrequests.py:58 -[sendrequests:execute_api_request] - 请求地址：http://*************/api/show/info/show_ticket_units
INFO - 2025-07-07 09:54:07,029 - sendrequests.py:59 -[sendrequests:execute_api_request] - 请求方式：GET
INFO - 2025-07-07 09:54:07,029 - sendrequests.py:60 -[sendrequests:execute_api_request] - 请求头：{'Content-Type': 'application/x-www-form-urlencoded', 'Connection': 'Keep-Alive', 'Accept-Encoding': 'gzip'}
INFO - 2025-07-07 09:54:07,029 - web_service.py:517 -[web_service:execute_command_simple] - [batch_1751853226] 第160行:  INFO - 2025-07-07 09:54:07,029 - sendrequests.py:59 -[sendrequests:execute_api_request] - 请求方式：GET
INFO - 2025-07-07 09:54:07,029 - sendrequests.py:61 -[sendrequests:execute_api_request] - 测试用例名：获取项目下场次票品列表信息
INFO - 2025-07-07 09:54:07,029 - sendrequests.py:62 -[sendrequests:execute_api_request] - cookies值：None
INFO - 2025-07-07 09:54:07,029 - sendrequests.py:67 -[sendrequests:execute_api_request] - 参数类型：params
INFO - 2025-07-07 09:54:07,029 - sendrequests.py:69 -[sendrequests:execute_api_request] - 请求参数：{"showId": "48", "channelCode": "C10000027", "token": "0sVf/9TsF/vUNfiRV10Rd78j9/KxSzElenZLaHZPfBXYeSVK8p+YV6865yT2Gq5WkbHRxS9K/4EOw2l8VxrP9AfQjRHVXZ3Q2LRUpzjehTn5b5j2V/3NtNIx0Jw1sIdK,C10000027"}
INFO - 2025-07-07 09:54:07,573 - apiutils_business.py:179 -[apiutils_business:execute_test_cases] - 接口实际返回结果：{"code":0,"msg":"","data":{"ticketUnits":[{"ticketUnitId":"214","showId":"48","name":null,"ticketName":"","minBuyLimit":1,"maxBuyLimit":6,"needRealName":false,"realNameLimit":0,"ticketLevel":"VVIP","rgb":"#FF4242","ticketPrice":"10","setNumber":1,"sellPriceList":[11,22,33,44,55,66,77,88,99,110,121,132,143,154,165,176,187,198,209,220],"limited":true,"sellStatus":3,"currentAmount":20,"baseTicketUnitId":null,"thirdTicketUnitId":16905302,"thirdBaseTicketUnitId":0},{"ticketUnitId":"215","showId":"48","name":null,"ticketName":"","minBuyLimit":1,"maxBuyLimit":6,"needRealName":false,"realNameLimit":0,"ticketLevel":"VIP","rgb":"#FF6FE4","ticketPrice":"20","setNumber":1,"sellPriceList":[22,44,66,88,110,132,154,176,198,220,242,264,286,308,330,352,374,396,418,440],"limited":true,"sellStatus":3,"currentAmount":20,"baseTicketUnitId":null,"thirdTicketUnitId":16905303,"thirdBaseTicketUnitId":0},{"ticketUnitId":"216","showId":"48","name":null,"ticketName":"","minBuyLimit":1,"maxBuyLimit":6,"needRealName":false,"realNameLimit":0,"ticketLevel":"A","rgb":"#FF8014","ticketPrice":"30","setNumber":1,"sellPriceList":[33,66,99,132,165,198,231,264,297,330,363,396,429,462,495,528,561,594,627,660],"limited":true,"sellStatus":3,"currentAmount":20,"baseTicketUnitId":null,"thirdTicketUnitId":16905304,"thirdBaseTicketUnitId":0},{"ticketUnitId":"217","showId":"48","name":null,"ticketName":"","minBuyLimit":1,"maxBuyLimit":6,"needRealName":false,"realNameLimit":0,"ticketLevel":"B","rgb":"#FFD70E","ticketPrice":"40","setNumber":1,"sellPriceList":[44,88,132,176,220,264,308,352,396,440,484,528,572,616,660,704,748,792,836,880],"limited":true,"sellStatus":3,"currentAmount":20,"baseTicketUnitId":null,"thirdTicketUnitId":16905305,"thirdBaseTicketUnitId":0},{"ticketUnitId":"218","showId":"48","name":null,"ticketName":"","minBuyLimit":1,"maxBuyLimit":6,"needRealName":false,"realNameLimit":0,"ticketLevel":"C","rgb":"#5A84FF","ticketPrice":"50","setNumber":1,"sellPriceList":[55,110,165,220,275,330,385,440,495,550,605,660,715,770,825,880,935,990,1045,1100],"limited":true,"sellStatus":3,"currentAmount":20,"baseTicketUnitId":null,"thirdTicketUnitId":16905306,"thirdBaseTicketUnitId":0},{"ticketUnitId":"219","showId":"48","name":"","ticketName":"","minBuyLimit":1,"maxBuyLimit":3,"needRealName":false,"realNameLimit":0,"ticketLevel":"VVIP","rgb":"#FF4242","ticketPrice":"10","setNumber":2,"sellPriceList":[20,40,60,80,99,119,139,159,179,198,218,238,258,278,297,317,337,357,377,396],"limited":true,"sellStatus":3,"currentAmount":20,"baseTicketUnitId":"214","thirdTicketUnitId":16905478,"thirdBaseTicketUnitId":16905302}]}}
INFO - 2025-07-07 09:54:07,576 - assertion_utils.py:35 -[assertion_utils:status_code_assert] - 状态码断言成功：接口实际返回状态码 200 == 200
INFO - 2025-07-07 09:54:07,577 - assertion_utils.py:94 -[assertion_utils:equal_assert] - 相等断言成功：接口实际结果 {'code': 0} == 预期结果：{'code': 0}
INFO - 2025-07-07 09:54:07,577 - assertion_utils.py:206 -[assertion_utils:assert_result] - 测试成功
INFO - 2025-07-07 09:54:07,589 - conftest.py:9 -[conftest:print_info] - ---------------接口测试结束---------------
INFO - 2025-07-07 09:54:07,590 - web_service.py:517 -[web_service:execute_command_simple] - [batch_1751853226] 第170行: PASSED INFO - 2025-07-07 09:54:07,589 - conftest.py:9 -[conftest:print_info] - ---------------接口测试结束---------------