INFO - 2025-07-07 09:26:30,379 - conftest.py:7 -[conftest:print_info] - ---------------接口测试开始---------------
INFO - 2025-07-07 09:26:30,398 - sendrequests.py:57 -[sendrequests:execute_api_request] - 接口名称：取消订单
INFO - 2025-07-07 09:26:30,398 - sendrequests.py:58 -[sendrequests:execute_api_request] - 请求地址：http://*************/api/show/order/cancel
INFO - 2025-07-07 09:26:30,399 - sendrequests.py:59 -[sendrequests:execute_api_request] - 请求方式：POST
INFO - 2025-07-07 09:26:30,399 - sendrequests.py:60 -[sendrequests:execute_api_request] - 请求头：{'Content-Type': 'application/x-www-form-urlencoded', 'Connection': 'Keep-Alive', 'Accept-Encoding': 'gzip'}
INFO - 2025-07-07 09:26:30,399 - sendrequests.py:61 -[sendrequests:execute_api_request] - 测试用例名：取消订单
INFO - 2025-07-07 09:26:30,399 - sendrequests.py:62 -[sendrequests:execute_api_request] - cookies值：None
INFO - 2025-07-07 09:26:30,399 - sendrequests.py:67 -[sendrequests:execute_api_request] - 参数类型：data
INFO - 2025-07-07 09:26:30,399 - sendrequests.py:69 -[sendrequests:execute_api_request] - 请求参数：{"orderId": "2025070709265158598", "channelCode": "C10000027", "token": "cFhOuiaXefoQIIFZi/rZq65kzE5/p5c4JI6ec0Ziccugep4bWnUKIgeEpUqRAaEpKkmSicNo+lzmbuXHkSzhWsCkVCAnsXKUuEkSZEovSbrteHKAi/gS0TQxfeQrlbYK,C10000027"}
INFO - 2025-07-07 09:26:31,641 - apiutils_business.py:179 -[apiutils_business:execute_test_cases] - 接口实际返回结果：{"code":0,"msg":"","data":[]}
INFO - 2025-07-07 09:26:31,643 - assertion_utils.py:35 -[assertion_utils:status_code_assert] - 状态码断言成功：接口实际返回状态码 200 == 200
INFO - 2025-07-07 09:26:31,643 - assertion_utils.py:94 -[assertion_utils:equal_assert] - 相等断言成功：接口实际结果 {'code': 0} == 预期结果：{'code': 0}
INFO - 2025-07-07 09:26:31,644 - assertion_utils.py:206 -[assertion_utils:assert_result] - 测试成功
INFO - 2025-07-07 09:26:31,654 - conftest.py:9 -[conftest:print_info] - ---------------接口测试结束---------------