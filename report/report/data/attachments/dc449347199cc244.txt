INFO - 2025-07-07 09:41:18,399 - conftest.py:7 -[conftest:print_info] - ---------------接口测试开始---------------
INFO - 2025-07-07 09:41:18,438 - sendrequests.py:57 -[sendrequests:execute_api_request] - 接口名称：下单
INFO - 2025-07-07 09:41:18,438 - sendrequests.py:58 -[sendrequests:execute_api_request] - 请求地址：http://*************/api/show/order/create
INFO - 2025-07-07 09:41:18,439 - sendrequests.py:59 -[sendrequests:execute_api_request] - 请求方式：POST
INFO - 2025-07-07 09:41:18,439 - sendrequests.py:60 -[sendrequests:execute_api_request] - 请求头：{'Content-Type': 'application/x-www-form-urlencoded', 'Connection': 'Keep-Alive', 'Accept-Encoding': 'gzip'}
INFO - 2025-07-07 09:41:18,439 - sendrequests.py:61 -[sendrequests:execute_api_request] - 测试用例名：创建订单
INFO - 2025-07-07 09:41:18,439 - sendrequests.py:62 -[sendrequests:execute_api_request] - cookies值：None
INFO - 2025-07-07 09:41:18,439 - web_service.py:517 -[web_service:execute_command_simple] - [play_1751852448] 第190行:  INFO - 2025-07-07 09:41:18,439 - sendrequests.py:61 -[sendrequests:execute_api_request] - 测试用例名：创建订单
INFO - 2025-07-07 09:41:18,439 - sendrequests.py:67 -[sendrequests:execute_api_request] - 参数类型：data
INFO - 2025-07-07 09:41:18,439 - sendrequests.py:69 -[sendrequests:execute_api_request] - 请求参数：{"recipientMobile": "19877292090", "recipientName": "购票人", "recipientIdNo": "", "fetchTicketWayId": 4386733, "recipientAddressId": "", "fetchTicketWayType": 5, "performanceId": "26", "showId": "48", "salesPlanId": "214", "salesPlanCount": "1", "totalTicketPrice": 11, "deliveryPrice": "0", "orderPrice": 11, "realNameIds": "", "seatRequest": "", "channelCode": "C10000027", "merchant_code": "", "token": "8y4eoCKpp9tcMjp51DV4UEmI1hegk73JSMjjz1bMf39BjiKzdJqOMBsgTCwgY7EYCzAR1uOc6Yqxj4jNblZyacedjpTnhEFWf6eGZZnSrZzreIYsloXPIsHpej7HdUL6,C10000027"}
INFO - 2025-07-07 09:41:19,652 - apiutils_business.py:179 -[apiutils_business:execute_test_cases] - 接口实际返回结果：{"code":0,"msg":"","data":{"orderId":"2025070709414228360"}}
INFO - 2025-07-07 09:41:19,655 - assertion_utils.py:35 -[assertion_utils:status_code_assert] - 状态码断言成功：接口实际返回状态码 200 == 200
INFO - 2025-07-07 09:41:19,656 - assertion_utils.py:94 -[assertion_utils:equal_assert] - 相等断言成功：接口实际结果 {'code': 0} == 预期结果：{'code': 0}
INFO - 2025-07-07 09:41:19,656 - assertion_utils.py:206 -[assertion_utils:assert_result] - 测试成功
INFO - 2025-07-07 09:41:19,672 - conftest.py:9 -[conftest:print_info] - ---------------接口测试结束---------------