INFO - 2025-07-07 10:02:02,836 - quick_test.py:15 -[quick_test:check_before_test] - === 测试前状态检查 ===
INFO - 2025-07-07 10:02:02,836 - quick_test.py:22 -[quick_test:check_before_test] - temp目录现有结果文件数量: 14
INFO - 2025-07-07 10:02:02,838 - quick_test.py:27 -[quick_test:check_before_test] - report目录存在
INFO - 2025-07-07 10:02:02,838 - quick_test.py:33 -[quick_test:run_test] - === 开始运行测试 ===
INFO - 2025-07-07 10:02:03,356 - run.py:17 -[run:<module>] - Python版本: 3.12.6 (tags/v3.12.6:a4a2d2b, Sep  6 2024, 20:11:23) [MSC v.1940 64 bit (AMD64)]
INFO - 2025-07-07 10:02:03,357 - run.py:18 -[run:<module>] - Python可执行文件: C:\vae\python3\3.12.6\python.exe
INFO - 2025-07-07 10:02:03,357 - run.py:19 -[run:<module>] - 当前工作目录: C:\vae\python_project\zy_ApiAuto
INFO - 2025-07-07 10:02:03,357 - run.py:20 -[run:<module>] - 脚本参数: ['run.py', 'play']
INFO - 2025-07-07 10:02:03,357 - run.py:36 -[run:<module>] - 执行路径【['./testcase/play']】
INFO - 2025-07-07 10:02:03,357 - run.py:39 -[run:<module>] - 开始清理所有旧的报告和结果文件...
INFO - 2025-07-07 10:02:03,357 - run.py:44 -[run:<module>] - 删除旧的报告目录...
INFO - 2025-07-07 10:02:03,389 - run.py:50 -[run:<module>] - 清理temp目录下的所有旧文件...
INFO - 2025-07-07 10:02:05,419 - run.py:64 -[run:<module>] - 旧文件清理完成，开始执行测试...
INFO - 2025-07-07 10:02:05,419 - run.py:72 -[run:<module>] - pytest参数: ['./testcase/play', '--tb=short', '--strict-markers', '--disable-warnings']
INFO - 2025-07-07 10:02:06,271 - zy_backend_login.py:57 -[zy_backend_login:login] - 登录成功！{'msg': '登录成功', 'uid': '1942041498546700288', 'code': '0', 'data': {'name': '管理员', 'roleName': '租户管理员', 'isDefaultPwd': '0', 'userType': '1'}}
INFO - 2025-07-07 10:02:06,803 - connectMysql.py:22 -[connectMysql:__init__] - 成功连接到数据库：数据库ip：*************
INFO - 2025-07-07 10:02:06,825 - connectMysql.py:43 -[connectMysql:insert] - 插入数据库SQL--INSERT INTO `wei_sms_code_log` (`id`, `phone`, `channel_code`, `code`, `code_status`, `type`, `create_by`, `create_date`, `update_by`, `update_date`) VALUES (383145674722462074, '19877292898', '*********', '123456', 0, 1, NULL, '2025-07-07 10:02:06', NULL, '2025-07-07 10:02:06');
INFO - 2025-07-07 10:02:06,825 - connectMysql.py:44 -[connectMysql:insert] - 数据库数据插入成功
INFO - 2025-07-07 10:02:07,154 - zy_app_login.py:103 -[zy_app_login:login] - 【++登录成功++】{'head': {'errCode': '0', 'errMsg': '', 'tradeId': 'login', 'timestamp': '20250707100230'}, 'uid': '1942041502837411840', 'body': {'birthday': '', 'lastTime': '20250704163752', 'isCurrentRegister': '0', 'zipCode': '', 'sign': 'd655cb60d93f2216353859c0ef0075db', 'imageNo': 0, 'points': '0', 'cityName': '', 'areaName': '', 'areaNo': '', 'imageUrl': '', 'provinceNo': '', 'email': '', 'QQ': '', 'cityNo': '', 'address': '', 'level': '1', 'nickName': '', 'sex': 2, 'mobile': '19877292898', 'MSN': '', 'identityCard': '', 'sessionId': '346518537506038683', 'userId': '346422044320518083', 'isMobileValid': 1, 'loginCount': 0, 'token': 'QLDa/9NjHgSYw1auSFBuL/8XzYY3TPXiWMx5EJW93pVrpQHb126Gk1kfi8cUb4IDLgkc0PtjUbyjV8veIqh7zBJvFCFmKV75avd5APTgvwTgAfR1w+Js7/54J0X48Xqh', 'realName': '', 'createTime': '1742204432000', 'provinceName': '', 'username': ''}}
INFO - 2025-07-07 10:02:07,918 - conftest.py:7 -[conftest:print_info] - ---------------接口测试开始---------------
INFO - 2025-07-07 10:02:07,970 - sendrequests.py:57 -[sendrequests:execute_api_request] - 接口名称：演出分类
INFO - 2025-07-07 10:02:07,970 - sendrequests.py:58 -[sendrequests:execute_api_request] - 请求地址：http://*************/api/show/info/category_list
INFO - 2025-07-07 10:02:07,970 - sendrequests.py:59 -[sendrequests:execute_api_request] - 请求方式：POST
INFO - 2025-07-07 10:02:07,970 - sendrequests.py:60 -[sendrequests:execute_api_request] - 请求头：{'Content-Type': 'application/x-www-form-urlencoded', 'Connection': 'Keep-Alive', 'Accept-Encoding': 'gzip'}
INFO - 2025-07-07 10:02:07,970 - sendrequests.py:61 -[sendrequests:execute_api_request] - 测试用例名：获取猫眼演出分类
INFO - 2025-07-07 10:02:07,971 - sendrequests.py:62 -[sendrequests:execute_api_request] - cookies值：None
INFO - 2025-07-07 10:02:07,971 - sendrequests.py:67 -[sendrequests:execute_api_request] - 参数类型：data
INFO - 2025-07-07 10:02:07,971 - sendrequests.py:69 -[sendrequests:execute_api_request] - 请求参数：{"channelCode": "*********", "merchant_code": "", "token": "QLDa/9NjHgSYw1auSFBuL/8XzYY3TPXiWMx5EJW93pVrpQHb126Gk1kfi8cUb4IDLgkc0PtjUbyjV8veIqh7zBJvFCFmKV75avd5APTgvwTgAfR1w+Js7/54J0X48Xqh,*********"}
INFO - 2025-07-07 10:02:08,251 - apiutils_business.py:179 -[apiutils_business:execute_test_cases] - 接口实际返回结果：{"code":0,"msg":"","data":[{"id":"10001","name":"演唱会","image":"http:\/\/*************\/resource\/play\/upload\/20250421\/175345K0n_6qP6Pi.jpg","tag_list":["测试文案","演出演唱会12"]},{"id":"10002","name":"体育赛事","image":"http:\/\/*************\/resource\/play\/upload\/20250408\/144744oaSxDsD3Qu.png","tag_list":["孙颖莎","全红婵"]},{"id":"10003","name":"戏曲艺术","image":"http:\/\/*************\/resource\/play\/upload\/20250408\/1448079S7mHpY2QU.png","tag_list":[]},{"id":"10017","name":"Livehouse","image":"http:\/\/*************\/resource\/play\/upload\/20250408\/1449279xNEIQlg9D.png","tag_list":[]},{"id":"10005","name":"舞蹈芭蕾","image":"http:\/\/*************\/resource\/play\/upload\/20250408\/145113dfAkJoKwCg.png","tag_list":[]},{"id":"10006","name":"音乐会","image":"http:\/\/*************\/resource\/play\/upload\/20250408\/1448369SbU_7vykE.png","tag_list":[]},{"id":"10007","name":"亲子演出","image":"http:\/\/*************\/resource\/play\/upload\/20250408\/144825FTdAI7yPy_.png","tag_list":[]},{"id":"10010","name":"音乐节","image":"http:\/\/*************\/resource\/play\/upload\/20250422\/145418ZAvXkbQMhU.png","tag_list":[]},{"id":"10014","name":"沉浸剧场","image":"http:\/\/*************\/resource\/play\/upload\/20250408\/144959tWsxiYBDA-.png","tag_list":[]},{"id":"10016","name":"相声","image":"http:\/\/*************\/resource\/play\/upload\/20250408\/144939LP2Ruhsuki.png","tag_list":[]},{"id":"10015","name":"脱口秀","image":"http:\/\/*************\/resource\/play\/upload\/20250408\/144949GXhLPNXxUa.png","tag_list":[]},{"id":"10009","name":"休闲展览","image":"http:\/\/*************\/resource\/play\/upload\/20250408\/144907oWhMCZrrG_.png","tag_list":[]},{"id":"10004","name":"话剧音乐剧","image":"http:\/\/*************\/resource\/play\/upload\/20250408\/145102PT7jed5WkQ.png","tag_list":[]},{"id":"10008","name":"其他","image":"http:\/\/*************\/resource\/play\/upload\/20250331\/164501dyI8a3idSN.png","tag_list":["标签 一","标签二"]}]}
INFO - 2025-07-07 10:02:08,258 - assertion_utils.py:35 -[assertion_utils:status_code_assert] - 状态码断言成功：接口实际返回状态码 200 == 200
INFO - 2025-07-07 10:02:08,258 - assertion_utils.py:94 -[assertion_utils:equal_assert] - 相等断言成功：接口实际结果 {'code': 0} == 预期结果：{'code': 0}
INFO - 2025-07-07 10:02:08,259 - assertion_utils.py:206 -[assertion_utils:assert_result] - 测试成功