INFO - 2025-07-07 13:33:36,477 - conftest.py:7 -[conftest:print_info] - ---------------接口测试开始---------------
INFO - 2025-07-07 13:33:36,521 - sendrequests.py:57 -[sendrequests:execute_api_request] - 接口名称：演出分类
INFO - 2025-07-07 13:33:36,522 - sendrequests.py:58 -[sendrequests:execute_api_request] - 请求地址：http://*************/api/show/info/category_list
INFO - 2025-07-07 13:33:36,522 - sendrequests.py:59 -[sendrequests:execute_api_request] - 请求方式：POST
INFO - 2025-07-07 13:33:36,522 - sendrequests.py:60 -[sendrequests:execute_api_request] - 请求头：{'Content-Type': 'application/x-www-form-urlencoded', 'Connection': 'Keep-Alive', 'Accept-Encoding': 'gzip'}
INFO - 2025-07-07 13:33:36,522 - sendrequests.py:61 -[sendrequests:execute_api_request] - 测试用例名：获取猫眼演出分类
INFO - 2025-07-07 13:33:36,522 - sendrequests.py:62 -[sendrequests:execute_api_request] - cookies值：None
INFO - 2025-07-07 13:33:36,522 - sendrequests.py:67 -[sendrequests:execute_api_request] - 参数类型：data
INFO - 2025-07-07 13:33:36,522 - sendrequests.py:69 -[sendrequests:execute_api_request] - 请求参数：{"channelCode": "C10000027", "merchant_code": "", "token": "lrDqVLEc8jnKaORIKiJB+y6nN1Io3lnKNueVwmjECnPs3mUEcTEbKdyma0pUIUY8s2ItjSU2QLLYFxywf0Grw9Hfdp3svbaytqb6Sc0oYJ/tO8wd56XQTybpwRkS8Qt8,C10000027"}
INFO - 2025-07-07 13:33:36,873 - apiutils_business.py:179 -[apiutils_business:execute_test_cases] - 接口实际返回结果：{"code":0,"msg":"","data":[{"id":"10001","name":"演唱会","image":"http:\/\/*************\/resource\/play\/upload\/20250421\/175345K0n_6qP6Pi.jpg","tag_list":["测试文案","演出演唱会12"]},{"id":"10002","name":"体育赛事","image":"http:\/\/*************\/resource\/play\/upload\/20250408\/144744oaSxDsD3Qu.png","tag_list":["孙颖莎","全红婵"]},{"id":"10003","name":"戏曲艺术","image":"http:\/\/*************\/resource\/play\/upload\/20250408\/1448079S7mHpY2QU.png","tag_list":[]},{"id":"10017","name":"Livehouse","image":"http:\/\/*************\/resource\/play\/upload\/20250408\/1449279xNEIQlg9D.png","tag_list":[]},{"id":"10005","name":"舞蹈芭蕾","image":"http:\/\/*************\/resource\/play\/upload\/20250408\/145113dfAkJoKwCg.png","tag_list":[]},{"id":"10006","name":"音乐会","image":"http:\/\/*************\/resource\/play\/upload\/20250408\/1448369SbU_7vykE.png","tag_list":[]},{"id":"10007","name":"亲子演出","image":"http:\/\/*************\/resource\/play\/upload\/20250408\/144825FTdAI7yPy_.png","tag_list":[]},{"id":"10010","name":"音乐节","image":"http:\/\/*************\/resource\/play\/upload\/20250422\/145418ZAvXkbQMhU.png","tag_list":[]},{"id":"10014","name":"沉浸剧场","image":"http:\/\/*************\/resource\/play\/upload\/20250408\/144959tWsxiYBDA-.png","tag_list":[]},{"id":"10016","name":"相声","image":"http:\/\/*************\/resource\/play\/upload\/20250408\/144939LP2Ruhsuki.png","tag_list":[]},{"id":"10015","name":"脱口秀","image":"http:\/\/*************\/resource\/play\/upload\/20250408\/144949GXhLPNXxUa.png","tag_list":[]},{"id":"10009","name":"休闲展览","image":"http:\/\/*************\/resource\/play\/upload\/20250408\/144907oWhMCZrrG_.png","tag_list":[]},{"id":"10004","name":"话剧音乐剧","image":"http:\/\/*************\/resource\/play\/upload\/20250408\/145102PT7jed5WkQ.png","tag_list":[]},{"id":"10008","name":"其他","image":"http:\/\/*************\/resource\/play\/upload\/20250331\/164501dyI8a3idSN.png","tag_list":["标签 一","标签二"]}]}
INFO - 2025-07-07 13:33:36,879 - assertion_utils.py:35 -[assertion_utils:status_code_assert] - 状态码断言成功：接口实际返回状态码 200 == 200
INFO - 2025-07-07 13:33:36,880 - assertion_utils.py:94 -[assertion_utils:equal_assert] - 相等断言成功：接口实际结果 {'code': 0} == 预期结果：{'code': 0}
INFO - 2025-07-07 13:33:36,881 - assertion_utils.py:206 -[assertion_utils:assert_result] - 测试成功
INFO - 2025-07-07 13:33:36,894 - conftest.py:9 -[conftest:print_info] - ---------------接口测试结束---------------