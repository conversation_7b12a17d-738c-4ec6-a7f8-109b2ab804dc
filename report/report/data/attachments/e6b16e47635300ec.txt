INFO - 2025-07-07 09:41:12,264 - web_service.py:517 -[web_service:execute_command_simple] - [play_1751852448] 第140行: testcase/play/test_play.py::TestPlay::test_play[api_info8]  INFO - 2025-07-07 09:41:12,264 - conftest.py:7 -[conftest:print_info] - ---------------接口测试开始---------------
INFO - 2025-07-07 09:41:12,271 - sendrequests.py:57 -[sendrequests:execute_api_request] - 接口名称：演出场次列表
INFO - 2025-07-07 09:41:12,271 - sendrequests.py:58 -[sendrequests:execute_api_request] - 请求地址：http://*************/api/show/info/performance_shows
INFO - 2025-07-07 09:41:12,272 - sendrequests.py:59 -[sendrequests:execute_api_request] - 请求方式：GET
INFO - 2025-07-07 09:41:12,272 - sendrequests.py:60 -[sendrequests:execute_api_request] - 请求头：{'Content-Type': 'application/x-www-form-urlencoded', 'Connection': 'Keep-Alive', 'Accept-Encoding': 'gzip'}
INFO - 2025-07-07 09:41:12,272 - sendrequests.py:61 -[sendrequests:execute_api_request] - 测试用例名：获取演出场次列表信息
INFO - 2025-07-07 09:41:12,272 - sendrequests.py:62 -[sendrequests:execute_api_request] - cookies值：None
INFO - 2025-07-07 09:41:12,272 - sendrequests.py:67 -[sendrequests:execute_api_request] - 参数类型：params
INFO - 2025-07-07 09:41:12,272 - sendrequests.py:69 -[sendrequests:execute_api_request] - 请求参数：{"performanceId": "26", "channelCode": "C10000027", "token": "8y4eoCKpp9tcMjp51DV4UEmI1hegk73JSMjjz1bMf39BjiKzdJqOMBsgTCwgY7EYCzAR1uOc6Yqxj4jNblZyacedjpTnhEFWf6eGZZnSrZzreIYsloXPIsHpej7HdUL6,C10000027"}
INFO - 2025-07-07 09:41:12,774 - apiutils_business.py:179 -[apiutils_business:execute_test_cases] - 接口实际返回结果：{"code":0,"msg":"","data":{"shows":[{"showId":"217","performanceId":"26","name":"2025-08-30 周六 20:00","showNote":"","startTime":"2025-08-30 20:00:00","endTime":"2025-08-30 22:00:00","showType":1,"showSeatType":0,"areaSvg":null,"areaUrl":null,"showUnusualStatus":0,"showRealNameLimit":null,"maxBuyLimitPerUser":null,"maxBuyLimitPerOrder":6,"onSaleTime":"2025-05-20 16:46:26","offSaleTime":"2025-08-30 20:00:00","saleable":1},{"showId":"48","performanceId":"26","name":"2025-12-01 周一 00:00","showNote":"","startTime":"2025-12-01 00:00:00","endTime":"2025-12-01 02:00:00","showType":1,"showSeatType":0,"areaSvg":null,"areaUrl":null,"showUnusualStatus":0,"showRealNameLimit":null,"maxBuyLimitPerUser":null,"maxBuyLimitPerOrder":6,"onSaleTime":"2025-03-13 14:21:40","offSaleTime":"2025-12-01 00:00:00","saleable":1}]}}
INFO - 2025-07-07 09:41:12,777 - assertion_utils.py:35 -[assertion_utils:status_code_assert] - 状态码断言成功：接口实际返回状态码 200 == 200
INFO - 2025-07-07 09:41:12,778 - web_service.py:517 -[web_service:execute_command_simple] - [play_1751852448] 第150行:  INFO - 2025-07-07 09:41:12,777 - assertion_utils.py:35 -[assertion_utils:status_code_assert] - 状态码断言成功：接口实际返回状态码 200 == 200
INFO - 2025-07-07 09:41:12,778 - assertion_utils.py:94 -[assertion_utils:equal_assert] - 相等断言成功：接口实际结果 {'code': 0} == 预期结果：{'code': 0}
INFO - 2025-07-07 09:41:12,779 - assertion_utils.py:206 -[assertion_utils:assert_result] - 测试成功
INFO - 2025-07-07 09:41:12,790 - conftest.py:9 -[conftest:print_info] - ---------------接口测试结束---------------