#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查Allure报告状态的诊断脚本
"""

import os
import json
import sys
from datetime import datetime

def check_report_status():
    """检查报告状态"""
    print("=" * 60)
    print("Allure报告状态检查")
    print("=" * 60)
    
    # 检查项目根目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    if 'unit_tools' in current_dir:
        # 从unit_tools目录运行
        project_root = os.path.dirname(current_dir)
    else:
        # 从report目录运行
        project_root = os.path.dirname(os.path.dirname(current_dir))
    print(f"项目根目录: {project_root}")
    
    # 检查temp目录（原始结果）
    temp_dir = os.path.join(project_root, 'report', 'result', 'temp')
    print(f"\n1. 检查原始结果目录: {temp_dir}")
    if os.path.exists(temp_dir):
        result_files = [f for f in os.listdir(temp_dir) if f.endswith('-result.json')]
        print(f"   ✓ 目录存在")
        print(f"   ✓ 测试结果文件数量: {len(result_files)}")
        
        # 显示最新的几个结果文件
        if result_files:
            result_files.sort(key=lambda x: os.path.getmtime(os.path.join(temp_dir, x)), reverse=True)
            print(f"   ✓ 最新的结果文件:")
            for i, file in enumerate(result_files[:5]):
                file_path = os.path.join(temp_dir, file)
                mtime = datetime.fromtimestamp(os.path.getmtime(file_path))
                print(f"      {i+1}. {file} (修改时间: {mtime})")
    else:
        print(f"   ✗ 目录不存在")
    
    # 检查生成的报告目录
    report_dir = os.path.join(project_root, 'report', 'report')
    print(f"\n2. 检查生成的报告目录: {report_dir}")
    if os.path.exists(report_dir):
        print(f"   ✓ 目录存在")
        
        # 检查summary.json
        summary_file = os.path.join(report_dir, 'widgets', 'summary.json')
        if os.path.exists(summary_file):
            print(f"   ✓ summary.json存在")
            try:
                with open(summary_file, 'r', encoding='utf-8') as f:
                    summary = json.load(f)
                    statistic = summary.get('statistic', {})
                    print(f"   ✓ 测试统计:")
                    print(f"      - 总数: {statistic.get('total', 0)}")
                    print(f"      - 通过: {statistic.get('passed', 0)}")
                    print(f"      - 失败: {statistic.get('failed', 0)}")
                    print(f"      - 跳过: {statistic.get('skipped', 0)}")
                    
                    # 检查时间戳
                    time_info = summary.get('time', {})
                    if 'start' in time_info:
                        start_time = datetime.fromtimestamp(time_info['start'] / 1000)
                        print(f"      - 开始时间: {start_time}")
                    if 'stop' in time_info:
                        stop_time = datetime.fromtimestamp(time_info['stop'] / 1000)
                        print(f"      - 结束时间: {stop_time}")
                        
            except Exception as e:
                print(f"   ✗ 读取summary.json失败: {e}")
        else:
            print(f"   ✗ summary.json不存在")
            
        # 检查test-cases目录
        test_cases_dir = os.path.join(report_dir, 'data', 'test-cases')
        if os.path.exists(test_cases_dir):
            test_case_files = [f for f in os.listdir(test_cases_dir) if f.endswith('.json')]
            print(f"   ✓ 测试用例文件数量: {len(test_case_files)}")
        else:
            print(f"   ✗ test-cases目录不存在")
            
        # 检查index.html
        index_file = os.path.join(report_dir, 'index.html')
        if os.path.exists(index_file):
            mtime = datetime.fromtimestamp(os.path.getmtime(index_file))
            print(f"   ✓ index.html存在 (修改时间: {mtime})")
        else:
            print(f"   ✗ index.html不存在")
    else:
        print(f"   ✗ 目录不存在")
    
    # 检查日志文件
    print(f"\n3. 检查今日日志文件:")
    logs_dir = os.path.join(project_root, 'logs')
    today = datetime.now().strftime('%Y%m%d')
    log_file = os.path.join(logs_dir, f'test.{today}.log')
    
    if os.path.exists(log_file):
        print(f"   ✓ 日志文件存在: {log_file}")
        
        # 统计测试开始和结束的次数
        with open(log_file, 'r', encoding='utf-8') as f:
            content = f.read()
            start_count = content.count('---------------接口测试开始---------------')
            end_count = content.count('---------------接口测试结束---------------')
            print(f"   ✓ 测试开始标记数量: {start_count}")
            print(f"   ✓ 测试结束标记数量: {end_count}")
            
            # 查找最后一次测试的时间
            lines = content.split('\n')
            last_test_time = None
            for line in reversed(lines):
                if '---------------接口测试结束---------------' in line:
                    # 提取时间戳
                    try:
                        time_part = line.split(' - ')[1].split(' - ')[0]
                        last_test_time = time_part
                        break
                    except:
                        pass
            
            if last_test_time:
                print(f"   ✓ 最后一次测试时间: {last_test_time}")
    else:
        print(f"   ✗ 日志文件不存在: {log_file}")
    
    print("\n" + "=" * 60)
    print("检查完成")
    print("=" * 60)
    
    # 给出建议
    print("\n建议操作:")
    print("1. 如果看到的报告数据不是最新的，请:")
    print("   - 清理浏览器缓存")
    print("   - 使用无痕/隐私模式打开报告")
    print("   - 或者运行 '刷新并打开报告.bat'")
    print("\n2. 如果报告文件不存在或数据不对，请:")
    print("   - 重新运行测试: python run.py play")
    print("   - 检查allure命令是否正确安装")

if __name__ == '__main__':
    check_report_status()
