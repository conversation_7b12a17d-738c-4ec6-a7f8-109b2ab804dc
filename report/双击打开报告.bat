@echo off
setlocal enabledelayedexpansion

set "chrome_path="
set reg_query_command=reg query "HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\App Paths\chrome.exe" /ve

for /f "tokens=2*" %%A in ('%reg_query_command%') do (
    if "%%A"=="REG_SZ" (
        set "tmp_chrome_path=%%B"
        if exist "!tmp_chrome_path!" (
            set "chrome_path=%%B"
        )
    )
)


if defined chrome_path (
    echo Chrome found at: "%chrome_path%"
    echo start a webserver ...
    start /b http_server.exe -port 5001
    timeout /t 2 /nobreak >nul
    start /WAIT chrome.exe --disable-web-security --disable-features=VizDisplayCompositor --no-first-run --disable-cache http://127.0.0.1:5001
) else (
    echo Chrome not found.
    echo start a webserver ...
    start /b http_server.exe -port 5001
    timeout /t 2 /nobreak >nul
    start /WAIT msedge.exe --disable-web-security --no-first-run http://127.0.0.1:5001
)


