{"name": "演出分类", "status": "passed", "attachments": [{"name": "接口地址", "source": "2eb71d7d-61b6-4bf1-a183-26b2bf6353c1-attachment.json", "type": "application/json"}, {"name": "接口名称", "source": "da9d60ae-0f6b-4813-a744-fece52777731-attachment.json", "type": "application/json"}, {"name": "请求方式", "source": "30ea2187-d7f3-45f6-a1eb-48a64cc75070-attachment.json", "type": "application/json"}, {"name": "请求头", "source": "6097a2fe-aef6-4493-9b02-2e5aff1cdf43-attachment.json", "type": "application/json"}, {"name": "<PERSON><PERSON>", "source": "c432ac25-7343-4100-aeb1-d6d5ad51c488-attachment.json", "type": "application/json"}, {"name": "测试用例名称", "source": "280f48bf-591a-41d3-988a-9a78d669728e-attachment.json", "type": "application/json"}, {"name": "参数类型", "source": "9e2ac806-3b12-480e-95d5-4d046135dd80-attachment.json", "type": "application/json"}, {"name": "请求参数json格式", "source": "4d79920c-d973-424a-a058-0cb6b6163ea2-attachment.json", "type": "application/json"}, {"name": "请求参数实际入参", "source": "95bf7d18-be17-497d-a7a2-c3ccf03283f4-attachment.json", "type": "application/json"}, {"name": "接口实际响应信息", "source": "dc7905a2-d17b-43dd-ba1c-5297473b5019-attachment.json", "type": "application/json"}, {"name": "状态码断言结果：成功", "source": "3dffdd5d-9276-4c05-81bb-89728e29016e-attachment.txt", "type": "text/plain"}, {"name": "相等断言结果：成功", "source": "71594067-45bf-4e7b-9c18-b0ab70b64bba-attachment.json", "type": "application/json"}, {"name": "📋 测试执行日志", "source": "0936b8bb-bed7-4a30-b023-bc75ffe8c6fa-attachment.txt", "type": "text/plain"}], "start": 1751851565677, "stop": 1751851566221, "uuid": "0fe39053-6aa1-4768-94ee-1a44141d1805", "historyId": "c36ab200ccb8723de7119149ea6c3088", "testCaseId": "e9970ce8394fe9b627f33514f0afecf5", "fullName": "testcase.play.test_play.TestPlay#test_play", "labels": [{"name": "story", "value": "C01_演出模块流程"}, {"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "34968-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}]}