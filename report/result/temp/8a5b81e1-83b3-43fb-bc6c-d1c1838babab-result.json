{"name": "**搜索**", "status": "passed", "attachments": [{"name": "接口地址", "source": "4e5e94a9-091a-498d-b8ad-cca92a8e3c37-attachment.json", "type": "application/json"}, {"name": "接口名称", "source": "7a906df6-1796-488e-9a68-a5de334f15b6-attachment.json", "type": "application/json"}, {"name": "请求方式", "source": "d529c314-66d3-4a73-920e-52a5c867e0b2-attachment.json", "type": "application/json"}, {"name": "请求头", "source": "03d0b139-93a9-42dd-8e64-ce47254bc9be-attachment.json", "type": "application/json"}, {"name": "<PERSON><PERSON>", "source": "8ef4fd56-72c6-4e11-b4d4-12bfdfefa55d-attachment.json", "type": "application/json"}, {"name": "测试用例名称", "source": "8f9a12d7-f84b-4206-90d0-4a43d7cb85de-attachment.json", "type": "application/json"}, {"name": "参数类型", "source": "cc192de6-3707-4b77-92bc-693416503a34-attachment.json", "type": "application/json"}, {"name": "请求参数json格式", "source": "e342d75a-7d22-4542-8dd4-a953b18ac60c-attachment.json", "type": "application/json"}, {"name": "请求参数实际入参", "source": "6436e737-1b43-4852-addf-adef056ec192-attachment.json", "type": "application/json"}, {"name": "接口实际响应信息", "source": "50d3421b-5043-43c0-a7df-1350e6a50077-attachment.json", "type": "application/json"}, {"name": "状态码断言结果：成功", "source": "6efb6825-3ed7-4082-a88d-b618d4edf2db-attachment.txt", "type": "text/plain"}, {"name": "相等断言结果：成功", "source": "d7e3575d-1e5c-49ab-868b-f8ba4cc26c81-attachment.json", "type": "application/json"}, {"name": "📋 测试执行日志", "source": "4af8095a-39dc-483e-8b5c-8a66733b8a56-attachment.txt", "type": "text/plain"}], "start": 1751853897749, "stop": 1751853898049, "uuid": "f9b24233-a7b1-4cf3-8b0e-696edbda3c0d", "historyId": "80ea16c71eb34b6e85110c3f875859f1", "testCaseId": "e9970ce8394fe9b627f33514f0afecf5", "fullName": "testcase.play.test_play.TestPlay#test_play", "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "39132-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}]}