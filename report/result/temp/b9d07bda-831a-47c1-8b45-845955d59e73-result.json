{"name": "首页广告位", "status": "passed", "attachments": [{"name": "接口地址", "source": "cd7a936c-778f-4de5-9d83-642c5b80b586-attachment.json", "type": "application/json"}, {"name": "接口名称", "source": "9e6bebb9-0351-428f-a9da-24f9c0a02007-attachment.json", "type": "application/json"}, {"name": "请求方式", "source": "489bb17f-a542-4e75-bddd-9d5b7ab4d214-attachment.json", "type": "application/json"}, {"name": "请求头", "source": "39142876-b17b-4454-8c25-dc210ed3c957-attachment.json", "type": "application/json"}, {"name": "<PERSON><PERSON>", "source": "71ded357-0e8c-4b99-8a65-8d7b3a39aed9-attachment.json", "type": "application/json"}, {"name": "测试用例名称", "source": "f681e98f-7cea-4ba9-8ca4-aed169df21de-attachment.json", "type": "application/json"}, {"name": "参数类型", "source": "b1516502-d0ed-49eb-8e44-80a875a2251d-attachment.json", "type": "application/json"}, {"name": "请求参数json格式", "source": "893ef169-6265-4221-8d32-f259a0724e25-attachment.json", "type": "application/json"}, {"name": "请求参数实际入参", "source": "5498ee6a-c98f-4ac9-857d-fb54b1376659-attachment.json", "type": "application/json"}, {"name": "接口实际响应信息", "source": "f7611421-dc34-4586-9c8a-1357296a2fad-attachment.json", "type": "application/json"}, {"name": "状态码断言结果：成功", "source": "b924bd1b-e515-45f1-ba07-ebaf6fb499a5-attachment.txt", "type": "text/plain"}, {"name": "相等断言结果：成功", "source": "e350c025-b16f-4767-89c2-9dc28d35e878-attachment.json", "type": "application/json"}, {"name": "📋 测试执行日志", "source": "fee0f0ae-ef50-4301-aca1-09aaa938ec31-attachment.txt", "type": "text/plain"}], "start": 1751866423947, "stop": 1751866424279, "uuid": "d5631706-35dc-45b3-8682-7bdd1f3605d9", "historyId": "998f7fb53b18ea3045f71922b591e307", "testCaseId": "e9970ce8394fe9b627f33514f0afecf5", "fullName": "testcase.play.test_play.TestPlay#test_play", "labels": [{"name": "story", "value": "C01_演出模块流程"}, {"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "16848-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}]}