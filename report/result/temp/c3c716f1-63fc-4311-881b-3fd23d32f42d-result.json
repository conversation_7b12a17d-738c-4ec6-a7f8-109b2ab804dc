{"name": "项目下场次票品列表", "status": "passed", "attachments": [{"name": "接口地址", "source": "b5484fd7-a4fe-4e11-9e45-ee976163bba5-attachment.json", "type": "application/json"}, {"name": "接口名称", "source": "1c1e5594-41b9-4fc1-bd42-613bb09e98d8-attachment.json", "type": "application/json"}, {"name": "请求方式", "source": "6c69aa8a-e933-4c84-bacc-55451f64fe4e-attachment.json", "type": "application/json"}, {"name": "请求头", "source": "fb964bc4-a150-4d8b-93b3-a76369830425-attachment.json", "type": "application/json"}, {"name": "<PERSON><PERSON>", "source": "c907f201-a1f9-428b-a7b7-4efeb31a24fb-attachment.json", "type": "application/json"}, {"name": "测试用例名称", "source": "4d532eb1-592f-459c-ba5c-9f92e9ce7c80-attachment.json", "type": "application/json"}, {"name": "参数类型", "source": "b8449a3b-413f-4070-9025-fde694a32806-attachment.json", "type": "application/json"}, {"name": "请求参数json格式", "source": "238fcbe6-f8d3-4ba3-ab13-7e96b13f5257-attachment.json", "type": "application/json"}, {"name": "请求参数实际入参", "source": "85706a3b-c6f4-440f-8a18-c44bd8288bb5-attachment.json", "type": "application/json"}, {"name": "接口实际响应信息", "source": "aaa3e031-ff34-4960-8f1a-80df82235422-attachment.json", "type": "application/json"}, {"name": "状态码断言结果：成功", "source": "c8991a87-feeb-4210-b90c-a00b2220ecdd-attachment.txt", "type": "text/plain"}, {"name": "相等断言结果：成功", "source": "26809f73-2b60-45eb-b1dc-53e4a1701e4a-attachment.json", "type": "application/json"}, {"name": "📋 测试执行日志", "source": "e7ad1817-d6c3-46f0-936b-3dad0990647a-attachment.txt", "type": "text/plain"}], "start": 1751851583032, "stop": 1751851583660, "uuid": "0c94e263-ea53-43b4-80e9-59affa921481", "historyId": "3287c72f85d7169cf4925a7d71668b28", "testCaseId": "e9970ce8394fe9b627f33514f0afecf5", "fullName": "testcase.play.test_play.TestPlay#test_play", "labels": [{"name": "story", "value": "C01_演出模块流程"}, {"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "34968-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}]}