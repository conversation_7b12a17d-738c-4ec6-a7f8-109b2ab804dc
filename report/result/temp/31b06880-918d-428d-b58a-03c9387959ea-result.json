{"name": "首页广告位", "status": "passed", "attachments": [{"name": "接口地址", "source": "87023dcf-cd58-47c4-9894-843cfa438788-attachment.json", "type": "application/json"}, {"name": "接口名称", "source": "f7a5be29-8199-4a1c-835c-c7ab1d835692-attachment.json", "type": "application/json"}, {"name": "请求方式", "source": "d9cc83e6-e546-47b8-922c-0d60cdfd7d2c-attachment.json", "type": "application/json"}, {"name": "请求头", "source": "c6b31fbc-65e0-43b8-8fd1-f6fcfdeff51c-attachment.json", "type": "application/json"}, {"name": "<PERSON><PERSON>", "source": "d275dbb4-8b8e-4e2c-8013-07f7e4c38e0e-attachment.json", "type": "application/json"}, {"name": "测试用例名称", "source": "8f56059f-e786-41f4-9558-cea96707cac0-attachment.json", "type": "application/json"}, {"name": "参数类型", "source": "414a8d16-cae6-40f4-a12e-957a1a0259c5-attachment.json", "type": "application/json"}, {"name": "请求参数json格式", "source": "0ffb5907-eb81-49a8-87d6-2e3ba1ef85f0-attachment.json", "type": "application/json"}, {"name": "请求参数实际入参", "source": "dc97f8fd-4a09-4cbf-b051-0a6c478383a9-attachment.json", "type": "application/json"}, {"name": "接口实际响应信息", "source": "49f794bf-a42e-4492-bf25-b863f8711b83-attachment.json", "type": "application/json"}, {"name": "状态码断言结果：成功", "source": "37ab9313-1275-41aa-9f0e-3718bb5e1543-attachment.txt", "type": "text/plain"}, {"name": "相等断言结果：成功", "source": "337090dd-d6bd-4244-b55d-c01634162cc6-attachment.json", "type": "application/json"}, {"name": "📋 测试执行日志", "source": "f260876a-5a5c-4db8-88c9-80fa45a1170b-attachment.txt", "type": "text/plain"}], "start": 1751852464680, "stop": 1751852464960, "uuid": "975aa52d-d1fd-4b8d-9746-6356ab7f0d58", "historyId": "998f7fb53b18ea3045f71922b591e307", "testCaseId": "e9970ce8394fe9b627f33514f0afecf5", "fullName": "testcase.play.test_play.TestPlay#test_play", "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "38092-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}]}