{"name": "项目下场次票品列表", "status": "passed", "attachments": [{"name": "接口地址", "source": "f93cf0e6-0e81-4531-bd7a-b0597eee8b6f-attachment.json", "type": "application/json"}, {"name": "接口名称", "source": "71cc1b84-f90e-4a35-a9a2-15554bf0c852-attachment.json", "type": "application/json"}, {"name": "请求方式", "source": "12862a69-5a16-4531-b4b6-e9682805873e-attachment.json", "type": "application/json"}, {"name": "请求头", "source": "2a4ddc09-8f7f-435e-86ab-580398b310c2-attachment.json", "type": "application/json"}, {"name": "<PERSON><PERSON>", "source": "35832dca-1fff-4a02-9c6c-afd67bd7e2e1-attachment.json", "type": "application/json"}, {"name": "测试用例名称", "source": "ddc851ae-1184-4dd4-8a9c-0110a6d34652-attachment.json", "type": "application/json"}, {"name": "参数类型", "source": "c9c3f293-605f-494b-87e7-d7b4e82899d2-attachment.json", "type": "application/json"}, {"name": "请求参数json格式", "source": "42768c01-702f-4c87-b286-54af45d8bad7-attachment.json", "type": "application/json"}, {"name": "请求参数实际入参", "source": "8036e0d7-211a-4549-a911-4f5f97de0140-attachment.json", "type": "application/json"}, {"name": "接口实际响应信息", "source": "c8d1da8b-46bb-4527-95a6-1083701d9fd0-attachment.json", "type": "application/json"}, {"name": "状态码断言结果：成功", "source": "cfaf95e5-b4c0-47fe-ac91-79e7d376c3b4-attachment.txt", "type": "text/plain"}, {"name": "相等断言结果：成功", "source": "13fc3a03-88af-4fa8-a547-d75129066165-attachment.json", "type": "application/json"}, {"name": "📋 测试执行日志", "source": "09ac7836-5dd1-469b-a9fc-1719971252e5-attachment.txt", "type": "text/plain"}], "start": 1751853247021, "stop": 1751853247589, "uuid": "d96caea2-0f7a-4108-a932-d7cc5fe3731f", "historyId": "3287c72f85d7169cf4925a7d71668b28", "testCaseId": "e9970ce8394fe9b627f33514f0afecf5", "fullName": "testcase.play.test_play.TestPlay#test_play", "labels": [{"name": "story", "value": "C01_演出模块流程"}, {"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "28812-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}]}