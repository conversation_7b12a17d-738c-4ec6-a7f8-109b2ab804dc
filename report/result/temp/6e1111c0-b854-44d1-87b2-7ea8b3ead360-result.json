{"name": "近期特惠", "status": "passed", "attachments": [{"name": "接口地址", "source": "abc3d184-bbd1-4392-b734-9fdf4cc6107c-attachment.json", "type": "application/json"}, {"name": "接口名称", "source": "ec005f7c-b5c0-4977-a1a9-7b0cc6f4b562-attachment.json", "type": "application/json"}, {"name": "请求方式", "source": "c3c7eebf-fe16-4e9a-a3f0-bdb205d827c2-attachment.json", "type": "application/json"}, {"name": "请求头", "source": "3392574d-8dcb-4bb7-ac5b-b624e03559b2-attachment.json", "type": "application/json"}, {"name": "<PERSON><PERSON>", "source": "f1d8b314-a922-4e27-baba-068eddceb25d-attachment.json", "type": "application/json"}, {"name": "测试用例名称", "source": "34b56418-f37f-4124-a582-9b8a25ad664b-attachment.json", "type": "application/json"}, {"name": "参数类型", "source": "26663951-2aa9-4c39-a2c3-2a35fd9d7fe6-attachment.json", "type": "application/json"}, {"name": "请求参数json格式", "source": "ee9a9ec4-47f8-471a-a753-accf517d325e-attachment.json", "type": "application/json"}, {"name": "请求参数实际入参", "source": "c36df9e6-8099-4d8a-90f2-ee7dc4bb7a9f-attachment.json", "type": "application/json"}, {"name": "接口实际响应信息", "source": "ae67a142-f24b-4411-96aa-170392219081-attachment.json", "type": "application/json"}, {"name": "状态码断言结果：成功", "source": "6c3b70dd-dbc7-4048-b4a6-db4ef8de2fad-attachment.txt", "type": "text/plain"}, {"name": "相等断言结果：成功", "source": "b4421dd7-5545-42d9-aa42-36962d94b4aa-attachment.json", "type": "application/json"}, {"name": "📋 测试执行日志", "source": "06abc5f2-37b9-4547-a7ca-80d6bfc7b3c8-attachment.txt", "type": "text/plain"}], "start": 1751852460603, "stop": 1751852461345, "uuid": "dd8ce8c6-b868-4215-ab94-e803b8b2f8da", "historyId": "b9dc0ab2757f5b153151bfbd4cc293dd", "testCaseId": "e9970ce8394fe9b627f33514f0afecf5", "fullName": "testcase.play.test_play.TestPlay#test_play", "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "38092-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}]}