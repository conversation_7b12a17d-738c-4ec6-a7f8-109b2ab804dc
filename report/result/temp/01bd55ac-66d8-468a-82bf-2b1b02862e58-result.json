{"name": "**搜索**", "status": "passed", "attachments": [{"name": "接口地址", "source": "f2f3a938-f86e-4b59-8206-f66753e0c310-attachment.json", "type": "application/json"}, {"name": "接口名称", "source": "3ff7a1b3-88b8-4589-be77-85c68f533720-attachment.json", "type": "application/json"}, {"name": "请求方式", "source": "a8102ed6-eef2-41b1-b4d4-067d5e6ee35e-attachment.json", "type": "application/json"}, {"name": "请求头", "source": "19d4d246-0f27-4a6f-96fb-bac4989064e1-attachment.json", "type": "application/json"}, {"name": "<PERSON><PERSON>", "source": "6ce60743-6444-4213-9452-7ea45e78642b-attachment.json", "type": "application/json"}, {"name": "测试用例名称", "source": "d48569da-79b0-4f8b-b977-cb1f0eeda880-attachment.json", "type": "application/json"}, {"name": "参数类型", "source": "4859eadd-36b8-41d7-b8a0-374bb1498c92-attachment.json", "type": "application/json"}, {"name": "请求参数json格式", "source": "307491e8-9800-4aa1-b2f1-276cacf4c5f0-attachment.json", "type": "application/json"}, {"name": "请求参数实际入参", "source": "5dc73a52-179e-4887-a598-2b1b80c02656-attachment.json", "type": "application/json"}, {"name": "接口实际响应信息", "source": "38d6b3c7-1cf1-4cf6-954a-2d64d7ef6542-attachment.json", "type": "application/json"}, {"name": "状态码断言结果：成功", "source": "0cd51cb3-aa9e-44c5-83be-7dff186e2743-attachment.txt", "type": "text/plain"}, {"name": "相等断言结果：成功", "source": "678f540f-b3b2-4c52-aa78-3a81813f0a64-attachment.json", "type": "application/json"}, {"name": "📋 测试执行日志", "source": "1f85cd75-b9cc-48cf-8da6-2d9a098140aa-attachment.txt", "type": "text/plain"}], "start": 1751866427644, "stop": 1751866427947, "uuid": "4e59a66a-0687-41cc-ba76-94aebf1802fd", "historyId": "80ea16c71eb34b6e85110c3f875859f1", "testCaseId": "e9970ce8394fe9b627f33514f0afecf5", "fullName": "testcase.play.test_play.TestPlay#test_play", "labels": [{"name": "story", "value": "C01_演出模块流程"}, {"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "16848-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}]}