{"name": "取消订单", "status": "passed", "attachments": [{"name": "接口地址", "source": "0e85c170-268e-42ff-a280-a37f6ffc680d-attachment.json", "type": "application/json"}, {"name": "接口名称", "source": "87fab9d2-f2b0-4e52-813a-816ef92d07f5-attachment.json", "type": "application/json"}, {"name": "请求方式", "source": "304c892a-dc23-409d-938f-c57d95d6951a-attachment.json", "type": "application/json"}, {"name": "请求头", "source": "6933ae90-9d4d-4c9d-993f-292e07d5f29d-attachment.json", "type": "application/json"}, {"name": "<PERSON><PERSON>", "source": "08bde1e6-d819-4415-a5bd-3ce93a5e7e21-attachment.json", "type": "application/json"}, {"name": "测试用例名称", "source": "11fb5f72-5276-4eb0-b984-9ac6342ba22e-attachment.json", "type": "application/json"}, {"name": "参数类型", "source": "1563a800-7fc3-43c2-bb18-1554baaa3592-attachment.json", "type": "application/json"}, {"name": "请求参数json格式", "source": "14bf91dc-44c9-48db-9e52-f89daa99b49e-attachment.json", "type": "application/json"}, {"name": "请求参数实际入参", "source": "a4b097ff-d564-4092-a737-f43384db5a49-attachment.json", "type": "application/json"}, {"name": "接口实际响应信息", "source": "4069bd82-1591-4eb3-92a4-56d22b81f0aa-attachment.json", "type": "application/json"}, {"name": "状态码断言结果：成功", "source": "389c1439-b5e2-4d87-8144-d16072c74ab8-attachment.txt", "type": "text/plain"}, {"name": "相等断言结果：成功", "source": "861a9e87-e838-4d58-8688-e3c014c40824-attachment.json", "type": "application/json"}, {"name": "📋 测试执行日志", "source": "4943b1e4-ee89-40b2-9187-fd34b1b0066f-attachment.txt", "type": "text/plain"}], "parameters": [{"name": "api_info", "value": "{'baseInfo': {'module': '演出票', 'api_name': '取消订单', 'host': '${get_host(play)}', 'url': '/api/show/order/cancel', 'method': 'post', 'headers': '${get_headers(app)}'}, 'testCase': {'case_name': '取消订单', 'validation': [{'code': 200}, {'eq': {'code': 0}}], 'extract': 0, 'extract_list': np.float64(0.0), 'data': {'orderId': '${get_extract_data(orderId)}', 'channelCode': '${appKey()}', 'token': '${get_extract_data(token)}'}}}"}], "start": 1751853016051, "stop": 1751853018081, "uuid": "0442eda7-d7f4-4d83-ae12-d8112ff3267e", "historyId": "86ddc9742985e7678bcca10364db8520", "testCaseId": "e9970ce8394fe9b627f33514f0afecf5", "fullName": "testcase.play.test_play.TestPlay#test_play", "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "37476-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}]}