{"name": "演出场次列表", "status": "passed", "attachments": [{"name": "接口地址", "source": "16b93148-2d0c-4100-b23e-fa21497dc549-attachment.json", "type": "application/json"}, {"name": "接口名称", "source": "58ea5c58-33af-47a6-9607-cddf8c9ae6ac-attachment.json", "type": "application/json"}, {"name": "请求方式", "source": "d65941d8-6ea8-4936-ab20-981998f8b751-attachment.json", "type": "application/json"}, {"name": "请求头", "source": "f5cb006d-8378-4219-8ac5-b521a3ced9c2-attachment.json", "type": "application/json"}, {"name": "<PERSON><PERSON>", "source": "f27617e4-1e19-431c-927a-c85264cd4626-attachment.json", "type": "application/json"}, {"name": "测试用例名称", "source": "68ad82ca-0c00-4832-90a7-b7222604e6c3-attachment.json", "type": "application/json"}, {"name": "参数类型", "source": "8b1b66a6-5a18-4014-a20d-5055d892c833-attachment.json", "type": "application/json"}, {"name": "请求参数json格式", "source": "60773a90-f0b7-4bb7-8aed-fccb6034f397-attachment.json", "type": "application/json"}, {"name": "请求参数实际入参", "source": "deb450c3-c942-456d-8dd2-dce27a7541af-attachment.json", "type": "application/json"}, {"name": "接口实际响应信息", "source": "4d023743-3fda-40d8-8f55-3a63017b369b-attachment.json", "type": "application/json"}, {"name": "状态码断言结果：成功", "source": "352751f2-fd73-4b43-8bd8-d0365be91c0d-attachment.txt", "type": "text/plain"}, {"name": "相等断言结果：成功", "source": "85b573cd-cfcd-43fe-827a-70df953a66ae-attachment.json", "type": "application/json"}, {"name": "📋 测试执行日志", "source": "61a3e475-8649-420d-a6cf-8cd367f3ca64-attachment.txt", "type": "text/plain"}], "parameters": [{"name": "api_info", "value": "{'baseInfo': {'module': '演出票', 'api_name': '演出场次列表', 'host': '${get_host(play)}', 'url': '/api/show/info/performance_shows', 'method': 'get', 'headers': '${get_headers(app)}'}, 'testCase': {'case_name': '获取演出场次列表信息', 'validation': [{'code': 200}, {'eq': {'code': 0}}], 'extract': {'extract_auto': 'performance_shows'}, 'extract_list': np.float64(0.0), 'params': {'performanceId': '${get_extract_data(performance_id)}', 'channelCode': '${appKey()}', 'token': '${get_extract_data(token)}'}}}"}], "start": 1751853006939, "stop": 1751853007470, "uuid": "720723ed-9d4f-46da-a834-46ab4f55d893", "historyId": "6b8675e1b81e5293918eed8f21bfedcf", "testCaseId": "e9970ce8394fe9b627f33514f0afecf5", "fullName": "testcase.play.test_play.TestPlay#test_play", "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "37476-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}]}