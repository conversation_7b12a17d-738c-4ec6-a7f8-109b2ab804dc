{"name": "今日必抢", "status": "passed", "attachments": [{"name": "接口地址", "source": "76b90b08-b4eb-437b-a148-a02888515d53-attachment.json", "type": "application/json"}, {"name": "接口名称", "source": "c3de2bed-1ae3-4c87-9268-ca44c6b79631-attachment.json", "type": "application/json"}, {"name": "请求方式", "source": "d2cb5aa7-4680-42ff-9d77-c3828b81835e-attachment.json", "type": "application/json"}, {"name": "请求头", "source": "fb5c32ca-e287-435e-b9bc-9e7641cc3f4a-attachment.json", "type": "application/json"}, {"name": "<PERSON><PERSON>", "source": "bc82afb1-1dd2-455e-8f1f-b6dfd38c44a1-attachment.json", "type": "application/json"}, {"name": "测试用例名称", "source": "cc654c03-7a54-4fa0-b786-da74e799a661-attachment.json", "type": "application/json"}, {"name": "参数类型", "source": "545d0b8b-6bc2-419d-862b-2707ed163d5b-attachment.json", "type": "application/json"}, {"name": "请求参数json格式", "source": "b0c0057f-dd8a-42b8-b21d-8f8a27dfc0d8-attachment.json", "type": "application/json"}, {"name": "请求参数实际入参", "source": "866dbfd2-12c5-4291-b144-538bbcb45264-attachment.json", "type": "application/json"}, {"name": "接口实际响应信息", "source": "8f5e6785-8dd2-4ba4-a386-8aa1396ebd68-attachment.json", "type": "application/json"}, {"name": "状态码断言结果：成功", "source": "c3e8a770-ae3b-4f18-a768-203c87064a64-attachment.txt", "type": "text/plain"}, {"name": "相等断言结果：成功", "source": "326a9324-c68b-4d70-a766-b2e7a7fe89a2-attachment.json", "type": "application/json"}, {"name": "📋 测试执行日志", "source": "f52c0488-d601-45f9-848d-ae2a07c71d5c-attachment.txt", "type": "text/plain"}], "start": 1751852458787, "stop": 1751852459086, "uuid": "5cfee988-b10c-4243-a4ed-398899524b7f", "historyId": "09729bd43bb947d1ebe3c16a80dc6e25", "testCaseId": "e9970ce8394fe9b627f33514f0afecf5", "fullName": "testcase.play.test_play.TestPlay#test_play", "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "38092-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}]}