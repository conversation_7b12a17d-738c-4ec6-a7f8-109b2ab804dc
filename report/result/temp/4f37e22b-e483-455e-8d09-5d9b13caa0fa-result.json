{"name": "根据场次获取取票方式", "status": "passed", "attachments": [{"name": "接口地址", "source": "a7097460-f288-4615-b989-d97c725398c0-attachment.json", "type": "application/json"}, {"name": "接口名称", "source": "df255e21-bc50-4a99-8afe-210ce1f0b68f-attachment.json", "type": "application/json"}, {"name": "请求方式", "source": "61dfb9eb-655a-4835-b6d4-84a008a3bc02-attachment.json", "type": "application/json"}, {"name": "请求头", "source": "4a105931-86f5-42db-8eb9-565340b9db8e-attachment.json", "type": "application/json"}, {"name": "<PERSON><PERSON>", "source": "3c51732c-7135-45c4-ba5b-6ce7ca3704e5-attachment.json", "type": "application/json"}, {"name": "测试用例名称", "source": "13f887ab-d628-4efd-9e8b-0dbf12d75a08-attachment.json", "type": "application/json"}, {"name": "参数类型", "source": "2e0d67c8-cc54-4b14-944a-87d82a57afdc-attachment.json", "type": "application/json"}, {"name": "请求参数json格式", "source": "387b5f42-44df-49ac-8c36-2a0d6fe08df2-attachment.json", "type": "application/json"}, {"name": "请求参数实际入参", "source": "e368bcd1-95da-47c7-89fa-5a46f89faee4-attachment.json", "type": "application/json"}, {"name": "接口实际响应信息", "source": "a382b7ef-21c6-4add-9107-31ab85f8cd87-attachment.json", "type": "application/json"}, {"name": "状态码断言结果：成功", "source": "312b44c7-91d2-45dc-8eb9-0929812ad261-attachment.txt", "type": "text/plain"}, {"name": "相等断言结果：成功", "source": "cdc35dd7-4217-4b7f-aff2-9d7403cd56b9-attachment.json", "type": "application/json"}, {"name": "📋 测试执行日志", "source": "1eb6f5d4-b62a-4c04-b512-215bb0338b68-attachment.txt", "type": "text/plain"}], "start": 1751853905776, "stop": 1751853906248, "uuid": "61b40f69-b900-49fb-81f1-67b555ba9803", "historyId": "40f667e7e7d9255278cb56eb99290821", "testCaseId": "e9970ce8394fe9b627f33514f0afecf5", "fullName": "testcase.play.test_play.TestPlay#test_play", "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "39132-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}]}