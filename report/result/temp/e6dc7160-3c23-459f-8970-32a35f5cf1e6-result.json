{"name": "今日必抢", "status": "passed", "attachments": [{"name": "接口地址", "source": "904929af-51af-42f8-acce-1b1767aba02a-attachment.json", "type": "application/json"}, {"name": "接口名称", "source": "9d16c080-9cd7-4e30-8efc-4181c18b69e3-attachment.json", "type": "application/json"}, {"name": "请求方式", "source": "41dd0e6d-9b44-4f22-b44b-0085b64503b0-attachment.json", "type": "application/json"}, {"name": "请求头", "source": "bc0078d4-2eef-425a-bd9d-f66cbeb02d3b-attachment.json", "type": "application/json"}, {"name": "<PERSON><PERSON>", "source": "56909700-1290-4e6b-8cd4-9dcaec4a863f-attachment.json", "type": "application/json"}, {"name": "测试用例名称", "source": "177f73b2-b0cc-42d8-ad1b-326c59c37bb7-attachment.json", "type": "application/json"}, {"name": "参数类型", "source": "517e6ded-5422-4897-b07c-d4a93f3e3dc4-attachment.json", "type": "application/json"}, {"name": "请求参数json格式", "source": "78ef1552-51d9-44f5-8cb0-83317ba25a9d-attachment.json", "type": "application/json"}, {"name": "请求参数实际入参", "source": "c1f22e27-1d3e-4881-886a-cb5b7d57af5d-attachment.json", "type": "application/json"}, {"name": "接口实际响应信息", "source": "638846d4-45b9-4a1c-b92d-62dd156f0dc4-attachment.json", "type": "application/json"}, {"name": "状态码断言结果：成功", "source": "bedf642c-752e-4880-a902-36cd2e9e3d5d-attachment.txt", "type": "text/plain"}, {"name": "相等断言结果：成功", "source": "86a2d84b-4b4d-47f1-aa38-d86f3893f03d-attachment.json", "type": "application/json"}, {"name": "📋 测试执行日志", "source": "23162079-abd8-4c55-baf1-65a4f5e1f238-attachment.txt", "type": "text/plain"}], "parameters": [{"name": "api_info", "value": "{'baseInfo': {'module': '演出票', 'api_name': '今日必抢', 'host': '${get_host(play)}', 'url': '/api/show/info/home_today_sale_list', 'method': 'post', 'headers': '${get_headers(app)}'}, 'testCase': {'case_name': '获取今日必抢演出列表', 'validation': [{'code': 200}, {'eq': {'code': 0}}], 'extract': {'extract_auto': 'home_today_sale_list'}, 'extract_list': np.float64(0.0), 'data': {'page': 1, 'pageSize': 20, 'channelCode': '${appKey()}', 'token': '${get_extract_data(token)}'}}}"}], "start": 1751852993830, "stop": 1751852994144, "uuid": "4ebd982a-3897-4853-8a5d-ca7035849961", "historyId": "240ea0a77309ba41fd9d13d8f2320d63", "testCaseId": "e9970ce8394fe9b627f33514f0afecf5", "fullName": "testcase.play.test_play.TestPlay#test_play", "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "37476-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}]}