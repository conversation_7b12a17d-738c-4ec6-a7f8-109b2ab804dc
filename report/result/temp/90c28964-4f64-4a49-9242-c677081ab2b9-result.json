{"name": "为你推荐", "status": "passed", "attachments": [{"name": "接口地址", "source": "6f49d9cb-034d-46de-9b57-216cec6ad457-attachment.json", "type": "application/json"}, {"name": "接口名称", "source": "4fb21c37-cf33-4a28-92d2-3f1de34c002c-attachment.json", "type": "application/json"}, {"name": "请求方式", "source": "c4ec79e1-4976-4b28-a5cd-a69376da13cb-attachment.json", "type": "application/json"}, {"name": "请求头", "source": "ca7e9e31-75e9-42a4-a854-eec6c5f5c16d-attachment.json", "type": "application/json"}, {"name": "<PERSON><PERSON>", "source": "103e4122-ddc6-4315-96a7-cbefb18063a6-attachment.json", "type": "application/json"}, {"name": "测试用例名称", "source": "560313cd-cf83-4738-8ff0-48e49a9bfa13-attachment.json", "type": "application/json"}, {"name": "参数类型", "source": "bb012171-56e1-43df-97ad-89969c1535e0-attachment.json", "type": "application/json"}, {"name": "请求参数json格式", "source": "390eadc3-f149-4b4c-b95e-af20e671fbc1-attachment.json", "type": "application/json"}, {"name": "请求参数实际入参", "source": "9dbb41a6-79dc-45c4-b5a9-6d0f0d616b04-attachment.json", "type": "application/json"}, {"name": "接口实际响应信息", "source": "b39b2bb1-bb0f-46ea-99b8-ead427fe902a-attachment.json", "type": "application/json"}, {"name": "状态码断言结果：成功", "source": "22844eef-cab7-4861-aae5-d704dd8b4f86-attachment.txt", "type": "text/plain"}, {"name": "相等断言结果：成功", "source": "c10d7077-6164-412c-97fd-26a95e5067bb-attachment.json", "type": "application/json"}, {"name": "📋 测试执行日志", "source": "c7cb550a-9976-43f9-bf07-be3a43e6f816-attachment.txt", "type": "text/plain"}], "start": 1751852462852, "stop": 1751852463165, "uuid": "73710ec1-9e81-45ca-8593-07fac3a0b515", "historyId": "a03ba38a51bcc9af12332b9cb9ea6b41", "testCaseId": "e9970ce8394fe9b627f33514f0afecf5", "fullName": "testcase.play.test_play.TestPlay#test_play", "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "38092-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}]}