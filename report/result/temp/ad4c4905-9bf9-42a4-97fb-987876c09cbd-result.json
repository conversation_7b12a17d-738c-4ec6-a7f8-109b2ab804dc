{"name": "今日必抢", "status": "passed", "attachments": [{"name": "接口地址", "source": "ae2e5ae5-af88-4776-959b-eca6943f2a45-attachment.json", "type": "application/json"}, {"name": "接口名称", "source": "87e15895-6a52-4dec-81c6-334947c31849-attachment.json", "type": "application/json"}, {"name": "请求方式", "source": "58d54cc1-8c80-4042-9c42-aa47e09d9c28-attachment.json", "type": "application/json"}, {"name": "请求头", "source": "ef0ea56a-49ee-4deb-96c8-80000f080047-attachment.json", "type": "application/json"}, {"name": "<PERSON><PERSON>", "source": "106d5b20-1ef1-4e98-9ee8-1aebe8822d6d-attachment.json", "type": "application/json"}, {"name": "测试用例名称", "source": "90092b65-1a7b-4b1e-8ea1-5f977155ef40-attachment.json", "type": "application/json"}, {"name": "参数类型", "source": "4ee1d1a1-4bfa-4977-92cc-2bfe27232ba5-attachment.json", "type": "application/json"}, {"name": "请求参数json格式", "source": "bc1887e2-6780-4e14-8182-3747153bcb25-attachment.json", "type": "application/json"}, {"name": "请求参数实际入参", "source": "4b90125b-379e-4e74-a3d4-a137439c02ad-attachment.json", "type": "application/json"}, {"name": "接口实际响应信息", "source": "e0747080-a206-4ac9-818d-a473dc5f549b-attachment.json", "type": "application/json"}, {"name": "状态码断言结果：成功", "source": "600eecb6-5274-4f7e-8dba-414d4c0e0503-attachment.txt", "type": "text/plain"}, {"name": "相等断言结果：成功", "source": "1521bd31-642f-4fad-bee9-3c711664de5e-attachment.json", "type": "application/json"}, {"name": "📋 测试执行日志", "source": "66a4b06f-9809-485f-aff7-22c089249ad8-attachment.txt", "type": "text/plain"}], "start": 1751866418410, "stop": 1751866418751, "uuid": "8b429dd4-4878-448d-97fe-184735f345f3", "historyId": "09729bd43bb947d1ebe3c16a80dc6e25", "testCaseId": "e9970ce8394fe9b627f33514f0afecf5", "fullName": "testcase.play.test_play.TestPlay#test_play", "labels": [{"name": "story", "value": "C01_演出模块流程"}, {"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "16848-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}]}