{"name": "近期特惠", "status": "passed", "attachments": [{"name": "接口地址", "source": "9948570a-d47d-4d2c-8a0d-9993ab9cc172-attachment.json", "type": "application/json"}, {"name": "接口名称", "source": "a7ce3b78-0ba2-4c5c-b76b-90c89e11e32a-attachment.json", "type": "application/json"}, {"name": "请求方式", "source": "fab79227-5013-4ef4-b5b4-5b9da4091ca3-attachment.json", "type": "application/json"}, {"name": "请求头", "source": "e49de431-c8ef-4244-8e29-97bd483727db-attachment.json", "type": "application/json"}, {"name": "<PERSON><PERSON>", "source": "7f27f921-573e-4c39-8de9-e155b6ac28bb-attachment.json", "type": "application/json"}, {"name": "测试用例名称", "source": "4e2c42b6-7b24-4c95-8c7b-40a63d17e9b7-attachment.json", "type": "application/json"}, {"name": "参数类型", "source": "7172841c-55e6-4bd4-a4b0-ea7f4d506f45-attachment.json", "type": "application/json"}, {"name": "请求参数json格式", "source": "ee78de57-379e-4400-8e98-023a3022c05b-attachment.json", "type": "application/json"}, {"name": "请求参数实际入参", "source": "f20d49ab-4376-4248-9dcb-f29ff7295685-attachment.json", "type": "application/json"}, {"name": "接口实际响应信息", "source": "d6779c64-d510-49ad-8622-ff573357a9eb-attachment.json", "type": "application/json"}, {"name": "状态码断言结果：成功", "source": "565b7ebb-29d3-4369-b2d1-22a683afb4fe-attachment.txt", "type": "text/plain"}, {"name": "相等断言结果：成功", "source": "491e4fc9-4618-4bfa-a3e8-da03180ca561-attachment.json", "type": "application/json"}, {"name": "📋 测试执行日志", "source": "188401a9-2945-4711-8fae-80eb03aa7d92-attachment.txt", "type": "text/plain"}], "start": 1751853890455, "stop": 1751853890758, "uuid": "5f775ebe-826e-475a-8dc9-ef8b33b4cece", "historyId": "b9dc0ab2757f5b153151bfbd4cc293dd", "testCaseId": "e9970ce8394fe9b627f33514f0afecf5", "fullName": "testcase.play.test_play.TestPlay#test_play", "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "39132-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}]}