{"name": "为你推荐", "status": "passed", "attachments": [{"name": "接口地址", "source": "245d1a2e-4bc5-4357-b4f4-ec1445b69378-attachment.json", "type": "application/json"}, {"name": "接口名称", "source": "6a945196-518e-4810-bffe-37845e508f8a-attachment.json", "type": "application/json"}, {"name": "请求方式", "source": "420c97a5-0a9c-46d0-8bff-492ae4fbe534-attachment.json", "type": "application/json"}, {"name": "请求头", "source": "fcef1049-2977-4d8b-9ab6-59d8f06e7fa2-attachment.json", "type": "application/json"}, {"name": "<PERSON><PERSON>", "source": "5c2074f3-f863-43ba-b716-7fc943b7ecdf-attachment.json", "type": "application/json"}, {"name": "测试用例名称", "source": "2b20ddf6-07ef-4515-86d6-0ee4a1a33e32-attachment.json", "type": "application/json"}, {"name": "参数类型", "source": "2d28b310-bb86-4ac5-9811-d6ee5f3f880f-attachment.json", "type": "application/json"}, {"name": "请求参数json格式", "source": "abd144f4-59ab-47e1-b567-a6e89defff4d-attachment.json", "type": "application/json"}, {"name": "请求参数实际入参", "source": "4169b0c0-4f9c-4e64-9602-3c4052ad518f-attachment.json", "type": "application/json"}, {"name": "接口实际响应信息", "source": "de6d8b0c-c590-4956-8f1f-c3d3cf27af05-attachment.json", "type": "application/json"}, {"name": "状态码断言结果：成功", "source": "9a9567e4-e101-4096-8b99-6daae63844d3-attachment.txt", "type": "text/plain"}, {"name": "相等断言结果：成功", "source": "00608204-5c77-414f-9edc-b81379644aa6-attachment.json", "type": "application/json"}, {"name": "📋 测试执行日志", "source": "40e0f316-6f80-4ab8-b389-b387164075e8-attachment.txt", "type": "text/plain"}], "start": 1751853235620, "stop": 1751853235932, "uuid": "7c751c79-b3f3-4a2a-846b-1dd92238d960", "historyId": "a03ba38a51bcc9af12332b9cb9ea6b41", "testCaseId": "e9970ce8394fe9b627f33514f0afecf5", "fullName": "testcase.play.test_play.TestPlay#test_play", "labels": [{"name": "story", "value": "C01_演出模块流程"}, {"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "28812-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}]}