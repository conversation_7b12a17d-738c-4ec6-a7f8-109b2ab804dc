{"name": "近期特惠", "status": "passed", "attachments": [{"name": "接口地址", "source": "db041910-a16b-4d6e-9492-91bb8ced332c-attachment.json", "type": "application/json"}, {"name": "接口名称", "source": "525504c3-0b40-4510-88cd-3c189a1daff1-attachment.json", "type": "application/json"}, {"name": "请求方式", "source": "d5a8a500-987b-4517-984e-88f15944a156-attachment.json", "type": "application/json"}, {"name": "请求头", "source": "a0da8a18-73e5-49e1-b2d1-7d09a01636ae-attachment.json", "type": "application/json"}, {"name": "<PERSON><PERSON>", "source": "3083fc86-c2f0-440c-a78d-68bd9fe3a481-attachment.json", "type": "application/json"}, {"name": "测试用例名称", "source": "ca76e523-3917-48c4-9f67-a96b4896c67b-attachment.json", "type": "application/json"}, {"name": "参数类型", "source": "8a7a95db-6610-40bd-95b3-de71ebfef5a6-attachment.json", "type": "application/json"}, {"name": "请求参数json格式", "source": "ac8574a0-83a9-4db6-a288-dcc3c4cd90b6-attachment.json", "type": "application/json"}, {"name": "请求参数实际入参", "source": "4646e864-b2c5-438e-9ead-86276d7d93e3-attachment.json", "type": "application/json"}, {"name": "接口实际响应信息", "source": "3a5ff78a-52a0-4db6-92be-0c9bc91992ce-attachment.json", "type": "application/json"}, {"name": "状态码断言结果：成功", "source": "4dfc82dd-fb7e-411e-9d44-0db893082199-attachment.txt", "type": "text/plain"}, {"name": "相等断言结果：成功", "source": "30b51bc1-0ba4-4780-8829-8a7c46c51bf7-attachment.json", "type": "application/json"}, {"name": "📋 测试执行日志", "source": "e93e1226-b4e4-4f96-87cb-64e71dd00552-attachment.txt", "type": "text/plain"}], "parameters": [{"name": "api_info", "value": "{'baseInfo': {'module': '演出票', 'api_name': '近期特惠', 'host': '${get_host(play)}', 'url': '/api/show/info/home_near_show_list', 'method': 'post', 'headers': '${get_headers(app)}'}, 'testCase': {'case_name': '获取近期特惠演出列表', 'validation': [{'code': 200}, {'eq': {'code': 0}}], 'extract': 0, 'extract_list': np.float64(0.0), 'data': {'page': 1, 'pageSize': 20, 'channelCode': '${appKey()}', 'token': '${get_extract_data(token)}'}}}"}], "start": 1751852995665, "stop": 1751852995983, "uuid": "92f6020f-fc91-44c9-84f8-f2ab7e33a11e", "historyId": "dcf03e580545918eaa3c4bf9273130d9", "testCaseId": "e9970ce8394fe9b627f33514f0afecf5", "fullName": "testcase.play.test_play.TestPlay#test_play", "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "37476-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}]}