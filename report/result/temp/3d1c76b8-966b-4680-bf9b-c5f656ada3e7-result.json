{"name": "演出项目详情", "status": "passed", "attachments": [{"name": "接口地址", "source": "b9e6dcb8-e374-4cde-97c1-816df87515c8-attachment.json", "type": "application/json"}, {"name": "接口名称", "source": "28f65e5a-42b5-40c2-a1a9-d325aea5362a-attachment.json", "type": "application/json"}, {"name": "请求方式", "source": "60784630-fa4d-47a4-b601-299ad50ec3a9-attachment.json", "type": "application/json"}, {"name": "请求头", "source": "49a9f4d8-9ae4-42c2-a09e-efe9483934aa-attachment.json", "type": "application/json"}, {"name": "<PERSON><PERSON>", "source": "e8aed076-de24-40c9-a317-a066096b1efd-attachment.json", "type": "application/json"}, {"name": "测试用例名称", "source": "7786af90-7e70-4cba-8528-f815b01370ce-attachment.json", "type": "application/json"}, {"name": "参数类型", "source": "11a91739-ec36-4c69-9454-03ac37bfdb77-attachment.json", "type": "application/json"}, {"name": "请求参数json格式", "source": "2c0ddb3a-9f88-4001-9ea8-7b55cb6c79a1-attachment.json", "type": "application/json"}, {"name": "请求参数实际入参", "source": "293b629e-3e1f-483b-8338-0ef2355b8971-attachment.json", "type": "application/json"}, {"name": "接口实际响应信息", "source": "07b06062-7653-412c-98d6-9aaf179a0470-attachment.json", "type": "application/json"}, {"name": "状态码断言结果：成功", "source": "819dd176-8abc-4c08-ac82-99679f4d417a-attachment.txt", "type": "text/plain"}, {"name": "相等断言结果：成功", "source": "a8c6688d-82bc-4973-9539-5d7b24c59b7f-attachment.json", "type": "application/json"}, {"name": "📋 测试执行日志", "source": "9c28bca4-2bf7-427c-83f7-6d354ad6dd1f-attachment.txt", "type": "text/plain"}], "parameters": [{"name": "api_info", "value": "{'baseInfo': {'module': '演出票', 'api_name': '演出项目详情', 'host': '${get_host(play)}', 'url': '/api/show/info/performance_info', 'method': 'get', 'headers': '${get_headers(app)}'}, 'testCase': {'case_name': '获取演出项目详情信息', 'validation': [{'code': 200}, {'eq': {'code': 0}}], 'extract': 0, 'extract_list': np.float64(0.0), 'params': {'performanceId': '${get_extract_data(performance_id)}', 'channelCode': '${appKey()}', 'token': '${get_extract_data(token)}'}}}"}], "start": 1751853004882, "stop": 1751853005406, "uuid": "6c8bab3c-9213-4f62-8782-6242fe09f24f", "historyId": "f1d7da39199bd96c94383323f4027e2f", "testCaseId": "e9970ce8394fe9b627f33514f0afecf5", "fullName": "testcase.play.test_play.TestPlay#test_play", "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "37476-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}]}