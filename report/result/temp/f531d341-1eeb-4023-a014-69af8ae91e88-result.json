{"name": "为你推荐", "status": "passed", "attachments": [{"name": "接口地址", "source": "49d01159-759f-4498-9e0b-131608aa6411-attachment.json", "type": "application/json"}, {"name": "接口名称", "source": "3cf07e2c-ad51-4db1-81d8-1c963c68bc29-attachment.json", "type": "application/json"}, {"name": "请求方式", "source": "bdf40094-13a5-46f9-a8f0-8a09429bfd74-attachment.json", "type": "application/json"}, {"name": "请求头", "source": "947f88f7-ebad-4a76-b87e-c39d10673ef3-attachment.json", "type": "application/json"}, {"name": "<PERSON><PERSON>", "source": "7e1e8fe8-a584-4380-a7a1-43fb9a026547-attachment.json", "type": "application/json"}, {"name": "测试用例名称", "source": "1b60151c-4163-417f-8602-62762cafa9e4-attachment.json", "type": "application/json"}, {"name": "参数类型", "source": "f7f31db3-9c03-4964-9ab3-db22e3154b23-attachment.json", "type": "application/json"}, {"name": "请求参数json格式", "source": "c3fce9b7-2c48-4287-8326-70a0f65345c0-attachment.json", "type": "application/json"}, {"name": "请求参数实际入参", "source": "dd7e7767-76e4-4521-b214-d44ce5d5b9ee-attachment.json", "type": "application/json"}, {"name": "接口实际响应信息", "source": "f89c27aa-347a-4b01-9acb-c01aa075d344-attachment.json", "type": "application/json"}, {"name": "状态码断言结果：成功", "source": "25eb1578-7026-4ed2-8ccc-1f22a5585e06-attachment.txt", "type": "text/plain"}, {"name": "相等断言结果：成功", "source": "7b7e577f-b5ab-4005-9742-870188bbe203-attachment.json", "type": "application/json"}, {"name": "📋 测试执行日志", "source": "2803dcdf-d0a3-4f1b-8f31-660d63f2649a-attachment.txt", "type": "text/plain"}], "start": 1751851571438, "stop": 1751851571750, "uuid": "0d771fcc-45f6-473c-9563-a4e784f97d69", "historyId": "a03ba38a51bcc9af12332b9cb9ea6b41", "testCaseId": "e9970ce8394fe9b627f33514f0afecf5", "fullName": "testcase.play.test_play.TestPlay#test_play", "labels": [{"name": "story", "value": "C01_演出模块流程"}, {"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "34968-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}]}