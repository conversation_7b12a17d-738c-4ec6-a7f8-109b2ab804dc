{"name": "根据场次获取取票方式", "status": "passed", "attachments": [{"name": "接口地址", "source": "bb76fea8-c029-478f-ac14-f211b1613f70-attachment.json", "type": "application/json"}, {"name": "接口名称", "source": "e795aed2-80e2-4b54-bf0f-71b6fbb3ce88-attachment.json", "type": "application/json"}, {"name": "请求方式", "source": "f36bb579-c535-4721-a52c-e492f7ca25bb-attachment.json", "type": "application/json"}, {"name": "请求头", "source": "500c4fc1-d2e7-45aa-914e-10188d979e64-attachment.json", "type": "application/json"}, {"name": "<PERSON><PERSON>", "source": "1f942e76-18c9-4d99-8e67-20577b0c5cd0-attachment.json", "type": "application/json"}, {"name": "测试用例名称", "source": "0e3b4c70-fd8b-4a91-87d3-de4f06ee19cb-attachment.json", "type": "application/json"}, {"name": "参数类型", "source": "34b001a7-fc99-445f-b7cc-1498a9334768-attachment.json", "type": "application/json"}, {"name": "请求参数json格式", "source": "1719f1ff-ff49-4e29-bf32-49ca718bfe97-attachment.json", "type": "application/json"}, {"name": "请求参数实际入参", "source": "70a31c5c-e296-4452-ae88-b81060c4e9d0-attachment.json", "type": "application/json"}, {"name": "接口实际响应信息", "source": "be24092b-2d43-454a-b51e-0d598b4d24ea-attachment.json", "type": "application/json"}, {"name": "状态码断言结果：成功", "source": "e17d3002-c2f1-4c4e-96b8-7b43ac6d82a4-attachment.txt", "type": "text/plain"}, {"name": "相等断言结果：成功", "source": "7b40ba2a-14ed-4f19-a6b5-5e65757532b1-attachment.json", "type": "application/json"}, {"name": "📋 测试执行日志", "source": "fa335f85-1812-4c6e-9aa3-7eb252b3a4ac-attachment.txt", "type": "text/plain"}], "parameters": [{"name": "api_info", "value": "{'baseInfo': {'module': '演出票', 'api_name': '根据场次获取取票方式', 'host': '${get_host(play)}', 'url': '/api/show/info/show_fetch_ticket_ways', 'method': 'get', 'headers': '${get_headers(app)}'}, 'testCase': {'case_name': '获取该场次的取票方式', 'validation': [{'code': 200}, {'eq': {'code': 0}}], 'extract': {'fetchTicketWayId': '$..fetchTicketWayId', 'fetchType': '$..fetchType'}, 'extract_list': np.float64(0.0), 'params': {'showId': '${get_extract_data(showInfo,showId)}', 'channelCode': '${appKey()}', 'token': '${get_extract_data(token)}'}}}"}], "start": 1751853011078, "stop": 1751853011579, "uuid": "a87c02f1-5618-4748-813b-63546c32057c", "historyId": "169108c6786ad08586d73174286032ec", "testCaseId": "e9970ce8394fe9b627f33514f0afecf5", "fullName": "testcase.play.test_play.TestPlay#test_play", "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "37476-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}]}