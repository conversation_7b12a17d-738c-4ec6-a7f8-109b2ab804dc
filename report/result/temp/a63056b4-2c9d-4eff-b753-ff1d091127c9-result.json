{"name": "项目下场次票品列表", "status": "passed", "attachments": [{"name": "接口地址", "source": "88e51602-950c-4dee-bdaa-95b4ad0594bb-attachment.json", "type": "application/json"}, {"name": "接口名称", "source": "32674b7c-0c5a-4ad6-bb9f-047a78bd5457-attachment.json", "type": "application/json"}, {"name": "请求方式", "source": "206af410-f439-49ff-96cc-553d173bba0e-attachment.json", "type": "application/json"}, {"name": "请求头", "source": "4c6ecc26-c48b-45f1-a427-a28a837db369-attachment.json", "type": "application/json"}, {"name": "<PERSON><PERSON>", "source": "79d115d0-c7f4-43a0-a633-a1f10b4a00af-attachment.json", "type": "application/json"}, {"name": "测试用例名称", "source": "01b5f994-ff0e-430c-b3c8-df4f9fee4cd9-attachment.json", "type": "application/json"}, {"name": "参数类型", "source": "51421087-28b2-4428-a8f0-63ec1e798dee-attachment.json", "type": "application/json"}, {"name": "请求参数json格式", "source": "98ab9524-5ddb-4223-9a22-7e834d1eb867-attachment.json", "type": "application/json"}, {"name": "请求参数实际入参", "source": "c1314062-ca44-46e5-ae00-bc80e42e4f34-attachment.json", "type": "application/json"}, {"name": "接口实际响应信息", "source": "488cc64e-5eba-4d37-a7c1-759f36ffe028-attachment.json", "type": "application/json"}, {"name": "状态码断言结果：成功", "source": "2c7a4afd-503c-4be7-b8e0-836283d14554-attachment.txt", "type": "text/plain"}, {"name": "相等断言结果：成功", "source": "9dbd03e5-7c94-474a-a422-0523b02a83a5-attachment.json", "type": "application/json"}, {"name": "📋 测试执行日志", "source": "17a4bda2-8f31-453b-a83b-ed0ccf594ff0-attachment.txt", "type": "text/plain"}], "start": 1751853903726, "stop": 1751853904261, "uuid": "223147e7-8dea-4a40-9e20-9edf8583a206", "historyId": "3287c72f85d7169cf4925a7d71668b28", "testCaseId": "e9970ce8394fe9b627f33514f0afecf5", "fullName": "testcase.play.test_play.TestPlay#test_play", "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "39132-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}]}