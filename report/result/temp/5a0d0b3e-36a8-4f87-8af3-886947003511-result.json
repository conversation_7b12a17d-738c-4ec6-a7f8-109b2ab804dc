{"name": "取消订单", "status": "passed", "attachments": [{"name": "接口地址", "source": "7f6d481e-e54f-4616-8a8e-e555f9c36166-attachment.json", "type": "application/json"}, {"name": "接口名称", "source": "0a855d62-965a-4ba7-817b-2138d1cca5f8-attachment.json", "type": "application/json"}, {"name": "请求方式", "source": "09ef64a2-10b8-478d-baa8-408a5a7bc9df-attachment.json", "type": "application/json"}, {"name": "请求头", "source": "20b67ca6-e295-43ef-891e-151711c7df9c-attachment.json", "type": "application/json"}, {"name": "<PERSON><PERSON>", "source": "a3de194b-96a1-4677-bab5-0e233e71fc8d-attachment.json", "type": "application/json"}, {"name": "测试用例名称", "source": "4e5fe955-c7f6-4254-b649-078769c77808-attachment.json", "type": "application/json"}, {"name": "参数类型", "source": "5603fa00-9123-485a-ba4b-743200f74294-attachment.json", "type": "application/json"}, {"name": "请求参数json格式", "source": "036acc29-2b39-4169-a6da-0aea4b2d4f08-attachment.json", "type": "application/json"}, {"name": "请求参数实际入参", "source": "1ba75aa0-9664-4471-b60f-8efffc937cff-attachment.json", "type": "application/json"}, {"name": "接口实际响应信息", "source": "9adcd3c9-38e4-4d81-adb3-5e9cd2f56492-attachment.json", "type": "application/json"}, {"name": "状态码断言结果：成功", "source": "d6edecfb-ffe4-4d2c-976d-5ea93dfbd773-attachment.txt", "type": "text/plain"}, {"name": "相等断言结果：成功", "source": "3d41ff88-8891-4d50-9720-ea203895038c-attachment.json", "type": "application/json"}, {"name": "📋 测试执行日志", "source": "109c731f-22fa-47d3-aa7f-433ed4f07a39-attachment.txt", "type": "text/plain"}], "start": 1751851590380, "stop": 1751851591654, "uuid": "2dcb3a50-516f-4cbf-b7f3-b2ab5e5ff792", "historyId": "097386a9d43be40d3c28c1874a6e169b", "testCaseId": "e9970ce8394fe9b627f33514f0afecf5", "fullName": "testcase.play.test_play.TestPlay#test_play", "labels": [{"name": "story", "value": "C01_演出模块流程"}, {"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "34968-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}]}