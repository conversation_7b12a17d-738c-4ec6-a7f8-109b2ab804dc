{"name": "今日必抢", "status": "passed", "attachments": [{"name": "接口地址", "source": "83754e4d-41c3-420f-8615-57a1acfeaac5-attachment.json", "type": "application/json"}, {"name": "接口名称", "source": "998b0b7d-f309-4775-8c4e-a9f0cdb31ac3-attachment.json", "type": "application/json"}, {"name": "请求方式", "source": "697d3dfb-09e5-4822-9e7d-480f27dc9bae-attachment.json", "type": "application/json"}, {"name": "请求头", "source": "1878d040-6e7b-47f4-a0cc-ccec5d1c0fd8-attachment.json", "type": "application/json"}, {"name": "<PERSON><PERSON>", "source": "66e1bd49-41dd-46cc-bb52-2be54a0f4b08-attachment.json", "type": "application/json"}, {"name": "测试用例名称", "source": "07d282c6-fd91-494b-84fa-333021302d69-attachment.json", "type": "application/json"}, {"name": "参数类型", "source": "3a57e131-e387-4e87-8931-770c096d0541-attachment.json", "type": "application/json"}, {"name": "请求参数json格式", "source": "afb78357-aabd-435c-a3ab-53adf087f1b1-attachment.json", "type": "application/json"}, {"name": "请求参数实际入参", "source": "23e6fd61-eb87-4eb2-95d9-236d8710f0a2-attachment.json", "type": "application/json"}, {"name": "接口实际响应信息", "source": "9e0bc019-eaba-42c4-955d-cf0c38c19fc2-attachment.json", "type": "application/json"}, {"name": "状态码断言结果：成功", "source": "0a9dcda7-dab4-4925-ab7d-b51860ecf768-attachment.txt", "type": "text/plain"}, {"name": "相等断言结果：成功", "source": "4d450e39-87de-4703-bead-bc7425f8ce2d-attachment.json", "type": "application/json"}, {"name": "📋 测试执行日志", "source": "182249b5-98ad-4e1d-bd54-75fc924af39b-attachment.txt", "type": "text/plain"}], "start": 1751853888634, "stop": 1751853888938, "uuid": "e51b91ca-f6f5-4ba0-9f7a-8d41e4119b31", "historyId": "09729bd43bb947d1ebe3c16a80dc6e25", "testCaseId": "e9970ce8394fe9b627f33514f0afecf5", "fullName": "testcase.play.test_play.TestPlay#test_play", "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "39132-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}]}