{"name": "演出场次列表", "status": "passed", "attachments": [{"name": "接口地址", "source": "085a0b6b-62c6-4e63-8c6e-adb92beef885-attachment.json", "type": "application/json"}, {"name": "接口名称", "source": "c5b03bd8-0503-49fb-a8e4-f19e07a96d3b-attachment.json", "type": "application/json"}, {"name": "请求方式", "source": "fa501da8-c862-48b3-9780-40dbe28bed12-attachment.json", "type": "application/json"}, {"name": "请求头", "source": "c9bdde87-862f-401a-bda8-bfc767c7dc6d-attachment.json", "type": "application/json"}, {"name": "<PERSON><PERSON>", "source": "dd15d472-11c5-414b-a6ea-fb9fee29a59f-attachment.json", "type": "application/json"}, {"name": "测试用例名称", "source": "06a26799-359e-4636-a8c5-8c87dd0996e7-attachment.json", "type": "application/json"}, {"name": "参数类型", "source": "beb56295-c03c-437c-8a6f-1240e42eb104-attachment.json", "type": "application/json"}, {"name": "请求参数json格式", "source": "c5352cbb-cd31-4f01-a3b2-7c2b8255d6b5-attachment.json", "type": "application/json"}, {"name": "请求参数实际入参", "source": "03c002d2-a5d0-4f4c-a623-776ed32645c6-attachment.json", "type": "application/json"}, {"name": "接口实际响应信息", "source": "5d8d2481-c13f-4157-8fd2-bfe747ffc776-attachment.json", "type": "application/json"}, {"name": "状态码断言结果：成功", "source": "5c7b0b45-838c-4720-b215-75e365e374ed-attachment.txt", "type": "text/plain"}, {"name": "相等断言结果：成功", "source": "5b8b99e6-9145-4bad-b28d-428af403666b-attachment.json", "type": "application/json"}, {"name": "📋 测试执行日志", "source": "baf4a2be-8589-4670-a4ee-b3c05784a6df-attachment.txt", "type": "text/plain"}], "start": 1751866431593, "stop": 1751866432144, "uuid": "c2a6f5ae-c098-4542-8c4c-84a7019df1e7", "historyId": "ce6724bce5907ddc1a7c9b8a022c9530", "testCaseId": "e9970ce8394fe9b627f33514f0afecf5", "fullName": "testcase.play.test_play.TestPlay#test_play", "labels": [{"name": "story", "value": "C01_演出模块流程"}, {"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "16848-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}]}