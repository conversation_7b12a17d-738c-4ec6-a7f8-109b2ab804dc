{"name": "演唱会分类", "status": "passed", "attachments": [{"name": "接口地址", "source": "008b8243-35ce-4211-b1b1-306de2ddc7f3-attachment.json", "type": "application/json"}, {"name": "接口名称", "source": "d94310c8-7e78-4368-8231-871cfe2c5796-attachment.json", "type": "application/json"}, {"name": "请求方式", "source": "d19a00dd-3a7f-4f37-8250-0a03a4602888-attachment.json", "type": "application/json"}, {"name": "请求头", "source": "2f775456-73f7-485c-bc3e-30b73b25dd92-attachment.json", "type": "application/json"}, {"name": "<PERSON><PERSON>", "source": "ea3a3935-8efb-4880-8862-0042fa3efc5b-attachment.json", "type": "application/json"}, {"name": "测试用例名称", "source": "b7fd5c22-4d69-41e6-a900-f14d88495390-attachment.json", "type": "application/json"}, {"name": "参数类型", "source": "a93a4bfd-5df3-4816-9dd9-9a9f97174b1b-attachment.json", "type": "application/json"}, {"name": "请求参数json格式", "source": "0af6fb08-7146-470d-9165-0aad74a65ce4-attachment.json", "type": "application/json"}, {"name": "请求参数实际入参", "source": "8069b7ff-f6a7-46df-be9a-164af7d89d1b-attachment.json", "type": "application/json"}, {"name": "接口实际响应信息", "source": "f88c9253-8c2c-4a22-9580-9805b1b34949-attachment.json", "type": "application/json"}, {"name": "状态码断言结果：成功", "source": "6c70ca90-c1f9-4af6-bad9-b625e6f771f6-attachment.txt", "type": "text/plain"}, {"name": "相等断言结果：成功", "source": "9680fcfb-8754-4f8b-abed-20dab70169cb-attachment.json", "type": "application/json"}, {"name": "📋 测试执行日志", "source": "a1da0188-06d5-4751-8de1-079be6d2ff9a-attachment.txt", "type": "text/plain"}], "start": 1751853239249, "stop": 1751853239571, "uuid": "38d394ef-2798-4a15-aa1d-aca7e5f1889f", "historyId": "47b52c96aa0c1d59c25f4bc63959c980", "testCaseId": "e9970ce8394fe9b627f33514f0afecf5", "fullName": "testcase.play.test_play.TestPlay#test_play", "labels": [{"name": "story", "value": "C01_演出模块流程"}, {"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "28812-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}]}