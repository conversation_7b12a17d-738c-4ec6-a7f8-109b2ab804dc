{"name": "演出分类", "status": "passed", "attachments": [{"name": "接口地址", "source": "7e0dfa0f-b436-44a0-8408-e61f2cf9b8c4-attachment.json", "type": "application/json"}, {"name": "接口名称", "source": "945b2b7d-d888-415b-a4f3-8f742370a2d3-attachment.json", "type": "application/json"}, {"name": "请求方式", "source": "4134ee62-20a0-499b-ad0d-538987dd19a6-attachment.json", "type": "application/json"}, {"name": "请求头", "source": "c0828cc6-7ecf-4074-b45c-6f4ba47234f2-attachment.json", "type": "application/json"}, {"name": "<PERSON><PERSON>", "source": "048441b7-3131-4f59-b78d-d78be455ac33-attachment.json", "type": "application/json"}, {"name": "测试用例名称", "source": "913962f6-7062-4efb-8ead-1db1d92b1224-attachment.json", "type": "application/json"}, {"name": "参数类型", "source": "2410472f-f35e-4a8f-845b-a917adb916f5-attachment.json", "type": "application/json"}, {"name": "请求参数json格式", "source": "810fdf0f-d34c-4a3d-8227-9f3900226d1f-attachment.json", "type": "application/json"}, {"name": "请求参数实际入参", "source": "a2bdc40b-e24e-4425-99ca-e32441a7d941-attachment.json", "type": "application/json"}, {"name": "接口实际响应信息", "source": "7319687e-ab37-44ea-83ad-2ced69da236e-attachment.json", "type": "application/json"}, {"name": "状态码断言结果：成功", "source": "710961ac-6d6f-4a73-b38e-076e1ff33c23-attachment.txt", "type": "text/plain"}, {"name": "相等断言结果：成功", "source": "60bb39fd-08ae-4dc6-a417-23c4d3bf5b28-attachment.json", "type": "application/json"}, {"name": "📋 测试执行日志", "source": "10f2ae9f-0865-4a15-b0ea-6b9f76f97051-attachment.txt", "type": "text/plain"}], "start": 1751853886749, "stop": 1751853887119, "uuid": "74cca4d1-daa5-4b70-846f-75031c0ad6ce", "historyId": "c36ab200ccb8723de7119149ea6c3088", "testCaseId": "e9970ce8394fe9b627f33514f0afecf5", "fullName": "testcase.play.test_play.TestPlay#test_play", "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "39132-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}]}