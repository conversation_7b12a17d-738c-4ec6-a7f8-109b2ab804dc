{"name": "演出分类", "status": "passed", "attachments": [{"name": "接口地址", "source": "64da2b0f-ef18-4997-8f6c-8f1f2446f6bd-attachment.json", "type": "application/json"}, {"name": "接口名称", "source": "99593875-cedd-411f-9e63-915f44b5ab1a-attachment.json", "type": "application/json"}, {"name": "请求方式", "source": "b6cb7c9a-45b5-4293-a038-adfced92b2a8-attachment.json", "type": "application/json"}, {"name": "请求头", "source": "b3dff33f-03cf-43f4-bea5-1565489b2e27-attachment.json", "type": "application/json"}, {"name": "<PERSON><PERSON>", "source": "3d399d87-817f-4af1-a0eb-96bab0da314e-attachment.json", "type": "application/json"}, {"name": "测试用例名称", "source": "3d2c1463-8255-4fd9-8299-4dafbd085f14-attachment.json", "type": "application/json"}, {"name": "参数类型", "source": "d2d4f6cf-d97c-45fc-a4a0-034cfe3c9ac0-attachment.json", "type": "application/json"}, {"name": "请求参数json格式", "source": "c62e143a-8cf6-4521-b9ba-89c787b06218-attachment.json", "type": "application/json"}, {"name": "请求参数实际入参", "source": "3d75f8b8-5caa-4138-8838-7b06370f1ffc-attachment.json", "type": "application/json"}, {"name": "接口实际响应信息", "source": "d3ff3508-f5ef-4df9-9646-2b8735e7906f-attachment.json", "type": "application/json"}, {"name": "状态码断言结果：成功", "source": "9626950b-9604-4896-a2ac-5c2d979db95a-attachment.txt", "type": "text/plain"}, {"name": "相等断言结果：成功", "source": "cc4d9c41-c436-4056-97ce-bcafa0dacbfe-attachment.json", "type": "application/json"}, {"name": "📋 测试执行日志", "source": "4da42e6f-89b9-470c-997b-1aa49ca5e04b-attachment.txt", "type": "text/plain"}], "start": 1751853230164, "stop": 1751853230535, "uuid": "985cb7c9-710d-4aed-825b-9ef20bcf49d0", "historyId": "c36ab200ccb8723de7119149ea6c3088", "testCaseId": "e9970ce8394fe9b627f33514f0afecf5", "fullName": "testcase.play.test_play.TestPlay#test_play", "labels": [{"name": "story", "value": "C01_演出模块流程"}, {"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "28812-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}]}