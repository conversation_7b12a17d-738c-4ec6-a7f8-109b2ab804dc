{"name": "演出场次列表", "status": "passed", "attachments": [{"name": "接口地址", "source": "f6d8711e-cffa-431c-ace3-308223cb5bc5-attachment.json", "type": "application/json"}, {"name": "接口名称", "source": "993a42a3-d2a0-4981-98f1-710b8bb7060c-attachment.json", "type": "application/json"}, {"name": "请求方式", "source": "500a95ef-0aa4-4459-b66b-4280a732108b-attachment.json", "type": "application/json"}, {"name": "请求头", "source": "c515a30d-62c1-4f5c-a010-69a8b69bb4a4-attachment.json", "type": "application/json"}, {"name": "<PERSON><PERSON>", "source": "5cc083e2-3db0-4bb1-a5a2-698b9cdaa36f-attachment.json", "type": "application/json"}, {"name": "测试用例名称", "source": "fafe5d45-4d70-4588-b126-dd198c51cc3a-attachment.json", "type": "application/json"}, {"name": "参数类型", "source": "5ba7a5e2-e970-4bb5-9eb6-d726dc210556-attachment.json", "type": "application/json"}, {"name": "请求参数json格式", "source": "4ed8a75e-d81f-49d7-8cfe-0441bb603590-attachment.json", "type": "application/json"}, {"name": "请求参数实际入参", "source": "af3097ed-b13e-4156-9c76-b81368f2cb14-attachment.json", "type": "application/json"}, {"name": "接口实际响应信息", "source": "1d77891b-3f30-44f6-867d-37e3944d3a3b-attachment.json", "type": "application/json"}, {"name": "状态码断言结果：成功", "source": "ae03ed59-f6f7-4b75-abb8-2db4f23d3a67-attachment.txt", "type": "text/plain"}, {"name": "相等断言结果：成功", "source": "c773443f-470a-4e34-a1fa-009953fa368f-attachment.json", "type": "application/json"}, {"name": "📋 测试执行日志", "source": "faf5b6a5-7ced-490a-941f-f6bf394b6841-attachment.txt", "type": "text/plain"}], "start": 1751853244955, "stop": 1751853245501, "uuid": "51e876c9-c04d-479c-9e90-4b9746e1bf40", "historyId": "ce6724bce5907ddc1a7c9b8a022c9530", "testCaseId": "e9970ce8394fe9b627f33514f0afecf5", "fullName": "testcase.play.test_play.TestPlay#test_play", "labels": [{"name": "story", "value": "C01_演出模块流程"}, {"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "28812-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}]}