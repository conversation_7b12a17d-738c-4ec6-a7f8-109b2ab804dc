{"name": "**搜索**", "status": "passed", "attachments": [{"name": "接口地址", "source": "4943cb17-f437-44c1-88ad-bb4fe02882d9-attachment.json", "type": "application/json"}, {"name": "接口名称", "source": "2463cd37-029a-4d2b-9c3c-b15724aa83db-attachment.json", "type": "application/json"}, {"name": "请求方式", "source": "7b2492e9-5824-474a-a467-353214bf30f4-attachment.json", "type": "application/json"}, {"name": "请求头", "source": "1415bd90-bcbf-4bb8-8bbe-2e11a78cd08f-attachment.json", "type": "application/json"}, {"name": "<PERSON><PERSON>", "source": "f42af9cf-01a8-4c15-9fb4-57d64a03e052-attachment.json", "type": "application/json"}, {"name": "测试用例名称", "source": "c983cb14-474f-4978-a993-08d41f962fc1-attachment.json", "type": "application/json"}, {"name": "参数类型", "source": "ee7df869-e864-4afd-9576-351737e0b9ac-attachment.json", "type": "application/json"}, {"name": "请求参数json格式", "source": "55d615d3-df60-4827-8a72-93e2c1fb8a01-attachment.json", "type": "application/json"}, {"name": "请求参数实际入参", "source": "0bb55a2a-4f3b-4f68-b285-5f06dd198c08-attachment.json", "type": "application/json"}, {"name": "接口实际响应信息", "source": "4b32bd7c-09c2-4aab-a248-36cd39a87e2f-attachment.json", "type": "application/json"}, {"name": "状态码断言结果：成功", "source": "f59b2299-e8e1-4cab-8cab-dbc0a4e4cee8-attachment.txt", "type": "text/plain"}, {"name": "相等断言结果：成功", "source": "79a3ea08-fbd3-47c1-9fb5-09d306175894-attachment.json", "type": "application/json"}, {"name": "📋 测试执行日志", "source": "c9a52048-d20b-40e8-beac-5d5e63cab0ed-attachment.txt", "type": "text/plain"}], "parameters": [{"name": "api_info", "value": "{'baseInfo': {'module': '演出票', 'api_name': '**搜索**', 'host': '${get_host(play)}', 'url': '/api/show/info/performance_search', 'method': 'post', 'headers': '${get_headers(app)}'}, 'testCase': {'case_name': '按演出名称关键字搜索', 'validation': [{'code': 200}, {'eq': {'code': 0}}], 'extract': 0, 'extract_list': np.float64(0.0), 'data': {'keyword': '演', 'channelCode': '${appKey()}', 'token': '${get_extract_data(token)}'}}}"}], "start": 1751853739998, "stop": 1751853740291, "uuid": "6136b2b9-96ae-4a2a-a04b-25413c990079", "historyId": "932055e7a8948657a1bdaf6fe3b4e09c", "testCaseId": "e9970ce8394fe9b627f33514f0afecf5", "fullName": "testcase.play.test_play.TestPlay#test_play", "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "39072-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}]}