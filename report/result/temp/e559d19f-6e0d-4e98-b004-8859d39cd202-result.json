{"name": "首页广告位", "status": "passed", "attachments": [{"name": "接口地址", "source": "9c4f64eb-4a27-44b5-8ef6-087b95344a02-attachment.json", "type": "application/json"}, {"name": "接口名称", "source": "123a7d4e-23aa-4d18-af9a-db2056240599-attachment.json", "type": "application/json"}, {"name": "请求方式", "source": "2a6be7c5-529a-4547-af43-90b9b55caed9-attachment.json", "type": "application/json"}, {"name": "请求头", "source": "84a2d8a6-ab4a-478a-bd57-b5d4ca294212-attachment.json", "type": "application/json"}, {"name": "<PERSON><PERSON>", "source": "ae6e5380-3f58-4727-87fb-404165059449-attachment.json", "type": "application/json"}, {"name": "测试用例名称", "source": "ac2707e9-7c17-416b-ad18-100df582641c-attachment.json", "type": "application/json"}, {"name": "参数类型", "source": "66c12d0b-df44-4b31-8f56-a04945fe7504-attachment.json", "type": "application/json"}, {"name": "请求参数json格式", "source": "4c4e6ea9-f90d-4c81-9224-0fb9d09fa3b0-attachment.json", "type": "application/json"}, {"name": "请求参数实际入参", "source": "078a4590-f7e8-4bc5-aa08-936e4204c913-attachment.json", "type": "application/json"}, {"name": "接口实际响应信息", "source": "322ce911-bff9-45e9-b670-8ce171a274fc-attachment.json", "type": "application/json"}, {"name": "状态码断言结果：成功", "source": "3f6ab181-ff11-43ef-8f6d-83de5e6479f4-attachment.txt", "type": "text/plain"}, {"name": "相等断言结果：成功", "source": "0c0620c3-4c8e-46a8-a7f5-570daaa24d10-attachment.json", "type": "application/json"}, {"name": "📋 测试执行日志", "source": "421f1e19-02aa-4b15-86dc-e9e31011cbc7-attachment.txt", "type": "text/plain"}], "start": 1751851573272, "stop": 1751851573608, "uuid": "8b016b33-1054-4dda-83d1-4565046e0639", "historyId": "998f7fb53b18ea3045f71922b591e307", "testCaseId": "e9970ce8394fe9b627f33514f0afecf5", "fullName": "testcase.play.test_play.TestPlay#test_play", "labels": [{"name": "story", "value": "C01_演出模块流程"}, {"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "34968-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}]}