{"name": "**搜索**", "status": "passed", "attachments": [{"name": "接口地址", "source": "df62c3cb-b17c-40a5-93d4-8b574312d9f0-attachment.json", "type": "application/json"}, {"name": "接口名称", "source": "e78a95c7-2861-4908-ad52-a40b97883127-attachment.json", "type": "application/json"}, {"name": "请求方式", "source": "24987dd9-623b-4fef-a032-7c402d7bcc65-attachment.json", "type": "application/json"}, {"name": "请求头", "source": "ef419347-2719-4fc1-af4f-8b870a24134b-attachment.json", "type": "application/json"}, {"name": "<PERSON><PERSON>", "source": "eb28b7c0-e516-4945-bf9e-13e0193306c3-attachment.json", "type": "application/json"}, {"name": "测试用例名称", "source": "cb20e810-f851-4e44-b17c-8b41edfca6b7-attachment.json", "type": "application/json"}, {"name": "参数类型", "source": "fd1440ee-efb7-4a12-b290-29e50de52761-attachment.json", "type": "application/json"}, {"name": "请求参数json格式", "source": "f565558e-48e7-437c-8f9f-3bf80be25145-attachment.json", "type": "application/json"}, {"name": "请求参数实际入参", "source": "8c0d3ac6-836d-4579-a899-7a4b13f67caf-attachment.json", "type": "application/json"}, {"name": "接口实际响应信息", "source": "116aebff-fd7e-49c0-9c34-33c827b2549d-attachment.json", "type": "application/json"}, {"name": "状态码断言结果：成功", "source": "c475388a-c648-4f91-9dfa-04977762624b-attachment.txt", "type": "text/plain"}, {"name": "相等断言结果：成功", "source": "c0b19152-6b55-4733-b543-ce60c9dccc0d-attachment.json", "type": "application/json"}, {"name": "📋 测试执行日志", "source": "771154e1-7d0b-45b1-82a0-c3ea99e80fe1-attachment.txt", "type": "text/plain"}], "start": 1751851576972, "stop": 1751851577312, "uuid": "d6554254-2439-481e-b6bc-a94ce8563589", "historyId": "80ea16c71eb34b6e85110c3f875859f1", "testCaseId": "e9970ce8394fe9b627f33514f0afecf5", "fullName": "testcase.play.test_play.TestPlay#test_play", "labels": [{"name": "story", "value": "C01_演出模块流程"}, {"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "34968-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}]}