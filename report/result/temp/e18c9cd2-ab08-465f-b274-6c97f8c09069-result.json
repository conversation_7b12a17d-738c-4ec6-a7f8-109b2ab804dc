{"name": "查询影讯接口", "status": "passed", "attachments": [{"name": "接口地址", "source": "2bd76430-d4a1-452a-97f9-cb27f37941e9-attachment.json", "type": "application/json"}, {"name": "接口名称", "source": "d24763e7-7317-4dbd-9be6-d147d3372dd0-attachment.json", "type": "application/json"}, {"name": "请求方式", "source": "f0fb36c2-790a-4880-b01a-38dd6a940477-attachment.json", "type": "application/json"}, {"name": "请求头", "source": "c7175bc8-14ad-4677-881d-4dfe73b22ec1-attachment.json", "type": "application/json"}, {"name": "<PERSON><PERSON>", "source": "6b457681-befc-4cc8-83fe-4e52d1c3237d-attachment.json", "type": "application/json"}, {"name": "测试用例名称", "source": "b6707bf8-d684-4f71-acfc-3bb901b8d06a-attachment.json", "type": "application/json"}, {"name": "参数类型", "source": "3d9bbf41-979d-4baa-88d2-4f7fe78178d7-attachment.json", "type": "application/json"}, {"name": "请求参数json格式", "source": "f49b3705-d95d-4a3a-acae-d8596e527b9c-attachment.json", "type": "application/json"}, {"name": "请求参数实际入参", "source": "f395dd1b-4b01-442a-b8ed-106eb5f7e78e-attachment.json", "type": "application/json"}, {"name": "接口实际响应信息", "source": "be5f0efd-1329-4d5d-ace3-01a9b0c28750-attachment.json", "type": "application/json"}, {"name": "状态码断言结果：成功", "source": "cac481d0-3ab2-49c7-b9ae-8f0923666b9b-attachment.txt", "type": "text/plain"}, {"name": "相等断言结果：成功", "source": "9c226866-9c5a-4af7-96b8-fdba7d4d2d32-attachment.json", "type": "application/json"}, {"name": "相等断言结果：成功", "source": "bcad3b3c-c916-4af3-afb3-a117dea47eb8-attachment.json", "type": "application/json"}, {"name": "📋 测试执行日志", "source": "f1253d3c-5e30-442a-a54f-6c5e18f4d1fd-attachment.txt", "type": "text/plain"}], "start": 1751853913524, "stop": 1751853913725, "uuid": "a55fe88f-352a-46cc-9079-7cf4f466330c", "historyId": "e9c2ea8fd9c81240c2b50d2a9668daf2", "testCaseId": "91ab679f57e05cf8b5a9e119b7e95056", "fullName": "testcase.demo_test.test_demo.TestFilmOrder#test_film_order", "labels": [{"name": "story", "value": "C02_电影下单流程"}, {"name": "feature", "value": "M02_电影订票模块接口测试"}, {"name": "parentSuite", "value": "testcase.demo_test"}, {"name": "suite", "value": "test_demo"}, {"name": "subSuite", "value": "TestFilmOrder"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "39132-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.demo_test.test_demo"}]}