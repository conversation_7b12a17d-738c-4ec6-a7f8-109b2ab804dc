{"name": "演唱会分类", "status": "passed", "attachments": [{"name": "接口地址", "source": "c142178d-f315-4b34-ada5-d0d506c9033a-attachment.json", "type": "application/json"}, {"name": "接口名称", "source": "2389c402-3e47-466e-9ff7-a76c918ead8a-attachment.json", "type": "application/json"}, {"name": "请求方式", "source": "6fe11790-8d11-47fb-b870-c963603d65d4-attachment.json", "type": "application/json"}, {"name": "请求头", "source": "8ebaa299-7838-4ad7-aa4b-60f3f8cacb97-attachment.json", "type": "application/json"}, {"name": "<PERSON><PERSON>", "source": "6d7f5634-e6ab-4285-a6ef-42089234801f-attachment.json", "type": "application/json"}, {"name": "测试用例名称", "source": "cb4545a6-6eef-44a5-88e6-c124b38e99cd-attachment.json", "type": "application/json"}, {"name": "参数类型", "source": "924bb835-bec7-4ba1-83af-b80efbee4f13-attachment.json", "type": "application/json"}, {"name": "请求参数json格式", "source": "cd89138b-50d8-4ff5-86a4-05dd56eb2b8e-attachment.json", "type": "application/json"}, {"name": "请求参数实际入参", "source": "3941ba39-4606-420a-9ca0-d51cf620978e-attachment.json", "type": "application/json"}, {"name": "接口实际响应信息", "source": "2b56f6d7-590b-4ce2-a74a-3c87a72753b9-attachment.json", "type": "application/json"}, {"name": "状态码断言结果：成功", "source": "20922e7b-**************-48c9826c8e57-attachment.txt", "type": "text/plain"}, {"name": "相等断言结果：成功", "source": "d27aaf7f-0cf9-4566-a98d-48bc98082fbc-attachment.json", "type": "application/json"}, {"name": "📋 测试执行日志", "source": "0d2f3003-5d04-4fe2-bec0-b2e3b624d328-attachment.txt", "type": "text/plain"}], "start": 1751851575127, "stop": 1751851575443, "uuid": "de41d318-131e-40b6-ad0c-45cd5281aa34", "historyId": "47b52c96aa0c1d59c25f4bc63959c980", "testCaseId": "e9970ce8394fe9b627f33514f0afecf5", "fullName": "testcase.play.test_play.TestPlay#test_play", "labels": [{"name": "story", "value": "C01_演出模块流程"}, {"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "34968-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}]}