{"name": "演唱会分类", "status": "passed", "attachments": [{"name": "接口地址", "source": "457b3d35-7abf-4ae4-8851-004b1456ff9a-attachment.json", "type": "application/json"}, {"name": "接口名称", "source": "cf4662da-4a2e-4284-9818-585f89eb1a8f-attachment.json", "type": "application/json"}, {"name": "请求方式", "source": "cf02ca48-964e-4583-9146-50a337a765df-attachment.json", "type": "application/json"}, {"name": "请求头", "source": "e6761fc0-686f-47fd-9c23-83e6e0633eda-attachment.json", "type": "application/json"}, {"name": "<PERSON><PERSON>", "source": "f6455f7b-fd16-48e8-aa33-5059d7e88891-attachment.json", "type": "application/json"}, {"name": "测试用例名称", "source": "e920b86a-a008-4a36-80e8-eb7f04016cac-attachment.json", "type": "application/json"}, {"name": "参数类型", "source": "93204162-ff81-4dcb-8505-acbad52f997c-attachment.json", "type": "application/json"}, {"name": "请求参数json格式", "source": "80fdc666-6f23-49e2-ab30-735ead6d5ecf-attachment.json", "type": "application/json"}, {"name": "请求参数实际入参", "source": "e31cb6f9-cd31-4e1f-90a6-e05fecd73b12-attachment.json", "type": "application/json"}, {"name": "接口实际响应信息", "source": "5996133b-e7b5-4d3f-bdd4-6c47bb7d7e69-attachment.json", "type": "application/json"}, {"name": "状态码断言结果：成功", "source": "2af542ea-5dd8-45a9-a5bb-a2598a34c48a-attachment.txt", "type": "text/plain"}, {"name": "相等断言结果：成功", "source": "b5387de4-ae2c-4424-b947-560f793f93da-attachment.json", "type": "application/json"}, {"name": "📋 测试执行日志", "source": "ff9ee85b-a3c4-462a-ac1d-cdb5e9e514aa-attachment.txt", "type": "text/plain"}], "start": 1751852466479, "stop": 1751852466828, "uuid": "9d201c4d-0d9e-441d-918a-bed4feaa6566", "historyId": "47b52c96aa0c1d59c25f4bc63959c980", "testCaseId": "e9970ce8394fe9b627f33514f0afecf5", "fullName": "testcase.play.test_play.TestPlay#test_play", "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "38092-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}]}