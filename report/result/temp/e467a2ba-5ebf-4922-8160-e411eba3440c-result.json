{"name": "演出项目详情", "status": "passed", "attachments": [{"name": "接口地址", "source": "be53028e-fdc4-4aa7-bdba-0f5da18c0bf5-attachment.json", "type": "application/json"}, {"name": "接口名称", "source": "a5882905-c4ed-4cdb-908c-9953878212dc-attachment.json", "type": "application/json"}, {"name": "请求方式", "source": "8b5d1727-a894-468d-bfb0-6fa84813e2a7-attachment.json", "type": "application/json"}, {"name": "请求头", "source": "f0ffbbce-1d7c-4cae-bdc1-faf7dfdf2cc9-attachment.json", "type": "application/json"}, {"name": "<PERSON><PERSON>", "source": "c5ef3ee7-b0d6-4b56-a831-b0b840d68f29-attachment.json", "type": "application/json"}, {"name": "测试用例名称", "source": "d8b0e6e8-9100-4f0f-bd63-f21896171978-attachment.json", "type": "application/json"}, {"name": "参数类型", "source": "e6facb97-d091-4543-95ab-3f3cb33f1d2a-attachment.json", "type": "application/json"}, {"name": "请求参数json格式", "source": "b2edd160-34d0-4857-b771-c95546c95477-attachment.json", "type": "application/json"}, {"name": "请求参数实际入参", "source": "43324994-708e-44f9-8045-2986631b363a-attachment.json", "type": "application/json"}, {"name": "接口实际响应信息", "source": "5fa91543-02a9-4418-842d-8026b0a76661-attachment.json", "type": "application/json"}, {"name": "状态码断言结果：成功", "source": "4b7cd75a-4290-4750-8c81-75ef707f96d1-attachment.txt", "type": "text/plain"}, {"name": "相等断言结果：成功", "source": "416daa3c-7fde-4969-a477-e0f6e67ac1ef-attachment.json", "type": "application/json"}, {"name": "📋 测试执行日志", "source": "ec31bda1-833f-4730-b1ab-a473952d24df-attachment.txt", "type": "text/plain"}], "start": 1751853242905, "stop": 1751853243436, "uuid": "91279a10-9735-48d1-8e3b-ad566bff592a", "historyId": "0457c216d6185e839b1ab99163c7bbc6", "testCaseId": "e9970ce8394fe9b627f33514f0afecf5", "fullName": "testcase.play.test_play.TestPlay#test_play", "labels": [{"name": "story", "value": "C01_演出模块流程"}, {"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "28812-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}]}