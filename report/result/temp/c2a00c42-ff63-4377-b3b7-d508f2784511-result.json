{"name": "下单", "status": "passed", "attachments": [{"name": "接口地址", "source": "5c48e23b-0f4f-439b-9a86-f0ef3c674a81-attachment.json", "type": "application/json"}, {"name": "接口名称", "source": "89332325-0ce5-4540-ae7e-35f55c1b5bf0-attachment.json", "type": "application/json"}, {"name": "请求方式", "source": "8d387b93-3b3f-4d82-9114-ed6d8d2b0cac-attachment.json", "type": "application/json"}, {"name": "请求头", "source": "8eeae90f-0375-423e-b2d5-9a3fa49d85e5-attachment.json", "type": "application/json"}, {"name": "<PERSON><PERSON>", "source": "29d02264-252e-427d-b449-c8870876cd33-attachment.json", "type": "application/json"}, {"name": "测试用例名称", "source": "5d4782bd-ad7e-4c06-b611-aae821722817-attachment.json", "type": "application/json"}, {"name": "参数类型", "source": "d2e88823-82ac-4d29-956f-8c9015b21c31-attachment.json", "type": "application/json"}, {"name": "请求参数json格式", "source": "2308090b-3aa9-4f59-b9c5-e837c0c268c6-attachment.json", "type": "application/json"}, {"name": "请求参数实际入参", "source": "2a9ae414-0351-4774-bc71-6455f936f58b-attachment.json", "type": "application/json"}, {"name": "接口实际响应信息", "source": "d4d00e0d-2021-4f5e-81a1-b659acc1d88e-attachment.json", "type": "application/json"}, {"name": "状态码断言结果：成功", "source": "14e2d213-1450-483f-ae33-6964b637255f-attachment.txt", "type": "text/plain"}, {"name": "相等断言结果：成功", "source": "a2b095ca-e181-4e5a-acfd-6a63138b0901-attachment.json", "type": "application/json"}, {"name": "📋 测试执行日志", "source": "d089f339-b9e8-4f46-a88c-b9751f624d7e-attachment.txt", "type": "text/plain"}], "parameters": [{"name": "api_info", "value": "{'baseInfo': {'module': '演出票', 'api_name': '下单', 'host': '${get_host(play)}', 'url': '/api/show/order/create', 'method': 'post', 'headers': '${get_headers(app)}'}, 'testCase': {'case_name': '创建订单', 'validation': [{'code': 200}, {'eq': {'code': 0}}], 'extract': {'orderId': '$..orderId'}, 'extract_list': np.float64(0.0), 'data': {'recipientMobile': '${get_extract_data(login_info,mobile)}', 'recipientName': '购票人', 'recipientIdNo': '', 'fetchTicketWayId': '${get_extract_data(fetchTicketWayId)}', 'recipientAddressId': '', 'fetchTicketWayType': '${get_extract_data(fetchType)}', 'performanceId': '${get_extract_data(performance_id)}', 'showId': '${get_extract_data(showInfo,showId)}', 'salesPlanId': '${get_extract_data(ticketInfo,ticketUnitId)}', 'salesPlanCount': '1', 'totalTicketPrice': '${get_extract_data(ticketInfo,price)}', 'deliveryPrice': '0', 'orderPrice': '${get_extract_data(ticketInfo,price)}', 'realNameIds': '', 'seatRequest': '', 'channelCode': '${appKey()}', 'merchant_code': '', 'token': '${get_extract_data(token)}'}}}"}], "start": 1751853013098, "stop": 1751853014532, "uuid": "99575649-129a-40d9-8d11-4da1b25c430e", "historyId": "340c0bd5adb900e5cd7660f86f67f4fa", "testCaseId": "e9970ce8394fe9b627f33514f0afecf5", "fullName": "testcase.play.test_play.TestPlay#test_play", "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "37476-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}]}