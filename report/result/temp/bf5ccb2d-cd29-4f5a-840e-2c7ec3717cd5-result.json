{"name": "演唱会分类", "status": "passed", "attachments": [{"name": "接口地址", "source": "b34ed3db-148f-4778-8b44-4ffa06f871b5-attachment.json", "type": "application/json"}, {"name": "接口名称", "source": "36685a7d-c10a-4255-b4f2-3a12fb5392a1-attachment.json", "type": "application/json"}, {"name": "请求方式", "source": "afbaca7a-6521-48d4-9458-739f0b41c3c2-attachment.json", "type": "application/json"}, {"name": "请求头", "source": "747046cb-f359-4c27-a681-773ea5588ee7-attachment.json", "type": "application/json"}, {"name": "<PERSON><PERSON>", "source": "70fae74c-b3b9-444a-bb90-4e732d4a6f62-attachment.json", "type": "application/json"}, {"name": "测试用例名称", "source": "0a5fe025-ee6a-4a5e-9675-24fc74e834a5-attachment.json", "type": "application/json"}, {"name": "参数类型", "source": "cab1d44d-cea7-4ce5-b62f-d74fac68005a-attachment.json", "type": "application/json"}, {"name": "请求参数json格式", "source": "cd5ee108-120a-4add-9c14-134843ec9ace-attachment.json", "type": "application/json"}, {"name": "请求参数实际入参", "source": "0866f71d-8ac5-41b5-ad6f-9027c2ee472a-attachment.json", "type": "application/json"}, {"name": "接口实际响应信息", "source": "0612ac74-e220-4898-8713-169a94ad81a2-attachment.json", "type": "application/json"}, {"name": "状态码断言结果：成功", "source": "e1f00894-c2dc-480c-955c-2a0a49fae29c-attachment.txt", "type": "text/plain"}, {"name": "相等断言结果：成功", "source": "75cc18c6-af3c-44eb-9c5b-772d3fe9b224-attachment.json", "type": "application/json"}, {"name": "📋 测试执行日志", "source": "3fc269f3-acde-416d-8ee0-3fa06aabe90b-attachment.txt", "type": "text/plain"}], "start": 1751853895908, "stop": 1751853896233, "uuid": "0780bfb8-d7d9-4b1a-a82d-204414c78d39", "historyId": "47b52c96aa0c1d59c25f4bc63959c980", "testCaseId": "e9970ce8394fe9b627f33514f0afecf5", "fullName": "testcase.play.test_play.TestPlay#test_play", "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "39132-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}]}