{"name": "今日必抢", "status": "passed", "attachments": [{"name": "接口地址", "source": "cf56905d-dd2c-4a4a-9b59-afd38e8e8e24-attachment.json", "type": "application/json"}, {"name": "接口名称", "source": "7d046eaa-9268-4b86-bb34-ad70f55ae3cf-attachment.json", "type": "application/json"}, {"name": "请求方式", "source": "c6a11afb-33cd-4794-96e2-e9a9a802a5f6-attachment.json", "type": "application/json"}, {"name": "请求头", "source": "35816b54-575f-4878-871c-e80c6a377aa3-attachment.json", "type": "application/json"}, {"name": "<PERSON><PERSON>", "source": "add67525-9de8-4def-9ab5-1f7e31ceb3e1-attachment.json", "type": "application/json"}, {"name": "测试用例名称", "source": "87e21a98-07eb-4cab-9c7a-2085519fefbe-attachment.json", "type": "application/json"}, {"name": "参数类型", "source": "48c06319-d574-4119-b472-33099a9b1374-attachment.json", "type": "application/json"}, {"name": "请求参数json格式", "source": "128dcf96-f734-4c9c-9ed2-0fb32816d1d1-attachment.json", "type": "application/json"}, {"name": "请求参数实际入参", "source": "6afcce7c-cced-4196-8791-695eb447a758-attachment.json", "type": "application/json"}, {"name": "接口实际响应信息", "source": "37d267ca-3454-4a60-af2c-668c68faeca2-attachment.json", "type": "application/json"}, {"name": "状态码断言结果：成功", "source": "aa591856-350f-4c98-a144-95ccceb38b61-attachment.txt", "type": "text/plain"}, {"name": "相等断言结果：成功", "source": "0a32c295-9b20-42fc-af63-c4001ea76f5d-attachment.json", "type": "application/json"}, {"name": "📋 测试执行日志", "source": "8ba09ea8-c412-4b39-b0ab-cea1bc5e3dfe-attachment.txt", "type": "text/plain"}], "start": 1751853232057, "stop": 1751853232377, "uuid": "850b9c41-c44f-452c-987e-c379fa7c7145", "historyId": "09729bd43bb947d1ebe3c16a80dc6e25", "testCaseId": "e9970ce8394fe9b627f33514f0afecf5", "fullName": "testcase.play.test_play.TestPlay#test_play", "labels": [{"name": "story", "value": "C01_演出模块流程"}, {"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "28812-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}]}