{"name": "演出场次列表", "status": "passed", "attachments": [{"name": "接口地址", "source": "8a3f2b94-cbe5-4a4e-af01-3f11ca30efb0-attachment.json", "type": "application/json"}, {"name": "接口名称", "source": "2c487c94-9172-46ca-b77e-7c330dfe8b2f-attachment.json", "type": "application/json"}, {"name": "请求方式", "source": "d0b7907d-833b-41cc-9353-5e4749f1a189-attachment.json", "type": "application/json"}, {"name": "请求头", "source": "7bdaaa57-f6c7-4c1c-9ee9-61d3f1e797e5-attachment.json", "type": "application/json"}, {"name": "<PERSON><PERSON>", "source": "9d544226-48c8-4bb2-826f-0738c87382ec-attachment.json", "type": "application/json"}, {"name": "测试用例名称", "source": "c837d96b-fb5b-426d-ab17-7944768c445f-attachment.json", "type": "application/json"}, {"name": "参数类型", "source": "d191ee2d-9677-4248-879b-9f60cd2a8795-attachment.json", "type": "application/json"}, {"name": "请求参数json格式", "source": "84bba499-fb9e-4344-be38-c6e2171f2323-attachment.json", "type": "application/json"}, {"name": "请求参数实际入参", "source": "36097c8a-4dbe-437d-b78f-d53a51dbd498-attachment.json", "type": "application/json"}, {"name": "接口实际响应信息", "source": "10105376-ee3e-4c53-83d4-1828fbe4f7f1-attachment.json", "type": "application/json"}, {"name": "状态码断言结果：成功", "source": "3581d83d-93d6-43bd-8aa1-88a331c54f06-attachment.txt", "type": "text/plain"}, {"name": "相等断言结果：成功", "source": "9d65b7ad-df52-469a-ab0c-8ff09dcde9d9-attachment.json", "type": "application/json"}, {"name": "📋 测试执行日志", "source": "b9e0483d-d48f-4b14-af81-1405e4fb7100-attachment.txt", "type": "text/plain"}], "start": 1751852472264, "stop": 1751852472789, "uuid": "1f0f9321-55db-495d-83b4-111f2aecf792", "historyId": "ce6724bce5907ddc1a7c9b8a022c9530", "testCaseId": "e9970ce8394fe9b627f33514f0afecf5", "fullName": "testcase.play.test_play.TestPlay#test_play", "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "38092-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}]}