{"name": "演唱会分类", "status": "passed", "attachments": [{"name": "接口地址", "source": "79883754-8b34-457c-907e-3cc7b0f38ee8-attachment.json", "type": "application/json"}, {"name": "接口名称", "source": "dd6a5738-b0df-421d-87c3-edc7dba1dbe9-attachment.json", "type": "application/json"}, {"name": "请求方式", "source": "be55b480-4213-4add-9370-9142423d26ad-attachment.json", "type": "application/json"}, {"name": "请求头", "source": "d8bb0f35-65e4-4df3-b567-ab769464b8cf-attachment.json", "type": "application/json"}, {"name": "<PERSON><PERSON>", "source": "7ddd02a6-313b-4d6f-b1e7-aca314f3456e-attachment.json", "type": "application/json"}, {"name": "测试用例名称", "source": "92b3248b-d88b-4735-87fe-e5bda564d6b3-attachment.json", "type": "application/json"}, {"name": "参数类型", "source": "3596a114-d459-40f0-a1be-e92146604710-attachment.json", "type": "application/json"}, {"name": "请求参数json格式", "source": "23323aa2-8e3f-46c5-9cca-986102d6ae8d-attachment.json", "type": "application/json"}, {"name": "请求参数实际入参", "source": "cf89403e-bbdc-4866-b02f-fe43725ed879-attachment.json", "type": "application/json"}, {"name": "接口实际响应信息", "source": "5ea077e6-e19d-4f29-a1db-46e902bb2a44-attachment.json", "type": "application/json"}, {"name": "状态码断言结果：成功", "source": "54a25a9b-ef3b-459c-836e-5dbe822f4bcb-attachment.txt", "type": "text/plain"}, {"name": "相等断言结果：成功", "source": "fedd8b93-7777-4fbb-ac18-64cb85b88fd9-attachment.json", "type": "application/json"}, {"name": "📋 测试执行日志", "source": "e3102c25-d79c-4ec1-864e-239713d2bdb0-attachment.txt", "type": "text/plain"}], "parameters": [{"name": "api_info", "value": "{'baseInfo': {'module': '演出票', 'api_name': '演唱会分类', 'host': '${get_host(play)}', 'url': '/api/show/info/performance_search', 'method': 'post', 'headers': '${get_headers(app)}'}, 'testCase': {'case_name': '按类型搜索=演唱会', 'validation': [{'code': 200}, {'eq': {'code': 0}}], 'extract': 0, 'extract_list': np.float64(0.0), 'data': {'category_id': '10001', 'channelCode': '${appKey()}', 'token': '${get_extract_data(token)}'}}}"}], "start": 1751853001175, "stop": 1751853001499, "uuid": "77b6863d-aa51-4b48-9809-6d955d792137", "historyId": "b8a74dca691f81270d24ebc4399d791b", "testCaseId": "e9970ce8394fe9b627f33514f0afecf5", "fullName": "testcase.play.test_play.TestPlay#test_play", "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "37476-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}]}