{"name": "下单", "status": "passed", "attachments": [{"name": "接口地址", "source": "ec8b39ea-271b-4219-a461-a3b0ed31b065-attachment.json", "type": "application/json"}, {"name": "接口名称", "source": "5d95a2be-a5b2-48ed-aaf4-16f79c89290f-attachment.json", "type": "application/json"}, {"name": "请求方式", "source": "0e138ce8-a8bb-4b34-ba0f-623c3dbd740d-attachment.json", "type": "application/json"}, {"name": "请求头", "source": "31deb774-3b3a-463b-a914-4eb1bbaa7728-attachment.json", "type": "application/json"}, {"name": "<PERSON><PERSON>", "source": "a750f7f3-944e-47d2-a1c1-0e0a23b3db29-attachment.json", "type": "application/json"}, {"name": "测试用例名称", "source": "93295d86-0e9d-498b-81ee-40df7887b04b-attachment.json", "type": "application/json"}, {"name": "参数类型", "source": "9b175e3d-6170-4976-997b-e5860eb34383-attachment.json", "type": "application/json"}, {"name": "请求参数json格式", "source": "66457b3c-47d2-4df6-bbf8-c1287c2da365-attachment.json", "type": "application/json"}, {"name": "请求参数实际入参", "source": "fcbda785-85f8-4516-a950-7b61ef4d167b-attachment.json", "type": "application/json"}, {"name": "接口实际响应信息", "source": "5c9388a9-2abc-414c-9f03-2c757e4f3dfd-attachment.json", "type": "application/json"}, {"name": "状态码断言结果：成功", "source": "eef88af7-3c95-4db7-adde-0125e0ef271c-attachment.txt", "type": "text/plain"}, {"name": "相等断言结果：成功", "source": "a5295c27-c394-46e8-b263-9e76145cb37a-attachment.json", "type": "application/json"}, {"name": "📋 测试执行日志", "source": "bede6996-1e2b-47a1-ae43-11e2d0417b72-attachment.txt", "type": "text/plain"}], "start": 1751866438347, "stop": 1751866440058, "uuid": "e0c19419-f58a-42b6-907a-af6aabf9e00e", "historyId": "df78d83b2c7879c3a2ecdde06190a07c", "testCaseId": "e9970ce8394fe9b627f33514f0afecf5", "fullName": "testcase.play.test_play.TestPlay#test_play", "labels": [{"name": "story", "value": "C01_演出模块流程"}, {"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "16848-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}]}