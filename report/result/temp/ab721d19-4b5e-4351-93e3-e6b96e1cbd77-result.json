{"name": "为你推荐", "status": "passed", "attachments": [{"name": "接口地址", "source": "07d07e25-d6a2-4762-b006-8ffcf9b256ae-attachment.json", "type": "application/json"}, {"name": "接口名称", "source": "5044a051-6dfa-4ad1-8537-92efe198b4ec-attachment.json", "type": "application/json"}, {"name": "请求方式", "source": "79e45336-ddc8-49c9-806e-ea4deb9cebcc-attachment.json", "type": "application/json"}, {"name": "请求头", "source": "7323db25-da71-4cb1-84d6-87a6c1a61365-attachment.json", "type": "application/json"}, {"name": "<PERSON><PERSON>", "source": "a3b0dbbe-94c1-438e-9ca7-c12946d61fe2-attachment.json", "type": "application/json"}, {"name": "测试用例名称", "source": "1b622247-6cf7-45b3-9ca1-66fe59044aaf-attachment.json", "type": "application/json"}, {"name": "参数类型", "source": "32eded03-46a9-474f-b8b9-a6303ee0fc11-attachment.json", "type": "application/json"}, {"name": "请求参数json格式", "source": "5647a793-92dc-42eb-a3b1-cd879a8ca9b3-attachment.json", "type": "application/json"}, {"name": "请求参数实际入参", "source": "d1a739f7-b1f5-47d6-b00d-23609ba6638f-attachment.json", "type": "application/json"}, {"name": "接口实际响应信息", "source": "184e29f7-a324-4a47-8095-c14700b415d1-attachment.json", "type": "application/json"}, {"name": "状态码断言结果：成功", "source": "539c3601-50c8-4d6e-8c84-bb0a1743b989-attachment.txt", "type": "text/plain"}, {"name": "相等断言结果：成功", "source": "a2b33b49-4e9f-4759-bfca-0a5abdf2ef69-attachment.json", "type": "application/json"}, {"name": "📋 测试执行日志", "source": "48a7b1c6-49b7-4cb0-926c-03194af50b6a-attachment.txt", "type": "text/plain"}], "start": 1751853892274, "stop": 1751853892596, "uuid": "f7b7c595-0266-4041-b3ef-b9074da738c5", "historyId": "a03ba38a51bcc9af12332b9cb9ea6b41", "testCaseId": "e9970ce8394fe9b627f33514f0afecf5", "fullName": "testcase.play.test_play.TestPlay#test_play", "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "39132-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}]}