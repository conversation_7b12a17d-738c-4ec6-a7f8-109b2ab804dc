{"name": "取消订单", "status": "passed", "attachments": [{"name": "接口地址", "source": "d1cea458-e7b6-44a1-b79c-8306a2e35519-attachment.json", "type": "application/json"}, {"name": "接口名称", "source": "d96171ec-21e2-4cdc-a9a7-f8762bf1b4a3-attachment.json", "type": "application/json"}, {"name": "请求方式", "source": "ec9f478b-7ae9-4407-8669-90518670b866-attachment.json", "type": "application/json"}, {"name": "请求头", "source": "b6547890-af6a-4906-a216-599b302452cf-attachment.json", "type": "application/json"}, {"name": "<PERSON><PERSON>", "source": "e8448f18-59e6-40a0-9f68-94fd2c5a6374-attachment.json", "type": "application/json"}, {"name": "测试用例名称", "source": "4733cc0e-cdb5-42a7-b201-86b94724a114-attachment.json", "type": "application/json"}, {"name": "参数类型", "source": "a59cf32d-c09c-4e05-8e81-4080c74d3f2c-attachment.json", "type": "application/json"}, {"name": "请求参数json格式", "source": "15745c83-e3d1-47e1-9f27-c670ec62c942-attachment.json", "type": "application/json"}, {"name": "请求参数实际入参", "source": "3c047a4f-2093-44cf-bb62-f849543a9ced-attachment.json", "type": "application/json"}, {"name": "接口实际响应信息", "source": "59dcd4b0-6703-483d-8840-fc5c18510bd9-attachment.json", "type": "application/json"}, {"name": "状态码断言结果：成功", "source": "49e7f81e-5bf9-431f-ac24-4ab4b5bfcea5-attachment.txt", "type": "text/plain"}, {"name": "相等断言结果：成功", "source": "8242c044-9a22-4420-8eba-b68f351c6a9d-attachment.json", "type": "application/json"}, {"name": "📋 测试执行日志", "source": "f257fa00-839a-4400-9e89-5f92076d732b-attachment.txt", "type": "text/plain"}], "start": 1751866441596, "stop": 1751866442868, "uuid": "f7509598-3a64-4dce-b82c-4b3e30bec2be", "historyId": "097386a9d43be40d3c28c1874a6e169b", "testCaseId": "e9970ce8394fe9b627f33514f0afecf5", "fullName": "testcase.play.test_play.TestPlay#test_play", "labels": [{"name": "story", "value": "C01_演出模块流程"}, {"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "16848-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}]}