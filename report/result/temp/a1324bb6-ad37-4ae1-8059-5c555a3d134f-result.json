{"name": "演出分类", "status": "passed", "attachments": [{"name": "接口地址", "source": "b35368c8-6157-4dae-b2f4-e234e2905a36-attachment.json", "type": "application/json"}, {"name": "接口名称", "source": "6cc0a22e-8ba3-4cbe-b340-f8b7f67b219e-attachment.json", "type": "application/json"}, {"name": "请求方式", "source": "a34e1d83-db42-433b-88b4-ef31341a6e16-attachment.json", "type": "application/json"}, {"name": "请求头", "source": "7ab1d014-4077-4d56-8c7e-78e8461dc425-attachment.json", "type": "application/json"}, {"name": "<PERSON><PERSON>", "source": "5fd8985d-7a19-4570-92c8-6f0fbbd00068-attachment.json", "type": "application/json"}, {"name": "测试用例名称", "source": "ec52c712-4d30-4cc5-a4fa-fb7856898b60-attachment.json", "type": "application/json"}, {"name": "参数类型", "source": "6230fe9e-53a0-4ae6-b366-7b30c29b0274-attachment.json", "type": "application/json"}, {"name": "请求参数json格式", "source": "590d7f9c-8a92-46bc-b3e3-d9eb1db15b1a-attachment.json", "type": "application/json"}, {"name": "请求参数实际入参", "source": "ee4dbc3c-474f-45e6-bf83-dd704246593d-attachment.json", "type": "application/json"}, {"name": "接口实际响应信息", "source": "6709b17c-d925-4cc5-bbb1-9ce3638c7eb3-attachment.json", "type": "application/json"}, {"name": "状态码断言结果：成功", "source": "9e2ed828-a791-4a34-ba08-2b06c7e2f7ae-attachment.txt", "type": "text/plain"}, {"name": "相等断言结果：成功", "source": "3ff0cf98-bd9a-4043-9c96-cbe2fa907fc3-attachment.json", "type": "application/json"}, {"name": "📋 测试执行日志", "source": "9d2af2b2-fcba-45b5-a41e-7f9efc990e63-attachment.txt", "type": "text/plain"}], "start": 1751866416482, "stop": 1751866416894, "uuid": "2edcfce8-bc06-4ddd-a12b-740eda549ed9", "historyId": "c36ab200ccb8723de7119149ea6c3088", "testCaseId": "e9970ce8394fe9b627f33514f0afecf5", "fullName": "testcase.play.test_play.TestPlay#test_play", "labels": [{"name": "story", "value": "C01_演出模块流程"}, {"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "16848-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}]}