{"name": "演出分类", "status": "passed", "attachments": [{"name": "接口地址", "source": "166ce1c8-5eaa-4e76-8c56-187106c8a2b5-attachment.json", "type": "application/json"}, {"name": "接口名称", "source": "5e81c6a8-34ac-4cc8-a9f9-70fa19db9ddf-attachment.json", "type": "application/json"}, {"name": "请求方式", "source": "3aa801c5-3887-4268-92e3-d7159e88732d-attachment.json", "type": "application/json"}, {"name": "请求头", "source": "a8dd3f73-6c25-454f-94ae-b30b25adaf69-attachment.json", "type": "application/json"}, {"name": "<PERSON><PERSON>", "source": "1e9e9aee-cbfd-4fe1-90fa-ddf071d83426-attachment.json", "type": "application/json"}, {"name": "测试用例名称", "source": "b249f75c-b6dd-4428-823d-c55a554b4d95-attachment.json", "type": "application/json"}, {"name": "参数类型", "source": "f76e967c-e7a7-4772-a57a-75d8c1b75d21-attachment.json", "type": "application/json"}, {"name": "请求参数json格式", "source": "eedc982d-ea5f-424f-acd0-640e79a3b88b-attachment.json", "type": "application/json"}, {"name": "请求参数实际入参", "source": "94942a31-c927-4844-b49a-279a5ce5faaa-attachment.json", "type": "application/json"}, {"name": "接口实际响应信息", "source": "486cfa2b-61a9-4f00-89b8-5f85ad8b91a9-attachment.json", "type": "application/json"}, {"name": "状态码断言结果：成功", "source": "ead56489-2b99-4a0e-b400-8d60aa1083da-attachment.txt", "type": "text/plain"}, {"name": "相等断言结果：成功", "source": "fc9f0160-5b0a-4cb4-be9d-3f66f91a0fe3-attachment.json", "type": "application/json"}, {"name": "📋 测试执行日志", "source": "7aafb496-2102-4a55-9db7-2ba0d601eefc-attachment.txt", "type": "text/plain"}], "parameters": [{"name": "api_info", "value": "{'baseInfo': {'module': '演出票', 'api_name': '演出分类', 'host': '${get_host(play)}', 'url': '/api/show/info/category_list', 'method': 'post', 'headers': '${get_headers(app)}'}, 'testCase': {'case_name': '获取猫眼演出分类', 'validation': [{'code': 200}, {'eq': {'code': 0}}], 'extract': 0, 'extract_list': np.float64(0.0), 'data': {'channelCode': '${appKey()}', 'merchant_code': '', 'token': '${get_extract_data(token)}'}}}"}], "start": 1751853727920, "stop": 1751853728267, "uuid": "89bcd316-6343-4a23-b1bd-4ab08bf5a5f7", "historyId": "8670a6529c6811d89314564f4152b621", "testCaseId": "e9970ce8394fe9b627f33514f0afecf5", "fullName": "testcase.play.test_play.TestPlay#test_play", "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "39072-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}]}