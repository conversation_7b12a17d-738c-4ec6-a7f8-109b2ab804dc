{"name": "演出项目详情", "status": "passed", "attachments": [{"name": "接口地址", "source": "d119765a-d014-4579-9d12-f6997af13ad0-attachment.json", "type": "application/json"}, {"name": "接口名称", "source": "2f5cb5d4-d69c-4276-a11a-616db3d0254f-attachment.json", "type": "application/json"}, {"name": "请求方式", "source": "5464736f-7a0d-4dc4-b72e-8108bf373ae0-attachment.json", "type": "application/json"}, {"name": "请求头", "source": "550eeefd-4e94-4b6a-a6ec-940c02477c4a-attachment.json", "type": "application/json"}, {"name": "<PERSON><PERSON>", "source": "517a2113-c472-455b-9a6a-dee476d30ad7-attachment.json", "type": "application/json"}, {"name": "测试用例名称", "source": "e692126f-4a4c-4f92-b3fe-4579c55afc8b-attachment.json", "type": "application/json"}, {"name": "参数类型", "source": "5e67c6a2-4b0a-4a07-9265-282c178e3548-attachment.json", "type": "application/json"}, {"name": "请求参数json格式", "source": "9a35fa5c-d9a4-4866-8693-797db3baf0e1-attachment.json", "type": "application/json"}, {"name": "请求参数实际入参", "source": "27b2dd75-0606-46aa-8a17-aaaeeb82413f-attachment.json", "type": "application/json"}, {"name": "接口实际响应信息", "source": "9f471825-31fe-42c7-b0b4-070ba5debe2e-attachment.json", "type": "application/json"}, {"name": "状态码断言结果：成功", "source": "0445f5dc-a274-4282-b0b4-73f0e19fad73-attachment.txt", "type": "text/plain"}, {"name": "相等断言结果：成功", "source": "34283eac-d8a5-422e-bb6b-58661e31cb98-attachment.json", "type": "application/json"}, {"name": "📋 测试执行日志", "source": "486542d9-37e8-4eb5-85b4-9157ce38e001-attachment.txt", "type": "text/plain"}], "start": 1751853899563, "stop": 1751853900106, "uuid": "d1ec765f-b18a-47a8-a282-612c72dc47eb", "historyId": "0457c216d6185e839b1ab99163c7bbc6", "testCaseId": "e9970ce8394fe9b627f33514f0afecf5", "fullName": "testcase.play.test_play.TestPlay#test_play", "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "39132-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}]}