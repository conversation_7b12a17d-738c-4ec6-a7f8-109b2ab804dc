{"name": "近期特惠", "status": "passed", "attachments": [{"name": "接口地址", "source": "43e1f45d-3673-4ade-8d08-2ff21093c5fb-attachment.json", "type": "application/json"}, {"name": "接口名称", "source": "9121b8e4-29e0-43fa-8f87-63418d6b00da-attachment.json", "type": "application/json"}, {"name": "请求方式", "source": "b5561c13-5001-457b-a176-5f156aed3f32-attachment.json", "type": "application/json"}, {"name": "请求头", "source": "69942bd8-da04-4b0b-b0ec-e701d5898f4b-attachment.json", "type": "application/json"}, {"name": "<PERSON><PERSON>", "source": "7c3521d2-e0d0-4feb-a674-f89b67fbe103-attachment.json", "type": "application/json"}, {"name": "测试用例名称", "source": "a4785f7d-0372-4751-ad50-0deacc764482-attachment.json", "type": "application/json"}, {"name": "参数类型", "source": "247a3e84-eb3b-4078-8ec1-bd5f9bede972-attachment.json", "type": "application/json"}, {"name": "请求参数json格式", "source": "0c86fa97-5561-4a29-8af6-32a3b23cc01a-attachment.json", "type": "application/json"}, {"name": "请求参数实际入参", "source": "4132ccc1-2726-448a-a744-3fc7a68049de-attachment.json", "type": "application/json"}, {"name": "接口实际响应信息", "source": "6e5348ab-022b-4cb4-bfd3-0cc799933f35-attachment.json", "type": "application/json"}, {"name": "状态码断言结果：成功", "source": "c2c704aa-7200-49a5-9f67-cb000f9bb29f-attachment.txt", "type": "text/plain"}, {"name": "相等断言结果：成功", "source": "28194863-00d5-4129-99ea-ddb627c6603f-attachment.json", "type": "application/json"}, {"name": "📋 测试执行日志", "source": "4bec26c3-2342-4ee7-84a8-aba589daac96-attachment.txt", "type": "text/plain"}], "start": 1751866420275, "stop": 1751866420583, "uuid": "0d0ec857-5440-4037-8e73-e05e5d36bad5", "historyId": "b9dc0ab2757f5b153151bfbd4cc293dd", "testCaseId": "e9970ce8394fe9b627f33514f0afecf5", "fullName": "testcase.play.test_play.TestPlay#test_play", "labels": [{"name": "story", "value": "C01_演出模块流程"}, {"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "16848-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}]}