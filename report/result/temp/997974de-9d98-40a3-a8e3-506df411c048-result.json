{"name": "今日必抢", "status": "passed", "attachments": [{"name": "接口地址", "source": "9f3d5053-afa6-4fd0-9361-0b149f9ea0e3-attachment.json", "type": "application/json"}, {"name": "接口名称", "source": "32b29ae8-e085-4a46-a0aa-81069992f3c7-attachment.json", "type": "application/json"}, {"name": "请求方式", "source": "01171f51-bfe0-4cc9-8cf2-f09a571f24c6-attachment.json", "type": "application/json"}, {"name": "请求头", "source": "5be83606-2b9a-4e60-9562-f75ee45667b9-attachment.json", "type": "application/json"}, {"name": "<PERSON><PERSON>", "source": "5495b6c9-9a9a-4e8d-8d81-e5d8abdfa959-attachment.json", "type": "application/json"}, {"name": "测试用例名称", "source": "297f0e14-0037-46b5-ab40-d5c995c90a47-attachment.json", "type": "application/json"}, {"name": "参数类型", "source": "1b88a5a9-2a00-468d-8ac4-2107bc469847-attachment.json", "type": "application/json"}, {"name": "请求参数json格式", "source": "aaec7530-aabb-4702-9ce3-2be7999d19c0-attachment.json", "type": "application/json"}, {"name": "请求参数实际入参", "source": "2a68c7eb-a6dd-4e19-82ba-89985b69948f-attachment.json", "type": "application/json"}, {"name": "接口实际响应信息", "source": "871163d3-47e3-453a-8241-03a281e7caf1-attachment.json", "type": "application/json"}, {"name": "状态码断言结果：成功", "source": "c7e43555-2485-4613-ab3e-7656f3d428be-attachment.txt", "type": "text/plain"}, {"name": "相等断言结果：成功", "source": "c2d153e1-ac6f-4da3-ad5a-ed130adf1d9c-attachment.json", "type": "application/json"}, {"name": "📋 测试执行日志", "source": "db021cc1-d7e7-48cf-9aa1-e1546d7e7cbe-attachment.txt", "type": "text/plain"}], "start": 1751851567735, "stop": 1751851568072, "uuid": "b9695e12-c664-41ee-90e5-f7cb89a63d93", "historyId": "09729bd43bb947d1ebe3c16a80dc6e25", "testCaseId": "e9970ce8394fe9b627f33514f0afecf5", "fullName": "testcase.play.test_play.TestPlay#test_play", "labels": [{"name": "story", "value": "C01_演出模块流程"}, {"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "34968-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}]}