{"name": "项目下场次票品列表", "status": "passed", "attachments": [{"name": "接口地址", "source": "ea15ac06-500a-419b-bfac-9286c4bb0ba9-attachment.json", "type": "application/json"}, {"name": "接口名称", "source": "d66381ab-0444-4d3b-92d3-500e55794e30-attachment.json", "type": "application/json"}, {"name": "请求方式", "source": "1ec50d31-ff20-4d43-8303-789b3181481f-attachment.json", "type": "application/json"}, {"name": "请求头", "source": "86520ca2-7aa5-48df-9153-54a2d4a7a80e-attachment.json", "type": "application/json"}, {"name": "<PERSON><PERSON>", "source": "0d5d59a4-4dbf-423f-be8c-9eda6f35c15e-attachment.json", "type": "application/json"}, {"name": "测试用例名称", "source": "60938b55-6170-4fdf-86b5-e140f3a49164-attachment.json", "type": "application/json"}, {"name": "参数类型", "source": "812d983a-5f9c-4ce6-8955-1b3e14dcab3d-attachment.json", "type": "application/json"}, {"name": "请求参数json格式", "source": "e344976d-9b06-4c5d-b846-0a28fb2e3888-attachment.json", "type": "application/json"}, {"name": "请求参数实际入参", "source": "b5cdb7c9-a0fd-45d7-8fb7-4b0c536aee98-attachment.json", "type": "application/json"}, {"name": "接口实际响应信息", "source": "2cc1c250-03ea-462a-a78e-168925db99b8-attachment.json", "type": "application/json"}, {"name": "状态码断言结果：成功", "source": "bc384f86-6eb4-4f38-a655-901ca25c9973-attachment.txt", "type": "text/plain"}, {"name": "相等断言结果：成功", "source": "fe3a9f8b-e4cf-4092-b3b9-396b5de4af0d-attachment.json", "type": "application/json"}, {"name": "📋 测试执行日志", "source": "62870cfd-c4ee-41a8-a633-eb2de4e41a7d-attachment.txt", "type": "text/plain"}], "parameters": [{"name": "api_info", "value": "{'baseInfo': {'module': '演出票', 'api_name': '项目下场次票品列表', 'host': '${get_host(play)}', 'url': '/api/show/info/show_ticket_units', 'method': 'get', 'headers': '${get_headers(app)}'}, 'testCase': {'case_name': '获取项目下场次票品列表信息', 'validation': [{'code': 200}, {'eq': {'code': 0}}], 'extract': {'extract_auto': 'show_ticket_units'}, 'extract_list': np.float64(0.0), 'params': {'showId': '${get_extract_data(showInfo,showId)}', 'channelCode': '${appKey()}', 'token': '${get_extract_data(token)}'}}}"}], "start": 1751853008988, "stop": 1751853009562, "uuid": "0fdc56b8-cda9-4e43-9831-f6d10c7432b2", "historyId": "92afa2451c3a3109d55af2cbc713b516", "testCaseId": "e9970ce8394fe9b627f33514f0afecf5", "fullName": "testcase.play.test_play.TestPlay#test_play", "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "37476-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}]}