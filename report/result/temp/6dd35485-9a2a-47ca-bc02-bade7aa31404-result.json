{"name": "**搜索**", "status": "passed", "attachments": [{"name": "接口地址", "source": "921991d0-f86d-4b80-93c9-e92e807c4cb0-attachment.json", "type": "application/json"}, {"name": "接口名称", "source": "2b70c38e-e911-465c-9dec-0f94ff9ebf94-attachment.json", "type": "application/json"}, {"name": "请求方式", "source": "52681f1d-7c30-404d-9861-dd5a9c43f6eb-attachment.json", "type": "application/json"}, {"name": "请求头", "source": "819b097a-9f49-47a0-8177-b6edf336d3eb-attachment.json", "type": "application/json"}, {"name": "<PERSON><PERSON>", "source": "f013c5b5-1886-43d0-8e36-01e340f05c35-attachment.json", "type": "application/json"}, {"name": "测试用例名称", "source": "0edb3680-a176-40af-80d6-bbb3709bd93d-attachment.json", "type": "application/json"}, {"name": "参数类型", "source": "03c1d188-9129-427b-b13b-c0b36c615e7c-attachment.json", "type": "application/json"}, {"name": "请求参数json格式", "source": "7030942c-7ca0-417c-a01d-326771318158-attachment.json", "type": "application/json"}, {"name": "请求参数实际入参", "source": "030c47b3-8193-4c08-9937-4f78313b2350-attachment.json", "type": "application/json"}, {"name": "接口实际响应信息", "source": "56e1270d-0c6d-4693-886d-fb3b3adf12fb-attachment.json", "type": "application/json"}, {"name": "状态码断言结果：成功", "source": "b8460c5b-edba-4273-afb4-0c0138653761-attachment.txt", "type": "text/plain"}, {"name": "相等断言结果：成功", "source": "08f69422-07e1-4547-835a-9faa5c10ef0e-attachment.json", "type": "application/json"}, {"name": "📋 测试执行日志", "source": "75395b2e-27c4-447f-b310-603d066b6ed6-attachment.txt", "type": "text/plain"}], "parameters": [{"name": "api_info", "value": "{'baseInfo': {'module': '演出票', 'api_name': '**搜索**', 'host': '${get_host(play)}', 'url': '/api/show/info/performance_search', 'method': 'post', 'headers': '${get_headers(app)}'}, 'testCase': {'case_name': '按演出名称关键字搜索', 'validation': [{'code': 200}, {'eq': {'code': 0}}], 'extract': 0, 'extract_list': np.float64(0.0), 'data': {'keyword': '演', 'channelCode': '${appKey()}', 'token': '${get_extract_data(token)}'}}}"}], "start": 1751853003029, "stop": 1751853003354, "uuid": "412f3189-615d-47f3-a2f6-307f90624f31", "historyId": "932055e7a8948657a1bdaf6fe3b4e09c", "testCaseId": "e9970ce8394fe9b627f33514f0afecf5", "fullName": "testcase.play.test_play.TestPlay#test_play", "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "37476-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}]}