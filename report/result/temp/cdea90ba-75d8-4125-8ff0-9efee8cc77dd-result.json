{"name": "项目下场次票品列表", "status": "passed", "attachments": [{"name": "接口地址", "source": "5b1885eb-cddb-40b8-91a1-71855b34c04e-attachment.json", "type": "application/json"}, {"name": "接口名称", "source": "23f6e4d2-e3cc-40dc-82ea-0754cb6ce7e4-attachment.json", "type": "application/json"}, {"name": "请求方式", "source": "048809e3-92a3-465d-98de-f4822b03852a-attachment.json", "type": "application/json"}, {"name": "请求头", "source": "d8502cc2-9b64-483f-b833-15a6e9f33241-attachment.json", "type": "application/json"}, {"name": "<PERSON><PERSON>", "source": "f2d911e6-df44-43c5-8f1a-5fb6c4e4b46d-attachment.json", "type": "application/json"}, {"name": "测试用例名称", "source": "562bd1a6-c0e6-4b52-b67e-8d8f9626559d-attachment.json", "type": "application/json"}, {"name": "参数类型", "source": "300d4264-eeee-4846-971f-0873d2ff64fe-attachment.json", "type": "application/json"}, {"name": "请求参数json格式", "source": "84a79a3a-6c5c-410e-8625-eab858b6067c-attachment.json", "type": "application/json"}, {"name": "请求参数实际入参", "source": "7353338e-4238-47e6-8543-cd14271b8607-attachment.json", "type": "application/json"}, {"name": "接口实际响应信息", "source": "35565160-e496-4163-b104-9e461820a0ec-attachment.json", "type": "application/json"}, {"name": "状态码断言结果：成功", "source": "b7b988ee-b70d-4b8f-b8b2-62121a397288-attachment.txt", "type": "text/plain"}, {"name": "相等断言结果：成功", "source": "33e9634b-4c2b-432e-a516-2f9dc02727b4-attachment.json", "type": "application/json"}, {"name": "📋 测试执行日志", "source": "0a6728c7-b57c-46d0-86ca-19a88c6c14d4-attachment.txt", "type": "text/plain"}], "parameters": [{"name": "api_info", "value": "{'baseInfo': {'module': '演出票', 'api_name': '项目下场次票品列表', 'host': '${get_host(play)}', 'url': '/api/show/info/show_ticket_units', 'method': 'get', 'headers': '${get_headers(app)}'}, 'testCase': {'case_name': '获取项目下场次票品列表信息', 'validation': [{'code': 200}, {'eq': {'code': 0}}], 'extract': {'extract_auto': 'show_ticket_units'}, 'extract_list': np.float64(0.0), 'params': {'showId': '${get_extract_data(showInfo,showId)}', 'channelCode': '${appKey()}', 'token': '${get_extract_data(token)}'}}}"}], "start": 1751853746300, "stop": 1751853746875, "uuid": "7c0b8121-7395-4c78-9264-ebdb00abdd77", "historyId": "92afa2451c3a3109d55af2cbc713b516", "testCaseId": "e9970ce8394fe9b627f33514f0afecf5", "fullName": "testcase.play.test_play.TestPlay#test_play", "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "39072-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}]}