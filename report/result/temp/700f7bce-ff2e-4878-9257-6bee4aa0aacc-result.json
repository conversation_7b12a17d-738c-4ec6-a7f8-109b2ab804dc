{"name": "根据场次获取取票方式", "status": "passed", "attachments": [{"name": "接口地址", "source": "8c7db85d-9413-4620-8bd6-68377dc72891-attachment.json", "type": "application/json"}, {"name": "接口名称", "source": "03ba660d-f7ed-43ee-84ba-2002d024be8d-attachment.json", "type": "application/json"}, {"name": "请求方式", "source": "5af559de-ecf8-47f6-b075-7ff5f03af29d-attachment.json", "type": "application/json"}, {"name": "请求头", "source": "e9997395-f208-4bac-b128-b1c126fae200-attachment.json", "type": "application/json"}, {"name": "<PERSON><PERSON>", "source": "02b05905-1ab7-43ac-9413-3ce963a690c5-attachment.json", "type": "application/json"}, {"name": "测试用例名称", "source": "aba524e3-10a8-4f3e-82e4-ee5f34ae1782-attachment.json", "type": "application/json"}, {"name": "参数类型", "source": "1ed8b1c5-0cc6-4314-b370-e90d62b6a79a-attachment.json", "type": "application/json"}, {"name": "请求参数json格式", "source": "b632481a-0ed4-43ee-8f91-48de29ce90ea-attachment.json", "type": "application/json"}, {"name": "请求参数实际入参", "source": "8fc3b9a5-8555-42fe-b7ad-3a8e2d789ba6-attachment.json", "type": "application/json"}, {"name": "接口实际响应信息", "source": "6e78d7c1-35cc-4598-a405-767119e927bc-attachment.json", "type": "application/json"}, {"name": "状态码断言结果：成功", "source": "db096730-9cac-44d3-9f48-a846ba0fcad5-attachment.txt", "type": "text/plain"}, {"name": "相等断言结果：成功", "source": "923c7d1c-15da-4125-ba5d-1f21d2fa25a3-attachment.json", "type": "application/json"}, {"name": "📋 测试执行日志", "source": "b5965c42-fbae-4c99-9a1f-3848522b76aa-attachment.txt", "type": "text/plain"}], "start": 1751853249103, "stop": 1751853249587, "uuid": "724f6db8-6d6d-4a7d-9161-4620dbfcb184", "historyId": "40f667e7e7d9255278cb56eb99290821", "testCaseId": "e9970ce8394fe9b627f33514f0afecf5", "fullName": "testcase.play.test_play.TestPlay#test_play", "labels": [{"name": "story", "value": "C01_演出模块流程"}, {"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "28812-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}]}