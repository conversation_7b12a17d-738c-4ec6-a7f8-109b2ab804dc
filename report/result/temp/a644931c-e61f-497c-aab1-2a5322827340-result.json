{"name": "查询影讯接口", "status": "passed", "attachments": [{"name": "接口地址", "source": "7b193778-4f05-48c7-b977-03435ca43b57-attachment.json", "type": "application/json"}, {"name": "接口名称", "source": "c1edf87e-a94b-48a4-88dd-1fec76e3d577-attachment.json", "type": "application/json"}, {"name": "请求方式", "source": "47426fef-7a56-42f7-a56e-40be9d1054dc-attachment.json", "type": "application/json"}, {"name": "请求头", "source": "7eb6f3fc-c28d-44d3-8ef4-672b89f8d6f6-attachment.json", "type": "application/json"}, {"name": "<PERSON><PERSON>", "source": "a95a07bf-3542-4380-9ec6-789326835b45-attachment.json", "type": "application/json"}, {"name": "测试用例名称", "source": "4b7f9b1e-7c2b-45a5-8eaa-ad83c0172381-attachment.json", "type": "application/json"}, {"name": "参数类型", "source": "4e41f2ea-d9e9-48ac-a3b6-936427f68db5-attachment.json", "type": "application/json"}, {"name": "请求参数json格式", "source": "41b24ad3-a22a-4950-ac91-de9d0a989340-attachment.json", "type": "application/json"}, {"name": "请求参数实际入参", "source": "b8d25ff7-61f1-44c1-b1ba-1c43b5caa9ff-attachment.json", "type": "application/json"}, {"name": "接口实际响应信息", "source": "56ccc456-84e0-4a3d-b041-a96e606c1acb-attachment.json", "type": "application/json"}, {"name": "状态码断言结果：成功", "source": "cb277bff-d4b6-4926-8061-446b12a85fc5-attachment.txt", "type": "text/plain"}, {"name": "相等断言结果：成功", "source": "aba0e742-aafc-4faa-82bb-dbf8d8d9962c-attachment.json", "type": "application/json"}, {"name": "相等断言结果：成功", "source": "134027df-61ba-4f85-8ee8-714a6226a60a-attachment.json", "type": "application/json"}, {"name": "📋 测试执行日志", "source": "8eabcbb5-74c7-4e93-8a24-881b26fb54c5-attachment.txt", "type": "text/plain"}], "start": 1751866444384, "stop": 1751866444761, "uuid": "b89fbc9d-4f4e-4e36-8579-f442439c2051", "historyId": "e9c2ea8fd9c81240c2b50d2a9668daf2", "testCaseId": "91ab679f57e05cf8b5a9e119b7e95056", "fullName": "testcase.demo_test.test_demo.TestFilmOrder#test_film_order", "labels": [{"name": "feature", "value": "M02_电影订票模块接口测试"}, {"name": "story", "value": "C02_电影下单流程"}, {"name": "parentSuite", "value": "testcase.demo_test"}, {"name": "suite", "value": "test_demo"}, {"name": "subSuite", "value": "TestFilmOrder"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "16848-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.demo_test.test_demo"}]}