{"name": "取消订单", "status": "passed", "attachments": [{"name": "接口地址", "source": "761776b2-3d67-46f1-8f06-c1e65d1db5e4-attachment.json", "type": "application/json"}, {"name": "接口名称", "source": "19eb0fbc-3f80-47dc-bdb4-0afffe858607-attachment.json", "type": "application/json"}, {"name": "请求方式", "source": "57f1e02c-c59c-4dfd-a687-9505e79c9c49-attachment.json", "type": "application/json"}, {"name": "请求头", "source": "e06a0d37-15b3-402c-a295-33af2eae7c29-attachment.json", "type": "application/json"}, {"name": "<PERSON><PERSON>", "source": "52cc1490-1c7a-4172-89dc-b522c2825356-attachment.json", "type": "application/json"}, {"name": "测试用例名称", "source": "e9ff0d1a-a3a9-41a1-b815-e43b19e6d37d-attachment.json", "type": "application/json"}, {"name": "参数类型", "source": "7b586dc2-b1d4-4eca-9eae-9479a0dcddd0-attachment.json", "type": "application/json"}, {"name": "请求参数json格式", "source": "bf9b96cd-36ee-4463-ac81-8d9b22e2f196-attachment.json", "type": "application/json"}, {"name": "请求参数实际入参", "source": "889dee25-5579-422b-a9e5-4a4d87bb8ebc-attachment.json", "type": "application/json"}, {"name": "接口实际响应信息", "source": "be8f5e05-e5aa-4031-91f3-4fc9e144fb5e-attachment.json", "type": "application/json"}, {"name": "状态码断言结果：成功", "source": "2cf4530e-4cb5-4aee-9e1f-806624f2cb93-attachment.txt", "type": "text/plain"}, {"name": "相等断言结果：成功", "source": "4084d0ef-4f3d-45cc-becd-954f225f444a-attachment.json", "type": "application/json"}, {"name": "📋 测试执行日志", "source": "3fd743a0-d85a-4d2f-907a-ebc905e2624b-attachment.txt", "type": "text/plain"}], "start": 1751853253909, "stop": 1751853255152, "uuid": "ba746817-9e7b-46b2-ae4a-4e5f6dc88eae", "historyId": "097386a9d43be40d3c28c1874a6e169b", "testCaseId": "e9970ce8394fe9b627f33514f0afecf5", "fullName": "testcase.play.test_play.TestPlay#test_play", "labels": [{"name": "story", "value": "C01_演出模块流程"}, {"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "28812-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}]}