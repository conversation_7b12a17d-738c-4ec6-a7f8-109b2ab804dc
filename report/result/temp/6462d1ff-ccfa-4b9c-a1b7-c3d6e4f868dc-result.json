{"name": "**搜索**", "status": "passed", "attachments": [{"name": "接口地址", "source": "72a60cea-3b8d-4ae7-9815-a3a25e073c87-attachment.json", "type": "application/json"}, {"name": "接口名称", "source": "ff07c79f-34cc-4772-bc58-cedca52ab77a-attachment.json", "type": "application/json"}, {"name": "请求方式", "source": "29cf8ba6-8858-4ab2-8487-ec19a0d861d5-attachment.json", "type": "application/json"}, {"name": "请求头", "source": "d8a75ca6-6541-4763-9e5d-f9c3826de522-attachment.json", "type": "application/json"}, {"name": "<PERSON><PERSON>", "source": "3e6fa0be-6051-49f2-824f-66fc6b3b40a2-attachment.json", "type": "application/json"}, {"name": "测试用例名称", "source": "f5c98952-50ef-4fba-a4a7-3ced01e939b3-attachment.json", "type": "application/json"}, {"name": "参数类型", "source": "d4bcbaf5-96d9-4ab2-8744-059e8ce6180f-attachment.json", "type": "application/json"}, {"name": "请求参数json格式", "source": "1ab1a873-c358-4b06-bed9-e86928e99c63-attachment.json", "type": "application/json"}, {"name": "请求参数实际入参", "source": "a8d7f257-4a9d-4b60-95ea-dea617c578a0-attachment.json", "type": "application/json"}, {"name": "接口实际响应信息", "source": "a7b94c3b-65c3-4d14-be24-14fab0685d31-attachment.json", "type": "application/json"}, {"name": "状态码断言结果：成功", "source": "60ba4e39-2340-4bc2-a43d-61849c0a5ad5-attachment.txt", "type": "text/plain"}, {"name": "相等断言结果：成功", "source": "48c3cce3-f723-44db-a698-b1caea7f87c1-attachment.json", "type": "application/json"}, {"name": "📋 测试执行日志", "source": "9d27bb4a-1a2a-466a-8b5f-bcd8369443c4-attachment.txt", "type": "text/plain"}], "start": 1751853241086, "stop": 1751853241389, "uuid": "88d7352e-457c-4912-9b62-ed87fbd6dacb", "historyId": "80ea16c71eb34b6e85110c3f875859f1", "testCaseId": "e9970ce8394fe9b627f33514f0afecf5", "fullName": "testcase.play.test_play.TestPlay#test_play", "labels": [{"name": "story", "value": "C01_演出模块流程"}, {"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "28812-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}]}