{"name": "**搜索**", "status": "passed", "attachments": [{"name": "接口地址", "source": "4187cdff-cb24-4140-9755-17b7e78d1346-attachment.json", "type": "application/json"}, {"name": "接口名称", "source": "bf746719-4c00-404e-982d-3fd478e96741-attachment.json", "type": "application/json"}, {"name": "请求方式", "source": "5b0d1b6a-7d97-425e-ba1d-5d90163c405f-attachment.json", "type": "application/json"}, {"name": "请求头", "source": "83465c17-299d-4328-adfc-d1d7586bb7fb-attachment.json", "type": "application/json"}, {"name": "<PERSON><PERSON>", "source": "e9e56e07-86ea-432f-b1c0-38b4c51099a2-attachment.json", "type": "application/json"}, {"name": "测试用例名称", "source": "8bcdc051-c775-4865-b508-3800ef618dc8-attachment.json", "type": "application/json"}, {"name": "参数类型", "source": "8f403a3b-574b-43f8-80ba-2734b4dcc3c4-attachment.json", "type": "application/json"}, {"name": "请求参数json格式", "source": "8cd29d4c-8b7b-4942-a757-49f951e1e619-attachment.json", "type": "application/json"}, {"name": "请求参数实际入参", "source": "09922d8e-5a3c-430e-aae1-6f318a5a80d0-attachment.json", "type": "application/json"}, {"name": "接口实际响应信息", "source": "0b44b9e4-b237-483b-acad-217d557ed025-attachment.json", "type": "application/json"}, {"name": "状态码断言结果：成功", "source": "d737d81e-5061-456b-bd37-2fa7e3c81574-attachment.txt", "type": "text/plain"}, {"name": "相等断言结果：成功", "source": "7922562e-5e87-4671-9bd1-3cca006aa89c-attachment.json", "type": "application/json"}, {"name": "📋 测试执行日志", "source": "dfe934a4-21ca-4e22-a280-6aecc27a39bf-attachment.txt", "type": "text/plain"}], "start": 1751852468344, "stop": 1751852468676, "uuid": "5a7b95c5-96f6-4bb6-9706-bacfbce3737f", "historyId": "80ea16c71eb34b6e85110c3f875859f1", "testCaseId": "e9970ce8394fe9b627f33514f0afecf5", "fullName": "testcase.play.test_play.TestPlay#test_play", "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "38092-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}]}