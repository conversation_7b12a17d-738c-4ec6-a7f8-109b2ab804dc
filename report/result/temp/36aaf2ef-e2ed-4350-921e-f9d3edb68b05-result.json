{"name": "根据场次获取取票方式", "status": "passed", "attachments": [{"name": "接口地址", "source": "e2b31f87-5bfa-42c7-9e0d-ef1eee354a04-attachment.json", "type": "application/json"}, {"name": "接口名称", "source": "eb4ea80c-adcd-45f0-8daf-66e1f770aae1-attachment.json", "type": "application/json"}, {"name": "请求方式", "source": "6128cab5-74a3-4a9d-936b-e5f74ce10cba-attachment.json", "type": "application/json"}, {"name": "请求头", "source": "5cbd2315-0ee6-4b88-a18c-b95a3b37ea54-attachment.json", "type": "application/json"}, {"name": "<PERSON><PERSON>", "source": "6eb10688-86a5-4c2e-8490-9cde9eca42a6-attachment.json", "type": "application/json"}, {"name": "测试用例名称", "source": "a3c26cf2-6f13-445d-bf5a-65872c574add-attachment.json", "type": "application/json"}, {"name": "参数类型", "source": "29ea16c0-376b-434b-802a-ce3ea9516173-attachment.json", "type": "application/json"}, {"name": "请求参数json格式", "source": "5c6fd1ed-2686-4ac3-91c3-c5e7e07f3bed-attachment.json", "type": "application/json"}, {"name": "请求参数实际入参", "source": "525e25d5-9432-43df-9c3b-8299227f3cd6-attachment.json", "type": "application/json"}, {"name": "接口实际响应信息", "source": "9e939670-0dd5-464d-91ba-c602bd1619d4-attachment.json", "type": "application/json"}, {"name": "状态码断言结果：成功", "source": "d15a0e24-63f3-4551-a207-8658de04dd39-attachment.txt", "type": "text/plain"}, {"name": "相等断言结果：成功", "source": "4e2e93b8-0598-41f1-9be7-911600c8f894-attachment.json", "type": "application/json"}, {"name": "📋 测试执行日志", "source": "62c9a939-0196-45e1-828d-8f732ac24fb5-attachment.txt", "type": "text/plain"}], "start": 1751851585180, "stop": 1751851585697, "uuid": "756fe0d2-bff3-4724-9c92-135eadf32bb8", "historyId": "40f667e7e7d9255278cb56eb99290821", "testCaseId": "e9970ce8394fe9b627f33514f0afecf5", "fullName": "testcase.play.test_play.TestPlay#test_play", "labels": [{"name": "story", "value": "C01_演出模块流程"}, {"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "34968-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}]}