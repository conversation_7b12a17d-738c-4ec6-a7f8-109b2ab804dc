{"name": "演出场次列表", "status": "passed", "attachments": [{"name": "接口地址", "source": "aa651eb1-2eb3-44f5-bb00-2d394ac88eed-attachment.json", "type": "application/json"}, {"name": "接口名称", "source": "44881383-9dd6-4639-a977-95df70f1a3a5-attachment.json", "type": "application/json"}, {"name": "请求方式", "source": "15c33e95-120e-45cd-861d-47e62808dc6b-attachment.json", "type": "application/json"}, {"name": "请求头", "source": "da994b0c-a874-4839-b380-515ec95af7a0-attachment.json", "type": "application/json"}, {"name": "<PERSON><PERSON>", "source": "28744299-d824-4cfe-b8e8-55ab419403de-attachment.json", "type": "application/json"}, {"name": "测试用例名称", "source": "26670335-64ef-4391-a867-f69a889d9ef9-attachment.json", "type": "application/json"}, {"name": "参数类型", "source": "90e460b4-5a1d-4777-8fe8-58a23ea3a945-attachment.json", "type": "application/json"}, {"name": "请求参数json格式", "source": "42efdadb-7f6b-4114-a497-0b92700fcb29-attachment.json", "type": "application/json"}, {"name": "请求参数实际入参", "source": "dadb2fb5-a569-4521-99a0-0bc5277feef7-attachment.json", "type": "application/json"}, {"name": "接口实际响应信息", "source": "01659032-0984-47af-9303-1afee5290b63-attachment.json", "type": "application/json"}, {"name": "状态码断言结果：成功", "source": "73d7d13d-6a07-4dd4-b118-fff5a3c1c02c-attachment.txt", "type": "text/plain"}, {"name": "相等断言结果：成功", "source": "fef68419-2255-44c7-80fe-18a70e4db34f-attachment.json", "type": "application/json"}, {"name": "📋 测试执行日志", "source": "a6e7520a-ab4b-4255-9559-0cefaf64c367-attachment.txt", "type": "text/plain"}], "start": 1751853901628, "stop": 1751853902213, "uuid": "9896c5c1-11a1-46b6-bc36-6544af8b3cf4", "historyId": "ce6724bce5907ddc1a7c9b8a022c9530", "testCaseId": "e9970ce8394fe9b627f33514f0afecf5", "fullName": "testcase.play.test_play.TestPlay#test_play", "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "39132-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}]}