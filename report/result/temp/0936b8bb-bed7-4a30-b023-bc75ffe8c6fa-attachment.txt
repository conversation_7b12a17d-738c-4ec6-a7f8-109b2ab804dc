INFO - 2025-07-07 09:26:02,451 - run.py:17 -[run:<module>] - Python版本: 3.12.6 (tags/v3.12.6:a4a2d2b, Sep  6 2024, 20:11:23) [MSC v.1940 64 bit (AMD64)]
INFO - 2025-07-07 09:26:02,452 - run.py:18 -[run:<module>] - Python可执行文件: C:\vae\python_project\zy_ApiAuto\.venv\Scripts\python.exe
INFO - 2025-07-07 09:26:02,452 - run.py:19 -[run:<module>] - 当前工作目录: C:\vae\python_project\zy_ApiAuto
INFO - 2025-07-07 09:26:02,452 - run.py:20 -[run:<module>] - 脚本参数: ['C:\\vae\\python_project\\zy_ApiAuto\\run.py']
INFO - 2025-07-07 09:26:02,452 - run.py:36 -[run:<module>] - 执行路径【['./testcase/play']】
INFO - 2025-07-07 09:26:04,279 - zy_backend_login.py:57 -[zy_backend_login:login] - 登录成功！{'msg': '登录成功', 'uid': '1942032429773586432', 'code': '0', 'data': {'name': '管理员', 'roleName': '租户管理员', 'isDefaultPwd': '0', 'userType': '1'}}
INFO - 2025-07-07 09:26:04,622 - connectMysql.py:22 -[connectMysql:__init__] - 成功连接到数据库：数据库ip：*************
INFO - 2025-07-07 09:26:04,674 - connectMysql.py:43 -[connectMysql:insert] - 插入数据库SQL--INSERT INTO `wei_sms_code_log` (`id`, `phone`, `channel_code`, `code`, `code_status`, `type`, `create_by`, `create_date`, `update_by`, `update_date`) VALUES (784993377987661856, '19877292090', 'C10000027', '123456', 0, 1, NULL, '2025-07-07 09:26:04', NULL, '2025-07-07 09:26:04');
INFO - 2025-07-07 09:26:04,674 - connectMysql.py:44 -[connectMysql:insert] - 数据库数据插入成功
INFO - 2025-07-07 09:26:04,907 - zy_app_login.py:103 -[zy_app_login:login] - 【++登录成功++】{'head': {'errCode': '0', 'errMsg': '', 'tradeId': 'login', 'timestamp': '20250707092628'}, 'uid': '1942032434005577728', 'body': {'birthday': '', 'lastTime': '20250704152900', 'isCurrentRegister': '0', 'zipCode': '', 'sign': '198a1633ef6b0e7b5a32deab53f00776', 'imageNo': 0, 'points': '0', 'cityName': '', 'areaName': '', 'areaNo': '', 'imageUrl': '', 'provinceNo': '', 'email': '', 'QQ': '', 'cityNo': '', 'address': '', 'level': '2', 'nickName': '', 'sex': 2, 'mobile': '19877292090', 'MSN': '', 'identityCard': '', 'sessionId': '346518515883278675', 'userId': '346516136741756608', 'isMobileValid': 1, 'loginCount': 0, 'token': 'cFhOuiaXefoQIIFZi/rZq65kzE5/p5c4JI6ec0Ziccugep4bWnUKIgeEpUqRAaEpKkmSicNo+lzmbuXHkSzhWsCkVCAnsXKUuEkSZEovSbrteHKAi/gS0TQxfeQrlbYK', 'realName': '', 'createTime': '1751613674000', 'provinceName': '', 'username': ''}}
INFO - 2025-07-07 09:26:05,673 - conftest.py:7 -[conftest:print_info] - ---------------接口测试开始---------------
INFO - 2025-07-07 09:26:05,719 - sendrequests.py:57 -[sendrequests:execute_api_request] - 接口名称：演出分类
INFO - 2025-07-07 09:26:05,719 - sendrequests.py:58 -[sendrequests:execute_api_request] - 请求地址：http://*************/api/show/info/category_list
INFO - 2025-07-07 09:26:05,719 - sendrequests.py:59 -[sendrequests:execute_api_request] - 请求方式：POST
INFO - 2025-07-07 09:26:05,719 - sendrequests.py:60 -[sendrequests:execute_api_request] - 请求头：{'Content-Type': 'application/x-www-form-urlencoded', 'Connection': 'Keep-Alive', 'Accept-Encoding': 'gzip'}
INFO - 2025-07-07 09:26:05,719 - sendrequests.py:61 -[sendrequests:execute_api_request] - 测试用例名：获取猫眼演出分类
INFO - 2025-07-07 09:26:05,719 - sendrequests.py:62 -[sendrequests:execute_api_request] - cookies值：None
INFO - 2025-07-07 09:26:05,719 - sendrequests.py:67 -[sendrequests:execute_api_request] - 参数类型：data
INFO - 2025-07-07 09:26:05,719 - sendrequests.py:69 -[sendrequests:execute_api_request] - 请求参数：{"channelCode": "C10000027", "merchant_code": "", "token": "cFhOuiaXefoQIIFZi/rZq65kzE5/p5c4JI6ec0Ziccugep4bWnUKIgeEpUqRAaEpKkmSicNo+lzmbuXHkSzhWsCkVCAnsXKUuEkSZEovSbrteHKAi/gS0TQxfeQrlbYK,C10000027"}
INFO - 2025-07-07 09:26:06,209 - apiutils_business.py:179 -[apiutils_business:execute_test_cases] - 接口实际返回结果：{"code":0,"msg":"","data":[{"id":"10001","name":"演唱会","image":"http:\/\/*************\/resource\/play\/upload\/20250421\/175345K0n_6qP6Pi.jpg","tag_list":["测试文案","演出演唱会12"]},{"id":"10002","name":"体育赛事","image":"http:\/\/*************\/resource\/play\/upload\/20250408\/144744oaSxDsD3Qu.png","tag_list":["孙颖莎","全红婵"]},{"id":"10003","name":"戏曲艺术","image":"http:\/\/*************\/resource\/play\/upload\/20250408\/1448079S7mHpY2QU.png","tag_list":[]},{"id":"10017","name":"Livehouse","image":"http:\/\/*************\/resource\/play\/upload\/20250408\/1449279xNEIQlg9D.png","tag_list":[]},{"id":"10005","name":"舞蹈芭蕾","image":"http:\/\/*************\/resource\/play\/upload\/20250408\/145113dfAkJoKwCg.png","tag_list":[]},{"id":"10006","name":"音乐会","image":"http:\/\/*************\/resource\/play\/upload\/20250408\/1448369SbU_7vykE.png","tag_list":[]},{"id":"10007","name":"亲子演出","image":"http:\/\/*************\/resource\/play\/upload\/20250408\/144825FTdAI7yPy_.png","tag_list":[]},{"id":"10010","name":"音乐节","image":"http:\/\/*************\/resource\/play\/upload\/20250422\/145418ZAvXkbQMhU.png","tag_list":[]},{"id":"10014","name":"沉浸剧场","image":"http:\/\/*************\/resource\/play\/upload\/20250408\/144959tWsxiYBDA-.png","tag_list":[]},{"id":"10016","name":"相声","image":"http:\/\/*************\/resource\/play\/upload\/20250408\/144939LP2Ruhsuki.png","tag_list":[]},{"id":"10015","name":"脱口秀","image":"http:\/\/*************\/resource\/play\/upload\/20250408\/144949GXhLPNXxUa.png","tag_list":[]},{"id":"10009","name":"休闲展览","image":"http:\/\/*************\/resource\/play\/upload\/20250408\/144907oWhMCZrrG_.png","tag_list":[]},{"id":"10004","name":"话剧音乐剧","image":"http:\/\/*************\/resource\/play\/upload\/20250408\/145102PT7jed5WkQ.png","tag_list":[]},{"id":"10008","name":"其他","image":"http:\/\/*************\/resource\/play\/upload\/20250331\/164501dyI8a3idSN.png","tag_list":["标签 一","标签二"]}]}
INFO - 2025-07-07 09:26:06,212 - assertion_utils.py:35 -[assertion_utils:status_code_assert] - 状态码断言成功：接口实际返回状态码 200 == 200
INFO - 2025-07-07 09:26:06,212 - assertion_utils.py:94 -[assertion_utils:equal_assert] - 相等断言成功：接口实际结果 {'code': 0} == 预期结果：{'code': 0}
INFO - 2025-07-07 09:26:06,213 - assertion_utils.py:206 -[assertion_utils:assert_result] - 测试成功