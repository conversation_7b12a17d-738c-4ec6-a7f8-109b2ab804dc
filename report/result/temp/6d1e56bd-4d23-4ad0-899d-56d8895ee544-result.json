{"name": "首页广告位", "status": "passed", "attachments": [{"name": "接口地址", "source": "495342fe-65e7-42f8-b234-a9cdc5cdc95a-attachment.json", "type": "application/json"}, {"name": "接口名称", "source": "66332a9f-405a-4b52-8328-d6964f56be1a-attachment.json", "type": "application/json"}, {"name": "请求方式", "source": "d724e10f-0c57-451e-81df-645fbfbe9776-attachment.json", "type": "application/json"}, {"name": "请求头", "source": "34654153-067b-4d1e-9fd5-1ad2d0a12128-attachment.json", "type": "application/json"}, {"name": "<PERSON><PERSON>", "source": "4dcf40b5-65b1-494a-a839-773f0b73bbee-attachment.json", "type": "application/json"}, {"name": "测试用例名称", "source": "b9af926e-ecf0-4ff6-9fda-2b368d1a2014-attachment.json", "type": "application/json"}, {"name": "参数类型", "source": "ec2d4c7c-25e3-4d3d-82fd-ab5d293b3843-attachment.json", "type": "application/json"}, {"name": "请求参数json格式", "source": "071788f4-c498-4bc2-87ee-3e151f2d323f-attachment.json", "type": "application/json"}, {"name": "请求参数实际入参", "source": "bfabef26-a170-410d-8768-aa79cdca83e8-attachment.json", "type": "application/json"}, {"name": "接口实际响应信息", "source": "b3df15f2-5e92-4e37-96f8-426707a70414-attachment.json", "type": "application/json"}, {"name": "状态码断言结果：成功", "source": "77a977ed-4e37-45ea-9a2c-85f1b75dff08-attachment.txt", "type": "text/plain"}, {"name": "相等断言结果：成功", "source": "aed84e63-0fec-4545-9817-f1283badc1b6-attachment.json", "type": "application/json"}, {"name": "📋 测试执行日志", "source": "6ca8f157-c095-46e5-94dc-519468f11306-attachment.txt", "type": "text/plain"}], "start": 1751853894112, "stop": 1751853894392, "uuid": "2e69a22b-cf60-42be-aaf7-06cef60ef21e", "historyId": "998f7fb53b18ea3045f71922b591e307", "testCaseId": "e9970ce8394fe9b627f33514f0afecf5", "fullName": "testcase.play.test_play.TestPlay#test_play", "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "39132-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}]}