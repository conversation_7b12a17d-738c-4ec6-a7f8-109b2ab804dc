{"name": "演出分类", "status": "passed", "attachments": [{"name": "接口地址", "source": "1a9a9668-7ff6-498d-b630-70368ac6d7d2-attachment.json", "type": "application/json"}, {"name": "接口名称", "source": "be615799-04c0-44b8-ba69-597935772a5c-attachment.json", "type": "application/json"}, {"name": "请求方式", "source": "c593a9be-fc00-44e2-a9e1-1b9de5ed234b-attachment.json", "type": "application/json"}, {"name": "请求头", "source": "aaec2ce7-c441-4f13-a7c9-b634c89ab39b-attachment.json", "type": "application/json"}, {"name": "<PERSON><PERSON>", "source": "973678bf-3e71-43d7-a9b5-8a623b93b31c-attachment.json", "type": "application/json"}, {"name": "测试用例名称", "source": "8cea2f62-8878-4619-8653-bc504dc3bf5d-attachment.json", "type": "application/json"}, {"name": "参数类型", "source": "06b4e935-b336-4d47-9115-f63385ce64d1-attachment.json", "type": "application/json"}, {"name": "请求参数json格式", "source": "16615253-5563-49ce-bcf5-60796b5219f1-attachment.json", "type": "application/json"}, {"name": "请求参数实际入参", "source": "8e2aa273-bfc8-4476-b88f-8f79dfdb64cc-attachment.json", "type": "application/json"}, {"name": "接口实际响应信息", "source": "a96fd696-351c-40d8-8a28-12cf5f9f8679-attachment.json", "type": "application/json"}, {"name": "状态码断言结果：成功", "source": "ce26520b-05f8-42d8-9a72-6c6bb612dd6e-attachment.txt", "type": "text/plain"}, {"name": "相等断言结果：成功", "source": "320b3ab6-98a2-4842-b6b6-f664e559a83a-attachment.json", "type": "application/json"}, {"name": "📋 测试执行日志", "source": "1f71cad5-e015-470b-999f-0501234ca31d-attachment.txt", "type": "text/plain"}], "start": 1751852456883, "stop": 1751852457271, "uuid": "65adf0f6-0b32-4384-895a-f028a8542c81", "historyId": "c36ab200ccb8723de7119149ea6c3088", "testCaseId": "e9970ce8394fe9b627f33514f0afecf5", "fullName": "testcase.play.test_play.TestPlay#test_play", "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "38092-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}]}