{"name": "首页广告位", "status": "passed", "attachments": [{"name": "接口地址", "source": "71090209-a634-48c7-ba62-670f5853621a-attachment.json", "type": "application/json"}, {"name": "接口名称", "source": "a9275e7b-fb1a-4826-a244-f2eda2fab281-attachment.json", "type": "application/json"}, {"name": "请求方式", "source": "d9d7fe7d-76b5-41a2-9295-5a7f448012db-attachment.json", "type": "application/json"}, {"name": "请求头", "source": "e7cbb448-7de0-4f3c-93b7-9b2126bd395c-attachment.json", "type": "application/json"}, {"name": "<PERSON><PERSON>", "source": "2ca97d97-33b5-4968-b8ff-19c0dfb16abe-attachment.json", "type": "application/json"}, {"name": "测试用例名称", "source": "dac2a63b-d93a-4d57-80c5-0ffa019e9641-attachment.json", "type": "application/json"}, {"name": "参数类型", "source": "e400cd7b-fa12-47ef-8c3c-f4b67f667171-attachment.json", "type": "application/json"}, {"name": "请求参数json格式", "source": "83b7c81e-c007-46b3-a9e7-21a86e70d42c-attachment.json", "type": "application/json"}, {"name": "请求参数实际入参", "source": "5de83737-fad4-4e73-8b95-d0565063eaf0-attachment.json", "type": "application/json"}, {"name": "接口实际响应信息", "source": "ff4bfe37-a1d1-4135-bda6-2f12235f5b76-attachment.json", "type": "application/json"}, {"name": "状态码断言结果：成功", "source": "d2977be6-5a2c-4961-bddd-ccd2c9be008f-attachment.txt", "type": "text/plain"}, {"name": "相等断言结果：成功", "source": "c622d489-d1b5-4860-bc53-ad1c03128ae4-attachment.json", "type": "application/json"}, {"name": "📋 测试执行日志", "source": "1b4b2f9d-e8d7-4971-ace7-e0f89fbfb559-attachment.txt", "type": "text/plain"}], "start": 1751853237445, "stop": 1751853237734, "uuid": "540badeb-4719-49c7-a6f2-68d29a90edac", "historyId": "998f7fb53b18ea3045f71922b591e307", "testCaseId": "e9970ce8394fe9b627f33514f0afecf5", "fullName": "testcase.play.test_play.TestPlay#test_play", "labels": [{"name": "story", "value": "C01_演出模块流程"}, {"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "28812-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}]}