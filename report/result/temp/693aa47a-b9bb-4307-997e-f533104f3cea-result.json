{"name": "演唱会分类", "status": "passed", "attachments": [{"name": "接口地址", "source": "1127feef-7339-4615-bb1d-75627dfb6db0-attachment.json", "type": "application/json"}, {"name": "接口名称", "source": "691d6499-7fc3-4761-a8ed-585a90900ae2-attachment.json", "type": "application/json"}, {"name": "请求方式", "source": "ec40291c-9eb6-441f-a6e9-cd05ed9b9f0b-attachment.json", "type": "application/json"}, {"name": "请求头", "source": "f8bb2359-c439-46f7-a29b-84a589527928-attachment.json", "type": "application/json"}, {"name": "<PERSON><PERSON>", "source": "a316a0ed-8dbd-4909-96cb-807859bb1b71-attachment.json", "type": "application/json"}, {"name": "测试用例名称", "source": "2038fe43-7f7d-4d89-9725-e48b6dcf02cd-attachment.json", "type": "application/json"}, {"name": "参数类型", "source": "98e65baf-0b09-4dcc-8c75-75b389d60878-attachment.json", "type": "application/json"}, {"name": "请求参数json格式", "source": "739fb0d7-02d5-417e-a255-9940652e1e5a-attachment.json", "type": "application/json"}, {"name": "请求参数实际入参", "source": "102d06bf-28f0-46e1-8fc5-f53cc41d5070-attachment.json", "type": "application/json"}, {"name": "接口实际响应信息", "source": "0e513f08-a50d-4056-9628-f75f1d21b73e-attachment.json", "type": "application/json"}, {"name": "状态码断言结果：成功", "source": "2a79eabe-35dc-499e-8a7f-d21cd7e86773-attachment.txt", "type": "text/plain"}, {"name": "相等断言结果：成功", "source": "d2f4cd34-1090-4e8d-9d23-805237eeaf7a-attachment.json", "type": "application/json"}, {"name": "📋 测试执行日志", "source": "4f38e8b8-9c08-455d-8210-19bfd5662d58-attachment.txt", "type": "text/plain"}], "parameters": [{"name": "api_info", "value": "{'baseInfo': {'module': '演出票', 'api_name': '演唱会分类', 'host': '${get_host(play)}', 'url': '/api/show/info/performance_search', 'method': 'post', 'headers': '${get_headers(app)}'}, 'testCase': {'case_name': '按类型搜索=演唱会', 'validation': [{'code': 200}, {'eq': {'code': 0}}], 'extract': 0, 'extract_list': np.float64(0.0), 'data': {'category_id': '10001', 'channelCode': '${appKey()}', 'token': '${get_extract_data(token)}'}}}"}], "start": 1751853738165, "stop": 1751853738463, "uuid": "2aa124d1-170f-425f-a1d0-7af82f9b1840", "historyId": "b8a74dca691f81270d24ebc4399d791b", "testCaseId": "e9970ce8394fe9b627f33514f0afecf5", "fullName": "testcase.play.test_play.TestPlay#test_play", "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "39072-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}]}