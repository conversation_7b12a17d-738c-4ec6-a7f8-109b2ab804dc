{"name": "取消订单", "status": "passed", "attachments": [{"name": "接口地址", "source": "a9bef454-5065-4d3a-976c-3ee0905566a8-attachment.json", "type": "application/json"}, {"name": "接口名称", "source": "46bd99da-8cc7-411d-9f0f-615d9cc48d35-attachment.json", "type": "application/json"}, {"name": "请求方式", "source": "6d92ff5b-2818-4387-9b44-a4783098eb1e-attachment.json", "type": "application/json"}, {"name": "请求头", "source": "791018dc-8670-41f6-8cd4-e8d47917dc72-attachment.json", "type": "application/json"}, {"name": "<PERSON><PERSON>", "source": "02dc5c9e-cadd-482d-9b19-909f9add1b90-attachment.json", "type": "application/json"}, {"name": "测试用例名称", "source": "58c01d73-6067-4975-842b-e2bd714857c3-attachment.json", "type": "application/json"}, {"name": "参数类型", "source": "aa363a70-468b-4150-b86d-d469af27a50e-attachment.json", "type": "application/json"}, {"name": "请求参数json格式", "source": "88bbcbc5-dff7-4ad0-b559-bfdcd7447806-attachment.json", "type": "application/json"}, {"name": "请求参数实际入参", "source": "218ea0e2-7583-4e20-90eb-31b693135126-attachment.json", "type": "application/json"}, {"name": "接口实际响应信息", "source": "2d5dde57-1b9a-46eb-84dd-17ca11aad8bc-attachment.json", "type": "application/json"}, {"name": "状态码断言结果：成功", "source": "4a739dcb-813a-478f-8f64-f13ab486e6d9-attachment.txt", "type": "text/plain"}, {"name": "相等断言结果：成功", "source": "24b49f31-98f8-47ab-a60f-2808dfc95f1c-attachment.json", "type": "application/json"}, {"name": "📋 测试执行日志", "source": "55e23f44-4ee4-4eb7-ab06-d6cc67631f1d-attachment.txt", "type": "text/plain"}], "start": 1751853910547, "stop": 1751853912006, "uuid": "4cab79af-0b28-4c81-b894-f95e5dac8406", "historyId": "097386a9d43be40d3c28c1874a6e169b", "testCaseId": "e9970ce8394fe9b627f33514f0afecf5", "fullName": "testcase.play.test_play.TestPlay#test_play", "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "39132-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}]}