{"name": "项目下场次票品列表", "status": "passed", "attachments": [{"name": "接口地址", "source": "6b8054de-4358-4e37-8584-b9adea8e1b0b-attachment.json", "type": "application/json"}, {"name": "接口名称", "source": "1f497b25-e33d-4a8c-afc0-fb3d1f33eb01-attachment.json", "type": "application/json"}, {"name": "请求方式", "source": "32f6e765-f266-4daf-937a-8f09c5d07bd2-attachment.json", "type": "application/json"}, {"name": "请求头", "source": "c0ffc94e-eed1-4ec7-a66d-7c5a777cafc6-attachment.json", "type": "application/json"}, {"name": "<PERSON><PERSON>", "source": "4f24fa6c-7f9a-4ab7-ac31-2d9d976d1ab2-attachment.json", "type": "application/json"}, {"name": "测试用例名称", "source": "52796a84-3bd8-4797-b2f2-211d3f85dba9-attachment.json", "type": "application/json"}, {"name": "参数类型", "source": "7d80dec3-1c8b-4114-8325-d0ef281f44e8-attachment.json", "type": "application/json"}, {"name": "请求参数json格式", "source": "be930173-092b-4e88-b352-c0ab1772b407-attachment.json", "type": "application/json"}, {"name": "请求参数实际入参", "source": "7c2e6af5-d901-490d-b959-d93813801d66-attachment.json", "type": "application/json"}, {"name": "接口实际响应信息", "source": "5e188cb2-2b7a-436b-9e11-965ca388ad30-attachment.json", "type": "application/json"}, {"name": "状态码断言结果：成功", "source": "2ceaed0f-0672-4d63-b526-69f4a4c393da-attachment.txt", "type": "text/plain"}, {"name": "相等断言结果：成功", "source": "57c15532-66a9-485b-ad8f-105c3304c1b4-attachment.json", "type": "application/json"}, {"name": "📋 测试执行日志", "source": "4052b4a2-8728-4c64-a57c-e0269b3e9bd9-attachment.txt", "type": "text/plain"}], "start": 1751866433661, "stop": 1751866434254, "uuid": "19ac5a4e-6de8-442b-9380-b780b3b5638d", "historyId": "3287c72f85d7169cf4925a7d71668b28", "testCaseId": "e9970ce8394fe9b627f33514f0afecf5", "fullName": "testcase.play.test_play.TestPlay#test_play", "labels": [{"name": "story", "value": "C01_演出模块流程"}, {"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "16848-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}]}