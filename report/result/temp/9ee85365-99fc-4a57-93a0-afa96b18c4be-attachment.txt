INFO - 2025-07-07 10:05:13,523 - conftest.py:7 -[conftest:print_info] - ---------------接口测试开始---------------
INFO - 2025-07-07 10:05:13,526 - sendrequests.py:57 -[sendrequests:execute_api_request] - 接口名称：查询影讯接口
INFO - 2025-07-07 10:05:13,526 - sendrequests.py:58 -[sendrequests:execute_api_request] - 请求地址：http://192.168.25.16:40/proxy/cnews/article/list
INFO - 2025-07-07 10:05:13,526 - web_service.py:517 -[web_service:execute_command_simple] - [batch_1751853880] 第220行:  INFO - 2025-07-07 10:05:13,526 - sendrequests.py:57 -[sendrequests:execute_api_request] - 接口名称：查询影讯接口
INFO - 2025-07-07 10:05:13,526 - sendrequests.py:60 -[sendrequests:execute_api_request] - 请求头：None
INFO - 2025-07-07 10:05:13,526 - sendrequests.py:61 -[sendrequests:execute_api_request] - 测试用例名：查询上架状态的影讯
INFO - 2025-07-07 10:05:13,526 - sendrequests.py:62 -[sendrequests:execute_api_request] - cookies值：None
INFO - 2025-07-07 10:05:13,526 - sendrequests.py:67 -[sendrequests:execute_api_request] - 参数类型：data
INFO - 2025-07-07 10:05:13,526 - sendrequests.py:69 -[sendrequests:execute_api_request] - 请求参数："pagination=%7B%22pageNumber%22%3A1%2C%22pageSize%22%3A50%2C%22queryParams%22%3A%7B%22title%22%3A%22%22%2C%22source%22%3A%22%22%2C%22tag_id%22%3A%22%22%2C%22status%22%3A%221%22%2C%22istop%22%3A%22%22%2C%22film_name%22%3A%22%22%2C%22startintime%22%3A%22%22%2C%22endinttime%22%3A%22%22%2C%22showtype%22%3A%22%22%2C%22startshowtime%22%3A%22%22%2C%22endshowttime%22%3A%22%22%2C%22min_program_video_status%22%3A%22%22%2C%22isplat%22%3A0%7D%7D"
INFO - 2025-07-07 10:05:13,698 - apiutils_business.py:179 -[apiutils_business:execute_test_cases] - 接口实际返回结果：{"code":0,"msg":"操作成功","data":{"totalCount":"111","records":[{"id":292,"title":"看一下资讯缩进1","author":"11","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":579,"url":"http:\/\/192.168.25.19:11280\/cnews\/upload\/base\/2023\/09\/18\/6507a64bd42b1\/o.png"},"showtime":"2023-09-18 09:21:00","uptime":"2025-06-27 14:42:31","intime":"2023-09-18 09:22:24","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":1,"ui_type":"文章","type":0,"min_program_video_status":0},{"id":291,"title":"看一下文章缩进","author":"测试","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":578,"url":"http:\/\/192.168.25.19:11280\/cnews\/upload\/base\/2023\/09\/18\/6507a5c183a72\/o.jpg"},"showtime":"2023-09-18 09:19:00","uptime":"2025-06-27 14:41:44","intime":"2023-09-18 09:20:11","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":2,"ui_type":"文章","type":0,"min_program_video_status":0},{"id":299,"title":"NS2","author":"百度","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":591,"url":"http:\/\/192.168.25.19:11280\/cnews\/upload\/base\/2025\/06\/19\/6853c4adeca12\/o.jpg"},"showtime":"2025-06-19 16:04:00","uptime":"2025-06-19 16:05:20","intime":"2025-06-19 16:05:20","status":1,"ui_status":"上架","film_code":"001X02212020","film_name":"图兰朵：魔咒缘起","isplat":0,"showtype":1,"ui_type":"文章","type":0,"min_program_video_status":0},{"id":277,"title":"测试通看播放按钮不显示","author":"测试12121","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":588,"url":"http:\/\/192.168.25.19:11280\/cnews\/upload\/base\/2025\/04\/11\/67f8e2be2112b\/o.jpg"},"showtime":"2022-07-29 09:45:00","uptime":"2025-04-11 17:47:32","intime":"2022-07-29 09:47:49","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":2,"ui_type":"小视频","type":1,"min_program_video_status":0},{"id":276,"title":"文章类","author":"luowt","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":589,"url":"http:\/\/192.168.25.19:11280\/cnews\/upload\/base\/2025\/04\/11\/67f8e4a211f3d\/o.jpg"},"showtime":"2022-07-29 09:45:00","uptime":"2025-04-11 17:45:06","intime":"2022-07-29 09:46:15","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":1,"ui_type":"文章","type":0,"min_program_video_status":0},{"id":295,"title":"测试云点播上传","author":"liean","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":583,"url":"http:\/\/192.168.25.19:11280\/cnews\/upload\/base\/2024\/04\/24\/66286143f28be\/o.jpg"},"showtime":"2024-04-24 09:30:00","uptime":"2024-10-22 10:58:25","intime":"2024-04-24 09:33:14","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":1,"ui_type":"小视频","type":1,"min_program_video_status":1},{"id":248,"title":"华纳发布新片片花混剪","author":"中国电影通1","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":525,"url":"http:\/\/192.168.25.19:11280\/cnews\/upload\/base\/2022\/07\/18\/62d5061ab8754\/o.png"},"showtime":"2021-01-28 10:17:00","uptime":"2024-01-25 14:43:59","intime":"2021-01-28 10:19:58","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":1,"ui_type":"小视频","type":1,"min_program_video_status":0},{"id":294,"title":"智桐反馈资讯问题","author":"测试","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":582,"url":"http:\/\/192.168.25.19:11280\/cnews\/upload\/base\/2023\/09\/18\/6507a7cb1e6fb\/o.gif"},"showtime":"2023-09-18 09:27:00","uptime":"2023-11-24 15:39:09","intime":"2023-09-18 09:28:44","status":1,"ui_status":"上架","film_code":"001X04052021","film_name":"金手指","isplat":0,"showtype":2,"ui_type":"文章","type":0,"min_program_video_status":0},{"id":293,"title":"智桐周末反馈问题1","author":"测试","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":580,"url":"http:\/\/192.168.25.19:11280\/cnews\/upload\/base\/2023\/09\/18\/6507a6ebdec5a\/o.png"},"showtime":"2023-09-18 09:24:00","uptime":"2023-09-18 09:27:40","intime":"2023-09-18 09:25:08","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":2,"ui_type":"文章","type":0,"min_program_video_status":0},{"id":260,"title":"type=2","author":"dd","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":540,"url":"http:\/\/192.168.25.19:11280\/cnews\/upload\/base\/2022\/07\/18\/62d530a7801eb\/o.png"},"showtime":"2021-04-25 10:58:00","uptime":"2023-09-18 09:18:32","intime":"2021-04-25 10:52:29","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":2,"ui_type":"文章","type":0,"min_program_video_status":0},{"id":287,"title":"写潘多拉传奇！","author":"中影电影通","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":574,"url":"http:\/\/192.168.25.19:11280\/cnews\/upload\/base\/2022\/11\/25\/638066dde28eb\/o.png"},"showtime":"2022-11-25 14:54:00","uptime":"2022-11-30 11:23:04","intime":"2022-11-25 14:55:28","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":2,"ui_type":"小视频","type":1,"min_program_video_status":1},{"id":288,"title":"主题曲《无名英雄》MV，由周笔畅深情献声致敬生活中默默无闻的平凡人。凡人微光，星火成炬！","author":"中国电影","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":575,"url":"http:\/\/192.168.25.19:11280\/cnews\/upload\/base\/2022\/11\/25\/638067df3606f\/o.jpeg"},"showtime":"2022-11-25 14:59:00","uptime":"2022-11-25 14:59:46","intime":"2022-11-25 14:59:46","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":2,"ui_type":"小视频","type":1,"min_program_video_status":1},{"id":285,"title":"七十一岁的老鱼塘淤泥太深 不过今天挖机没陷车 确把公鸡陷得很深","author":"@棒哥带你开挖机确把公鸡陷得很","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":572,"url":"http:\/\/192.168.25.19:11280\/cnews\/upload\/base\/2022\/11\/25\/6380634e3f558\/o.png"},"showtime":"2022-11-25 14:39:00","uptime":"2022-11-25 14:41:54","intime":"2022-11-25 14:40:20","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":2,"ui_type":"小视频","type":1,"min_program_video_status":0},{"id":284,"title":"你可以怀疑","author":"@one_live","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":571,"url":"http:\/\/192.168.25.19:11280\/cnews\/upload\/base\/2022\/11\/25\/63805fe81244f\/o.png"},"showtime":"2022-11-25 14:24:00","uptime":"2022-11-25 14:26:42","intime":"2022-11-25 14:25:59","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":2,"ui_type":"小视频","type":1,"min_program_video_status":0},{"id":283,"title":"高清重温：邓卓翔世界波，国足破32年恐韩症！","author":"@尚足球","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":570,"url":"http:\/\/192.168.25.19:11280\/cnews\/upload\/base\/2022\/11\/25\/6380583ac513b\/o.png"},"showtime":"2022-11-25 13:52:00","uptime":"2022-11-25 13:54:11","intime":"2022-11-25 13:53:03","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":2,"ui_type":"小视频","type":1,"min_program_video_status":0},{"id":282,"title":"李云龙成功伏击鬼子观摩团，全歼200名鬼子军官!","author":"@特务兔说剧","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":569,"url":"http:\/\/192.168.25.19:11280\/cnews\/upload\/base\/2022\/11\/25\/63803b694352f\/o.png"},"showtime":"2022-11-25 11:49:00","uptime":"2022-11-25 11:50:07","intime":"2022-11-25 11:50:07","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":2,"ui_type":"小视频","type":1,"min_program_video_status":0},{"id":281,"title":"跌宕起伏！C罗点射创纪录！葡萄牙3比2加纳取开门红","author":"@小鱼人足球","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":568,"url":"http:\/\/192.168.25.19:11280\/cnews\/upload\/base\/2022\/11\/25\/63803a7390a4b\/o.jpg"},"showtime":"2022-11-24 11:45:00","uptime":"2022-11-25 11:47:03","intime":"2022-11-25 11:47:03","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":2,"ui_type":"小视频","type":1,"min_program_video_status":0},{"id":280,"title":"test-logo","author":"seif","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":567,"url":"http:\/\/192.168.25.19:11280\/cnews\/upload\/base\/2022\/08\/16\/62fb3000c40df\/o.jpeg"},"showtime":"2022-08-16 13:46:00","uptime":"2022-08-16 13:50:34","intime":"2022-08-16 13:50:34","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":1,"ui_type":"小视频","type":1,"min_program_video_status":1},{"id":278,"title":"视频","author":"luowt","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":565,"url":"http:\/\/192.168.25.19:11280\/cnews\/upload\/base\/2022\/07\/29\/62e33ea6b2740\/o.jpg"},"showtime":"2022-07-29 09:57:00","uptime":"2022-08-16 13:45:59","intime":"2022-07-29 09:58:20","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":1,"ui_type":"小视频","type":1,"min_program_video_status":1},{"id":279,"title":"GIF","author":"luowt","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":566,"url":"http:\/\/192.168.25.19:11280\/cnews\/upload\/base\/2022\/07\/29\/62e33fca0b1a5\/o.gif"},"showtime":"2022-07-29 10:01:00","uptime":"2022-07-29 10:02:56","intime":"2022-07-29 10:02:56","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":1,"ui_type":"小视频","type":1,"min_program_video_status":0},{"id":247,"title":"测试数据","author":"测试数据","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":561,"url":"http:\/\/192.168.25.19:11280\/cnews\/upload\/base\/2022\/07\/28\/62e2077f83eba\/o.png"},"showtime":"2021-01-28 09:56:00","uptime":"2022-07-28 11:50:24","intime":"2021-01-28 09:56:50","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":1,"ui_type":"小视频","type":1,"min_program_video_status":0},{"id":245,"title":"压缩测试","author":"aa","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":560,"url":"http:\/\/192.168.25.19:11280\/cnews\/upload\/base\/2022\/07\/28\/62e2075cabcb2\/o.png"},"showtime":"2021-01-27 18:42:00","uptime":"2022-07-28 11:49:49","intime":"2021-01-27 18:44:05","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":2,"ui_type":"小视频","type":1,"min_program_video_status":0},{"id":271,"title":"深圳晚霞","author":"luowt","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":559,"url":"http:\/\/192.168.25.19:11280\/cnews\/upload\/base\/2022\/07\/28\/62e206e09f4d3\/o.png"},"showtime":"2022-07-15 15:14:00","uptime":"2022-07-28 11:47:45","intime":"2022-07-15 15:18:27","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":1,"ui_type":"小视频","type":1,"min_program_video_status":0},{"id":261,"title":"辣妹又辣了辣妹又辣了辣妹又辣了辣妹又辣了辣妹又辣了辣妹又辣了辣妹又辣了辣妹又辣了辣妹又辣了","author":"luowt","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":548,"url":"http:\/\/192.168.25.19:11280\/cnews\/upload\/base\/2022\/07\/20\/62d79aeca8720\/o.jpg"},"showtime":"2021-07-07 17:01:00","uptime":"2022-07-22 09:54:53","intime":"2021-07-07 17:02:32","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":3,"ui_type":"小视频","type":1,"min_program_video_status":0},{"id":235,"title":"《疯狂原始人2》发布中文配音版预告","author":"中影电影通","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":539,"url":"http:\/\/192.168.25.19:11280\/cnews\/upload\/base\/2022\/07\/18\/62d51fe1c5c6d\/o.png"},"showtime":"2020-11-13 13:50:00","uptime":"2022-07-18 18:07:31","intime":"2020-11-13 13:55:06","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":1,"ui_type":"文章","type":0,"min_program_video_status":0},{"id":236,"title":"《疯狂原始人2》发布中文配音版预告","author":"中影电影通","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":537,"url":"http:\/\/192.168.25.19:11280\/cnews\/upload\/base\/2022\/07\/18\/62d506b230614\/o.png"},"showtime":"2020-11-13 13:55:00","uptime":"2022-07-18 15:07:31","intime":"2020-11-13 13:56:15","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":1,"ui_type":"小视频","type":1,"min_program_video_status":0},{"id":237,"title":"《古董局中局》曝光先导预告","author":"中影电影通","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":536,"url":"http:\/\/192.168.25.19:11280\/cnews\/upload\/base\/2022\/07\/18\/62d5068c1bd56\/o.png"},"showtime":"2020-11-23 13:36:00","uptime":"2022-07-18 15:06:57","intime":"2020-11-23 13:39:28","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":1,"ui_type":"小视频","type":1,"min_program_video_status":0},{"id":238,"title":"《外太空的莫扎特》曝光概念海报","author":"中影电影通","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":535,"url":"http:\/\/192.168.25.19:11280\/cnews\/upload\/base\/2022\/07\/18\/62d5068212d25\/o.png"},"showtime":"2020-11-23 13:39:00","uptime":"2022-07-18 15:06:42","intime":"2020-11-23 13:40:50","status":1,"ui_status":"上架","film_code":"001X03752019","film_name":"征途","isplat":0,"showtype":2,"ui_type":"小视频","type":1,"min_program_video_status":0},{"id":239,"title":"范娟测试1123-1","author":"范娟","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":534,"url":"http:\/\/192.168.25.19:11280\/cnews\/upload\/base\/2022\/07\/18\/62d5067691601\/o.png"},"showtime":"2020-11-23 15:46:00","uptime":"2022-07-18 15:06:31","intime":"2020-11-23 15:44:03","status":1,"ui_status":"上架","film_code":"001X02042019","film_name":"悟空奇遇记","isplat":0,"showtype":1,"ui_type":"小视频","type":1,"min_program_video_status":0},{"id":240,"title":"范娟测试1126-1","author":"范娟","tag_title":"test","tag_id":17,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":533,"url":"http:\/\/192.168.25.19:11280\/cnews\/upload\/base\/2022\/07\/18\/62d506669b96f\/o.png"},"showtime":"2020-11-26 11:44:00","uptime":"2022-07-18 15:06:15","intime":"2020-11-26 11:44:50","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":2,"ui_type":"小视频","type":1,"min_program_video_status":0},{"id":242,"title":"fj-12.10短视频","author":"范娟","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":532,"url":"http:\/\/192.168.25.19:11280\/cnews\/upload\/base\/2022\/07\/18\/62d50657a543c\/o.png"},"showtime":"2020-12-10 14:30:00","uptime":"2022-07-18 15:06:00","intime":"2020-12-10 14:27:56","status":1,"ui_status":"上架","film_code":"001X05262020","film_name":"送你一朵小红花","isplat":0,"showtype":1,"ui_type":"小视频","type":1,"min_program_video_status":0},{"id":243,"title":"xxxxx","author":"xxxx","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":531,"url":"http:\/\/192.168.25.19:11280\/cnews\/upload\/base\/2022\/07\/18\/62d5064cdc5b5\/o.png"},"showtime":"2021-01-11 15:51:00","uptime":"2022-07-18 15:05:49","intime":"2021-01-11 15:52:05","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":2,"ui_type":"小视频","type":1,"min_program_video_status":0},{"id":246,"title":"360分辨率","author":"11","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":527,"url":"http:\/\/192.168.25.19:11280\/cnews\/upload\/base\/2022\/07\/18\/62d5063129a25\/o.png"},"showtime":"2021-01-27 18:51:00","uptime":"2022-07-18 15:05:21","intime":"2021-01-27 18:52:56","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":2,"ui_type":"小视频","type":1,"min_program_video_status":0},{"id":274,"title":"上班那点小事type=1","author":"zkf","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":524,"url":"http:\/\/192.168.25.19:11280\/cnews\/upload\/base\/2022\/07\/18\/62d5011d5278d\/o.png"},"showtime":"2022-07-18 14:42:00","uptime":"2022-07-18 14:44:55","intime":"2022-07-18 14:44:55","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":1,"ui_type":"小视频","type":1,"min_program_video_status":0},{"id":273,"title":"type=1","author":"中国电影","tag_title":"幕后","tag_id":4,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":523,"url":"http:\/\/192.168.25.19:11280\/cnews\/upload\/base\/2022\/07\/18\/62d4ff5d004fe\/o.jpg"},"showtime":"2022-07-18 14:35:00","uptime":"2022-07-18 14:36:17","intime":"2022-07-18 14:36:17","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":1,"ui_type":"文章","type":0,"min_program_video_status":0},{"id":258,"title":"showtype=3","author":"范娟","tag_title":"幕后","tag_id":4,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":518,"url":"http:\/\/192.168.25.19:11280\/cnews\/upload\/base\/2022\/07\/18\/62d4fc939b274\/o.jpg"},"showtime":"2021-04-23 12:05:00","uptime":"2022-07-18 14:34:19","intime":"2021-04-23 11:59:55","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":3,"ui_type":"文章","type":0,"min_program_video_status":0},{"id":263,"title":"张同学幕后团队","author":"张同学","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":507,"url":"http:\/\/192.168.25.19:11280\/cnews\/upload\/base\/2022\/07\/15\/62d103f1bcf47\/o.jpeg"},"showtime":"2021-11-30 14:48:00","uptime":"2022-07-15 14:06:46","intime":"2021-11-30 14:50:06","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":1,"ui_type":"文章","type":0,"min_program_video_status":0},{"id":270,"title":"测试原图1.2M-liugx","author":"刘庚鑫","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":503,"url":"http:\/\/192.168.25.19:11280\/cnews\/upload\/base\/2022\/07\/05\/62c400711f272\/o.jpeg"},"showtime":"2022-07-05 16:38:00","uptime":"2022-07-05 17:12:18","intime":"2022-07-05 17:02:07","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":1,"ui_type":"文章","type":0,"min_program_video_status":0},{"id":269,"title":"lance小视频","author":"lance","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":496,"url":"http:\/\/192.168.25.19:11280\/cnews\/upload\/base\/2022\/01\/10\/61dbab2e749c3\/o.gif"},"showtime":"2022-01-10 11:41:00","uptime":"2022-01-10 11:46:06","intime":"2022-01-10 11:43:37","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":1,"ui_type":"小视频","type":1,"min_program_video_status":0},{"id":232,"title":"范娟测试","author":"范娟","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":390,"url":"http:\/\/192.168.25.19:11280\/cnews\/upload\/base\/2020\/10\/29\/5f9a368e09299\/o.png"},"showtime":"2020-10-29 11:29:00","uptime":"2020-11-13 10:29:05","intime":"2020-10-29 11:27:12","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":2,"ui_type":"文章","type":0,"min_program_video_status":0},{"id":223,"title":"建行H5","author":"范德萨","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":389,"url":"http:\/\/192.168.25.19:11280\/cnews\/upload\/base\/2020\/10\/29\/5f9a3611f093e\/o.png"},"showtime":"2020-10-15 10:08:00","uptime":"2020-10-29 11:25:19","intime":"2020-10-15 10:10:38","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":1,"ui_type":"文章","type":0,"min_program_video_status":0},{"id":224,"title":"测试视频10.22","author":"测试","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":378,"url":"http:\/\/192.168.25.19:11280\/cnews\/upload\/base\/2020\/10\/22\/5f913f651a4f5\/o.png"},"showtime":"2020-10-22 11:07:00","uptime":"2020-10-23 13:50:31","intime":"2020-10-22 11:10:43","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":1,"ui_type":"文章","type":0,"min_program_video_status":0},{"id":222,"title":"范娟-短视频","author":"范娟","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":376,"url":"http:\/\/192.168.25.19:11280\/cnews\/upload\/base\/2020\/10\/15\/5f8813933246e\/o.png"},"showtime":"2020-10-14 09:37:00","uptime":"2020-10-16 11:13:17","intime":"2020-10-15 09:37:07","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":1,"ui_type":"文章","type":0,"min_program_video_status":0},{"id":221,"title":"范娟-纯文字","author":"范娟","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":364,"url":"http:\/\/192.168.25.19:11280\/cnews\/upload\/base\/2020\/10\/15\/5f87a68067430\/o.png"},"showtime":"2020-10-14 09:33:00","uptime":"2020-10-15 09:31:52","intime":"2020-10-15 09:31:52","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":1,"ui_type":"文章","type":0,"min_program_video_status":0},{"id":296,"title":"测试测试","author":"呵呵","tag_title":"推荐","tag_id":3,"istop":0,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":584,"url":"http:\/\/192.168.25.19:11280\/cnews\/upload\/base\/2024\/10\/17\/671072cb02393\/o.jpg"},"showtime":"2024-10-17 10:10:00","uptime":"2024-10-17 10:14:06","intime":"2024-10-17 10:14:06","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":2,"ui_type":"小视频","type":1,"min_program_video_status":1},{"id":289,"title":"这是视频标题","author":"丹丹","tag_title":"推荐","tag_id":3,"istop":0,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":576,"url":"http:\/\/192.168.25.19:11280\/cnews\/upload\/base\/2022\/11\/29\/63857ecde065c\/o.png"},"showtime":"2022-11-29 11:34:00","uptime":"2022-11-29 11:39:27","intime":"2022-11-29 11:39:27","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":1,"ui_type":"小视频","type":1,"min_program_video_status":1},{"id":275,"title":"测试播放按钮是否显示","author":"测试12121","tag_title":"推荐","tag_id":3,"istop":0,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":562,"url":"http:\/\/192.168.25.19:11280\/cnews\/upload\/base\/2022\/07\/29\/62e33bc2d2560\/o.jpg"},"showtime":"2022-07-29 09:44:00","uptime":"2022-07-29 09:45:47","intime":"2022-07-29 09:45:47","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":2,"ui_type":"文章","type":0,"min_program_video_status":0},{"id":272,"title":"showtype=1","author":"中国电影","tag_title":"推荐","tag_id":3,"istop":0,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":521,"url":"http:\/\/192.168.25.19:11280\/cnews\/upload\/base\/2022\/07\/18\/62d4fe866fe15\/o.jpg"},"showtime":"2022-07-18 14:27:00","uptime":"2022-07-18 14:32:47","intime":"2022-07-18 14:32:47","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":1,"ui_type":"文章","type":0,"min_program_video_status":0},{"id":267,"title":"测试GIF-0105","author":"测试GIF-0105","tag_title":"推荐","tag_id":3,"istop":0,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":472,"url":"http:\/\/192.168.25.19:11280\/cnews\/upload\/base\/2022\/01\/05\/61d536d46cc0f\/o.gif"},"showtime":"2022-01-05 14:11:00","uptime":"2022-01-07 10:25:45","intime":"2022-01-05 14:12:37","status":1,"ui_status":"上架","film_code":"001X05442020","film_name":"村里来了个洋媳妇","isplat":0,"showtype":2,"ui_type":"文章","type":0,"min_program_video_status":0},{"id":266,"title":"tea.0102","author":"tea.0102","tag_title":"推荐","tag_id":3,"istop":0,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":464,"url":"http:\/\/192.168.25.19:11280\/cnews\/upload\/base\/2022\/01\/04\/61d3a5c58bac3\/o.gif"},"showtime":"2022-01-04 09:40:00","uptime":"2022-01-04 09:42:01","intime":"2022-01-04 09:42:01","status":1,"ui_status":"上架","film_code":"001X04142021","film_name":"农民院士","isplat":0,"showtype":2,"ui_type":"小视频","type":1,"min_program_video_status":0}]}}
INFO - 2025-07-07 10:05:13,702 - assertion_utils.py:35 -[assertion_utils:status_code_assert] - 状态码断言成功：接口实际返回状态码 200 == 200
INFO - 2025-07-07 10:05:13,705 - assertion_utils.py:94 -[assertion_utils:equal_assert] - 相等断言成功：接口实际结果 {'code': 0} == 预期结果：{'code': 0}
INFO - 2025-07-07 10:05:13,705 - web_service.py:517 -[web_service:execute_command_simple] - [batch_1751853880] 第230行:  INFO - 2025-07-07 10:05:13,705 - assertion_utils.py:94 -[assertion_utils:equal_assert] - 相等断言成功：接口实际结果 {'code': 0} == 预期结果：{'code': 0}
INFO - 2025-07-07 10:05:13,708 - assertion_utils.py:94 -[assertion_utils:equal_assert] - 相等断言成功：接口实际结果 {'msg': '操作成功'} == 预期结果：{'msg': '操作成功'}
INFO - 2025-07-07 10:05:13,709 - assertion_utils.py:206 -[assertion_utils:assert_result] - 测试成功
INFO - 2025-07-07 10:05:13,725 - conftest.py:9 -[conftest:print_info] - ---------------接口测试结束---------------
INFO - 2025-07-07 10:05:13,526 - sendrequests.py:59 -[sendrequests:execute_api_request] - 请求方式：POST