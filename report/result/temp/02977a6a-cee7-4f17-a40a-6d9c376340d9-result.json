{"name": "根据场次获取取票方式", "status": "passed", "attachments": [{"name": "接口地址", "source": "3d6508e8-0429-43fb-937a-641a1ea360c8-attachment.json", "type": "application/json"}, {"name": "接口名称", "source": "67a9a1fe-b878-4ecd-8b83-22d763cdd032-attachment.json", "type": "application/json"}, {"name": "请求方式", "source": "2c250917-83fc-45b0-b20e-2b520639cf2c-attachment.json", "type": "application/json"}, {"name": "请求头", "source": "c510d3dc-2a5c-4254-bc62-19c0e8e22616-attachment.json", "type": "application/json"}, {"name": "<PERSON><PERSON>", "source": "d33f099c-106d-4465-9ac5-75efc555b84b-attachment.json", "type": "application/json"}, {"name": "测试用例名称", "source": "c0542053-1f41-47c8-b493-32340507ba4f-attachment.json", "type": "application/json"}, {"name": "参数类型", "source": "f3b6945b-f713-4592-8d57-a21a96ec5f2c-attachment.json", "type": "application/json"}, {"name": "请求参数json格式", "source": "c34b0181-e42d-42c3-b4fb-fb1ca264bc79-attachment.json", "type": "application/json"}, {"name": "请求参数实际入参", "source": "4f20f02f-b07f-4e1c-9717-7e87447363e3-attachment.json", "type": "application/json"}, {"name": "接口实际响应信息", "source": "12aafd00-3597-4c7d-a8c3-e2d8ce496618-attachment.json", "type": "application/json"}, {"name": "状态码断言结果：成功", "source": "a79f4dcb-9f54-427e-bb15-2349c645e8db-attachment.txt", "type": "text/plain"}, {"name": "相等断言结果：成功", "source": "88fd5382-5605-413d-961a-3b3792527395-attachment.json", "type": "application/json"}, {"name": "📋 测试执行日志", "source": "aff9d201-1c82-4b60-8092-a9b879070d1f-attachment.txt", "type": "text/plain"}], "start": 1751866435788, "stop": 1751866436826, "uuid": "aaa72cb7-adc6-4bd6-9115-29381218f96b", "historyId": "40f667e7e7d9255278cb56eb99290821", "testCaseId": "e9970ce8394fe9b627f33514f0afecf5", "fullName": "testcase.play.test_play.TestPlay#test_play", "labels": [{"name": "story", "value": "C01_演出模块流程"}, {"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "16848-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}]}