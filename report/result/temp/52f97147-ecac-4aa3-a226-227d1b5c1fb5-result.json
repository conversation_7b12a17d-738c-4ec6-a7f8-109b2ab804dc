{"name": "下单", "status": "passed", "attachments": [{"name": "接口地址", "source": "dc644610-2c8c-4642-8615-5de4bb344101-attachment.json", "type": "application/json"}, {"name": "接口名称", "source": "6f5b877e-fea0-407e-b39f-1b674da5740f-attachment.json", "type": "application/json"}, {"name": "请求方式", "source": "2f21b3d0-657c-4278-9234-f195d9566f41-attachment.json", "type": "application/json"}, {"name": "请求头", "source": "61e90ba8-a64d-49d9-8ac6-56207f6b62f7-attachment.json", "type": "application/json"}, {"name": "<PERSON><PERSON>", "source": "a7a354ab-00fe-4a07-94dc-3720ca186a8e-attachment.json", "type": "application/json"}, {"name": "测试用例名称", "source": "2d129e34-dfc5-45a5-b78b-977819358bf6-attachment.json", "type": "application/json"}, {"name": "参数类型", "source": "40dbf056-b9b3-4dd9-a836-f9211c7efd97-attachment.json", "type": "application/json"}, {"name": "请求参数json格式", "source": "1f3ba684-5e40-4197-850f-0a1f61bd62a3-attachment.json", "type": "application/json"}, {"name": "请求参数实际入参", "source": "4d0de8ec-edf9-45fd-9cc2-c6e6ac352e59-attachment.json", "type": "application/json"}, {"name": "接口实际响应信息", "source": "78fc2cee-0981-490a-bb62-1fdbe7790621-attachment.json", "type": "application/json"}, {"name": "状态码断言结果：成功", "source": "2b413788-b651-4073-a04d-7f5707021a38-attachment.txt", "type": "text/plain"}, {"name": "相等断言结果：成功", "source": "5e959695-5bbf-4339-82d6-87271129e6bd-attachment.json", "type": "application/json"}, {"name": "📋 测试执行日志", "source": "5911a909-5af1-43da-8996-94fbc20b77aa-attachment.txt", "type": "text/plain"}], "start": 1751853251105, "stop": 1751853252391, "uuid": "a054371a-deb6-4c9a-bf93-6d37fcc8d8fb", "historyId": "df78d83b2c7879c3a2ecdde06190a07c", "testCaseId": "e9970ce8394fe9b627f33514f0afecf5", "fullName": "testcase.play.test_play.TestPlay#test_play", "labels": [{"name": "story", "value": "C01_演出模块流程"}, {"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "28812-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}]}