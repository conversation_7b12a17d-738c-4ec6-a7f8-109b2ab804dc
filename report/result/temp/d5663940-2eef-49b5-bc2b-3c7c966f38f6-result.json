{"name": "演出场次列表", "status": "passed", "attachments": [{"name": "接口地址", "source": "9cd539dc-4c11-4893-b1db-7f73a66942d1-attachment.json", "type": "application/json"}, {"name": "接口名称", "source": "81f7c260-2842-4eda-ae6f-fa152ffbb478-attachment.json", "type": "application/json"}, {"name": "请求方式", "source": "fde09fea-4eb4-4ec3-bedc-629dc38bc1ce-attachment.json", "type": "application/json"}, {"name": "请求头", "source": "edc6259f-9afd-4a52-8a38-c14486c6651c-attachment.json", "type": "application/json"}, {"name": "<PERSON><PERSON>", "source": "64043c9b-cf15-49fc-bc09-21ead2096638-attachment.json", "type": "application/json"}, {"name": "测试用例名称", "source": "95c8b424-f3cc-459b-b343-9bcb2333cd26-attachment.json", "type": "application/json"}, {"name": "参数类型", "source": "38ee08cc-c03a-46ec-9cb3-ad3049060e07-attachment.json", "type": "application/json"}, {"name": "请求参数json格式", "source": "75982f93-548f-402c-bea8-587913a1cdb4-attachment.json", "type": "application/json"}, {"name": "请求参数实际入参", "source": "db0b917b-0662-49bb-ba5e-d3f2673e8648-attachment.json", "type": "application/json"}, {"name": "接口实际响应信息", "source": "4c4b76c2-9964-454c-930d-9116cb6ca30f-attachment.json", "type": "application/json"}, {"name": "状态码断言结果：成功", "source": "202d34a8-0dd3-4de1-84fa-9625756d2f03-attachment.txt", "type": "text/plain"}, {"name": "相等断言结果：成功", "source": "a06d111e-8010-40f2-b32d-5f226eba43a7-attachment.json", "type": "application/json"}, {"name": "📋 测试执行日志", "source": "aa821952-2b76-4454-b6f3-3aca1a7b8ff1-attachment.txt", "type": "text/plain"}], "start": 1751851580962, "stop": 1751851581511, "uuid": "*************-449d-93ce-0d1916db7610", "historyId": "ce6724bce5907ddc1a7c9b8a022c9530", "testCaseId": "e9970ce8394fe9b627f33514f0afecf5", "fullName": "testcase.play.test_play.TestPlay#test_play", "labels": [{"name": "story", "value": "C01_演出模块流程"}, {"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "34968-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}]}