{"name": "根据场次获取取票方式", "status": "passed", "attachments": [{"name": "接口地址", "source": "05975a6a-ecfd-4714-9c05-72f1be00a1da-attachment.json", "type": "application/json"}, {"name": "接口名称", "source": "7f5ad37f-c066-415d-b953-f5969350a715-attachment.json", "type": "application/json"}, {"name": "请求方式", "source": "31c22c7a-9bdc-4c9f-9b3a-4b20bf46f75f-attachment.json", "type": "application/json"}, {"name": "请求头", "source": "eb68a242-6c93-495d-a79c-aeecfbefb360-attachment.json", "type": "application/json"}, {"name": "<PERSON><PERSON>", "source": "9693eb09-c761-4fd1-a2e2-c8370606f8eb-attachment.json", "type": "application/json"}, {"name": "测试用例名称", "source": "b04af8cc-fc66-4b5d-82bd-7f770dff2da9-attachment.json", "type": "application/json"}, {"name": "参数类型", "source": "8f91eff3-9255-405e-a926-69acac4bbfdb-attachment.json", "type": "application/json"}, {"name": "请求参数json格式", "source": "f86997d4-9e98-473a-8465-4f99ebcbd762-attachment.json", "type": "application/json"}, {"name": "请求参数实际入参", "source": "0ecfdc01-f337-48bd-85de-c1c972773b05-attachment.json", "type": "application/json"}, {"name": "接口实际响应信息", "source": "f69cd87c-2745-4d01-a822-b9a8ffb1573f-attachment.json", "type": "application/json"}, {"name": "状态码断言结果：成功", "source": "0e074bab-3504-477b-9323-c3b60dff2703-attachment.txt", "type": "text/plain"}, {"name": "相等断言结果：成功", "source": "e7170f54-f44d-43fa-943d-0bcd644143ec-attachment.json", "type": "application/json"}, {"name": "📋 测试执行日志", "source": "0c987781-b37e-4d15-bcb5-b4627a541790-attachment.txt", "type": "text/plain"}], "start": 1751852476394, "stop": 1751852476878, "uuid": "702e9d04-e7f3-4d83-9805-d7a241d0df58", "historyId": "40f667e7e7d9255278cb56eb99290821", "testCaseId": "e9970ce8394fe9b627f33514f0afecf5", "fullName": "testcase.play.test_play.TestPlay#test_play", "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "38092-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}]}