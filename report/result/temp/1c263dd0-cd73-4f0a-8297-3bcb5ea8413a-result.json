{"name": "近期特惠", "status": "passed", "attachments": [{"name": "接口地址", "source": "9fe558d0-426c-43da-a25d-a6ff44779acb-attachment.json", "type": "application/json"}, {"name": "接口名称", "source": "4a49f4fa-db5b-4247-b568-77fd2d874a52-attachment.json", "type": "application/json"}, {"name": "请求方式", "source": "d6f3b1aa-10c7-432f-bc38-aa5dea51a73a-attachment.json", "type": "application/json"}, {"name": "请求头", "source": "8a40b8c2-bc65-4e66-b53e-0cd017bc6807-attachment.json", "type": "application/json"}, {"name": "<PERSON><PERSON>", "source": "0b078a4d-b57d-4bad-8c25-243ae6e17407-attachment.json", "type": "application/json"}, {"name": "测试用例名称", "source": "9835b54d-5a0e-45b4-bc00-3cbbec685555-attachment.json", "type": "application/json"}, {"name": "参数类型", "source": "6ca1ec1e-a3c2-4015-bf31-7ff7e43d1322-attachment.json", "type": "application/json"}, {"name": "请求参数json格式", "source": "330ba3ed-2d39-4064-af0b-10712414c65c-attachment.json", "type": "application/json"}, {"name": "请求参数实际入参", "source": "068e446d-1a1b-4f41-af2a-c6bfa21de410-attachment.json", "type": "application/json"}, {"name": "接口实际响应信息", "source": "3d0738b4-1dcf-4f76-8af6-5ae2a8d6f71c-attachment.json", "type": "application/json"}, {"name": "状态码断言结果：成功", "source": "a73b590f-f95e-42c5-916a-f1ff08993668-attachment.txt", "type": "text/plain"}, {"name": "相等断言结果：成功", "source": "798f8662-00bb-4944-b941-16eef20e3ca8-attachment.json", "type": "application/json"}, {"name": "📋 测试执行日志", "source": "44f45fd5-380d-485c-9a8f-478e6c3f5c6e-attachment.txt", "type": "text/plain"}], "start": 1751853233896, "stop": 1751853234106, "uuid": "ca900f08-7f36-465b-b6c9-d197677c59dc", "historyId": "b9dc0ab2757f5b153151bfbd4cc293dd", "testCaseId": "e9970ce8394fe9b627f33514f0afecf5", "fullName": "testcase.play.test_play.TestPlay#test_play", "labels": [{"name": "story", "value": "C01_演出模块流程"}, {"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "28812-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}]}