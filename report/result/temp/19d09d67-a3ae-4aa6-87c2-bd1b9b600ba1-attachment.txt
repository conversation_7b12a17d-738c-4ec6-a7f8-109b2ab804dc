INFO - 2025-07-07 10:02:28,389 - conftest.py:7 -[conftest:print_info] - ---------------接口测试开始---------------
INFO - 2025-07-07 10:02:28,408 - sendrequests.py:57 -[sendrequests:execute_api_request] - 接口名称：根据场次获取取票方式
INFO - 2025-07-07 10:02:28,408 - sendrequests.py:58 -[sendrequests:execute_api_request] - 请求地址：http://*************/api/show/info/show_fetch_ticket_ways
INFO - 2025-07-07 10:02:28,408 - sendrequests.py:59 -[sendrequests:execute_api_request] - 请求方式：GET
INFO - 2025-07-07 10:02:28,408 - sendrequests.py:60 -[sendrequests:execute_api_request] - 请求头：{'Content-Type': 'application/x-www-form-urlencoded', 'Connection': 'Keep-Alive', 'Accept-Encoding': 'gzip'}
INFO - 2025-07-07 10:02:28,408 - sendrequests.py:61 -[sendrequests:execute_api_request] - 测试用例名：获取该场次的取票方式
INFO - 2025-07-07 10:02:28,408 - sendrequests.py:62 -[sendrequests:execute_api_request] - cookies值：None
INFO - 2025-07-07 10:02:28,408 - sendrequests.py:67 -[sendrequests:execute_api_request] - 参数类型：params
INFO - 2025-07-07 10:02:28,408 - sendrequests.py:69 -[sendrequests:execute_api_request] - 请求参数：{"showId": "48", "channelCode": "*********", "token": "QLDa/9NjHgSYw1auSFBuL/8XzYY3TPXiWMx5EJW93pVrpQHb126Gk1kfi8cUb4IDLgkc0PtjUbyjV8veIqh7zBJvFCFmKV75avd5APTgvwTgAfR1w+Js7/54J0X48Xqh,*********"}
INFO - 2025-07-07 10:02:28,857 - apiutils_business.py:179 -[apiutils_business:execute_test_cases] - 接口实际返回结果：{"code":0,"msg":"","data":{"fetchTicketWays":[{"fetchTicketWayId":4386733,"showId":"48","fetchType":5,"needIdCard":false,"tips":"","postage":"0","onTime":"2025-03-13 14:21:40","offTime":"2025-12-01 00:00:00","fetchAddress":null,"contactMobile":null,"fetchTime":null,"feePayType":0,"feeType":0}]}}
INFO - 2025-07-07 10:02:28,860 - assertion_utils.py:35 -[assertion_utils:status_code_assert] - 状态码断言成功：接口实际返回状态码 200 == 200
INFO - 2025-07-07 10:02:28,861 - assertion_utils.py:94 -[assertion_utils:equal_assert] - 相等断言成功：接口实际结果 {'code': 0} == 预期结果：{'code': 0}
INFO - 2025-07-07 10:02:28,861 - assertion_utils.py:206 -[assertion_utils:assert_result] - 测试成功
INFO - 2025-07-07 10:02:28,880 - conftest.py:9 -[conftest:print_info] - ---------------接口测试结束---------------