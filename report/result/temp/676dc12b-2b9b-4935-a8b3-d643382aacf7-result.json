{"name": "演出场次列表", "status": "passed", "attachments": [{"name": "接口地址", "source": "aa5b09d0-04ed-47e0-93c4-77fd7148ab68-attachment.json", "type": "application/json"}, {"name": "接口名称", "source": "7b282440-0e5d-4a88-94a2-541572b19fa2-attachment.json", "type": "application/json"}, {"name": "请求方式", "source": "555d6d74-b850-4fc7-b15a-2718ea520339-attachment.json", "type": "application/json"}, {"name": "请求头", "source": "309433d2-b60f-4050-a3d6-24b4bc0685b4-attachment.json", "type": "application/json"}, {"name": "<PERSON><PERSON>", "source": "94b84a80-0e4c-404d-a4cb-9f24a5a253c9-attachment.json", "type": "application/json"}, {"name": "测试用例名称", "source": "77b712d5-9bc6-48a9-8cfe-7fbfe44816c2-attachment.json", "type": "application/json"}, {"name": "参数类型", "source": "ea9b6f75-2251-406c-bfa7-b9d314df09c8-attachment.json", "type": "application/json"}, {"name": "请求参数json格式", "source": "322a61fa-e624-4f2d-b3db-258214522a78-attachment.json", "type": "application/json"}, {"name": "请求参数实际入参", "source": "42c57219-570e-4302-b0d2-5ee135413830-attachment.json", "type": "application/json"}, {"name": "接口实际响应信息", "source": "5974633e-a72c-4152-88f7-73326bd161fc-attachment.json", "type": "application/json"}, {"name": "状态码断言结果：成功", "source": "73779fda-7051-4278-94fa-885230dd7917-attachment.txt", "type": "text/plain"}, {"name": "相等断言结果：成功", "source": "3d928a17-4a6c-4bf2-8e73-12d2dba550ea-attachment.json", "type": "application/json"}, {"name": "📋 测试执行日志", "source": "b0ff3d4a-d3ba-485f-962b-5bd04844d12c-attachment.txt", "type": "text/plain"}], "parameters": [{"name": "api_info", "value": "{'baseInfo': {'module': '演出票', 'api_name': '演出场次列表', 'host': '${get_host(play)}', 'url': '/api/show/info/performance_shows', 'method': 'get', 'headers': '${get_headers(app)}'}, 'testCase': {'case_name': '获取演出场次列表信息', 'validation': [{'code': 200}, {'eq': {'code': 0}}], 'extract': {'extract_auto': 'performance_shows'}, 'extract_list': np.float64(0.0), 'params': {'performanceId': '${get_extract_data(performance_id)}', 'channelCode': '${appKey()}', 'token': '${get_extract_data(token)}'}}}"}], "start": 1751853744202, "stop": 1751853744783, "uuid": "34cc0fc8-39e0-4110-b679-a29f8b9da12d", "historyId": "6b8675e1b81e5293918eed8f21bfedcf", "testCaseId": "e9970ce8394fe9b627f33514f0afecf5", "fullName": "testcase.play.test_play.TestPlay#test_play", "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "39072-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}]}