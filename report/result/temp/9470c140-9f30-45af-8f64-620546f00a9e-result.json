{"name": "为你推荐", "status": "passed", "attachments": [{"name": "接口地址", "source": "f893dcc2-26d8-474a-a839-0ee8199d2bf9-attachment.json", "type": "application/json"}, {"name": "接口名称", "source": "694e2687-dbfd-424f-990f-86a9ccd9d9c1-attachment.json", "type": "application/json"}, {"name": "请求方式", "source": "015a8580-f260-4571-b555-49ed33e52a03-attachment.json", "type": "application/json"}, {"name": "请求头", "source": "c9c6c22e-4661-4d39-a0d5-5efb17c82e56-attachment.json", "type": "application/json"}, {"name": "<PERSON><PERSON>", "source": "d8de91cd-69c3-4ecb-a5b1-c82b57f9fce8-attachment.json", "type": "application/json"}, {"name": "测试用例名称", "source": "37710a2f-4168-478a-a206-b71ee00920f5-attachment.json", "type": "application/json"}, {"name": "参数类型", "source": "f0e0a0b4-ebcb-4c09-b8b1-f6b51093679c-attachment.json", "type": "application/json"}, {"name": "请求参数json格式", "source": "7fe5e543-32b3-457f-a05b-ff556657ab38-attachment.json", "type": "application/json"}, {"name": "请求参数实际入参", "source": "70ac983c-8891-4d3b-a8be-6005c6052bc4-attachment.json", "type": "application/json"}, {"name": "接口实际响应信息", "source": "8b3fc8cf-9d34-4877-bea6-6b0b9edd2312-attachment.json", "type": "application/json"}, {"name": "状态码断言结果：成功", "source": "61a1498e-a147-4712-97b6-62e76e7dfbef-attachment.txt", "type": "text/plain"}, {"name": "相等断言结果：成功", "source": "3058af7e-e848-4632-ab9e-b01911655eaf-attachment.json", "type": "application/json"}, {"name": "📋 测试执行日志", "source": "f00c5643-a592-4f33-97c8-0398a64edc97-attachment.txt", "type": "text/plain"}], "start": 1751866422104, "stop": 1751866422426, "uuid": "abbaf0b4-e8da-4523-8d85-07c79bf0dd19", "historyId": "a03ba38a51bcc9af12332b9cb9ea6b41", "testCaseId": "e9970ce8394fe9b627f33514f0afecf5", "fullName": "testcase.play.test_play.TestPlay#test_play", "labels": [{"name": "story", "value": "C01_演出模块流程"}, {"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "16848-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}]}