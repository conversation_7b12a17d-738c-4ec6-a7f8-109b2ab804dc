{"name": "演唱会分类", "status": "passed", "attachments": [{"name": "接口地址", "source": "b208f3b8-e1c2-473d-9f4d-170ad8c6df3a-attachment.json", "type": "application/json"}, {"name": "接口名称", "source": "390748f7-aee1-4564-87ab-09e8daa60192-attachment.json", "type": "application/json"}, {"name": "请求方式", "source": "4cfcddbb-0132-428f-976a-a9c88254933f-attachment.json", "type": "application/json"}, {"name": "请求头", "source": "3461e481-ab2f-4553-be25-c71bb3bf1705-attachment.json", "type": "application/json"}, {"name": "<PERSON><PERSON>", "source": "47ec4631-7450-4dc9-b2f5-1ceb6f374329-attachment.json", "type": "application/json"}, {"name": "测试用例名称", "source": "9fd6350d-be2b-48c1-96d4-ed83229578c1-attachment.json", "type": "application/json"}, {"name": "参数类型", "source": "22877149-db16-49df-9151-1b8f8e988022-attachment.json", "type": "application/json"}, {"name": "请求参数json格式", "source": "42a40c1e-92f9-46fb-b14e-fcc0fdc78c1a-attachment.json", "type": "application/json"}, {"name": "请求参数实际入参", "source": "7359d8c3-c5db-4919-9854-d077027f2dca-attachment.json", "type": "application/json"}, {"name": "接口实际响应信息", "source": "77940162-b8da-4782-99e7-8982b7f971d2-attachment.json", "type": "application/json"}, {"name": "状态码断言结果：成功", "source": "94e58987-5153-4260-82e6-eb7e81e4f4a5-attachment.txt", "type": "text/plain"}, {"name": "相等断言结果：成功", "source": "bce849f2-44c0-453e-8abc-57c9b8403ecc-attachment.json", "type": "application/json"}, {"name": "📋 测试执行日志", "source": "8121eaf4-18ce-423f-a3e7-2f4cb245ec0f-attachment.txt", "type": "text/plain"}], "start": 1751866425803, "stop": 1751866426119, "uuid": "0c8d9582-6153-4d2c-a64f-ba5dec08ca26", "historyId": "47b52c96aa0c1d59c25f4bc63959c980", "testCaseId": "e9970ce8394fe9b627f33514f0afecf5", "fullName": "testcase.play.test_play.TestPlay#test_play", "labels": [{"name": "story", "value": "C01_演出模块流程"}, {"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "16848-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}]}