{"name": "演出项目详情", "status": "passed", "attachments": [{"name": "接口地址", "source": "92d78c02-e08d-447b-881d-131983b71be4-attachment.json", "type": "application/json"}, {"name": "接口名称", "source": "8a274e77-541e-417d-beb3-2d984c813e6d-attachment.json", "type": "application/json"}, {"name": "请求方式", "source": "187a82aa-c6e8-42b9-8d4e-1e15a9a91da1-attachment.json", "type": "application/json"}, {"name": "请求头", "source": "ee125f5f-5e4c-4586-a1d3-4f03756b6a95-attachment.json", "type": "application/json"}, {"name": "<PERSON><PERSON>", "source": "d58d28bc-d8c1-447b-b5d8-977901bd1969-attachment.json", "type": "application/json"}, {"name": "测试用例名称", "source": "17b5acc7-24a1-41e8-936f-60d6f19d2566-attachment.json", "type": "application/json"}, {"name": "参数类型", "source": "10e87eae-72d1-45fc-86df-6049fa4026a6-attachment.json", "type": "application/json"}, {"name": "请求参数json格式", "source": "9bf7e1b9-7e36-49ca-8995-be89959ba3b5-attachment.json", "type": "application/json"}, {"name": "请求参数实际入参", "source": "d6176414-7b34-46e9-865a-ebd7f8e28b2b-attachment.json", "type": "application/json"}, {"name": "接口实际响应信息", "source": "0f5119dc-3866-4afb-9cb0-d430860020d1-attachment.json", "type": "application/json"}, {"name": "状态码断言结果：成功", "source": "464acabb-5ffd-48e2-8de2-0c3caf556f4c-attachment.txt", "type": "text/plain"}, {"name": "相等断言结果：成功", "source": "0fdafb68-06ee-4ca4-979e-fbcee271729b-attachment.json", "type": "application/json"}, {"name": "📋 测试执行日志", "source": "4601dfa3-0e44-4994-a4d4-ae7a8e7a3400-attachment.txt", "type": "text/plain"}], "start": 1751852470200, "stop": 1751852470748, "uuid": "cdf379d4-fd2b-463a-9a7b-86df2ce32b5f", "historyId": "0457c216d6185e839b1ab99163c7bbc6", "testCaseId": "e9970ce8394fe9b627f33514f0afecf5", "fullName": "testcase.play.test_play.TestPlay#test_play", "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "38092-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}]}