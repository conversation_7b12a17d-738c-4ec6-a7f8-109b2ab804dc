{"name": "演出项目详情", "status": "passed", "attachments": [{"name": "接口地址", "source": "b91c6edf-05c1-4b37-98a9-9490dd66a22d-attachment.json", "type": "application/json"}, {"name": "接口名称", "source": "0038551d-ed39-48b5-9ecb-4ffc3c4fcede-attachment.json", "type": "application/json"}, {"name": "请求方式", "source": "8e7a354d-b686-413d-aa59-ad394613a63a-attachment.json", "type": "application/json"}, {"name": "请求头", "source": "d0822f89-ef6f-471b-af66-80d118f42c0e-attachment.json", "type": "application/json"}, {"name": "<PERSON><PERSON>", "source": "688e98f4-5e87-481d-9b45-655eecb08977-attachment.json", "type": "application/json"}, {"name": "测试用例名称", "source": "c201a64d-8e29-47ce-aa92-0742109cc18e-attachment.json", "type": "application/json"}, {"name": "参数类型", "source": "d54e2cd7-e457-4b4d-b115-267720939048-attachment.json", "type": "application/json"}, {"name": "请求参数json格式", "source": "25d27490-e181-405c-be46-d99bf83b5fb4-attachment.json", "type": "application/json"}, {"name": "请求参数实际入参", "source": "60882263-ebbb-4282-87ea-fd4495a462a1-attachment.json", "type": "application/json"}, {"name": "接口实际响应信息", "source": "ba14025b-71ec-47e2-b986-375938bcdf90-attachment.json", "type": "application/json"}, {"name": "状态码断言结果：成功", "source": "192aa1fd-5612-4ce4-96ae-9e123723c894-attachment.txt", "type": "text/plain"}, {"name": "相等断言结果：成功", "source": "f43f19c2-8d36-4c5b-92a9-e40b6c464650-attachment.json", "type": "application/json"}, {"name": "📋 测试执行日志", "source": "7a244074-27bb-44b3-8da2-9ed4f925e993-attachment.txt", "type": "text/plain"}], "start": 1751866429468, "stop": 1751866430073, "uuid": "52490f2c-b44f-4ebc-8dc5-c511a210ee2a", "historyId": "0457c216d6185e839b1ab99163c7bbc6", "testCaseId": "e9970ce8394fe9b627f33514f0afecf5", "fullName": "testcase.play.test_play.TestPlay#test_play", "labels": [{"name": "story", "value": "C01_演出模块流程"}, {"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "16848-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}]}