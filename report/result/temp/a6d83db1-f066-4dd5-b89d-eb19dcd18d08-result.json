{"name": "查询影讯接口", "status": "passed", "attachments": [{"name": "接口地址", "source": "817e9dad-36f7-44b9-91f6-80a8571b7bf6-attachment.json", "type": "application/json"}, {"name": "接口名称", "source": "556cd863-607e-4d10-bd01-8366935800ba-attachment.json", "type": "application/json"}, {"name": "请求方式", "source": "db8b0fc1-c4bf-4001-a913-955d93c1d1ed-attachment.json", "type": "application/json"}, {"name": "请求头", "source": "edb20053-cfdb-4c3a-8a41-0fdce9787456-attachment.json", "type": "application/json"}, {"name": "<PERSON><PERSON>", "source": "a5bcd393-d37a-4979-92eb-111cf6c6ceea-attachment.json", "type": "application/json"}, {"name": "测试用例名称", "source": "448a0701-c4d6-4503-b30b-8be2005f4250-attachment.json", "type": "application/json"}, {"name": "参数类型", "source": "350987a6-8a05-4319-830c-5546fd4f4987-attachment.json", "type": "application/json"}, {"name": "请求参数json格式", "source": "fec56aac-4653-4dde-a4ba-d9bf89531555-attachment.json", "type": "application/json"}, {"name": "请求参数实际入参", "source": "9fb35759-a3bf-41d1-8162-d3a53f695bff-attachment.json", "type": "application/json"}, {"name": "接口实际响应信息", "source": "5556ed28-d7f7-4fd8-9d52-07a190cb647c-attachment.json", "type": "application/json"}, {"name": "状态码断言结果：成功", "source": "6c972c2c-4694-479a-9b27-05cd0fe99718-attachment.txt", "type": "text/plain"}, {"name": "相等断言结果：成功", "source": "dd3fe1b0-4d99-422c-8638-870b82b7742a-attachment.json", "type": "application/json"}, {"name": "相等断言结果：成功", "source": "993da869-9851-48b4-bca5-bc6e4ae5aafd-attachment.json", "type": "application/json"}, {"name": "📋 测试执行日志", "source": "f7a588e2-ad11-481f-9ca1-a86dcfa40958-attachment.txt", "type": "text/plain"}], "start": 1751853256667, "stop": 1751853256971, "uuid": "449e0684-0849-4bb9-aa21-bf30a1064d82", "historyId": "e9c2ea8fd9c81240c2b50d2a9668daf2", "testCaseId": "91ab679f57e05cf8b5a9e119b7e95056", "fullName": "testcase.demo_test.test_demo.TestFilmOrder#test_film_order", "labels": [{"name": "feature", "value": "M02_电影订票模块接口测试"}, {"name": "story", "value": "C02_电影下单流程"}, {"name": "parentSuite", "value": "testcase.demo_test"}, {"name": "suite", "value": "test_demo"}, {"name": "subSuite", "value": "TestFilmOrder"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "28812-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.demo_test.test_demo"}]}