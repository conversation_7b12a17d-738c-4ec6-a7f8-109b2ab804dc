{"name": "为你推荐", "status": "passed", "attachments": [{"name": "接口地址", "source": "9a21bc1d-6f90-4bbb-85aa-9782b127d1b7-attachment.json", "type": "application/json"}, {"name": "接口名称", "source": "4554f46d-5720-43dc-a876-59a379ba6fb9-attachment.json", "type": "application/json"}, {"name": "请求方式", "source": "85904f91-91ec-4899-99cc-f9797db2e413-attachment.json", "type": "application/json"}, {"name": "请求头", "source": "3c9431e1-95c5-48cd-8085-75191f4feaba-attachment.json", "type": "application/json"}, {"name": "<PERSON><PERSON>", "source": "53760cae-1222-46e7-bbae-6d63e2056fff-attachment.json", "type": "application/json"}, {"name": "测试用例名称", "source": "e428daec-fc9d-4da2-8a5a-91a749f283cb-attachment.json", "type": "application/json"}, {"name": "参数类型", "source": "5b0aa4f4-6b7c-4ee1-a209-e1b2855c79e8-attachment.json", "type": "application/json"}, {"name": "请求参数json格式", "source": "fedc2e6d-af19-4fd2-8adf-675e96b6748f-attachment.json", "type": "application/json"}, {"name": "请求参数实际入参", "source": "30e7cdc2-4c9f-4031-8bb1-5c9c277d2955-attachment.json", "type": "application/json"}, {"name": "接口实际响应信息", "source": "451517bd-2228-4d71-b17f-45ba098d8861-attachment.json", "type": "application/json"}, {"name": "状态码断言结果：成功", "source": "dbb63d3e-739e-47be-afc1-9a061b999311-attachment.txt", "type": "text/plain"}, {"name": "相等断言结果：成功", "source": "4035af62-9aad-4f40-bd35-9b8ae5342d80-attachment.json", "type": "application/json"}, {"name": "📋 测试执行日志", "source": "f9edcb5e-b64f-4957-8ee2-5e8a0df67626-attachment.txt", "type": "text/plain"}], "parameters": [{"name": "api_info", "value": "{'baseInfo': {'module': '演出票', 'api_name': '为你推荐', 'host': '${get_host(play)}', 'url': '/api/show/info/home_recommend_list', 'method': 'post', 'headers': '${get_headers(app)}'}, 'testCase': {'case_name': '获取为你推荐演出列表', 'validation': [{'code': 200}, {'eq': {'code': 0}}], 'extract': 0, 'extract_list': np.float64(0.0), 'data': {'page': 1, 'pageSize': 20, 'channelCode': '${appKey()}', 'token': '${get_extract_data(token)}'}}}"}], "start": 1751853734481, "stop": 1751853734798, "uuid": "a6715099-4bb9-48e3-9447-789fb24364d0", "historyId": "93b5e0aac89859d5ee60ab661699f09c", "testCaseId": "e9970ce8394fe9b627f33514f0afecf5", "fullName": "testcase.play.test_play.TestPlay#test_play", "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "39072-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}]}