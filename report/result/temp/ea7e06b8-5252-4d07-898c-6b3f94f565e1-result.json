{"name": "首页广告位", "status": "passed", "attachments": [{"name": "接口地址", "source": "3f325da7-fa29-4b0c-a691-4b4d4b5b9f31-attachment.json", "type": "application/json"}, {"name": "接口名称", "source": "f6905c43-5e02-42ed-9d15-635a4a725165-attachment.json", "type": "application/json"}, {"name": "请求方式", "source": "40351ac1-ddee-4c25-be1f-3359621355e4-attachment.json", "type": "application/json"}, {"name": "请求头", "source": "952ddc62-866a-4704-b971-fa21b0de7160-attachment.json", "type": "application/json"}, {"name": "<PERSON><PERSON>", "source": "26091183-22d5-45aa-86b3-f3d3a5e2f07e-attachment.json", "type": "application/json"}, {"name": "测试用例名称", "source": "5337d3c3-39e8-4d1f-98fd-6b0f37bc47a0-attachment.json", "type": "application/json"}, {"name": "参数类型", "source": "88a6d9e0-7615-4d44-8459-e6ebf29023b1-attachment.json", "type": "application/json"}, {"name": "请求参数json格式", "source": "4907fcb2-e72c-43b7-b18d-a7ce4432a24e-attachment.json", "type": "application/json"}, {"name": "请求参数实际入参", "source": "83e80cd2-870b-4a66-b705-212e6f341090-attachment.json", "type": "application/json"}, {"name": "接口实际响应信息", "source": "52909f4b-1714-4c17-a2be-5ba58adf65e5-attachment.json", "type": "application/json"}, {"name": "状态码断言结果：成功", "source": "04475f71-af2b-41d8-91fc-28f65b9f635b-attachment.txt", "type": "text/plain"}, {"name": "相等断言结果：成功", "source": "b7d77803-796b-4840-988d-ed042d682be5-attachment.json", "type": "application/json"}, {"name": "📋 测试执行日志", "source": "4ab808e7-31a1-4c52-b0de-90d171465137-attachment.txt", "type": "text/plain"}], "parameters": [{"name": "api_info", "value": "{'baseInfo': {'module': '演出票', 'api_name': '首页广告位', 'host': '${get_host(play)}', 'url': '/api/show/ad/select_list', 'method': 'post', 'headers': '${get_headers(app)}'}, 'testCase': {'case_name': '获取首页广告列表', 'validation': [{'code': 200}, {'eq': {'code': 0}}], 'extract': 0, 'extract_list': np.float64(0.0), 'data': {'channelCode': '${appKey()}', 'token': '${get_extract_data(token)}'}}}"}], "start": 1751853736318, "stop": 1751853736640, "uuid": "b549616d-13b0-4d97-a00c-ed030161fd12", "historyId": "16615fd56de330c5e7df9e74f610a0a1", "testCaseId": "e9970ce8394fe9b627f33514f0afecf5", "fullName": "testcase.play.test_play.TestPlay#test_play", "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "39072-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}]}