{"name": "近期特惠", "status": "passed", "attachments": [{"name": "接口地址", "source": "fd961695-7a70-4940-8fa3-fdae3629ac64-attachment.json", "type": "application/json"}, {"name": "接口名称", "source": "3ab23952-8aaa-4122-9989-812b25865ff5-attachment.json", "type": "application/json"}, {"name": "请求方式", "source": "6ac8937c-95eb-4f67-934c-9391c00042bc-attachment.json", "type": "application/json"}, {"name": "请求头", "source": "47cbc5a7-0c92-4271-88c3-268095f39c62-attachment.json", "type": "application/json"}, {"name": "<PERSON><PERSON>", "source": "3155f033-f6fa-43a8-bf58-7cdc7befaf32-attachment.json", "type": "application/json"}, {"name": "测试用例名称", "source": "62cd9c41-1309-4012-b9e0-674b032be3e6-attachment.json", "type": "application/json"}, {"name": "参数类型", "source": "d6ed589e-1eaf-4dc9-a818-c311638606d0-attachment.json", "type": "application/json"}, {"name": "请求参数json格式", "source": "91cddcd4-05fc-4e04-876d-242e198fef68-attachment.json", "type": "application/json"}, {"name": "请求参数实际入参", "source": "4ea850f5-455c-4caf-8192-09ba4f0f1ebc-attachment.json", "type": "application/json"}, {"name": "接口实际响应信息", "source": "b6b851f4-a7a1-4d94-973f-4a4db7273fc6-attachment.json", "type": "application/json"}, {"name": "状态码断言结果：成功", "source": "9383eb92-45fa-4bed-8b88-3b370892d1b6-attachment.txt", "type": "text/plain"}, {"name": "相等断言结果：成功", "source": "7d1129bd-190a-4834-89a0-d152340804e3-attachment.json", "type": "application/json"}, {"name": "📋 测试执行日志", "source": "871f5d7e-604f-48cd-8e9f-5caf5f54e6ec-attachment.txt", "type": "text/plain"}], "start": 1751851569591, "stop": 1751851569922, "uuid": "a9bf319b-9816-4dc4-b861-a89561501024", "historyId": "b9dc0ab2757f5b153151bfbd4cc293dd", "testCaseId": "e9970ce8394fe9b627f33514f0afecf5", "fullName": "testcase.play.test_play.TestPlay#test_play", "labels": [{"name": "story", "value": "C01_演出模块流程"}, {"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "34968-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}]}