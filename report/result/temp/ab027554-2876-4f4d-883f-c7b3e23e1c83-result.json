{"name": "下单", "status": "passed", "attachments": [{"name": "接口地址", "source": "75a15750-363a-4aed-a154-39c083fdc939-attachment.json", "type": "application/json"}, {"name": "接口名称", "source": "d478bd89-8965-4208-b11c-b8a0cd61dfe0-attachment.json", "type": "application/json"}, {"name": "请求方式", "source": "e616caa0-690b-4b23-a0bd-37555d9960b4-attachment.json", "type": "application/json"}, {"name": "请求头", "source": "ebe13eaa-4ef1-4ceb-88a3-53f152ed1b69-attachment.json", "type": "application/json"}, {"name": "<PERSON><PERSON>", "source": "bfaeb36e-6b69-4d2d-a5f1-685d920a185b-attachment.json", "type": "application/json"}, {"name": "测试用例名称", "source": "2d8304a7-5ff6-4755-aa89-37674815d181-attachment.json", "type": "application/json"}, {"name": "参数类型", "source": "ca2e09f3-87ab-4326-a399-928260448b41-attachment.json", "type": "application/json"}, {"name": "请求参数json格式", "source": "3a9f99e9-745b-4b2e-994b-7016c2c17562-attachment.json", "type": "application/json"}, {"name": "请求参数实际入参", "source": "bbab13f7-d2f6-4832-9f7c-96739d0b521f-attachment.json", "type": "application/json"}, {"name": "接口实际响应信息", "source": "e322d1d1-c6a3-4dd9-878f-435a22d715f3-attachment.json", "type": "application/json"}, {"name": "状态码断言结果：成功", "source": "a9cee166-6ada-44b7-b2ba-57b398ddb293-attachment.txt", "type": "text/plain"}, {"name": "相等断言结果：成功", "source": "53a2e277-aae2-4bf0-a635-0086cd7d0a9f-attachment.json", "type": "application/json"}, {"name": "📋 测试执行日志", "source": "6720d323-eea3-437f-a105-44534d5e8065-attachment.txt", "type": "text/plain"}], "start": 1751851587216, "stop": 1751851588859, "uuid": "31018da1-93a0-48d4-b13c-399956c0762a", "historyId": "df78d83b2c7879c3a2ecdde06190a07c", "testCaseId": "e9970ce8394fe9b627f33514f0afecf5", "fullName": "testcase.play.test_play.TestPlay#test_play", "labels": [{"name": "story", "value": "C01_演出模块流程"}, {"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "34968-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}]}