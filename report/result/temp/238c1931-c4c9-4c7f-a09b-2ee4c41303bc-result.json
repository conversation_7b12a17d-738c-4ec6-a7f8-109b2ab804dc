{"name": "取消订单", "status": "passed", "attachments": [{"name": "接口地址", "source": "68a509a4-0621-41be-a7c2-7e870959a153-attachment.json", "type": "application/json"}, {"name": "接口名称", "source": "0c760cc4-e49c-4ccc-9560-0c99914a2ac0-attachment.json", "type": "application/json"}, {"name": "请求方式", "source": "d6d55fe9-ff4b-4144-a4de-626e69f2ed1d-attachment.json", "type": "application/json"}, {"name": "请求头", "source": "19e8b3e3-911e-47f6-b1a2-a8ad3f5e3cc5-attachment.json", "type": "application/json"}, {"name": "<PERSON><PERSON>", "source": "b3496ed8-8f88-4d3b-a61f-19fe9ea4b105-attachment.json", "type": "application/json"}, {"name": "测试用例名称", "source": "05d3b70e-e9ae-4e70-8649-f451ee00cd8c-attachment.json", "type": "application/json"}, {"name": "参数类型", "source": "05bca981-04a0-4104-ae9e-8d0b42c33ad9-attachment.json", "type": "application/json"}, {"name": "请求参数json格式", "source": "2255e538-ec9c-460b-9a19-406ad47d6f40-attachment.json", "type": "application/json"}, {"name": "请求参数实际入参", "source": "9f8015c9-9ea5-46c8-b158-43731ca1b972-attachment.json", "type": "application/json"}, {"name": "接口实际响应信息", "source": "67c957e4-8fd0-4bc7-8649-0fb7b087a33a-attachment.json", "type": "application/json"}, {"name": "状态码断言结果：成功", "source": "c8708cd8-912d-4f82-9ae3-983eecc3fa75-attachment.txt", "type": "text/plain"}, {"name": "相等断言结果：成功", "source": "b6c1dfff-0348-4574-8156-1c39f47484e5-attachment.json", "type": "application/json"}, {"name": "📋 测试执行日志", "source": "89a34597-f7c2-4fe3-8317-36e4660466fb-attachment.txt", "type": "text/plain"}], "start": 1751852481196, "stop": 1751852482456, "uuid": "9862bcd6-5b17-4cf2-afe0-42ab71b2b995", "historyId": "097386a9d43be40d3c28c1874a6e169b", "testCaseId": "e9970ce8394fe9b627f33514f0afecf5", "fullName": "testcase.play.test_play.TestPlay#test_play", "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "38092-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}]}