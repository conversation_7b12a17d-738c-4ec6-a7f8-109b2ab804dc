{"name": "下单", "status": "passed", "attachments": [{"name": "接口地址", "source": "4fa54b1a-9fb4-402f-ab09-419533a04e4e-attachment.json", "type": "application/json"}, {"name": "接口名称", "source": "77233168-3c8e-415d-a046-15cf609cda3b-attachment.json", "type": "application/json"}, {"name": "请求方式", "source": "c9798f98-19fc-495c-9e9e-ad43703664a7-attachment.json", "type": "application/json"}, {"name": "请求头", "source": "c7d11fa7-486d-4d69-b8eb-1ebb7b374058-attachment.json", "type": "application/json"}, {"name": "<PERSON><PERSON>", "source": "d2e1ed45-e53f-49c9-aad2-bdad78473587-attachment.json", "type": "application/json"}, {"name": "测试用例名称", "source": "ada736b9-6d29-49ea-9e9e-540c34faf921-attachment.json", "type": "application/json"}, {"name": "参数类型", "source": "50664eb5-b10e-4c18-91bc-0c1b8f6d83a4-attachment.json", "type": "application/json"}, {"name": "请求参数json格式", "source": "c54b150c-c93c-41ee-ac87-ad35775340ad-attachment.json", "type": "application/json"}, {"name": "请求参数实际入参", "source": "a9829563-fd57-4fc4-bdb3-f6c3db72e3c6-attachment.json", "type": "application/json"}, {"name": "接口实际响应信息", "source": "c1aa3a28-d102-48eb-8d9d-377143cbc93a-attachment.json", "type": "application/json"}, {"name": "状态码断言结果：成功", "source": "074efd83-a75a-4f7f-aaaa-3d501a558a0c-attachment.txt", "type": "text/plain"}, {"name": "相等断言结果：成功", "source": "10f30cd4-bc0d-4793-8f55-7ed90c22b368-attachment.json", "type": "application/json"}, {"name": "📋 测试执行日志", "source": "7ea6fe76-0351-428e-8643-d41444ef0b8a-attachment.txt", "type": "text/plain"}], "start": 1751853907768, "stop": 1751853909033, "uuid": "e48b7e55-d118-4bc9-90a3-61364c7a7d44", "historyId": "df78d83b2c7879c3a2ecdde06190a07c", "testCaseId": "e9970ce8394fe9b627f33514f0afecf5", "fullName": "testcase.play.test_play.TestPlay#test_play", "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "39132-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}]}