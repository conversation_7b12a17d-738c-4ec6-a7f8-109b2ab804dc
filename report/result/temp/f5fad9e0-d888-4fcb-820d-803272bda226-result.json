{"name": "演出项目详情", "status": "passed", "attachments": [{"name": "接口地址", "source": "9ebd0679-a965-4395-bd40-006044c8108d-attachment.json", "type": "application/json"}, {"name": "接口名称", "source": "627863bf-d3e2-4f6f-b3f2-b9d9720739e2-attachment.json", "type": "application/json"}, {"name": "请求方式", "source": "d061330d-34f8-447d-8e71-2067aca1a122-attachment.json", "type": "application/json"}, {"name": "请求头", "source": "d342e913-5b32-42a0-947e-d765224d910e-attachment.json", "type": "application/json"}, {"name": "<PERSON><PERSON>", "source": "74747956-46a6-4e85-9737-fea49498b7eb-attachment.json", "type": "application/json"}, {"name": "测试用例名称", "source": "44af18b0-afff-4b89-8029-1b82225907bc-attachment.json", "type": "application/json"}, {"name": "参数类型", "source": "764676ea-2afd-4333-8182-de30e9eebc8a-attachment.json", "type": "application/json"}, {"name": "请求参数json格式", "source": "5fc22796-dc42-42f4-a92a-de8fed5d4eaa-attachment.json", "type": "application/json"}, {"name": "请求参数实际入参", "source": "2ddc3769-f3e3-45af-a973-0beb0762dd48-attachment.json", "type": "application/json"}, {"name": "接口实际响应信息", "source": "b0635a27-c191-4126-8bcf-************-attachment.json", "type": "application/json"}, {"name": "状态码断言结果：成功", "source": "66154dff-4d48-4b8f-a717-db12c35975cf-attachment.txt", "type": "text/plain"}, {"name": "相等断言结果：成功", "source": "eb071ad9-fafa-4c1f-804e-d5a590dcf9ad-attachment.json", "type": "application/json"}, {"name": "📋 测试执行日志", "source": "9dfb0399-55dc-445a-ba00-06129e959a92-attachment.txt", "type": "text/plain"}], "start": 1751851578842, "stop": 1751851579441, "uuid": "ab1099ca-ed1f-4210-af8f-4d88e9e944c3", "historyId": "0457c216d6185e839b1ab99163c7bbc6", "testCaseId": "e9970ce8394fe9b627f33514f0afecf5", "fullName": "testcase.play.test_play.TestPlay#test_play", "labels": [{"name": "story", "value": "C01_演出模块流程"}, {"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "34968-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}]}