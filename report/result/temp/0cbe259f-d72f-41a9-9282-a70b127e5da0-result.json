{"name": "下单", "status": "passed", "attachments": [{"name": "接口地址", "source": "fd751762-499f-4e8b-aae6-ebc22817d31b-attachment.json", "type": "application/json"}, {"name": "接口名称", "source": "aec8235c-cb80-439c-a01a-3ce208a31e96-attachment.json", "type": "application/json"}, {"name": "请求方式", "source": "8e89eeb9-2590-4f7f-a844-34778f261400-attachment.json", "type": "application/json"}, {"name": "请求头", "source": "22a2280d-bcf1-4059-a995-5fb72a09b866-attachment.json", "type": "application/json"}, {"name": "<PERSON><PERSON>", "source": "42457f98-2d5b-444d-b471-fb1b5a25cae5-attachment.json", "type": "application/json"}, {"name": "测试用例名称", "source": "fcfe473e-04d0-4120-be3d-050105277ee4-attachment.json", "type": "application/json"}, {"name": "参数类型", "source": "d69e9c1a-7e00-4eb6-a4c4-fe5fcab9e58a-attachment.json", "type": "application/json"}, {"name": "请求参数json格式", "source": "96be0810-f882-4cd3-a34b-76d31e8e5274-attachment.json", "type": "application/json"}, {"name": "请求参数实际入参", "source": "4c3cffb0-3441-4a9a-892d-81c6b0291453-attachment.json", "type": "application/json"}, {"name": "接口实际响应信息", "source": "2f8e48dc-3680-48a7-b86a-11c41af1e928-attachment.json", "type": "application/json"}, {"name": "状态码断言结果：成功", "source": "1d20fe74-47ae-431d-9325-562e5b0db96b-attachment.txt", "type": "text/plain"}, {"name": "相等断言结果：成功", "source": "0fc7ba51-9a51-4223-b917-852e740ed915-attachment.json", "type": "application/json"}, {"name": "📋 测试执行日志", "source": "f7099e93-4061-4a7b-8a1d-3a2badc98b2e-attachment.txt", "type": "text/plain"}], "start": 1751852478401, "stop": 1751852479673, "uuid": "a5d56aa3-89a8-428a-b9cc-80a9806c79e8", "historyId": "df78d83b2c7879c3a2ecdde06190a07c", "testCaseId": "e9970ce8394fe9b627f33514f0afecf5", "fullName": "testcase.play.test_play.TestPlay#test_play", "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "38092-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}]}