{"name": "项目下场次票品列表", "status": "passed", "attachments": [{"name": "接口地址", "source": "899f84ff-f197-4a71-a9df-18fc630231c4-attachment.json", "type": "application/json"}, {"name": "接口名称", "source": "a144410b-c6bc-4bb9-a1f6-f036b5e21a29-attachment.json", "type": "application/json"}, {"name": "请求方式", "source": "1ed9b660-dbb3-4e89-9530-eed391e68025-attachment.json", "type": "application/json"}, {"name": "请求头", "source": "7cb24ed3-8e0a-4699-8171-03e5e9029d2c-attachment.json", "type": "application/json"}, {"name": "<PERSON><PERSON>", "source": "f17d90e6-7024-4ac1-b0b6-bcc1ef390faa-attachment.json", "type": "application/json"}, {"name": "测试用例名称", "source": "6e7530a5-3b3e-42b5-b9a8-ae4be7d206d8-attachment.json", "type": "application/json"}, {"name": "参数类型", "source": "376369cb-bcf7-4562-a299-9a0f252fe167-attachment.json", "type": "application/json"}, {"name": "请求参数json格式", "source": "73599b58-2f9c-4545-9c41-435f8f01f3c0-attachment.json", "type": "application/json"}, {"name": "请求参数实际入参", "source": "450aa2a0-b5c6-4532-be9c-faa1845ceed5-attachment.json", "type": "application/json"}, {"name": "接口实际响应信息", "source": "4d407b78-12e7-4579-ace1-b862f461f36a-attachment.json", "type": "application/json"}, {"name": "状态码断言结果：成功", "source": "b5e875f3-9365-4049-b7b9-57241ed513a9-attachment.txt", "type": "text/plain"}, {"name": "相等断言结果：成功", "source": "601ef866-eaeb-4762-a10f-fba3ff94d304-attachment.json", "type": "application/json"}, {"name": "📋 测试执行日志", "source": "a55bb9c5-e091-4b6d-82f8-b952d244f206-attachment.txt", "type": "text/plain"}], "start": 1751852474303, "stop": 1751852474883, "uuid": "b6b79c40-8031-42ea-808b-594f34f00635", "historyId": "3287c72f85d7169cf4925a7d71668b28", "testCaseId": "e9970ce8394fe9b627f33514f0afecf5", "fullName": "testcase.play.test_play.TestPlay#test_play", "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "38092-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}]}