{"name": "取消订单", "status": "passed", "attachments": [{"name": "接口地址", "source": "0053125e-8fbb-4fb7-967a-2b2e3d710acd-attachment.json", "type": "application/json"}, {"name": "接口名称", "source": "3de411f7-fb41-4268-ad5e-1cd1d13feb3f-attachment.json", "type": "application/json"}, {"name": "请求方式", "source": "60756094-fab9-48c5-a544-76e4b26c6d71-attachment.json", "type": "application/json"}, {"name": "请求头", "source": "553d4eeb-32c1-450f-a64b-0355f8a77a08-attachment.json", "type": "application/json"}, {"name": "<PERSON><PERSON>", "source": "8e59466b-97ae-4eb6-9008-a6679b7d58f6-attachment.json", "type": "application/json"}, {"name": "测试用例名称", "source": "ccae69aa-e8aa-4186-8f41-1a112090313d-attachment.json", "type": "application/json"}, {"name": "参数类型", "source": "2afa1197-21c2-4705-9a49-492b31e00c80-attachment.json", "type": "application/json"}, {"name": "请求参数json格式", "source": "ccd015b7-5d62-4e21-8e72-00f8e176055f-attachment.json", "type": "application/json"}, {"name": "请求参数实际入参", "source": "7ffb9e31-0f8a-4aaa-b091-37eaefe4b657-attachment.json", "type": "application/json"}, {"name": "接口实际响应信息", "source": "3cef354e-003b-46c0-8609-0c73c4f2afac-attachment.json", "type": "application/json"}, {"name": "状态码断言结果：成功", "source": "f4a32f01-16b1-42b7-98e3-ccd3d99d7bd8-attachment.txt", "type": "text/plain"}, {"name": "相等断言结果：成功", "source": "cb103e88-8afe-4f48-a244-a8a9f48c4d20-attachment.json", "type": "application/json"}, {"name": "📋 测试执行日志", "source": "f721cb84-f134-432d-b13d-a98ce14b9bcc-attachment.txt", "type": "text/plain"}], "parameters": [{"name": "api_info", "value": "{'baseInfo': {'module': '演出票', 'api_name': '取消订单', 'host': '${get_host(play)}', 'url': '/api/show/order/cancel', 'method': 'post', 'headers': '${get_headers(app)}'}, 'testCase': {'case_name': '取消订单', 'validation': [{'code': 200}, {'eq': {'code': 0}}], 'extract': 0, 'extract_list': np.float64(0.0), 'data': {'orderId': '${get_extract_data(orderId)}', 'channelCode': '${appKey()}', 'token': '${get_extract_data(token)}'}}}"}], "start": 1751853753154, "stop": 1751853754353, "uuid": "0a0e9728-04e0-43f0-9c1d-a2e6091a3cdd", "historyId": "86ddc9742985e7678bcca10364db8520", "testCaseId": "e9970ce8394fe9b627f33514f0afecf5", "fullName": "testcase.play.test_play.TestPlay#test_play", "labels": [{"name": "feature", "value": "M01_演出模块接口测试"}, {"name": "story", "value": "C01_演出模块流程"}, {"name": "parentSuite", "value": "testcase.play"}, {"name": "suite", "value": "test_play"}, {"name": "subSuite", "value": "TestPlay"}, {"name": "host", "value": "deepbug"}, {"name": "thread", "value": "39072-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcase.play.test_play"}]}