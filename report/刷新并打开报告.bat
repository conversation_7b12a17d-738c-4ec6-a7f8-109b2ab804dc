@echo off
echo 正在刷新Allure报告...

REM 停止可能运行的http服务器
taskkill /f /im http_server.exe >nul 2>&1

REM 等待一秒
timeout /t 1 /nobreak >nul

REM 重新生成报告
echo 重新生成报告...
cd /d "%~dp0.."
allure generate ./report/result/temp -o ./report/report --clean

REM 复制环境文件
copy report\result\environment.xml .\report\result\temp >nul 2>&1

echo 报告已刷新，正在启动服务器...

REM 回到report目录
cd /d "%~dp0"

REM 查找Chrome路径
set "chrome_path="
set reg_query_command=reg query "HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\App Paths\chrome.exe" /ve

for /f "tokens=2*" %%A in ('%reg_query_command%') do (
    if "%%A"=="REG_SZ" (
        set "tmp_chrome_path=%%B"
        if exist "!tmp_chrome_path!" (
            set "chrome_path=%%B"
        )
    )
)

REM 启动服务器并打开浏览器
if defined chrome_path (
    echo Chrome found at: "%chrome_path%"
    echo start a webserver ...
    start /b http_server.exe -port 5001
    timeout /t 2 /nobreak >nul
    start chrome.exe --new-window --disable-cache --disable-application-cache --disable-offline-load-stale-cache --disable-gpu-sandbox --disable-extensions --disable-plugins --incognito http://127.0.0.1:5001
) else (
    echo Chrome not found, trying Edge...
    start /b http_server.exe -port 5001
    timeout /t 2 /nobreak >nul
    start msedge.exe --new-window --disable-cache --inprivate http://127.0.0.1:5001
)

echo 报告已在浏览器中打开（无缓存模式）
pause
