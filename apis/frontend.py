"""
@Project    :zy_ApiAuto
@File       :frontend.py
@IDE        :PyCharm
<AUTHOR>
@Date       :2025/4/22 17:38
"""
from configs.setting import area_code, lon, lat

FRONTEND_APIS = {
    ### ————————————————————首页相关接口————————————————————————
    # 首页顶部广告列表接口
    'getAdList': {
        'param': {
            "head": "${head(getAdList)}",
            "body": {
                "cityNo": area_code,
                "userId": "${get_extract_data(login_info,userId)}",
                "positionCode": "",
            }
        }
    },
    # 电影周边商品列表接口 "*********"渠道还能查到 "*********"android 渠道已关闭提示"msg": "渠道已关闭。"
    'getShopGoods': {
        'param': {
            "head": "${head(getShopGoods)}",
            "body": {
                "sign": "${sign(getShopGoods)}",  # sign=MD5(cityNo+key)
                "cityNo": area_code
            }
        }
    },
    # 首页栏目
    'getTopicList': {
        'param': {
            "head": "${head(getTopicList)}",
            "body": {
                "cityNo": area_code
            }
        }
    },
    ### ————————————————————首页影讯————————————————————————
    # 影讯列表接口
    'getNewsList': {
        'param': {
            "head": "${head(getNewsList)}",
            "body": {
                "pageRows": "20",
                "newsId": "",
                "type": "0",
                "pageIndex": "1"
            }
        }
    },
    # 影讯详情接口
    'getNewsDetail': {
        "param": {
            "head": "${head(getNewsDetail)}",
            "body": {
                "newsId": "${get_extract_data(newsId)}"
            }
        }
    },
    # 影讯评论列表接口
    'getNewsReviews': {
        "param": {
            "head": "${head(getNewsReviews)}",
            "body": {
                "newsId": "${get_extract_data(newsId)}",
                "mainCommentID": "0",
                "numPerPage": "5",
                "pageIdx": "1",
                "userId": "${get_extract_data(login_info,userId)}",
                "replyCommentID": "0"
            }
        }
    },
    # 更新点赞接口
    'updateNewsCommentZan': {
        "param": {
            "head": "${head(updateNewsCommentZan)}",
            "body": {
                "sign": "${sign(updateNewsCommentZan)}",
                "commentId": "${get_extract_data(newsId)}",
                "type": "1",
                "userId": "${get_extract_data(login_info,userId)}"
            }
        }
    },
    # 影讯评论接口
    'submitNewsReview': {
        "param": {
            "head": "${head(submitNewsReview)}",
            "body": {
                "sign": "${sign(submitNewsReview)}",
                "newsId": "${get_extract_data(newsId)}",
                "mainCommentID": "0",
                "userId": "${get_extract_data(login_info,userId)}",
                "replyCommentID": "0",
                "content": "${get_extract_data(content)}"
            }
        }
    },
    # 正在热映接口 ----------------- 深圳 -------------------
    'getHotFilms': {
        'param': {
            "head": "${head(getHotFilms)}",
            "body": {
                "pageRows": "20",
                "areaNo": area_code,
                "pageIndex": "1"
            }
        }
    },
    # 约映接口
    'getBookedFilms': {
        'param': {
            "head": "${head(getBookedFilms)}",
            "body": {
                "areaNo": area_code,  # 深圳
                "pageIndex": "1",
                "pageRows": "15",
            }
        }
    },
    # 待映片接口
    'getComingFilms': {
        'param': {
            "head": "${head(getComingFilms)}",
            "body": {
                "userId": "${get_extract_data(login_info,userId)}",
                "cityNo": area_code,
                "mobileNo": "${get_extract_data(login_info,mobile)}"
            }
        }
    },
    # 搜索影片
    'getFilmsByKey': {
        'param': {
            "head": "${head(getFilmsByKey)}",
            "body": {
                "sign": "${sign(getFilmsByKey)}",
                "pageSize": "10",
                "areaNo": area_code,
                "pageIndex": "1",
                "key": "${get_extract_data(filmName)}"
            }
        }
    },
    # 获取影片详情
    'getFilmInfo': {
        'param': {
            "head": "${head(getFilmInfo)}",
            "body": {
                "cityNo": area_code,
                "type": "1",  # 类型，1=热映影片，2=即将上映影片
                "filmNo": "${get_extract_data(filmNo)}",
                "userId": "${get_extract_data(login_info,userId)}",
            }
        }
    },
    # 获取影片放映日期
    'getFilmShowDates': {
        'param': {
            "head": "${head(getFilmShowDates)}",
            "body": {
                "cinemaNo": "",
                "actId": "",
                "areaNo": area_code,
                "filmNo": "${get_extract_data(filmNo)}"
            }
        }
    },
    # 获取影评
    'getFilmReviews': {
        'param': {
            "head": "${head(getFilmReviews)}",
            "body": {
                "mainCommentID": "0",
                "numPerPage": "10",
                "pageIdx": "1",
                "userId": "${get_extract_data(login_info,userId)}",
                "replyCommentID": "0",
                "filmNo": "${get_extract_data(filmNo)}",
            }
        }
    },
    # 添加影评
    'submitReview': {
        'param': {
            "head": "${head(submitReview)}",
            "body": {
                "score": "6.0",
                "replyCommentID": "0",
                "reviewType": "1",
                "mainCommentID": "0",
                "itemNo": "${get_extract_data(filmNo)}",
                "userId": "${get_extract_data(login_info,userId)}",
                "content": "123456"
            }
        }
    },
    # 获取影院列表筛选条件V2
    'getCinemaListParamV2': {
        'param': {
            "head": "${head(getCinemaListParamV2)}",
            "body": {
                "cityNo": area_code,
                "filmNo": "${get_extract_data(filmNo)}",
            }
        }
    },
    # 影院列表接口
    'getCinemas': {
        'param': {
            "head": "${head(getCinemas)}",
            "body": {
                "sortFlg": "2",
                "pageIdx": "1",
                "numPerPage": "10",
                "longitude": lon,
                "latitude": lat,
                "cityNo": area_code,
                "showDate": "${get_extract_data(showDate)}",
                "filmNo": "${get_extract_data(filmNo)}",
            }
        }
    },
    # 影院排期
    'getCinemaShowTimeV2': {
        'param': {
            "head": "${head(getCinemaShowTimeV2)}",
            "body": {
                "cinemaNo": "${get_extract_data(cinemaNo)}",
                "flg": "one",
                "bookType": ""
            }
        }
    },
    # 查询影院详情
    'getCinemaInfo': {
        'param': {
            "head": "${head(getCinemaInfo)}",
            "body": {
                "cinemaNo": "${get_extract_data(cinemaNo)}",
                "userId": "${get_extract_data(login_info,userId)}"
            }
        }
    },
    # 查询影院卖品列表
    'queryOrderSnacks': {
        'param': {
            "head": "${head(queryOrderSnacks)}",
            "body": {
                "cinemaNo": "${get_extract_data(cinemaNo)}",
                # "sign": "754c660a1f6ae75846f0221ee52f2e96" # sign可以不传
            }
        }
    },
    # 查询影院卡券列表
    'getCardTicketInfos': {
        'param': {
            "head": "${head(getCardTicketInfos)}",
            "body": {
                "cinemaNo": "${get_extract_data(cinemaNo)}",
                "sign": "${sign(getCardTicketInfos)}",
                "isReturnDCEP": "1",
                "businessType": "0",
                "cardNo": ""
            }
        }
    },
    # 查询影院影片列表
    'getCinemaFilms': {
        'param': {
            "head": "${head(getCinemaFilms)}",
            "body": {
                "areaNo": area_code,
                "cinemaNo": "${get_extract_data(cinemaNo)}",
            }
        }
    },
    # 查询影片排期V2
    'getShowTimeV2': {
        'param': {
            "head": "${head(getShowTimeV2)}",
            "body": {
                "cinemaNo": "${get_extract_data(cinemaNo)}",
                "filmNo": "${get_extract_data(filmNo)}",
                "showDate": "${get_extract_data(showDate)}",
            }
        }
    },
    # 查询影厅实时座位
    'getSeat': {
        'param': {
            "head": "${head(getSeat)}",
            "body": {
                "showNo": "${get_extract_data(showNo)}"
            }
        }
    },
    # 查询零食
    'querySnacks': {
        'param': {
            "head": "${head(querySnacks)}",
            "body": {
                "cinemaNo": "${get_extract_data(cinemaNo)}"
            }
        }
    },
    # 确认选座
    'createSeatTicketOrder': {
        'param': {
            "head": "${head(createSeatTicketOrder)}",
            "body": {
                "orderType": "1",
                "mobile": "${get_extract_data(login_info,mobile)}",
                "sign": "${sign(createSeatTicketOrder)}",
                "userId": "${get_extract_data(login_info,userId)}",
                "seats": "${get_extract_data(seatInfo,sectionNo)}|${get_extract_data(seatInfo,rowNo)}|${get_extract_data(seatInfo,colNo)}",
                "showNo": "${get_extract_data(showNo)}",
                "price": "${get_extract_data(seatInfo,areaPrice)}",
                "sendType": "0",
                "lon": lon,
                "lat": lat
            }
        }
    },
    # 获取影院公告
    'getCinemaAffiche': {
        'param': {
            "head": "${head(getCinemaAffiche)}",
            "body": {
                "cinemaNo": "${get_extract_data(cinemaNo)}",
            }
        }
    },
    # 获取订单支付信息
    'getOrderPayInfoV3': {
        'param': {
            "head": "${head(getOrderPayInfoV3)}",
            "body": {
                "orderNo": "${get_extract_data(orderNo)}",
                "cinemaNo": "${get_extract_data(cinemaNo)}",
                "userId": "${get_extract_data(login_info,userId)}",
            }
        }
    },
    # 获取可用电子券列表V4
    'getAvailableCouponListV4': {
        'param': {
            "head": "${head(getAvailableCouponListV4)}",
            "body": {
                "orderNo": "${get_extract_data(orderNo)}",
                "userId": "${get_extract_data(login_info,userId)}",
                "sign": "${sign(getAvailableCouponListV4)}",  # MD5(userId+orderNo+lon+lat+key)
                "lon": lon,
                "lat": lat
            }
        }
    },
    # 查询订单可用储值卡
    'getOrderPayAvailablePrepaidCard': {
        'param': {
            "head": "${head(getOrderPayAvailablePrepaidCard)}",
            "body": {
                "orderNo": "${get_extract_data(orderNo)}",
                "sign": "${sign(getOrderPayAvailablePrepaidCard)}",  # MD5(userId+orderNo+key)
            }
        }
    },
    # 查询影城会员卡
    'getOrderPayAvailableCinemaCard': {
        'param': {
            "head": "${head(getOrderPayAvailableCinemaCard)}",
            "body": {
                "orderNo": "${get_extract_data(orderNo)}",
                "userId": "${get_extract_data(login_info,userId)}",
                "sign": "${sign(getOrderPayAvailableCinemaCard)}",  # MD5(userId+orderNo+key)
            }
        }
    },
    # 获取可用点卡/次卡列表
    'getAvailablePcardListV2': {
        'param': {
            "head": "${head(getAvailablePcardListV2)}",
            "body": {
                "orderNo": "${get_extract_data(orderNo)}",
                "sign": "${sign(getAvailablePcardListV2)}",  # MD5(orderNo+key)
            }
        }
    },
    # 根据订单号查询活动列表
    'getSales': {
        'param': {
            "head": "${head(getSales)}",
            "body": {
                "orderNo": "${get_extract_data(orderNo)}",
                "userId": "${get_extract_data(login_info,userId)}",
            }
        }
    },
    # 查询退改签须知
    'getRefundRenewNotice': {
        'param': {
            "head": "${head(getRefundRenewNotice)}",
            "body": {
                "orderNo": "${get_extract_data(orderNo)}",
                "sign": "${sign(getRefundRenewNotice)}",  # MD5(orderNo+key)
            }
        }
    },
    # 解锁座位/卖品
    'cancelOrder': {
        'param': {
            "head": "${head(cancelOrder)}",
            "body": {
                "orderNo": "${get_extract_data(orderNo)}",
                "sign": "${sign(cancelOrder)}",  # MD5(orderNo +userId+key)
                "businessType": "1",
                "userId": "${get_extract_data(login_info,userId)}",
            }
        }
    },
    # ---------------------------个人中心----------------------
    # 查询商务合作接口
    'getBusinessCooperations': {
        'param': {
            "head": "${head(getBusinessCooperations)}",
            "body": {}
        }
    },
    # 查询个人中心栏目列表
    'getMenuPersonal': {
        'param': {
            "head": "${head(getMenuPersonal)}",
            "body": {
                "cityNo": area_code
            }
        }
    },
    # 查询我的服务列表
    'getMenuPersonalServiceList': {
        'param': {
            "head": "${head(getMenuPersonalServiceList)}",
            "body": {
                "cityNo": area_code
            }
        }
    },
    # 查询券数量
    'getCouponsStatCount': {
        'param': {
            "head": "${head(getCouponsStatCount)}",
            "body": {}
        }
    },
    # 查询用户积分信息
    'getPointsByUser': {
        'param': {
            "head": "${head(getPointsByUser)}",
            "body": {}
        }
    },
    # ---------------------------演出票模块----------------------
}
