<!DOCTYPE html>
<html>
<head>
    <title>测试任务监控</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 20px; 
            background-color: #f5f5f5;
        }
        .container { 
            max-width: 1200px; 
            margin: 0 auto; 
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #007bff;
        }
        .controls {
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
        .controls button {
            margin: 5px;
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .btn-primary { background-color: #007bff; color: white; }
        .btn-success { background-color: #28a745; color: white; }
        .btn-warning { background-color: #ffc107; color: black; }
        .btn-danger { background-color: #dc3545; color: white; }
        .btn-info { background-color: #17a2b8; color: white; }
        
        .task-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .task-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background-color: white;
        }
        .task-header {
            font-weight: bold;
            font-size: 16px;
            margin-bottom: 10px;
            padding-bottom: 8px;
            border-bottom: 1px solid #eee;
        }
        .status-running { border-left: 4px solid #ffc107; }
        .status-completed { border-left: 4px solid #28a745; }
        .status-failed { border-left: 4px solid #dc3545; }
        .status-error { border-left: 4px solid #dc3545; }
        .status-partial_failed { border-left: 4px solid #fd7e14; }
        
        .progress-bar {
            width: 100%;
            height: 20px;
            background-color: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-fill {
            height: 100%;
            background-color: #007bff;
            transition: width 0.3s ease;
            text-align: center;
            line-height: 20px;
            color: white;
            font-size: 12px;
        }
        .task-details {
            font-size: 12px;
            color: #666;
            margin-top: 10px;
        }
        .batch-tasks {
            margin-top: 10px;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 4px;
        }
        .sub-task {
            margin: 5px 0;
            padding: 5px;
            border-radius: 3px;
            font-size: 12px;
        }
        .auto-refresh {
            margin: 10px 0;
        }
        .timestamp {
            font-size: 11px;
            color: #999;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 测试任务监控中心</h1>
            <p>实时监控测试用例执行状态</p>
        </div>
        
        <div class="controls">
            <h3>快速操作</h3>
            <button class="btn-primary" onclick="executeSingle('demo_test')">执行 Demo Test 示例</button>
            <button class="btn-success" onclick="executeAll()">执行所有测试</button>
            <button class="btn-warning" onclick="executeBatch()">自定义批量执行</button>
            <button class="btn-info" onclick="refreshTasks()">刷新状态</button>
            <button class="btn-danger" onclick="clearCompleted()">清除已完成任务</button>
            
            <div class="auto-refresh">
                <label>
                    <input type="checkbox" id="autoRefresh"> 自动刷新 (每5秒)
                </label>
                <span style="margin-left: 20px; color: #666; font-size: 12px;">
                    建议：执行任务时关闭自动刷新以避免影响性能
                </span>
            </div>
        </div>
        
        <div id="taskContainer">
            <p>正在加载任务状态...</p>
        </div>
    </div>

    <script>
        const BASE_URL = window.location.origin;
        let autoRefreshInterval;

        // 页面加载时开始监控
        document.addEventListener('DOMContentLoaded', function() {
            refreshTasks();
            // 默认不开启自动刷新
        });

        // 自动刷新控制
        function startAutoRefresh() {
            const checkbox = document.getElementById('autoRefresh');
            if (checkbox.checked) {
                autoRefreshInterval = setInterval(refreshTasks, 5000); // 改为5秒
            }
        }

        document.getElementById('autoRefresh').addEventListener('change', function() {
            if (this.checked) {
                startAutoRefresh();
            } else {
                clearInterval(autoRefreshInterval);
            }
        });

        // 执行单个测试用例
        async function executeSingle(testcase) {
            try {
                const response = await fetch(`${BASE_URL}/project/${testcase}`);
                const result = await response.json();
                console.log('执行结果:', result);
                setTimeout(refreshTasks, 1000);
            } catch (error) {
                alert('执行失败: ' + error.message);
            }
        }

        // 执行所有测试用例
        async function executeAll() {
            try {
                const response = await fetch(`${BASE_URL}/batch/all`);
                const result = await response.json();
                console.log('批量执行结果:', result);
                setTimeout(refreshTasks, 1000);
            } catch (error) {
                alert('批量执行失败: ' + error.message);
            }
        }

        // 自定义批量执行
        async function executeBatch() {
            const testcases = prompt('请输入要执行的测试用例（用逗号分隔）:', 'demo_test');
            if (testcases) {
                try {
                    const response = await fetch(`${BASE_URL}/batch/execute`, {
                        method: 'POST',
                        headers: {'Content-Type': 'application/json'},
                        body: JSON.stringify({testcases: testcases.split(',').map(s => s.trim())})
                    });
                    const result = await response.json();
                    console.log('自定义批量执行结果:', result);
                    setTimeout(refreshTasks, 1000);
                } catch (error) {
                    alert('自定义批量执行失败: ' + error.message);
                }
            }
        }

        // 刷新任务状态
        async function refreshTasks() {
            try {
                const response = await fetch(`${BASE_URL}/tasks`);
                const tasks = await response.json();
                displayTasks(tasks);
            } catch (error) {
                document.getElementById('taskContainer').innerHTML = 
                    '<p style="color: red;">获取任务状态失败: ' + error.message + '</p>';
            }
        }

        // 显示任务状态
        function displayTasks(tasks) {
            const container = document.getElementById('taskContainer');
            
            if (Object.keys(tasks).length === 0) {
                container.innerHTML = '<p>暂无任务</p>';
                return;
            }

            let html = '<div class="task-grid">';
            
            for (const [taskId, task] of Object.entries(tasks)) {
                html += createTaskCard(taskId, task);
            }
            
            html += '</div>';
            container.innerHTML = html;
        }

        // 创建任务卡片
        function createTaskCard(taskId, task) {
            const statusClass = `status-${task.status}`;
            const progress = task.progress || 0;
            
            let html = `
                <div class="task-card ${statusClass}">
                    <div class="task-header">
                        📋 ${taskId}
                        <span style="float: right; font-size: 12px;" class="timestamp">
                            ${task.start_time || ''}
                        </span>
                    </div>
                    
                    <div><strong>状态:</strong> ${getStatusText(task.status)}</div>
                    <div><strong>类型:</strong> ${task.type === 'batch' ? '批量任务' : '单个任务'}</div>
            `;

            if (task.command) {
                html += `<div><strong>命令:</strong> <code>${task.command}</code></div>`;
            }

            // 进度条
            html += `
                <div class="progress-bar">
                    <div class="progress-fill" style="width: ${progress}%">${progress}%</div>
                </div>
            `;

            // 批量任务的子任务
            if (task.type === 'batch' && task.tasks) {
                html += `
                    <div><strong>任务进度:</strong> ${task.completed_tasks || 0}/${task.total_tasks || 0}</div>
                    <div class="batch-tasks">
                        <strong>子任务:</strong>
                `;
                for (const [subName, subTask] of Object.entries(task.tasks)) {
                    html += `
                        <div class="sub-task status-${subTask.status}">
                            ${subName}: ${getStatusText(subTask.status)}
                        </div>
                    `;
                }
                html += '</div>';
            }

            // 时间信息
            if (task.end_time) {
                html += `<div class="task-details">完成时间: ${task.end_time}</div>`;
            }

            html += '</div>';
            return html;
        }

        // 获取状态文本
        function getStatusText(status) {
            const statusMap = {
                'running': '🔄 运行中',
                'completed': '✅ 已完成',
                'failed': '❌ 失败',
                'error': '💥 错误',
                'partial_failed': '⚠️ 部分失败',
                'pending': '⏳ 等待中'
            };
            return statusMap[status] || status;
        }

        // 清除已完成的任务（这里只是前端隐藏，实际需要后端API支持）
        function clearCompleted() {
            if (confirm('确定要清除所有已完成的任务吗？')) {
                // 这里可以调用后端API来清除任务
                alert('清除功能需要后端API支持');
            }
        }
    </script>
</body>
</html>
