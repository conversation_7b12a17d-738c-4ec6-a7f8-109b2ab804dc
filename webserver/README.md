# Web服务使用说明

## 启动命令

```bash
python webserver/web_service.py --daemon --silent
```

## 参数说明

- `--daemon`: 以守护进程模式运行（后台运行）
- `--silent`: 静默模式，不在控制台输出日志
- `--restart`: 重启现有服务
- `--daemon-child`: 内部参数，用于Windows守护进程子进程

## 目录结构

```
webserver/
├── web_service.py    # 主服务文件
├── monitor.html      # 监控页面模板
└── README.md         # 本说明文件
```

## 日志统一管理

所有日志（包括Web服务、前端请求、后端请求、测试执行）都统一记录到：
- `logs/test.YYYYMMDD.log` - 按日期自动分割的日志文件

## 访问地址

服务启动后可通过以下地址访问：
- 主页: http://localhost:5000/
- 监控页面: http://localhost:5000/monitor
- 执行demo_test示例: http://localhost:5000/project/demo_test
- 查看可用测试用例: http://localhost:5000/testcases
- 健康检查: http://localhost:5000/health

## 特性

1. **统一日志管理** - 所有组件的日志都记录到logs目录
2. **自动进程管理** - 自动检测并停止现有进程
3. **守护进程支持** - 支持后台运行
4. **实时监控** - 提供Web界面监控测试执行状态
5. **批量执行** - 支持批量执行多个测试用例
