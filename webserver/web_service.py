#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Web服务：将Python项目启动为Web服务
当浏览器访问特定URL时，自动执行相应的shell命令
"""

import os
import sys
import subprocess
import threading
import time
import signal
import atexit
import socket
import json
import logging
from flask import Flask, jsonify, request, render_template_string, g
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor

# 添加父目录到Python路径，确保可以导入项目模块
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, PROJECT_ROOT)

# 导入项目的日志模块
from unit_tools.log_util.recordlog import logs

# PID文件路径
PID_FILE = os.path.join(PROJECT_ROOT, 'webserver', 'web_service.pid')

app = Flask(__name__)

# 存储任务执行状态
task_status = {}

# 线程池用于并发执行任务
executor = ThreadPoolExecutor(max_workers=5)

def log_request_info():
    """记录HTTP请求信息"""
    try:
        # 记录请求开始时间
        g.start_time = time.time()

        # 获取客户端IP
        client_ip = request.environ.get('HTTP_X_FORWARDED_FOR', request.environ.get('REMOTE_ADDR', 'unknown'))

        # 记录请求信息
        logs.info(f"HTTP请求 - {request.method} {request.url} - 客户端IP: {client_ip}")

        # 如果有请求参数，记录参数
        if request.args:
            logs.info(f"请求参数 - GET参数: {dict(request.args)}")

        # 如果是POST请求且有数据，记录请求体
        if request.method in ['POST', 'PUT', 'PATCH'] and request.content_length and request.content_length > 0:
            try:
                if request.is_json:
                    logs.info(f"请求体 - JSON: {request.get_json()}")
                elif request.form:
                    logs.info(f"请求体 - Form: {dict(request.form)}")
                elif request.data:
                    # 限制日志长度，避免过长的请求体
                    data_str = request.data.decode('utf-8', errors='ignore')
                    if len(data_str) > 500:
                        data_str = data_str[:500] + "... (截断)"
                    logs.info(f"请求体 - Raw: {data_str}")
            except Exception as e:
                logs.warning(f"无法解析请求体: {e}")
    except Exception as e:
        logs.error(f"记录请求信息时发生错误: {e}")

def log_response_info(response):
    """记录HTTP响应信息"""
    try:
        # 计算请求处理时间
        if hasattr(g, 'start_time'):
            duration = round((time.time() - g.start_time) * 1000, 2)  # 毫秒
            logs.info(f"HTTP响应 - 状态码: {response.status_code} - 处理时间: {duration}ms")
        else:
            logs.info(f"HTTP响应 - 状态码: {response.status_code}")

        # 记录响应内容类型
        if response.content_type:
            logs.info(f"响应类型: {response.content_type}")

        # 如果是JSON响应且不是太长，记录响应内容
        if response.is_json and response.content_length and response.content_length < 1000:
            try:
                response_data = response.get_json()
                logs.info(f"响应内容: {json.dumps(response_data, ensure_ascii=False)}")
            except Exception as e:
                logs.warning(f"无法解析响应JSON: {e}")
        elif response.content_length and response.content_length < 500:
            try:
                response_text = response.get_data(as_text=True)
                if len(response_text) < 500:
                    logs.info(f"响应内容: {response_text}")
            except Exception as e:
                logs.warning(f"无法获取响应内容: {e}")
    except Exception as e:
        logs.error(f"记录响应信息时发生错误: {e}")

    return response

# 注册请求前处理函数
@app.before_request
def before_request():
    log_request_info()

# 注册响应后处理函数
@app.after_request
def after_request(response):
    return log_response_info(response)

# 注释：setup_logging函数已删除，直接使用统一的logs模块

def write_pid_file():
    """写入PID文件"""
    try:
        with open(PID_FILE, 'w') as f:
            f.write(str(os.getpid()))
        logs.info(f"PID文件已创建: {PID_FILE}, PID: {os.getpid()}")
    except Exception as e:
        logs.error(f"创建PID文件失败: {e}")

def remove_pid_file():
    """删除PID文件"""
    try:
        if os.path.exists(PID_FILE):
            os.remove(PID_FILE)
            logs.info(f"PID文件已删除: {PID_FILE}")
    except Exception as e:
        logs.error(f"删除PID文件失败: {e}")

def check_existing_process():
    """检查是否有现有进程运行"""
    if not os.path.exists(PID_FILE):
        logs.info(f"PID文件不存在: {PID_FILE}")
        return None

    try:
        with open(PID_FILE, 'r') as f:
            pid = int(f.read().strip())

        logs.info(f"从PID文件读取到进程ID: {pid}")

        # 检查进程是否存在
        try:
            if os.name == 'nt':  # Windows
                import subprocess
                # 使用tasklist命令检查进程是否存在
                result = subprocess.run(['tasklist', '/FI', f'PID eq {pid}'],
                                      capture_output=True, text=True)
                if str(pid) in result.stdout:
                    logs.info(f"确认进程 {pid} 正在运行")
                    return pid
                else:
                    logs.info(f"进程 {pid} 不存在，删除过期的PID文件")
                    remove_pid_file()
                    return None
            else:  # Unix/Linux
                os.kill(pid, 0)  # 发送信号0检查进程是否存在
                logs.info(f"确认进程 {pid} 正在运行")
                return pid
        except (OSError, subprocess.SubprocessError) as e:
            logs.info(f"进程 {pid} 不存在或无法访问: {e}，删除过期的PID文件")
            remove_pid_file()
            return None
    except (ValueError, FileNotFoundError) as e:
        logs.error(f"读取PID文件失败: {e}")
        return None

def stop_existing_process():
    """停止现有进程"""
    existing_pid = check_existing_process()
    if existing_pid:
        try:
            logs.info(f"正在停止现有服务进程 PID: {existing_pid}...")

            # Windows和Unix系统的进程终止方法
            if os.name == 'nt':  # Windows
                import subprocess
                # 先尝试优雅终止
                logs.info(f"尝试优雅停止服务进程 {existing_pid}")
                result = subprocess.run(['taskkill', '/PID', str(existing_pid)],
                                      capture_output=True, text=True)
                time.sleep(2)  # 等待进程优雅退出

                # 检查进程是否还在运行
                check_result = subprocess.run(['tasklist', '/FI', f'PID eq {existing_pid}'],
                                            capture_output=True, text=True)
                if str(existing_pid) not in check_result.stdout:
                    logs.info(f"服务进程 {existing_pid} 已成功停止")
                    return True

                # 如果还在运行，强制终止
                logs.warning(f"服务进程 {existing_pid} 未响应优雅停止，执行强制停止...")
                result = subprocess.run(['taskkill', '/F', '/PID', str(existing_pid)],
                                      capture_output=True, text=True)
                if result.returncode == 0:
                    logs.info(f"服务进程 {existing_pid} 已强制停止")
                    time.sleep(1)  # 等待进程完全退出
                    return True
                else:
                    logs.error(f"强制停止服务进程失败: {result.stderr}")
                    return False
            else:  # Unix/Linux
                os.kill(existing_pid, signal.SIGTERM)
                time.sleep(2)  # 等待进程优雅退出

                # 检查进程是否已停止
                try:
                    os.kill(existing_pid, 0)
                    # 如果进程仍然存在，强制终止
                    logs.warning(f"进程 {existing_pid} 未响应SIGTERM，强制终止...")
                    os.kill(existing_pid, signal.SIGKILL)
                    time.sleep(1)
                except OSError:
                    pass  # 进程已停止

                logs.info(f"现有进程 {existing_pid} 已停止")
                return True

        except Exception as e:
            logs.error(f"停止现有进程失败: {e}")
            return False
    return True

def cleanup_on_exit():
    """程序退出时的清理工作"""
    logs.info("Web服务正在关闭...")
    remove_pid_file()

# 注释：已改用统一的logs模块，不再需要全局logger变量

# 注册退出处理函数
atexit.register(cleanup_on_exit)

def daemonize():
    """将进程转为守护进程（仅适用于Unix/Linux系统）"""
    if os.name == 'nt':
        # Windows系统不支持传统的fork守护进程
        logs.warning("Windows系统不支持fork守护进程，将以普通后台进程运行")
        return

    try:
        # 第一次fork
        pid = os.fork()
        if pid > 0:
            # 父进程退出
            sys.exit(0)
    except OSError as e:
        logs.error(f"第一次fork失败: {e}")
        sys.exit(1)

    # 脱离父进程的控制
    os.chdir("/")
    os.setsid()
    os.umask(0)

    try:
        # 第二次fork
        pid = os.fork()
        if pid > 0:
            # 父进程退出
            sys.exit(0)
    except OSError as e:
        logs.error(f"第二次fork失败: {e}")
        sys.exit(1)

    # 重定向标准输入输出
    sys.stdout.flush()
    sys.stderr.flush()

    # 关闭标准输入输出
    with open(os.devnull, 'r') as f:
        os.dup2(f.fileno(), sys.stdin.fileno())
    with open(os.devnull, 'w') as f:
        os.dup2(f.fileno(), sys.stdout.fileno())
        os.dup2(f.fileno(), sys.stderr.fileno())

def run_as_windows_service():
    """在Windows上以后台服务方式运行"""
    import subprocess

    # 获取当前脚本路径
    script_path = os.path.abspath(__file__)

    # 使用pythonw.exe而不是python.exe，避免弹出控制台窗口
    pythonw_exe = sys.executable.replace('python.exe', 'pythonw.exe')
    if not os.path.exists(pythonw_exe):
        # 如果pythonw.exe不存在，使用python.exe但隐藏窗口
        pythonw_exe = sys.executable

    # Windows进程创建标志
    DETACHED_PROCESS = 0x00000008
    CREATE_NEW_PROCESS_GROUP = 0x00000200
    CREATE_NO_WINDOW = 0x08000000  # 不创建窗口

    # 启动信息，隐藏窗口
    startupinfo = subprocess.STARTUPINFO()
    startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
    startupinfo.wShowWindow = subprocess.SW_HIDE

    process = subprocess.Popen(
        [pythonw_exe, script_path, '--daemon-child', '--silent'],
        creationflags=DETACHED_PROCESS | CREATE_NEW_PROCESS_GROUP | CREATE_NO_WINDOW,
        startupinfo=startupinfo,
        stdout=subprocess.DEVNULL,
        stderr=subprocess.DEVNULL,
        stdin=subprocess.DEVNULL,
        shell=False
    )

    logs.info(f"后台服务进程已启动，PID: {process.pid}")
    return process.pid

# 可用的测试用例（动态扫描）
def get_available_testcases():
    """动态扫描testcase目录下的测试用例"""
    testcases = {}
    testcase_dir = os.path.join(PROJECT_ROOT, 'testcase')

    if os.path.exists(testcase_dir):
        for item in os.listdir(testcase_dir):
            item_path = os.path.join(testcase_dir, item)
            # 只扫描目录，排除__pycache__和以.开头的目录
            if (os.path.isdir(item_path) and
                not item.startswith('.') and
                item != '__pycache__' and
                item != 'conftest.py'):

                # 检查目录中是否有test_开头的Python文件
                has_test_files = any(
                    f.startswith('test_') and f.endswith('.py')
                    for f in os.listdir(item_path)
                    if os.path.isfile(os.path.join(item_path, f))
                )

                if has_test_files:
                    # 生成描述
                    if item == 'demo_test':
                        description = '演示测试项目'
                    elif item == 'play':
                        description = '演出接口测试'
                    elif item == 'video_news':
                        description = '影讯接口测试'
                    else:
                        description = f'{item}测试用例'

                    testcases[item] = description

    # 确保demo_test始终存在
    if 'demo_test' not in testcases:
        testcases['demo_test'] = '演示测试项目'

    return testcases

# 静态定义，用于向后兼容
AVAILABLE_TESTCASES = {
    'demo_test': '演示测试项目'
}

def execute_command(task_id, command):
    """在后台执行命令，优化性能避免卡顿"""
    try:
        task_status[task_id] = {
            'status': 'running',
            'start_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'command': command,
            'output': '',
            'error': '',
            'progress': 0
        }

        # 执行命令 - 简化版本，避免实时读取导致的阻塞
        process = subprocess.Popen(
            command,
            shell=True,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            cwd=os.getcwd()
        )

        # 等待进程完成并获取输出
        stdout, stderr = process.communicate()

        # 更新任务状态
        task_status[task_id].update({
            'status': 'completed' if process.returncode == 0 else 'failed',
            'end_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'return_code': process.returncode,
            'output': stdout,
            'error': stderr,
            'progress': 100
        })

    except Exception as e:
        task_status[task_id].update({
            'status': 'error',
            'end_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'error': str(e),
            'progress': 0
        })

def execute_command_simple(task_id, command):
    """简化版命令执行，支持实时日志输出"""
    original_cwd = os.getcwd()  # 在函数开始就保存当前目录
    try:
        task_status[task_id] = {
            'status': 'running',
            'start_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'command': command,
            'output': '任务正在后台执行...',
            'error': '',
            'progress': 50
        }

        # 切换到项目根目录执行命令
        os.chdir(PROJECT_ROOT)

        # 增强日志记录，帮助诊断生产环境问题
        logs.info(f"开始执行命令: {command}")
        logs.info(f"当前工作目录: {os.getcwd()}")
        logs.info(f"项目根目录: {PROJECT_ROOT}")
        logs.info(f"Python可执行文件: {sys.executable}")
        logs.info(f"环境变量PATH: {os.environ.get('PATH', 'N/A')}")

        # 检查关键文件是否存在
        run_py_path = os.path.join(PROJECT_ROOT, 'run.py')
        logs.info(f"run.py文件是否存在: {os.path.exists(run_py_path)}")

        # 检查pytest是否可用
        try:
            import pytest
            logs.info(f"pytest版本: {pytest.__version__}")
        except ImportError as e:
            logs.error(f"pytest导入失败: {e}")

        # 使用绝对路径执行命令，提高生产环境兼容性
        if command.startswith('python run.py'):
            # 将相对命令转换为绝对路径命令
            python_exe = sys.executable
            run_script = os.path.join(PROJECT_ROOT, 'run.py')
            args = command.split()[2:]  # 获取run.py后面的参数
            full_command = f'"{python_exe}" "{run_script}" {" ".join(args)}'
            logs.info(f"转换后的完整命令: {full_command}")
            command = full_command

        # 使用subprocess执行命令，实时捕获输出
        logs.info(f"准备启动subprocess进程...")

        try:
            if os.name == 'nt':  # Windows
                # Windows系统，隐藏命令行窗口
                startupinfo = subprocess.STARTUPINFO()
                startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
                startupinfo.wShowWindow = subprocess.SW_HIDE

                # 创建进程，隐藏窗口，实时输出
                process = subprocess.Popen(
                    command,
                    shell=True,
                    startupinfo=startupinfo,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.STDOUT,  # 合并stderr到stdout
                    stdin=subprocess.DEVNULL,
                    cwd=PROJECT_ROOT,
                    creationflags=subprocess.CREATE_NO_WINDOW,
                    universal_newlines=True,
                    bufsize=1  # 行缓冲
                )
            else:  # Unix/Linux
                # Unix/Linux系统
                process = subprocess.Popen(
                    command,
                    shell=True,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.STDOUT,  # 合并stderr到stdout
                    stdin=subprocess.DEVNULL,
                    cwd=PROJECT_ROOT,
                    universal_newlines=True,
                    bufsize=1  # 行缓冲
                )

            logs.info(f"subprocess进程已启动，PID: {process.pid}")

        except Exception as e:
            logs.error(f"启动subprocess进程失败: {e}")
            task_status[task_id].update({
                'status': 'failed',
                'end_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'error': f'启动进程失败: {str(e)}',
                'progress': 0
            })
            return

        # 实时读取输出并记录到日志
        output_lines = []
        logs.info(f"开始读取进程输出...")

        line_count = 0
        while True:
            output = process.stdout.readline()
            if output == '' and process.poll() is not None:
                break
            if output:
                line = output.strip()
                output_lines.append(line)
                line_count += 1

                # 记录关键输出到日志（每10行记录一次，避免日志过多）
                if line_count % 10 == 0 or 'pytest' in line.lower() or 'error' in line.lower():
                    logs.info(f"[{task_id}] 第{line_count}行: {line}")

        # 等待进程完成
        return_code = process.poll()
        logs.info(f"进程执行完成，返回码: {return_code}，总输出行数: {len(output_lines)}")

        # 恢复原始工作目录
        os.chdir(original_cwd)

        # 准备输出信息
        output_text = "任务执行完成"
        if output_lines:
            # 记录完整输出到日志文件
            logs.info(f"命令输出: {' '.join(output_lines)}")

            # 只显示最后几行重要信息，避免输出过长
            if len(output_lines) > 10:
                output_text = "...\n" + "\n".join(output_lines[-10:])
            else:
                output_text = "\n".join(output_lines)
        else:
            logs.warning(f"命令没有产生任何输出")

        error_text = ""
        if return_code != 0:
            error_text = f'命令执行失败，返回码: {return_code}'
            logs.error(f"命令执行失败: {error_text}")
        else:
            logs.info(f"命令执行成功")

        task_status[task_id].update({
            'status': 'completed' if return_code == 0 else 'failed',
            'end_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'return_code': return_code,
            'output': output_text,
            'error': error_text,
            'progress': 100
        })

    except Exception as e:
        # 确保恢复原始工作目录
        try:
            os.chdir(original_cwd)
        except:
            pass

        logs.error(f"执行命令时发生异常: {str(e)}")
        task_status[task_id].update({
            'status': 'error',
            'end_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'error': str(e),
            'progress': 0
        })

def execute_batch_commands(batch_id, commands):
    """批量执行多个命令"""
    try:
        task_status[batch_id] = {
            'status': 'running',
            'start_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'type': 'batch',
            'total_tasks': len(commands),
            'completed_tasks': 0,
            'failed_tasks': 0,
            'tasks': {},
            'progress': 0
        }

        for i, (task_name, command) in enumerate(commands):
            sub_task_id = f"{batch_id}_{task_name}"
            task_status[batch_id]['tasks'][task_name] = {
                'task_id': sub_task_id,
                'status': 'pending',
                'command': command
            }

            # 执行单个任务
            logs.info(f"执行任务 {i+1}/{len(commands)}: {task_name}")
            execute_command(sub_task_id, command)

            # 更新批量任务状态
            if task_status[sub_task_id]['status'] == 'completed':
                task_status[batch_id]['completed_tasks'] += 1
                task_status[batch_id]['tasks'][task_name]['status'] = 'completed'
            else:
                task_status[batch_id]['failed_tasks'] += 1
                task_status[batch_id]['tasks'][task_name]['status'] = 'failed'

            # 更新进度
            progress = ((i + 1) / len(commands)) * 100
            task_status[batch_id]['progress'] = int(progress)

        # 批量任务完成
        task_status[batch_id].update({
            'status': 'completed' if task_status[batch_id]['failed_tasks'] == 0 else 'partial_failed',
            'end_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'progress': 100
        })

    except Exception as e:
        task_status[batch_id].update({
            'status': 'error',
            'end_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'error': str(e),
            'progress': 0
        })

@app.route('/')
def index():
    """首页，显示可用的测试用例"""
    html_template = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Python项目Web服务</title>
        <meta charset="utf-8">
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; }
            .container { max-width: 800px; margin: 0 auto; }
            .testcase { margin: 10px 0; padding: 10px; border: 1px solid #ddd; border-radius: 5px; }
            .testcase a { text-decoration: none; color: #007bff; font-weight: bold; }
            .testcase a:hover { text-decoration: underline; }
            .description { color: #666; margin-top: 5px; }
            .status { margin-top: 20px; padding: 15px; background-color: #f8f9fa; border-radius: 5px; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>Python项目Web服务</h1>
            <p>点击下面的链接执行相应的测试用例：</p>
            
            <div class="testcase">
                <a href="/project/demo_test">执行 demo_test 示例项目</a>
                <div class="description">URL: /project/demo_test → 命令: python run.py demo_test</div>
            </div>
            
            <div class="status">
                <h3>批量执行</h3>
                <p><a href="/batch/all">执行所有测试用例</a></p>
                <p style="font-size: 12px; color: #666;">
                    URL格式: /project/用例1&用例2&用例3<br>
                    示例: /project/demo_test&其他用例
                </p>
            </div>

            <div class="status">
                <h3>其他功能</h3>
                <p><a href="/monitor" target="_blank">📊 任务监控中心</a> (推荐)</p>
                <p><a href="/tasks">查看所有任务状态 (JSON)</a></p>
                <p><a href="/testcases">查看可用测试用例</a></p>
                <p><a href="/health">健康检查</a></p>
            </div>

            <div class="status">
                <h3>使用说明：</h3>
                <ul>
                    <li><strong>单个执行:</strong> /project/demo_test</li>
                    <li><strong>批量执行:</strong> /project/demo_test&其他用例</li>
                    <li>执行结果会以JSON格式返回</li>
                    <li>可以通过 /status/&lt;task_id&gt; 查看任务执行状态</li>
                    <li>可以通过 /tasks 查看所有任务状态</li>
                </ul>
            </div>
        </div>
    </body>
    </html>
    """
    return render_template_string(html_template)



@app.route('/status/<task_id>', methods=['GET', 'POST'])
def get_task_status(task_id):
    """获取任务执行状态 - 支持GET和POST请求"""
    if task_id in task_status:
        return jsonify(task_status[task_id])
    else:
        return jsonify({'error': 'Task not found'}), 404

@app.route('/tasks', methods=['GET', 'POST'])
def get_all_tasks():
    """获取所有任务状态 - 支持GET和POST请求"""
    return jsonify(task_status)

@app.route('/project/<path:testcases>', methods=['GET', 'POST'])
def execute_project_batch(testcases):
    """执行单个或多个测试用例 - 支持 GET和POST请求，支持 play&dev_monitor 格式"""
    try:
        # 解析测试用例，支持 & 分隔符
        if '&' in testcases:
            # 批量执行：play&dev_monitor
            testcase_list = [tc.strip() for tc in testcases.split('&') if tc.strip()]

            # 验证测试用例是否存在
            available_testcases = get_available_testcases()
            invalid_cases = [tc for tc in testcase_list if tc not in available_testcases]
            if invalid_cases:
                return jsonify({
                    'error': f'无效的测试用例: {invalid_cases}',
                    'available_testcases': list(available_testcases.keys())
                }), 400

            # 生成批量任务ID
            batch_id = f"batch_{int(time.time())}"

            # 构建单个命令（run.py 支持多个参数）
            command = f"python run.py {' '.join(testcase_list)}"

            # 在后台线程中执行命令 - 使用简化版避免阻塞
            thread = threading.Thread(target=execute_command_simple, args=(batch_id, command))
            thread.daemon = True
            thread.start()

            return jsonify({
                'message': f'开始批量执行 {len(testcase_list)} 个测试用例',
                'batch_id': batch_id,
                'testcases': testcase_list,
                'command': command,
                'status_url': f'/status/{batch_id}',
                'start_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            })
        else:
            # 单个执行：原有逻辑
            testcase = testcases.strip()

            # 验证测试用例是否存在
            available_testcases = get_available_testcases()
            if testcase not in available_testcases:
                return jsonify({
                    'error': f'无效的测试用例: {testcase}',
                    'available_testcases': list(available_testcases.keys())
                }), 400

            # 生成任务ID
            task_id = f"{testcase}_{int(time.time())}"
            command = f"python run.py {testcase}"

            # 在后台线程中执行命令 - 使用简化版避免阻塞
            thread = threading.Thread(target=execute_command_simple, args=(task_id, command))
            thread.daemon = True
            thread.start()

            return jsonify({
                'message': f'开始执行测试用例: {testcase}',
                'task_id': task_id,
                'command': command,
                'status_url': f'/status/{task_id}',
                'start_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/batch/execute', methods=['GET', 'POST'])
def execute_batch_post():
    """批量执行多个测试用例 - 支持GET和POST请求"""
    try:
        testcases = []

        if request.method == 'POST':
            # POST请求：从JSON数据中获取测试用例
            data = request.get_json()
            if not data or 'testcases' not in data:
                return jsonify({'error': '请提供testcases参数'}), 400
            testcases = data['testcases']

        elif request.method == 'GET':
            # GET请求：从URL参数中获取测试用例
            testcases_param = request.args.get('testcases', '')
            if not testcases_param:
                return jsonify({'error': '请提供testcases参数'}), 400
            # 支持逗号分隔的测试用例列表
            testcases = [tc.strip() for tc in testcases_param.split(',') if tc.strip()]

        if not isinstance(testcases, list) or len(testcases) == 0:
            return jsonify({'error': '测试用例列表不能为空'}), 400

        # 验证测试用例是否存在
        available_testcases = get_available_testcases()
        invalid_cases = [tc for tc in testcases if tc not in available_testcases]
        if invalid_cases:
            return jsonify({
                'error': f'无效的测试用例: {invalid_cases}',
                'available_testcases': list(available_testcases.keys())
            }), 400

        # 生成批量任务ID
        batch_id = f"batch_{request.method.lower()}_{int(time.time())}"

        # 构建单个命令（run.py 支持多个参数）
        command = f"python run.py {' '.join(testcases)}"

        # 在后台线程中执行命令 - 使用简化版避免阻塞
        thread = threading.Thread(target=execute_command_simple, args=(batch_id, command))
        thread.daemon = True
        thread.start()

        return jsonify({
            'message': f'开始批量执行 {len(testcases)} 个测试用例',
            'batch_id': batch_id,
            'testcases': testcases,
            'command': command,
            'status_url': f'/status/{batch_id}',
            'start_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'method': request.method
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/batch/all', methods=['GET', 'POST'])
def execute_all_testcases():
    """执行所有可用的测试用例（实时扫描）- 支持GET和POST请求"""
    available_testcases = get_available_testcases()
    testcases = list(available_testcases.keys())
    batch_id = f"batch_all_{int(time.time())}"

    # 构建单个命令（run.py 支持多个参数）
    command = f"python run.py {' '.join(testcases)}"

    # 在后台线程中执行命令 - 使用简化版避免阻塞
    thread = threading.Thread(target=execute_command_simple, args=(batch_id, command))
    thread.daemon = True
    thread.start()

    return jsonify({
        'message': f'开始执行所有 {len(testcases)} 个测试用例',
        'batch_id': batch_id,
        'testcases': testcases,
        'command': command,
        'status_url': f'/status/{batch_id}',
        'start_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    })

@app.route('/testcases', methods=['GET', 'POST'])
def get_available_testcases_api():
    """获取所有可用的测试用例（实时扫描）- 支持GET和POST请求"""
    available_testcases = get_available_testcases()
    return jsonify({
        'available_testcases': available_testcases,
        'count': len(available_testcases),
        'scan_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    })

@app.route('/monitor', methods=['GET', 'POST'])
def monitor_page():
    """任务监控页面"""
    try:
        # 使用当前文件所在目录的monitor.html
        monitor_file = os.path.join(os.path.dirname(__file__), 'monitor.html')
        with open(monitor_file, 'r', encoding='utf-8') as f:
            return f.read()
    except FileNotFoundError:
        return jsonify({'error': '监控页面文件不存在'}), 404

@app.route('/health', methods=['GET', 'POST'])
def health_check():
    """健康检查接口 - 支持GET和POST请求"""
    # 测试日志记录
    logs.info("健康检查接口被调用")
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        'service': 'Python项目Web服务'
    })

def start_web_service(silent=False, restart=False, daemon=False, daemon_child=False):
    """启动Web服务"""
    # 注释：已改用统一的logs模块，不再需要初始化logger
    import logging  # 确保logging在函数作用域内可用

    # 如果是静默模式，禁用所有控制台输出
    if silent:
        import sys

        # 重定向标准输出和标准错误到空设备
        if os.name == 'nt':  # Windows
            devnull = open('nul', 'w')
        else:  # Unix/Linux
            devnull = open('/dev/null', 'w')
        sys.stdout = devnull
        sys.stderr = devnull

        # 禁用logs模块的控制台输出
        logger = logging.getLogger('unit_tools.log_util.recordlog')
        # 移除所有StreamHandler，只保留文件处理器
        for handler in logger.handlers[:]:
            if isinstance(handler, logging.StreamHandler) and not hasattr(handler, 'baseFilename'):
                logger.removeHandler(handler)

    try:
        # 先检查是否有现有进程（无论是否守护进程模式）
        existing_pid = check_existing_process()
        if existing_pid:
            if restart:
                logs.info(f"检测到现有服务 (PID: {existing_pid})，正在重启...")
            else:
                # 检测到现有服务时直接停止并启动新服务
                logs.info(f"检测到现有服务 (PID: {existing_pid})，正在停止旧服务并启动新服务...")

            if not stop_existing_process():
                logs.error("无法停止现有服务，启动失败")
                return False

            logs.info("旧服务已停止，正在启动新服务...")
            # 等待一下确保进程完全停止
            time.sleep(1)

        # 如果是守护进程模式且不是子进程，启动守护进程
        if daemon and not daemon_child:
            if os.name == 'nt':
                # Windows系统
                logs.info("启动Windows后台服务...")
                daemon_pid = run_as_windows_service()
                logs.info(f"后台服务已启动，PID: {daemon_pid}")
                return True
            else:
                # Unix/Linux系统
                logs.info("启动守护进程...")
                daemonize()
                logs.info("守护进程已启动")

        # 如果不是守护进程模式，继续正常启动流程
        if daemon_child or not daemon:
            if restart:
                logs.info(f"检测到现有服务 (PID: {existing_pid})，正在重启...")
            else:
                # 检测到现有服务时直接停止并启动新服务
                logs.info(f"检测到现有服务 (PID: {existing_pid})，正在停止旧服务并启动新服务...")

            if not stop_existing_process():
                logs.error("无法停止现有服务，启动失败")
                return False

            logs.info("旧服务已停止，正在启动新服务...")
            # 等待一下确保进程完全停止
            time.sleep(1)

        # 写入新的PID文件
        write_pid_file()

        # 获取本机IP地址
        try:
            hostname = socket.gethostname()
            ip_address = socket.gethostbyname(hostname)
        except:
            ip_address = "127.0.0.1"

        # 记录启动信息到日志
        logs.info("="*50)
        logs.info("Python项目Web服务已启动!")
        logs.info("="*50)
        logs.info("访问地址:")
        logs.info(f"  主页: http://{ip_address}:5000/")
        logs.info(f"  监控页面: http://{ip_address}:5000/monitor")
        logs.info(f"  执行demo_test示例: http://{ip_address}:5000/project/demo_test")
        logs.info(f"  查看可用测试用例: http://{ip_address}:5000/testcases")
        logs.info("="*50)
        logs.info(f"服务PID: {os.getpid()}")
        logs.info("按 Ctrl+C 停止服务")
        logs.info("="*50)

        # 如果不是静默模式，也输出到控制台
        if not silent:
            logs.info("="*50)
            logs.info("Python项目Web服务已启动!")
            logs.info("="*50)
            logs.info("访问地址:")
            logs.info(f"  主页: http://{ip_address}:5000/")
            logs.info(f"  监控页面: http://{ip_address}:5000/monitor")
            logs.info(f"  执行demo_test示例: http://{ip_address}:5000/project/demo_test")
            logs.info(f"  查看可用测试用例: http://{ip_address}:5000/testcases")
            logs.info("="*50)
            logs.info(f"服务PID: {os.getpid()}")
            logs.info("按 Ctrl+C 停止服务")
            logs.info("="*50)

        # 配置Flask应用的日志，确保使用我们的日志配置
        app.logger.handlers.clear()  # 清除Flask默认的日志处理器
        app.logger.propagate = False   # 不传播到根日志记录器

        # 让Flask使用我们的日志记录器
        app.logger.addHandler(logs.handlers[0])  # 添加我们的日志处理器
        app.logger.setLevel(logging.INFO)

        # 启动Flask应用
        # 在生产环境中禁用debug模式，减少控制台输出
        app.run(host='0.0.0.0', port=5000, debug=False, use_reloader=False)

        return True

    except KeyboardInterrupt:
        logs.info("收到停止信号，正在关闭服务...")
        return True
    except Exception as e:
        logs.error(f"启动Web服务失败: {e}")
        return False

if __name__ == '__main__':
    import argparse

    parser = argparse.ArgumentParser(description='Python项目Web服务')
    parser.add_argument('--silent', action='store_true', help='静默模式，不输出到控制台')
    parser.add_argument('--restart', action='store_true', help='重启服务（停止现有进程并启动新进程）')
    parser.add_argument('--daemon', action='store_true', help='以守护进程模式运行（后台运行）')
    parser.add_argument('--daemon-child', action='store_true', help='内部参数，用于Windows守护进程子进程')

    args = parser.parse_args()

    start_web_service(
        silent=args.silent,
        restart=args.restart,
        daemon=args.daemon,
        daemon_child=args.daemon_child
    )
