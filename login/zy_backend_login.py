"""
@Project    :zy_api_auto
@File       :zy_backend_login.py
@IDE        :PyCharm
<AUTHOR>
@Date       :2025/3/17 16:10
"""
from unit_tools.encrypt import md5
import requests
from configs.setting import test_users, api_env_dev
from unit_tools.handle_data.configParse import ConfigParse
from unit_tools.log_util.recordlog import logs
from unit_tools.singleton import singleton


@singleton
class ZyLogin:
    """
    中影后台登录接口，请求头必须带refer、cookie
    """
    host = ConfigParse.get_host('backend')
    headers = {
        # "Host": "*************:40",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "Content-Type": "application/x-www-form-urlencoded",
        # "X-Requested-With": "XMLHttpRequest",
        "Referer": f"http://{host}/login.html",  # 防盗链一定要携带，不然登录报错
    }
    session = requests.session()
    session.headers.update(headers)

    def __init__(self, user=test_users[0], password=test_users[1]):
        self.user = user
        self.password = password

    def get_uuid(self):
        try:
            url = self.host + "/proxy/cloud/platform/loadVcode"
            return self.session.get(url=url).json()['data']['LV_UUID']
        except:
            logs.error("请求失败！请检查网络")
            return 0

    def login(self):
        uid = self.get_uuid()
        if uid:
            try:
                url = self.host + "/proxy/cloud/platform/login"
                data = {
                    'passport': self.user,  # 账号 WEI
                    # 密码 p 是md5加密  o是明文密码123456  y是上个接口获取的uuid
                    'password': md5(md5(self.password) + str(uid)),
                    'uuid': str(uid)
                }
                # print(data)
                res = self.session.post(url=url, data=data)
                logs.info(f"登录成功！{res.json()}")
                # 把token返回,测试发现一定需要session请求才能正常访问其他接口
                # return res.headers['Set-Cookie'].split(';')[0]
                return self.session
            except:
                logs.error("登录失败！请检查参数是否正确")

    def tes_post(self):
        self.login()
        url = f'{self.host}/proxy/card/site/benefitcard/v1/queryBCTypes'
        data = {
            'pagination': {
                'pageNumber': 1,
                'pageSize': 50
            }
        }
        # url_encoded_data = 'pagination=%7B%22pageNumber%22%3A1%2C%22pageSize%22%3A50%7D'
        from unit_tools.params_util import parse_params
        url_encoded_data = parse_params(data)
        response = self.session.post(url, data=url_encoded_data)
        return response.json()

session = ZyLogin().login()

if __name__ == '__main__':
    zy = ZyLogin()
    # print(zy.login())
    print(zy.tes_post())
