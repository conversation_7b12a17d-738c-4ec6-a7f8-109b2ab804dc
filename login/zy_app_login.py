"""
@Project    :zy_api_auto
@File       :zy_app_login.py
@IDE        :PyCharm
<AUTHOR>
@Date       :2025/3/17 16:10
"""
import json
import random
import requests
from configs.setting import phone, api_env_dev, channel, key
from unit_tools.db_connector.connectMysql import ConnectMysql
from unit_tools.debugtalk import DebugTalk
from unit_tools.encrypt import md5
from unit_tools.handle_data.configParse import ConfigParse
from unit_tools.log_util.recordlog import logs
from unit_tools.params_util import parse_params
from unit_tools.time_util import TimeUtil
from unit_tools.singleton import singleton


@singleton
class ZyAppLogin:
    """
    app登录，获取token
    """

    def __init__(self):
        # 验证码登录
        self.req = DebugTalk
        self.base_url = ConfigParse.get_host('app')
        # #############---请求头里不要加host 有时候没注意区分环境反而会报错------++++++********
        self.headers = {
            # "User-Agent": "Android/HUAWEI-BRA-AL00-marlin-android9-2.49.0-1080*1920-1080.0*1920.0",
            # "User-Agent": "Android/OPPO-PCRT00-marlin-android9-2.51.0-1080*1920-1080.0*1920.0",
            "Content-Type": "application/x-www-form-urlencoded",
            # "Connection": "Keep-Alive",
            # "Accept-Encoding": "gzip"
        }
        self.req.extract_data({f'headers': f'$.headers'}, {'headers': self.headers})
        self.conn = ConnectMysql()

    def login(self):
        """
        登录,手动等待验证码录入
        :return:
        """
        if api_env_dev:
            t = self.req.timestamp()
            url = 'https://ownapi.cfc.com.cn/api/distributer/v1/login'
            h = {
                # "User-Agent": "Android/HUAWEI-JAD-AL50-HWJAD-android12-2.51.0-1200*2000-1228.0*2578.0",
                "Content-Type": "application/x-www-form-urlencoded"
            }
            data = {
                'param': {
                    "code": channel,
                    "mobile": phone,
                    "nickName": "",
                    "imageUrl": "",
                    "timestamp": t,
                    "sign": md5(channel + phone + t + key)
                }
            }
            data = parse_params(data)
            res = requests.post(url, data=data, headers=h)
            return res.json()
        else:
            try:
                # 测试环境直接数据库插入固定验证码验证码123456
                sms_code = 123456
                # 生成18位随机数
                sms_id = int(''.join(str(random.randint(0, 9)) for _ in range(18)))
                current_time = TimeUtil.get_current_time()
                insert_sql = f"INSERT INTO `wei_sms_code_log` (`id`, `phone`, `channel_code`, `code`, `code_status`, `type`, `create_by`, `create_date`, `update_by`, `update_date`) VALUES ({sms_id}, '{phone}', '{channel}', '{sms_code}', 0, 1, NULL, '{current_time}', NULL, '{current_time}');"
                self.conn.insert(insert_sql)
                param = {
                    "head": self.req.head('login'),
                    # "body": {"mode":"7","mobile":"${mobile}","verifyCode":"037089"}
                    "body": {
                        "mode": "7",
                        "features": "key0:false,key1:false,key2:false,key3:false,key4:false,key5:false,key6:true",
                        "password": "D41D8CD98F00B204E9800998ECF8427E",
                        "verifyCode": f"{sms_code}",
                        "pictureCode": "",
                        "mobile": f"{phone}",
                        "userId": "",
                        "username": ""
                    }
                }
                # print(param)
                json_param_value = json.dumps(param, separators=(',', ':'))
                # 登录需要带上UUID，否则会报--设备验证不通过！
                payload = {
                    # 'zip': "false",
                    'uuid': "MDAwMDAwMDAzYWE5MDdjNWZmZmZmZmZmODU2YjE3MjAtMnVNWSs4RXg5cHlxcEFJdFI4MGgrTmVzMFRYdkpYRE9HaXo1VG5qT2dmTT0=",
                    # 'version': "129",
                    'param': json_param_value,
                }
                response = requests.post(self.base_url, data=payload, headers=self.headers)
                # {'head': {'errCode': '1002', 'errMsg': '设备验证不通过！', 'tradeId': 'login'
                if "token" in response.text:
                    logs.info(f'【++登录成功++】{response.json()}')
                    return response.json()
                else:
                    logs.error(f'登录失败--{response.text}')
                # 不管是否登录成功后删除插入的验证码
                delete_sql = f"DELETE FROM `wei_sms_code_log` WHERE id = {sms_id};"
                self.conn.delete(delete_sql)
            except Exception as e:
                logs.error(f'登录过程发生异常: {str(e)}')

    def write_token(self):
        """
        把token写入文件
        :return:
        """
        login_info = self.login()
        self.req.extract_data({f'login_info': f'$.login_info'}, {'login_info': login_info['body']})
        self.req.extract_data({f'token': f'$.token'}, login_info['body']['token'])

    def test_post(self):
        self.write_token()
        token = self.req.get_extract_data('token')
        # 测试点赞
        param = {
            "head": {
                "tradeId": "updateNewsCommentZan",
                "timestamp": self.req.timestamp(),
                "validCode": self.req.validCode(),
                "appKey": channel
            },
            "body": {
                "sign": self.req.sign('updateNewsCommentZan'),
                "commentId": self.req.get_extract_data('newsId'),
                "type": "1",
                "userId": self.req.get_extract_data('login_info', 'userId'),
            }
        }
        # param_str = json.dumps(param, separators=(',', ':'))
        payload = {
            'param': param
        }
        # print(payload)
        data = parse_params(payload)
        data += f'&token={token}'
        # print(data)
        response = requests.post(self.base_url, data=data, headers=self.headers)
        print(response.text)  # {'head': {'errCode': '0', 'errMsg': '操作成功。'


if __name__ == '__main__':
    zy = ZyAppLogin()
    # print(zy.send_sms())
    # print(zy.login())
    # zy.test_post()
    zy.write_token()
