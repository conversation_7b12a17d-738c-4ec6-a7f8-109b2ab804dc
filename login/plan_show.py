# -*- coding:utf-8 -*-
"""
@Project    :zy_ApiAuto
@File       :plan_show.py
@IDE        :PyCharm
<AUTHOR>
@Date       :2025/5/22 10:39
"""
import time
import requests
from playwright.sync_api import sync_playwright
from unit_tools.handle_data.configParse import ConfigParse
from unit_tools.log_util.recordlog import logs
from unit_tools.params_util import parse_params
from unit_tools.time_util import time_util


class PlanShow:
    """
    影片排期  只允许测试环境排片，所以，只用鼎新账号配置，写死了影片名称
    """

    def __init__(self):
        self.filmName = '再见，李可乐'
        # self.host = ConfigParse.get_host('backend')
        self.host = 'http://*************:40'
        self.headers = {
            'Host': "*************:40",
            'User-Agent': "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
            'Accept-Encoding': "gzip, deflate",
            'X-Requested-With': "XMLHttpRequest",
            'Origin': f"{self.host}",
            'Referer': f"{self.host}/frame.html",
            'Accept-Language': "zh-CN,zh;q=0.9",
            'Cookie': "X-Auth-Token=ad79a7317f620dd2e24fc984d3230406",
            # 'Cookie': f"X-Auth-Token={self.login()}"
        }
        self.browser = sync_playwright().start().chromium.launch(headless=False)
        self.context = self.browser.new_context()
        self.page = self.context.new_page()
        self.login()
        # print(self.headers)

    def login(self):
        self.page.goto(f"{self.host}/login.html")

        # 清空输入框内容
        self.page.get_by_placeholder("请输入用户名").click()
        self.page.get_by_placeholder("请输入用户名").fill("")
        self.page.get_by_placeholder("请输入用户名").fill("dingxin")
        self.page.get_by_placeholder("请输入密码").click()
        self.page.get_by_placeholder("请输入密码").fill("")
        self.page.get_by_placeholder("请输入密码").fill("123456")
        self.page.get_by_text("登录").click()
        self.page.get_by_role("link", name="基础模块").click()
        # 获取登录用户的cookie
        self.headers['Cookie'] = f"X-Auth-Token={self.page.context.cookies()[0]['value']}"

    def logout(self):
        self.page.locator("span").filter(has_text="鼎新").nth(1).click()
        self.page.get_by_text("退出").click()
        self.context.close()
        self.browser.close()

    def findHallList(self):
        """
        获取影厅列表
        :return:
        """
        url = f"{self.host}/proxy/cloud-n/cnfs/site/show/findHallList"

        response = requests.post(url, headers=self.headers)
        # 68de26c5-bf2e-4674-8f5d-edd2cf64d87b  001106122023
        return response.json()['data'][2]['hallId']

    def findExtShow(self):
        """
        编辑排期，获取价格
        :return:
        """
        url = f"{self.host}/proxy/cloud-n/cnfs/site/show/findExtShow"
        payload = {
            'filmCode': "001106122023",
            'hallId': "68de26c5-bf2e-4674-8f5d-edd2cf64d87b"
        }
        response = requests.post(url, data=payload, headers=self.headers)
        return int(response.json()['data']['minPrice'])

    def save(self):
        """
        保存排期
        :return:
        """
        url = f"{self.host}/proxy/cloud-n/cnfs/site/show/save"
        day = time_util.getDate(7)
        price = self.findExtShow()
        payload = {
            'bizDate': day,
            'filmCode': "001106122023",
            'hallId': "68de26c5-bf2e-4674-8f5d-edd2cf64d87b",
            'minPrice': price,
            'price': price,
            'serviceFee': "0",
            'showTime': f"{day} {time_util.get_h_m_s()}",
            'showAreaPrices[0].code': "1",
            'showAreaPrices[0].id': "",
            'showAreaPrices[0].showCode': "",
            'showAreaPrices[0].name': "默认区域",
            'showAreaPrices[0].price': "0"
        }
        response = requests.post(url, data=payload, headers=self.headers)
        logs.info('保存排期成功')
        # print(response.json())
        return response.json()

    def submitAudit(self, ids):
        """
        提交审核
        :return:
        """
        url = f"{self.host}/proxy/cloud-n/cnfs/site/show/submitAudit"
        payload = {
            'ids': ids,
        }
        response = requests.post(url, data=payload, headers=self.headers)

        logs.info('提交审核')

    def get_show_list(self, auditStatus=None, status=None):
        """
        获取排期列表
        :return:
        """
        url = f"{self.host}/proxy/cloud-n/cnfs/site/show/page"

        payload = {
            'current': "1",
            'size': "20",
            'auditStatus': auditStatus,  # 2待审核
            'filmName': self.filmName,
            'status': status,  # 1启用
            'showTimeStart': time_util.getDate(7),
            'showTimeEnd': time_util.getDate(7),
        }

        response = requests.post(url, data=payload, headers=self.headers)

        return response.json()

    def stop_show(self, ids):
        """
        停用排期
        :return:
        """
        url = f"{self.host}/proxy/cloud-n/cnfs/site/show/stop"

        payload = {
            'ids': ids
        }

        response = requests.post(url, data=payload, headers=self.headers)

        logs.info(f'停用排期----{ids}')

    def delete_show(self, ids):
        """
        删除排期
        :return:
        """
        url = f"{self.host}/proxy/cloud-n/cnfs/site/show/del"

        payload = {
            'id': ids
        }

        response = requests.post(url, data=payload, headers=self.headers)

        logs.info(f'删除排期----{ids}')

    def submitSuccess(self):
        """
        审核通过
        :return:
        """
        ids = self.get_show_list(auditStatus=2)['data']['records'][0]['id']
        url = f"{self.host}/proxy/cloud-n/cnfs/site/show/submitSuccess"
        payload = {
            'ids': ids
        }

        response = requests.post(url, data=payload, headers=self.headers)

        logs.info(f'审核通过----{ids}')

    def queryShows(self):
        """
        查询排期
        :return:
        """
        url = f"{self.host}/proxy/price/site/show/v1/queryShows"

        payload = {
            "pagination": {
                "pageNumber": 1,
                "pageSize": 50,
                "queryParams": {
                    "cinemaId": "280c54a6-fe12-40c4-8b5a-b455a5907826",
                    "code": "",
                    "filmName": self.filmName,
                    "hallName": "",
                    "cinemaCode": "",
                    "status": "1",
                    "startDate": time_util.getDate(7),
                    "endDate": time_util.getDate(7),
                    "bookType": "",
                    "synmd5": ""
                }
            }
        }
        request_params = parse_params(payload)
        # print(request_params)
        response = requests.post(url, data=request_params, headers=self.headers)
        return response.json()['data']['records'][0]['showId']

    def syncShows(self):
        """
        同步影院排期
        :return:
        """
        url = f"{self.host}/proxy/price/site/show/v1/syncShows"

        payload = {
            'cinemaId': "280c54a6-fe12-40c4-8b5a-b455a5907826"
        }

        response = requests.post(url, data=payload, headers=self.headers)
        logs.info('同步影院排期')
        return response.text

    def batchGenChannelShows(self):
        """
        批量重新生成渠道排期
        :return:
        """
        url = "http://*************:40/proxy/price/site/show/v1/batchGenChannelShows"

        payload = {
            'showIds': [
                {
                    "showId": self.queryShows()
                }
            ]
        }
        payload = parse_params(payload)
        response = requests.post(url, data=payload, headers=self.headers)
        logs.info('批量重新生成渠道排期')
        # print(response.text)

    def do_plan_show(self):
        data = self.save()
        if '该场次排期时间重叠' in str(data):
            logs.warning('------该场次排期时间重叠,重新生成渠道排期------')
            self.batchGenChannelShows()
        else:
            self.submitAudit(data['data'])
            self.submitSuccess()
            self.syncShows()
            # 等待排期同步
            time.sleep(30)
            self.batchGenChannelShows()
        time.sleep(30)
        # ---------------------
        self.logout()


if __name__ == '__main__':
    plan = PlanShow()
    # print(plan.get_random_time())
    # # ##提交审批
    # plan.submitAudit()
    # # ##审核通过
    # plan.submitSuccess()
    plan.queryShows()
    # plan.syncShows()
    # plan.do_plan_show()
