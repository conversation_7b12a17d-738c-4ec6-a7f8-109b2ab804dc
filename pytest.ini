[pytest]
addopts = -p no:warnings -s -v --alluredir=./report/result/temp --clean-alluredir
;addopts = -v --reruns 5 --reruns-delay 2

# 日志配置 - 禁用pytest自带的日志输出，避免重复打印
log_cli = false
log_cli_level = INFO
log_cli_format = %(levelname)s - %(asctime)s - %(filename)s:%(lineno)d -[%(module)s:%(funcName)s] - %(message)s

# 文件日志配置
log_file_level = INFO
log_file_format = %(levelname)s - %(asctime)s - %(filename)s:%(lineno)d -[%(module)s:%(funcName)s] - %(message)s

; 可以命令行入参中取到
;testpaths = ./testcase/play

python_files = test_*.py

python_classes = Test*

python_functions = test_*

# 注册自定义标记
markers =
    last
    second
    first
    P1
    P2
    P3
