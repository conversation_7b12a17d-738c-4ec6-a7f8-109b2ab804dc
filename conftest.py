import time
import warnings
from datetime import timedelta
import pytest
import allure
import logging
import io
import sys
from configs.setting import api_env_dev
from login.zy_app_login import ZyAppLogin
from unit_tools.log_util.recordlog import logs
from unit_tools.time_util import time_util
from unit_tools.handle_data.yaml_handler import clear_yaml, write_yaml, extract_data
import requests


# 全局日志捕获器
class AllureLogCapture:
    def __init__(self):
        self.log_capture_string = io.StringIO()
        self.log_handler = logging.StreamHandler(self.log_capture_string)
        self.log_handler.setLevel(logging.INFO)
        # 设置日志格式，包含时间戳
        formatter = logging.Formatter(
            '%(levelname)s - %(asctime)s - %(filename)s:%(lineno)d -[%(module)s:%(funcName)s] - %(message)s'
        )
        self.log_handler.setFormatter(formatter)

    def start_capture(self):
        # 只捕获测试相关的日志记录器，避免与Web服务日志冲突
        test_logger = logging.getLogger('unit_tools.log_util.recordlog')
        test_logger.addHandler(self.log_handler)
        # 不设置propagate，避免重复打印

    def stop_capture_and_attach(self):
        # 获取捕获的日志
        log_contents = self.log_capture_string.getvalue()

        # 同时从日志文件中获取当前测试的日志
        file_logs = self.get_test_logs_from_file()

        # 合并日志内容
        all_logs = []
        if file_logs:
            all_logs.extend(file_logs)
        if log_contents.strip():
            all_logs.extend(log_contents.split('\n'))

        if all_logs:
            # 过滤掉空行和重复行，确保格式整洁
            filtered_logs = []
            seen_logs = set()
            for line in all_logs:
                line = line.strip()
                if line and line not in seen_logs:
                    # 清理ANSI颜色代码
                    import re
                    clean_line = re.sub(r'\x1b\[[0-9;]*m', '', line)
                    filtered_logs.append(clean_line)
                    seen_logs.add(line)

            if filtered_logs:
                # 附加到Allure报告
                allure.attach(
                    '\n'.join(filtered_logs),
                    name="📋 测试执行日志 (完整记录)",
                    attachment_type=allure.attachment_type.TEXT
                )

        # 清理
        test_logger = logging.getLogger('unit_tools.log_util.recordlog')
        if self.log_handler in test_logger.handlers:
            test_logger.removeHandler(self.log_handler)
        self.log_capture_string.close()

    def get_test_logs_from_file(self):
        """从日志文件中获取当前测试的日志"""
        try:
            import os
            from datetime import datetime

            # 获取今天的日志文件
            today = datetime.now().strftime('%Y%m%d')
            log_file = f'logs/test.{today}.log'

            if not os.path.exists(log_file):
                return []

            with open(log_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            # 查找当前测试相关的日志
            test_logs = []
            test_start_found = False

            # 从后往前查找最近的测试日志
            for i in range(len(lines) - 1, max(0, len(lines) - 100), -1):
                line = lines[i].strip()

                # 查找测试结束标记
                if '---------------接口测试结束---------------' in line:
                    test_logs.insert(0, line)
                    test_start_found = True
                    continue

                # 如果找到了结束标记，继续收集日志直到找到开始标记
                if test_start_found:
                    test_logs.insert(0, line)

                    # 查找测试开始标记
                    if '---------------接口测试开始---------------' in line:
                        break

            return test_logs

        except Exception as e:
            logs.warning(f"从文件获取测试日志失败: {str(e)}")
            return []

@pytest.fixture(scope="function", autouse=True)
def capture_logs_for_allure():
    """为每个测试函数捕获日志并附加到Allure报告"""
    log_capture = AllureLogCapture()
    log_capture.start_capture()
    yield
    log_capture.stop_capture_and_attach()

@pytest.fixture(scope="session", autouse=True)
def clear_extract():
    # 取消requests警告
    requests.packages.urllib3.disable_warnings()
    warnings.filterwarnings('ignore')
    do_test_before()


@pytest.fixture(scope="function", autouse=True)
def function_wait():
    """每个函数执行都开始结束等待2秒，避免操作过快"""
    time.sleep(0.75)
    yield
    time.sleep(0.75)


def do_test_before():
    # #清空yaml文件数据
    clear_yaml()
    # 登录把token写入文件
    ZyAppLogin().write_token()


def format_duration(seconds):
    """将秒数转换为时分秒格式"""
    return str(timedelta(seconds=seconds)).split('.')[0]


def pytest_terminal_summary(terminalreporter, exitstatus, config):
    """
    Pytest框架里面预定义的钩子函数，用于在测试结束后自动化收集测试结果
    :param terminalreporter:
    :param exitstatus:
    :param config:
    :return:
    """
    # 获取测试用例统计信息
    testcase_total = terminalreporter._numcollected
    passed_num = len(terminalreporter.stats.get('passed', []))
    failed_num = len(terminalreporter.stats.get('failed', []))
    error_num = len(terminalreporter.stats.get('error', []))
    skip_num = len(terminalreporter.stats.get('skipped', []))

    # 如果收集阶段失败，尝试从实际执行的测试数量计算总数
    if testcase_total == 0:
        # 计算实际执行的测试总数
        actual_total = passed_num + failed_num + error_num + skip_num
        if actual_total > 0:
            testcase_total = actual_total
            logs.info(f"收集阶段失败，使用实际执行数量作为总数: {testcase_total}")

    duration = round(time.time() - terminalreporter._sessionstarttime, 2)
    formatted_duration = format_duration(duration)

    # 统计通过率、失败率、错误率
    pass_rate = f"{(passed_num / testcase_total) * 100:.0f}%" if testcase_total > 0 else "N/A"
    fail_rate = f"{(failed_num / testcase_total) * 100:.0f}%" if testcase_total > 0 else "N/A"
    error_rate = f"{(error_num / testcase_total) * 100:.0f}%" if testcase_total > 0 else "N/A"
    env = "生产环境" if api_env_dev else "测试环境"
    summary = f"""
    {env}！！！！！
    详情请查看附件~~~
    自动化测试结果简单汇总，通知如下，执行结果如下：
    测试用例总数：{testcase_total}  
    测试用例通过数：{passed_num}
    通过率：{pass_rate}
    测试用例失败数：{failed_num}
    失败率：{fail_rate}
    错误数量：{error_num}
    错误率：{error_rate}       
    跳过执行数量：{skip_num}
    执行总时长：{duration}s ({formatted_duration})
    """
    logs.info(summary)
    write_yaml({'summary': summary})

"""
# 使用钩子函数处理响应数据的例子
# 使用 item 对象存储响应数据，用例执行完成钩子函数处理数据
def test_get_user_better(api_client, request):
    response = api_client.get_user(123)
    assert response["status"] == "success"
    # 将响应数据存储到测试项中
    request.node.response_data = response
    return response


@pytest.hookimpl(tryfirst=True, hookwrapper=True)
def pytest_runtest_makereport(item, call):
    outcome = yield
    report = outcome.get_result()

    if report.when == "call" and hasattr(item, "response_data"):
        response_data = item.response_data
        print(f"\n处理测试用例 {item.name} 的响应数据:")

        # 响应数据处理逻辑
        if "data" in response_data:
            print(f"用户邮箱: {response_data['data']['email']}")

            # 这里可以添加更复杂的处理，如数据验证、存储等
            validate_email_format(response_data['data']['email'])


def validate_email_format(email):
    # 简单的邮箱格式验证
    assert "@" in email, f"无效的邮箱格式: {email}"
    print(f"邮箱验证通过: {email}")"""


'''

# !/usr/bin/env python
# -*- coding: utf-8 -*-
"""
 -------------------------------------------------
    File Name:     collect_hooks.py
    Description:   文件收集相关钩子🪝
 -------------------------------------------------
"""
import yaml
from pathlib import Path
from typing import Generator
import pytest
from unit_tools.log_util.recordlog import logs


@pytest.fixture(autouse=True)
def print_info():
    logs.info('---------------接口测试开始---------------')
    yield
    logs.info('---------------接口测试结束---------------')


def pytest_collect_file(parent: pytest.Collector, path: Path) -> pytest.File | None:
    """
    pytest 的文件收集钩子函数。
    用于在 pytest 的收集阶段判断如何处理目录中的文件。
    :param parent: pytest 的父节点（通常是目录节点）。
    :param path: 当前文件路径（类型为 pathlib.Path）。
    :return: 如果是 .yaml 文件，返回自定义的 YamlFile 收集器；否则返回 None。
    """
    if path.suffix == ".yaml":  # 使用 pathlib.Path 的 suffix 检查文件扩展名
        logs.info(f"Collecting YAML file: {path.name}")
        return YamlFile.from_parent(parent, path=path)


class YamlFile(pytest.File):
    """
    自定义 YAML 文件收集器，用于解析 YAML 文件并生成测试用例项。
    """

    def collect(self) -> Generator[pytest.Item, None, None]:
        """
        解析 YAML 文件并生成测试用例项。
        :return: 生成器对象，每个对象表示一个测试用例（YamlItem）。
        """
        try:
            with self.path.open() as f:
                data = yaml.safe_load(f)  # 使用 PyYAML 加载 YAML 文件内容
        except yaml.YAMLError as e:
            raise pytest.UsageError(f"Failed to parse YAML file {self.path}: {e}")
        filename = self.path.stem  # 获取文件名（不包括扩展名）
        # 遍历 YAML 文件中的 "test_cases" 列表
        for case in data.get("testCase", []):
            case_name = case.get("case_name")
            if not case_name:
                raise pytest.UsageError(f"Test case in {self.path} is missing a 'name' field.")
            # 使用文件名和用例名称组合成唯一的测试项名称
            full_case_name = f"{filename}::{case_name}"
            yield YamlItem.from_parent(self, name=full_case_name, case_data=case)


class YamlItem(pytest.Item):
    """
    自定义测试用例项，用于表示 YAML 文件中的单个测试用例。
    """

    def __init__(self, parent: pytest.Collector, name: str, case_data: dict):
        """
        初始化 YamlItem 实例。
        :param name: 测试用例名称。
        :param parent: 所属的 YAML 文件（YamlFile）。
        :param case_data: 测试用例的具体数据。
        """
        super().__init__(name, parent)  # 调用 pytest.Item 的构造方法
        self.case_data = case_data  # 保存测试用例数据

    def runtest(self):
        """
        执行测试用例的逻辑。
        这里模拟发送 HTTP 请求，并根据期望的状态码进行断言。
        """
        logs.info(f"Running test: {self.name}")


'''
