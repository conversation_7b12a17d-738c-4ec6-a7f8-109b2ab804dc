from requests import request
from unit_tools.handle_data.excel_utils import get_excel_case_data
from configs.setting import DIR_PATH
from unit_tools.handle_data.yaml_handler import get_extract_data
from unit_tools.log_util.recordlog import logs
from unit_tools.params_util import parse_params, parse_and_replace_variables as prv


def play_test(idx=-1):
    # 读取Excel文件
    excel_file = DIR_PATH + '/testcase/play/演出接口测试用例.xlsx'
    data = get_excel_case_data(excel_file)[idx]
    if data:
        data = prv(data)
        method = data['baseInfo']['method']
        url = data['baseInfo']['host'] + data['baseInfo']['url']
        headers = data['baseInfo']['headers']
        # 找到公共key
        ll = ['params', 'data', 'json']
        se = set(ll)
        common_keys = se & data['testCase'].keys()
        key = list(common_keys)[0]
        # params = parse_params(data['testCase'][key])
        params = data['testCase'][key]  # 演出接口入参不需要做特殊处理
        # 但是要做token的特殊处理在后面加上渠道编码
        params['token'] += f',{get_extract_data('login_info','code')}'
        playload = {
            'headers': headers,
            'method': method,
            'url': url,
            key: params,
        }
        logs.info(f'请求参数：\n{playload}')
        response = request(**playload)
        print(response.json())
    else:
        print('没有找到接口信息')


if __name__ == '__main__':
    play_test()
