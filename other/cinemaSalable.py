# -*- coding:utf-8 -*-
"""
@Project    :zy_ApiAuto
@File       :cinemaSalable.py
@IDE        :PyCharm
<AUTHOR>
@Date       :2025/6/25 17:25
@Description: 影院管理：起售、停售
"""
from configs.setting import zy_serve
from login.zy_backend_login import session
from unit_tools.log_util.recordlog import logs
from unit_tools.params_util import parse_params


class CinemaSalable:
    """
    影院管理：起售、停售
    """

    def __init__(self):
        self.host = zy_serve['backend']['host']

    def queryCinemas(self,cinemaName=None):
        """
        查询影院列表或者传入影院名称查询影院uuid
        :return: 返回查询到的影院uuid  b779bcf7-4da7-4a58-af53-5e0a7257eb68
        """
        url = f"{self.host}/proxy/base/site/ticket/v1/queryCinemas"

        payload = {
            'pagination': {
                "pageNumber": 1,
                "pageSize": 50,
                "queryParams": {
                    "cinemaCode": "",
                    "gbCode": "",
                    "cinemaName": cinemaName,
                    "status": "",
                    "provider": "",
                    "cinemaType": "",
                    "cinemaGroup": "",
                    "county": "",
                    "channelId": "",
                    "salable": "",
                    "ticketSetted": "",
                    "stopType": ""
                }
            }
        }
        payload = parse_params(payload)
        response = session.post(url, data=payload)

        print(response.text)

    def cinemaCloseSalable(self, cinemaId):
        """
        停售
        :return:
        """
        url = f"{self.host}/proxy/base/site/ticket/v1/cinemaCloseSalable"

        payload = {
            # 'cinemaId': "b779bcf7-4da7-4a58-af53-5e0a7257eb68"
            'cinemaId': cinemaId
        }
        response = session.post(url, data=payload)

        print(response.text)

    def cinemaOpenSalable(self, cinemaId):
        """
        起售
        :return:
        """
        url = f"{self.host}/proxy/base/site/ticket/v1/cinemaOpenSalable"

        payload = {
            'cinemaId': cinemaId
        }
        response = session.post(url, data=payload)

        print(response.text)

    def syncHall(self, cinemaId):
        """
        同步影厅
        :return:
        """

        url = f"{self.host}/proxy/base/site/ticket/v1/syncHall"

        payload = {
            'cinemaId': cinemaId
        }

        response = session.post(url, data=payload)

        print(response.text)

    def syncShows(self, cinemaId):
        """
        同步影院排期
        :return:
        """
        url = f"{self.host}/proxy/price/site/show/v1/syncShows"

        payload = {
            'cinemaId': cinemaId
        }

        response = session.post(url, data=payload)
        logs.info('同步影院排期')
        return response.text


if __name__ == '__main__':
    cinema = CinemaSalable()
    cinema.queryCinemas()
