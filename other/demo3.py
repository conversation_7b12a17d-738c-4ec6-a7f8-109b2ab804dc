import random
import string

from unit_tools.params_util import parse_params


def update_nested_dict(data, path, new_value):
    keys = path.split('.')  # 将路径字符串按 '.' 分割成键列表
    current = data  # 从根字典开始
    for key in keys[:-1]:  # 遍历到倒数第二个键
        current = current[key]  # 深入到下一层字典
    # 替换最后一个键对应的值
    current[keys[-1]] = new_value
    return data


# 原始数据
data = {
    "pagination": {
        "pageNumber": 1,
        "pageSize": 50,
        "queryParams": {
            "title": "",
            "source": "",
            "tag_id": "",
            "status": "1",
            "istop": "",
            "film_name": "",
            "startintime": "",
            "endinttime": "",
            "showtype": "",
            "startshowtime": "",
            "endshowttime": "",
            "min_program_video_status": "",
            "isplat": 0
        },
        "anotherSection": {
            "details": {
                "status": "3"
            }
        }
    }
}

# 替换值
path = "pagination.queryParams.status"
new_value = "666"

# 调用函数并打印结果
updated_data = update_nested_dict(data, path, new_value)
print(updated_data)

payload = {
    'showIds': [
        {
            "showId": "a956c395-a14b-431f-ab30-e1df4c2d79d4"
        }
    ]
}

request_params = parse_params(payload)
print(request_params)
'showIds=%5B%7B%22showId%22%3A%22a956c395-a14b-431f-ab30-e1df4c2d79d4%22%7D%5D'
# 获取随机4位字符串
random_str = ''.join(random.choices(string.ascii_letters + string.digits, k=4))
print(random_str)