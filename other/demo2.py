from collections import defaultdict

"""统计日志中关键词出现次数"""


def count_keywords(keywords, log_file):
    keyword_count = defaultdict(int)
    with open(log_file, 'r', encoding='utf-8') as f:
        for line in f:
            for keyword in keywords:
                if keyword in line:
                    keyword_count[keyword] += 1
    return keyword_count


# 使用示例
keywords = ["ERROR", "WARNING", "INFO"]
log_file = '../logs/test.20250401.log'
result = count_keywords(keywords, log_file)
print(result)

"""按时间范围过滤日志"""
from datetime import datetime


def filter_logs_by_time(log_file, start_time, end_time, output_file):
    start = datetime.strptime(start_time, "%Y-%m-%d %H:%M:%S")
    end = datetime.strptime(end_time, "%Y-%m-%d %H:%M:%S")
    with open(log_file, 'r') as f:
        logs = f.readlines()
    filtered_logs = []
    for log in logs:
        log_time_str = log.split()[0] + "" + log.split()[1]  # 假设时间戳在日志的前两部分
        log_time = datetime.strptime(log_time_str, "%Y-%m-%d %H:%M:%$")
        if start <= log_time <= end:
            filtered_logs.append(log)


    with open(output_file, 'w') as f:
        f.writelines(filtered_logs)
