import pymysql
from unit_tools.handle_data.configParse import ConfigParse
from unit_tools.log_util.recordlog import logs
from configs.setting import api_env_dev, phone, channel
from unit_tools.singleton import singleton


@singleton
class ConnectMysql:
    """
    连接MySQL数据库，进行增删改查操作
    """

    def __init__(self):
        self.conf = None if api_env_dev else ConfigParse().mysql_conn()
        if self.conf:
            try:
                self.conn = pymysql.connect(**self.conf)
                # 获取操作游标
                # cursor=pymysql.cursors.DictCursor：将数据库表字段显示，以key-value形式显示
                self.cursor = self.conn.cursor(cursor=pymysql.cursors.DictCursor)
                logs.info(f'成功连接到数据库：数据库ip：{self.conf.get("host")}')
            except Exception as e:
                logs.error(f"连接数据库失败，原因：{e}")
        else:
            logs.warning("生产不需要连接数据库，跳过连接数据库操作")

    def close(self):
        if self.conn and self.cursor:
            self.conn.close()
            self.cursor.close()
        return True

    def insert(self, sql):
        """
        插入数据库数据
        :param sql: 插入的SQL语句
        :return:
        """
        try:
            self.cursor.execute(sql)
            self.conn.commit()
            logs.info(f'插入数据库SQL--{sql}')
            logs.info('数据库数据插入成功')
        except Exception as e:
            logs.error(f'插入数据库数据出现异常，{e}')

    def query(self, sql, fetchall=False):
        """
        查询数据库数据
        :param sql: 查询的SQL语句
        :param fetchall: 查询全部数据，默认为False则查询单条数据
        :return:
        """
        try:
            logs.info(f'查询数据库SQL--{sql}')
            self.cursor.execute(sql)
            self.conn.commit()
            if fetchall:
                res = self.cursor.fetchall()
            else:
                res = self.cursor.fetchone()
            logs.info(f'数据查询成功{res}--{sql}')
            return res
        except Exception as e:
            logs.error(f'查询数据库内容出现异常，{e}')
        finally:
            self.close()

    def delete(self, sql):
        """
        删除数据库内容
        :param sql: 删除的SQL语句
        :return:
        """
        try:
            logs.info(f'删除数据库SQL--{sql}')
            self.cursor.execute(sql)
            self.conn.commit()
            logs.info('数据库数据删除成功')
        except Exception as e:
            logs.error(f'删除数据库数据出现异常，{e}')
        finally:
            self.close()


if __name__ == '__main__':
    conn = ConnectMysql()
    # conn1= ConnectMysql()
    # sql = "SELECT CODE FROM wei_sms_code_log WHERE phone ='18867134383' LIMIT 1;"
    # print(conn.query(sql))
    # print(conn1.query(sql))
    # # code = conn.query('SELECT code FROM `wei_sms_code_log` WHERE phone="18853652208" ORDER BY create_date desc LIMIT 1;')
    # # print(type(code))
    # # print(code['code'])
    # sms_code = 123456
    # sms_id = 123456654321666666
    # current_time = TimeUtil.get_current_time()
    insert_sql = f"INSERT INTO `wei_sms_code_log` (`id`, `phone`, `channel_code`, `code`, `code_status`, `type`, `create_by`, `create_date`, `update_by`, `update_date`) VALUES ({sms_id}, '{phone}', '{channel}', '{sms_code}', 0, 1, NULL, '{current_time}', NULL, '{current_time}');"
    # print(insert_sql)
    conn.insert(insert_sql)
