from sqlalchemy import create_engine
from unit_tools.handle_data.configParse import ConfigParse
from unit_tools.handle_data.excel_utils import parse_case
import pandas as pd
from unit_tools.log_util.recordlog import logs


def get_sql_connection():
    db_info = ConfigParse().mysql_conn()
    # conn = pymysql.connect(**db_info)  ## import pymysql 这个库会有警告
    conn = create_engine('mysql+pymysql://%s:%s@%s:%s/%s?charset=utf8' % (
        db_info['user'], db_info['password'],
        db_info['base_url'], db_info['port'], db_info['database']))
    logs.info(f"数据库{db_info['database']}----连接成功")
    return conn


def get_mysql_case_data_for_pandas(module):
    """
    读取mysql数据库相应模块的用例数据，返回pandas的DataFrame格式
    :param module: where 后面的关键字
    :return: 返回用例列表
    """
    conn = get_sql_connection()
    # sql = "select * from ts_api_case where module = '{}'".format(module)
    sql = "select * from ts_api_case where module = '{}'".format(module)
    logs.info(f"SQL----{sql}")
    df = pd.read_sql(sql, conn)
    # 将None值转换为0
    df = df.where(pd.notnull(df), 0)
    # print(df)
    return parse_case(df, module)


if __name__ == '__main__':
    # print(get_mysql_case_data_for_pandas('轻资产'))
    print(ConfigParse().mysql_conn())
