"""
@Project    :zy_app_api
@File       :encrypt.py
@IDE        :PyCharm
<AUTHOR>
@Date       :2025/4/17 15:03
"""
import base64
import hashlib
import hmac
from Crypto.Cipher import AES, PKCS1_OAEP
from Crypto.PublicKey import RSA
from Crypto.Util.Padding import pad, unpad


def md5(raw_string, is_upper=False):
    """
    md5加密
    :param raw_string:
    :param is_upper:
    :return:
    """
    md5_text = hashlib.md5(raw_string.encode('utf-8')).hexdigest()
    return md5_text.upper() if is_upper else md5_text


def sha256(raw_string, is_upper=False):
    """
    sha256加密
    :param raw_string:
    :param is_upper:
    :return:
    """
    sha256_hash = hashlib.sha256(raw_string.encode()).hexdigest()
    return sha256_hash.upper() if is_upper else sha256_hash


def hmac_sha256(raw_string, key, is_upper=False):
    """
    HMAC-SHA256加密
    :param raw_string:
    :param key:
    :param is_upper:
    :return:
    """
    hmac_sha256 = hmac.new(key, raw_string.encode(), hashlib.sha256).hexdigest()
    # print(hmac_sha256,'-------------')
    return hmac_sha256.upper() if is_upper else hmac_sha256


def hmac_sha1(raw_string, key, is_upper=False):
    """
    HMAC-SHA1加密
    :param raw_string:
    :param key:
    :param is_upper:
    :return:
    """
    # 把字符串key转成字节类型
    if isinstance(key, str):
        key = key.encode()
    signature = hmac.new(key, raw_string.encode(), hashlib.sha1).hexdigest()
    return signature.upper() if is_upper else signature


def base64_encode(raw_string, is_upper=False):
    """
    base64加密
    :param is_upper:
    :param raw_string:
    :return:
    """
    encoded_text = base64.b64encode(raw_string.encode('utf-8')).decode('utf-8')
    return encoded_text.upper() if is_upper else encoded_text


def base64_decode(raw_string):
    """
    base64解密
    :param raw_string:
    :return:
    """
    return base64.b64decode(raw_string.encode('utf-8')).decode('utf-8')


def aes_encrypt(e,t="ghjsdf89923"):
    """
    AES 加密（对称加密）
    :param raw_string:
    :return:
    """
    key = t.encode('utf-8')
    pad_length = 16 - (len(key) % 16)
    if pad_length < 16:
        key += bytes([pad_length] * pad_length)  # PKCS7填充

    # 处理明文并加密
    e_bytes = e.encode('utf-8')
    cipher = AES.new(key, AES.MODE_ECB)
    encrypted = cipher.encrypt(pad(e_bytes, AES.block_size))

    # 返回Base64字符串（与CryptoJS默认行为一致）
    return base64.b64encode(encrypted).decode('utf-8')


def aes_decrypt(e, t="ghjsdf89923"):
    """
    AES 解密（对称加密）
    :param ct_bytes:
    :return:
    """
    # 处理密钥：填充到16字节（AES-128）
    key = t.encode('utf-8')
    pad_length = 16 - (len(key) % 16)
    if pad_length < 16:
        key += bytes([pad_length] * pad_length)

    # 解码加密数据（假设输入e是base64格式）
    e_bytes = base64.b64decode(e)

    # 创建AES解密器（ECB模式）
    cipher = AES.new(key, AES.MODE_ECB)

    # 解密并去除PKCS7填充
    decrypted = unpad(cipher.decrypt(e_bytes), AES.block_size)

    # 转换为UTF-8字符串
    return decrypted.decode('utf-8')


def rsa_encrypt(raw_string):
    """
    RSA 加密（非对称加密）
    :param raw_string:
    :return:
    """
    public_key = RSA.import_key(open('public.pem').read())
    cipher_rsa = PKCS1_OAEP.new(public_key)
    encrypted_data = cipher_rsa.encrypt(raw_string.encode('utf-8'))
    return base64.b64encode(encrypted_data).decode('utf-8')


def rsa_decrypt(ct_bytes):
    """
    RSA 解密（非对称加密）
    :param ct_bytes:
    :return:
    """
    private_key = RSA.import_key(open('private.pem').read())
    cipher_rsa = PKCS1_OAEP.new(private_key)
    decrypted_data = cipher_rsa.decrypt(base64.b64decode(ct_bytes)).decode('utf-8')
    return decrypted_data


if __name__ == '__main__':
    # print(md5('123456'))
    # print(md5('123456', is_upper=True))
    # print(sha256('123456'))
    # print(hmac_sha256('123456', b'123456', is_upper=True))
    # print(base64_encode('123456'))
    # print(base64_decode('MTIzNDU2'))
    # print(aes_encrypt('123456'))
    # print(hmac_sha1('123456', 'ZHONGYINGYU'))
    # print(aes_decrypt('bluDwNwocDtDQfbsDY/mxiV0hG2NbWXH08aARVexmfo='))
    # salt = os.urandom(16)
    # print(salt)
    # hashed_password = hashlib.pbkdf2_hmac('sha256', b'password', salt, 100000)
    # print(salt + hashed_password)
    e1 = '{"current":1,"optimizeCountSql":true,"orders":[],"pages":1,"records":[{"avgBoxOffice2":"38.22","boxOffice":3714819.32,"boxOfficeW2":"371.48","jump":false,"name":"合计","noDataLine":"内蒙古民族、华夏联合、北京红鲤鱼院线、完美世界、横店影视、北京新影联、广州金逸珠江、西安长安、重庆保利万和、四川峨眉、深影橙天、浙江时代、山东奥卡新世纪、福建中兴、中影南方新干线、四川太平洋、湖北银兴、河南奥斯卡、上海联和、吉林吉影、辽宁中影北方、山东文旅、河北中联影业、北京嘉凯数字、温州雁荡、江苏东方、甘肃新视界、上海大光明、博纳院线、贵州星空、新疆华夏天山、中广国际、湖南潇湘、湖南楚湘、武汉天河影业、北京九州中原、长城沃美、天津银光、世纪环球、江西星河、中影院线2","serNum":"0","session":7129,"sessionQ2":"7.13","sessionW2":"0.71","totalPeople":97194,"totalPeopleW2":"9.72"},{"avgBoxOffice2":"39.04","bizCode":"FILM","bizTime":1704038400000,"boxOffice":1838903.66,"boxOfficeW2":"183.89","code":"012X00282024","createTime":1749484800000,"filmTotalBoxOffice":1838903.66,"filmTotalBoxOfficeW2":"183.89","id":1932311510755389442,"isChina":2,"jump":false,"name":"你想活出怎样的人生","nationalBoxOffice":3714819.32,"nationalBoxOfficeRatio":49.502,"publicDate":"2024-04-03","region":"日本","serNum":"1","session":3200,"sessionQ2":"3.20","sessionW2":"0.32","sort":"1","totalPeople":47107,"totalPeopleW2":"4.71","type":5,"updateTime":1749484800000,"year":2024},{"avgBoxOffice2":"35.62","bizCode":"FILM","bizTime":1704038400000,"boxOffice":1367991.31,"boxOfficeW2":"136.80","code":"051X00272024","createTime":1749484800000,"filmTotalBoxOffice":1113590991.31,"filmTotalBoxOfficeW2":"111359.10","id":1932311510759583745,"isChina":2,"jump":false,"name":"哥斯拉大战金刚2：帝国崛起","nationalBoxOffice":3714819.32,"nationalBoxOfficeRatio":36.825,"publicDate":"2024-03-29","region":"美国","serNum":"2","session":2919,"sessionQ2":"2.92","sessionW2":"0.29","sort":"2","totalPeople":38400,"totalPeopleW2":"3.84","type":5,"updateTime":1749484800000,"year":2024},{"avgBoxOffice2":"43.46","bizCode":"FILM","bizTime":1704038400000,"boxOffice":507924.35,"boxOfficeW2":"50.79","code":"051X00192024","createTime":1749484800000,"filmTotalBoxOffice":507924.35,"filmTotalBoxOfficeW2":"50.79","id":1932311510763778050,"isChina":2,"jump":false,"name":"功夫熊猫4","nationalBoxOffice":3714819.32,"nationalBoxOfficeRatio":13.673,"publicDate":"2024-03-22","region":"美国","serNum":"3","session":1010,"sessionQ2":"1.01","sessionW2":"0.10","sort":"3","totalPeople":11687,"totalPeopleW2":"1.17","type":5,"updateTime":1749484800000,"year":2024}],"searchCount":true,"size":99999,"total":3}'
    t1 = '0CNJUv5Qyw1W8jUd'
    print(aes_encrypt(e1, t1))

    ee = "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"
    tt = '0CNJUv5Qyw1W8jUd'
    print(aes_decrypt(ee, tt))
