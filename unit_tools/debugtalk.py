import random
import string
import time
import faker
from typing import Union
from configs.setting import api_env_dev, phone, key, channel
from unit_tools.create_sign import create_sign
from unit_tools.handle_data.configParse import ConfigParse
from unit_tools.handle_data.yaml_handler import get_extract_data
from unit_tools.handle_data.yaml_handler import extract_data, extract_data_list
from unit_tools.params_util import update_dict
from unit_tools.time_util import time_util
from unit_tools.encrypt import md5


class DebugTalk:

    def __init__(self):
        self.fake = faker.Faker(locale='zh_CN')

    @staticmethod
    def get_extract_data(*args, **kwargs):
        return get_extract_data(*args, **kwargs)

    @staticmethod
    def extract_data(testcase_extract, resp: Union[str, dict, list]):
        return extract_data(testcase_extract, resp)

    @staticmethod
    def extract_data_list(testcase_extract_list, response_text):
        return extract_data_list(testcase_extract_list, response_text)

    @classmethod
    def get_now_time(cls):
        return time.time()

    @classmethod
    def get_date(cls):
        return time_util.getDate()

    @classmethod
    def get_time(cls):
        """返回时间戳"""
        return time_util.get_millisecond_timestamp()

    @classmethod
    def get_date_and_time(cls):
        """返回2024-07-24 10:21:07的日期格式"""
        return time_util.get_later_hour()

    @classmethod
    def get_h_m_s_time(cls):
        """返回10:21:07的日期格式"""
        return time_util.get_h_m_s()

    @classmethod
    def get_random_num(cls):
        return random.randint(0, 999999)

    @classmethod
    def get_max_num(cls):
        return 999999999

    @classmethod
    def get_min_num(cls):
        return 0

    def get_phone(self):
        _phone = phone if api_env_dev else self.fake.phone_number()
        extract_data({'phone': '$.phone'}, _phone)
        return _phone

    @staticmethod
    def get_authCode():
        """
        获取短信验证码
        :return:
        """
        return get_extract_data('authCode') if api_env_dev else '8888'

    @classmethod
    def get_max_text(cls):
        return '接口自动测试最长文本' * 100

    # 生成随机字符串
    @staticmethod
    def get_random_string(length=4):
        return ''.join(random.choices(string.ascii_letters, k=length))

    @staticmethod
    def timestamp():
        # 获取年月日时分秒
        return time_util.get_current_time('%Y%m%d%H%M%S')

    @staticmethod
    def appKey():
        """
        获取app的key 渠道编码C10000027
        :return: C10000027
        """
        # extract_data({'appKey': '$.appKey'}, channel)
        return channel

    @staticmethod
    def head(url_key):
        """
        获取APP请求head公共部分
        :return:
        """
        timestamp = time_util.get_current_time('%Y%m%d%H%M%S')
        return {
            'tradeId': url_key,
            'timestamp': timestamp,
            'validCode': md5(timestamp + key).upper(),
            'appKey': channel
        }


    @staticmethod
    def sign(param):
        return create_sign(param)

    @staticmethod
    def get_host(param):
        """
        获取对应服务host
        :return:
        """
        return ConfigParse.get_host(param)

    @staticmethod
    def update_value(_data: dict, jsonpath_expr: dict):
        """
        更新字典的值
        :return:
        """
        return update_dict(_data, jsonpath_expr)

    @classmethod
    def get_headers(cls, params_type='app'):
        """
        获取请求头
        :param params_type: 参数类型，如“data”或“json”
        :return:
        """
        # if api_env_dev:
        #     token = get_extract_yaml('token')
        # else:
        #     token = get_extract_yaml('token')
        #     if not token:
        #         logs.info('上个ERROR报错是由于用户未登录提取不到token，可忽略，下面马上登录')
        #         from login.zy_app_login import ZyAppLogin
        #         ZyAppLogin().write_token()
        #         token = get_extract_yaml('token')
        # # 提取默认请求头
        # headers = get_extract_yaml('headers')
        # headers.update({'token': token})
        # headers_mapping = {
        #     'data': headers,
        #     'json': headers,
        #     'file': headers,
        #     'phone': headers,
        # }
        # header = headers_mapping.get(params_type)
        # if header is None:
        #     raise ValueError('不支持其他类型的请求头设置！')
        # return header
        return ConfigParse.get_headers(params_type)


if __name__ == '__main__':
    debug = DebugTalk()
    # print(debug.get_random_clueState())
    """d1 = {
        "id": "a61730def50b7c3da3b77efc8af94c41",
        "cityGroup": "华南区域",
        "cityName": "宁波",
        "cityCompany": "宁波公司",
        "landType": 2,
        "landName": "240910JBG测试jvDu",
        "setup": {
            "processType": 1,
            "state": 3,
            "stateStr": "已完成",
            "tabType": 0,
            "bpmProcessUrl": "https://bpm02-uat.cmsk1979.com//Workflow/MTApprovalView.aspx?procInstID=24091000018&key=7059626715176",
            "oldLandInfoId": None,
            "startTime": None,
            "landInfoId": None,
            "notes": None,
            "recordId": None,
            "landName": "240910JBG测试jvDu",
            "landState": None,
            "proInfoFlag": None,
            "landDealType": None,
            "landDiscType": 1
        }
    }
    debug.extract_data({'cityGroup': '$.cityGroup'}, d1)
    debug.extract_data({'cityCompany': '"cityCompany": "(.*?)",'}, d1)
    s1 = '6666666666666'
    debug.extract_data({'str_1': '$.str_1'}, s1)
    s2 = '6666666666666aa'
    debug.extract_data({'str_2': '$.str_2'}, s2)
    s3 = '{"str_3":"6666666666666aa"}'
    debug.extract_data({'str_3': '$.str_3'}, s3)
    l1 = [1, 2, 3, 4, 5]
    debug.extract_data({'l1': '$.l1'}, l1)
    l2 = '[1, 2, 3, 4, 5]'
    debug.extract_data({'l2': '$.l2'}, l2)"""
    # print(debug.get_host('app'))
    # print(debug.get_headers())
    # print(faker.Faker(locale='zh_CN').phone_number())
    # print(debug.sign('createSeatTicketOrder'))
    # print(md5(f'346431306902072451188671343830125110008841726.00116.4166339.922707888888'))
    print(debug.head('getFilmInfo'))
