"""
@Project    :zy_api_auto
@File       :qw_robot.py
@IDE        :PyCharm
<AUTHOR>
@Date       :2025/4/17 17:55
"""
import requests
from configs.setting import qw_key, FILE_PATH, api_env_dev
from unit_tools.handle_data.yaml_handler import get_extract_yaml
from unit_tools.log_util.recordlog import logs


class Robot:
    def __init__(self):
        # 机器人的webhook地址
        self.url = f'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key={qw_key}'
        self.id_url = f'https://qyapi.weixin.qq.com/cgi-bin/webhook/upload_media?key={qw_key}&type=file'
        self.dirname = FILE_PATH['file']
        self.file_path = f'{self.dirname}/report.zip'

    def send_message(self):
        headers = {'Content-Type': 'application/json'}
        summary = get_extract_yaml('summary')
        summary = [item.strip().split('：')[-1] for item in summary.strip().split('\n')[3:]]
        # print(summary)
        # > 报告详情请查看附件：[allure报告,请点击后进入查看]()
        env = "生产环境" if api_env_dev else "测试环境"
        summary.insert(0, env)
        data = {
            "msgtype": "markdown",
            "markdown": {
                "content":
                    '''<font color=\"warning\">【【【{}】】】\n提醒！自动化测试反馈\n请相关同事注意，及时跟进！</font>\n
                    > 测试用例总数:<font color=\"info\">{}</font>\n
                    > 测试用例通过数:<font color=\"comment\">{}</font>\n
                    > 通过率:<font color=\"comment\">{}</font>\n
                    > 测试用例失败数:<font color=\"info\">{}</font>\n
                    > 失败率:<font color=\"warning\">{}</font>\n
                    > 错误数量:<font color=\"warning\">{}</font>\n
                    > 错误率:<font color=\"warning\">{}</font>\n
                    > 跳过执行数量:<font color=\"warning\">{}</font>\n
                    > 执行总时长:<font color=\"warning\">{}</font>\n
                    > 查看报告前请关闭电脑安全防护软件！！！
                    > 报告详情请查看附件：👇👇
                '''.format(*summary)
            }
        }
        # print(data)
        requests.post(url=self.url, json=data)

    # 发送文件
    def post_file(self):
        data = {
            'file': open(self.file_path, 'rb')
        }
        # 请求id_url(将文件上传微信临时平台),返回media_id
        response = requests.post(url=self.id_url, files=data)
        logs.info(response.text)
        json_res = response.json()
        media_id = json_res['media_id']
        data = {
            "msgtype": "file",
            "file": {
                "media_id": media_id
            }
        }
        # 发送文件
        result = requests.post(url=self.url, json=data)
        logs.info(result.text)
        return result


def send_qw_msg():
    rb = Robot()
    rb.send_message()
    rb.post_file()


if __name__ == '__main__':
    send_qw_msg()
