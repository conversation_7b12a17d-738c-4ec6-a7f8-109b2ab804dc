# 每天都要加油哦
# 作者：vae
import smtplib
import os
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from configs.setting import mail_host,sender, sender_password, receivers, FILE_PATH
from email.mime.image import MIMEImage
from email.mime.application import MIMEApplication
from unit_tools.log_util.recordlog import logs


def qq_email(_msg=''):
    """https://www.bilibili.com/video/BV1JL41117Ys?p=4&spm_id_from=pageDriver&vd_source=bf5cb75167af0ccf02231b25689a7653
      jenkins部署后自动发送邮箱配置"""
    # 配置邮箱信息
    _mail_host = mail_host
    _sender = sender  # 发件人的地址
    _password = sender_password  # 此处是我们刚刚在邮箱中获取的授权码
    _receivers = receivers  # 邮件接受方邮箱地址，可以配置多个，实现群发，注意这里要是字符串

    # 邮件内容设置
    content = MIMEText(f"<html><h2>测试报告，勿回复</h2><h3>{_msg}</h3>", _subtype="html", _charset="utf-8")
    msg = MIMEMultipart('related')
    msg.attach(content)

    # 添加附件
    file = f"{FILE_PATH['file']}/report.zip"
    part = MIMEImage(open(file, 'rb').read(), file.split('.')[-1])
    part.add_header('Content-Disposition', 'attachment', filename=os.path.basename(file))
    msg.attach(part)
    #
    # file = r"F:\jb\baogao1.png"
    # part = MIMEImage(open(file, 'rb').read(), file.split('.')[-1])
    # part.add_header('Content-Disposition', 'attachment', filename=file)
    # msg.attach(part)

    # 邮件标题设置
    msg['Subject'] = 'python发送测试报告'

    # 发件人信息
    msg['From'] = _sender

    # 收件人信息
    msg['To'] = _receivers

    # 通过授权码,登录邮箱,并发送邮件
    try:
        # 发起连接，smtplib.SMTP 端口一般为25；如果是使用SSL，则需要改成 smtplib.SMTP_SSL(mail_host, 465) 端口是465或587
        # server = smtplib.SMTP('smtp.163.com')  # 配置网易服务器地址
        server = smtplib.SMTP_SSL(_mail_host, 465)  # 配置qq服务器地址
        server.login(_sender, _password)
        server.sendmail(msg['From'], msg['To'].split(','), msg.as_string())
        logs.info('邮件发送成功！')
        server.quit()

    except smtplib.SMTPException as e:
        print('error', e)


# summary = f"""
#     自动化测试结果，通知如下，具体执行结果：
#     测试用例总数：111
#     测试用例通过数：111
#     通过率：100%
#     测试用例失败数：0
#     失败率：0%
#     错误数量：0
#     错误率：0
#     跳过执行数量：0
#     执行总时长：242s (4分02秒)
#     """
# qq_email(summary)
