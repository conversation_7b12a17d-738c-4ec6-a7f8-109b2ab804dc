# -*- coding:utf-8 -*-
"""
@Project    :zy_ApiAuto
@File       :create_sign.py
@IDE        :PyCharm
<AUTHOR>
@Date       :2025/6/20 11:18
"""
import random
import string
from configs.setting import key, area_code, lon, lat
from unit_tools.handle_data.yaml_handler import get_extract_yaml, get_extract_data
from unit_tools.handle_data.yaml_handler import extract_data, extract_data_list
from unit_tools.encrypt import md5


def create_sign(param):
    """
    生成签名
    :param param: 接口名称
    :return: MD5加密后的字符串
    """
    userId = get_extract_yaml('login_info', 'userId')
    # 点赞sign
    if param == 'updateNewsCommentZan':
        newsId = get_extract_yaml('newsId')
        return md5(f"{userId}{newsId}{key}")
    # 评论sign
    if param == 'submitNewsReview':
        newsId = get_extract_yaml('newsId')
        content = get_extract_yaml('content')
        if not content:
            random_str = ''.join(random.choices(string.ascii_letters + string.digits, k=4))
            content = f'测试评论{random_str}'
            extract_data({f'content': f'$.content'}, content)
        return md5(f"{userId}{newsId}00{content}{key}")
    # 影票下单sign
    if param == 'createSeatTicketOrder':
        mobile = get_extract_yaml('login_info', 'mobile')
        showNo = get_extract_yaml('showNo')
        areaPrice = get_extract_data('seatInfo', 'areaPrice')
        return md5(f'{userId}{mobile}01{showNo}{areaPrice}{lon}{lat}{key}')
    # 搜索影片sign
    if param == 'getFilmsByKey':
        filmName = get_extract_yaml('filmName')
        if not filmName:
            filmName = "黑客帝国"  # 如果用户没有设置filmName，就默认设置
            extract_data({f'filmName': f'$.filmName'}, filmName)
        return md5(f"{area_code}{filmName}{key}")
    # 获取影城卡券信息sign
    if param == 'getCardTicketInfos':
        cinemaNo = get_extract_yaml('cinemaNo')
        return md5(f'{cinemaNo}{key}')
    # 电影周边商品列表接口sign
    if param == 'getShopGoods':
        return md5(f'{area_code}{key}')
    # 获取可用电子券列表V4  MD5(userId+orderNo+lon+lat+key)
    if param == 'getAvailableCouponListV4':
        orderNo = get_extract_yaml('orderNo')
        return md5(f'{userId}{orderNo}{lon}{lat}{key}')
    # 查询订单可用储值卡 sign=MD5(userId+orderNo+key)
    if param in ['getOrderPayAvailablePrepaidCard', 'getOrderPayAvailableCinemaCard']:
        orderNo = get_extract_yaml('orderNo')
        return md5(f'{userId}{orderNo}{key}')

    # 获取可用点卡/次卡列表 sign=MD5(orderNo+key)
    if param in ['getAvailablePcardListV2', 'getRefundRenewNotice']:
        orderNo = get_extract_yaml('orderNo')
        return md5(f'{orderNo}{key}')
    # 取消订单 sign=MD5(orderNo +userId+key)
    if param == 'cancelOrder':
        orderNo = get_extract_yaml('orderNo')
        return md5(f'{orderNo}{userId}{key}')


if __name__ == '__main__':
    print(create_sign('getFilmsByKey'))
