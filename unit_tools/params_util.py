# -*- coding:utf-8 -*-
"""
@Project    :zy_ApiAuto
@File       :parse_params.py
@IDE        :PyCharm
<AUTHOR>
@Date       :2025/4/23 09:48
明确需求：
    1. 你有一个字典，需要替换指定键的值
    2. 表达式是jsonpath格式 {"$..status":"value"}
    3. 你需要使用 JSONPath 表达式来定位并修改 status 键的值。
    4. 你需要编写一个函数 update_status_in_dict，该函数接受三个参数：data（原始字典）、jsonpath_expr（JSONPath 表达式）和 new_value（新的值）。
    5. 你需要使用 jsonpath_ng 库来解析 JSONPath 表达式，并找到匹配的元素。
    6. 你需要遍历找到的指定匹配元素，并将它们的值修改为 new_value。并返回修改后的字典
"""
import re
from typing import Any
from apis.frontend import FRONTEND_APIS
import jsonpath_ng.ext as jsonpath
from unit_tools.log_util.recordlog import logs
import json
import urllib
from urllib.parse import urlencode


def parse_params(raw_data: dict) -> str:
    """
    入参扁平化处理
    :param raw_data: 入参是字典类型的数据
    :return: str 示例：pagination=%7B%22pageNumber%22%3A1%2C%22pageSize%22%3A50%7D
    """
    res = ''
    for key, value in raw_data.items():
        # 处理value里的空格 扁平化处理
        json_param_value = json.dumps(value, separators=(',', ':'))
        # 使用 urllib.parse.quote 进行 URL 编码
        url_encoded_string = urllib.parse.quote(json_param_value)
        # 拼装参数
        res += f'{key}={url_encoded_string}&'
    return res[:-1]


def parse_and_replace_variables(raw_data: Any) -> Any:
    """
    解析并替换用例数据中的变量引用，如：${get_extract_data(goodsId,1)}
    :param raw_data: 解析的用例数据
    :return: 返回的是dict类型
    """
    from unit_tools.debugtalk import DebugTalk
    if isinstance(raw_data, dict):
        for key, value in raw_data.items():
            raw_data[key] = parse_and_replace_variables(value)
    elif isinstance(raw_data, list):
        raw_data = [parse_and_replace_variables(item) for item in raw_data]
    elif isinstance(raw_data, str):
        for _ in range(raw_data.count('${')):
            if '${' in raw_data and '}' in raw_data:
                start_index = raw_data.index('$')
                end_index = raw_data.index('}', start_index)
                variable_data = raw_data[start_index:end_index + 1]
                # par2是}后面的那部分字符串
                par2 = raw_data[end_index + 1:]

                # 使用正则表达式提取函数名和参数
                match = re.match(r'\$\{(\w+)\((.*?)\)\}', variable_data)
                if match:
                    func_name, func_params = match.groups()
                    func_params = func_params.split(',') if func_params else []
                    # 使用面向对象反射getattr调用函数
                    extract_data = getattr(DebugTalk(), func_name)(*func_params)
                    if extract_data == 'error_key':
                        logs.error(f'提取数据表达式错误请检查--{variable_data}')
                        extract_data = None
                    if start_index or par2:
                        # 如果有前后部分，一定是字符串
                        raw_data = raw_data[:start_index] + str(extract_data if extract_data else '') + par2
                    else:
                        # isinstance(extract_data, (list, dict)) 返回原来的数据格式
                        return extract_data
    return raw_data


def update_node(data: dict, path: str, new_value: Any) -> dict:
    keys = path.split('.')  # 将路径字符串按 '.' 分割成键列表 pagination.queryParams.status
    current = data  # 从根字典开始
    for key in keys[:-1]:  # 遍历到倒数第二个键
        current = current[key]  # 深入到下一层字典
    # 替换最后一个键对应的值
    current[keys[-1]] = new_value
    return data


def update_dict(data: dict, jsonpath_expr: dict) -> dict:
    """
    :param data:  原始字典
    :param jsonpath_expr: jsonpath 表达式
    :return:
    """
    jsonpath_expr = parse_and_replace_variables(jsonpath_expr)
    for key, value in jsonpath_expr.items():
        jsonpath_expr = jsonpath.parse(key)
        for match in jsonpath_expr.find(data):
            data = update_node(data, str(match.full_path), value)
    return data


if __name__ == '__main__':
    # params = BACKEND_APIS['/proxy/cnews/article/list']
    #
    # data = {
    #     "pagination": {
    #         "pageNumber": 1,
    #         "pageSize": 50,
    #         "queryParams": {
    #             "title": "",
    #             "source": "",
    #             "tag_id": "",
    #             "status": "1",
    #             "istop": "",
    #             "film_name": "",
    #             "startintime": "",
    #             "endinttime": "",
    #             "showtype": "",
    #             "startshowtime": "",
    #             "endshowttime": "",
    #             "min_program_video_status": "",
    #             "isplat": 0
    #         },
    #         "anotherSection": {
    #             "details": {
    #                 "status": "3"
    #             }
    #         }
    #     }
    # }
    # print(update_dict(params, {"$..status": "1", "$..pageNumber": "2"}))
    # print(update_dict(data, {"$..queryParams.status": "6666"}))
    b = FRONTEND_APIS['getCinemas']
    print(parse_and_replace_variables(b))