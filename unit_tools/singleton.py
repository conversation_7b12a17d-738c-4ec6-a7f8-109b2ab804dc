"""
@Project    :zy_api_auto
@File       :singleton.py
@IDE        :PyCharm
<AUTHOR>
@Date       :2025/7/3
@Description: 单例模式装饰器
"""
import threading
from functools import wraps


def singleton(cls):
    """
    单例模式装饰器
    
    使用方法:
    @singleton
    class MyClass:
        pass
    
    特点:
    1. 线程安全
    2. 支持带参数的构造函数
    3. 每个类只会创建一个实例
    """
    instances = {}
    lock = threading.Lock()
    
    @wraps(cls)
    def get_instance(*args, **kwargs):
        # 双重检查锁定模式，确保线程安全
        if cls not in instances:
            with lock:
                if cls not in instances:
                    instances[cls] = cls(*args, **kwargs)
        return instances[cls]
    
    return get_instance


def singleton_with_args(cls):
    """
    支持参数的单例模式装饰器
    
    使用方法:
    @singleton_with_args
    class MyClass:
        def __init__(self, param1, param2):
            pass
    
    特点:
    1. 线程安全
    2. 根据构造函数参数创建不同的实例
    3. 相同参数只会创建一个实例
    """
    instances = {}
    lock = threading.Lock()
    
    @wraps(cls)
    def get_instance(*args, **kwargs):
        # 创建参数的唯一标识
        key = (cls, args, tuple(sorted(kwargs.items())))
        
        if key not in instances:
            with lock:
                if key not in instances:
                    instances[key] = cls(*args, **kwargs)
        return instances[key]
    
    return get_instance


class SingletonMeta(type):
    """
    单例模式元类
    
    使用方法:
    class MyClass(metaclass=SingletonMeta):
        pass
    
    特点:
    1. 线程安全
    2. 使用元类实现
    3. 每个类只会创建一个实例
    """
    _instances = {}
    _lock = threading.Lock()
    
    def __call__(cls, *args, **kwargs):
        if cls not in cls._instances:
            with cls._lock:
                if cls not in cls._instances:
                    cls._instances[cls] = super().__call__(*args, **kwargs)
        return cls._instances[cls]


# 示例用法
if __name__ == "__main__":
    # 测试基本单例装饰器
    @singleton
    class TestClass1:
        def __init__(self, value=None):
            self.value = value
            print(f"创建实例: {id(self)}")
    
    # 测试带参数的单例装饰器
    @singleton_with_args
    class TestClass2:
        def __init__(self, name, age):
            self.name = name
            self.age = age
            print(f"创建实例: {id(self)}, name={name}, age={age}")
    
    # 测试元类单例
    class TestClass3(metaclass=SingletonMeta):
        def __init__(self, value=None):
            self.value = value
            print(f"创建实例: {id(self)}")
    
    # 测试
    print("=== 测试基本单例装饰器 ===")
    obj1 = TestClass1("test1")
    obj2 = TestClass1("test2")  # 不会创建新实例
    print(f"obj1 is obj2: {obj1 is obj2}")
    print(f"obj1.value: {obj1.value}, obj2.value: {obj2.value}")
    
    print("\n=== 测试带参数的单例装饰器 ===")
    obj3 = TestClass2("Alice", 25)
    obj4 = TestClass2("Alice", 25)  # 相同参数，不会创建新实例
    obj5 = TestClass2("Bob", 30)    # 不同参数，会创建新实例
    print(f"obj3 is obj4: {obj3 is obj4}")
    print(f"obj3 is obj5: {obj3 is obj5}")
    
    print("\n=== 测试元类单例 ===")
    obj6 = TestClass3("test1")
    obj7 = TestClass3("test2")  # 不会创建新实例
    print(f"obj6 is obj7: {obj6 is obj7}")
