# -*- coding: utf-8 -*-
import json
import os
import random
import re
from typing import Union

import jsonpath
import yaml
from configs.setting import FILE_PATH
from unit_tools.extract_auto import extract_auto
from unit_tools.log_util.recordlog import logs


def read_yaml(yaml_path):
    """
    读取数据
    :return:
    """
    if not os.path.exists(yaml_path):
        with open(yaml_path, 'w', encoding='utf-8'):
            pass
    try:
        with open(yaml_path, 'r', encoding='utf-8') as f:
            _data = yaml.safe_load(f)
        return _data
    except UnicodeDecodeError:
        logs.error(f'{yaml_path}文件编码格式错误，--尝试使用utf-8去解码YAML文件发生错误，请确保你的yaml文件是utf-8格式！')
    except yaml.YAMLError as e:
        logs.error(f'Error：读取yaml文件失败，请检查格式 -{yaml_path}，{e}')
    except Exception as e:
        logs.error(f'读取{yaml_path}文件时出现异常，原因：{e}')


def read_yaml_case(yaml_path):
    """
    读取yaml文件数据
    :param yaml_path: 文件路径
    :return:
    """
    testcase_list = []
    data = read_yaml(yaml_path)
    # 处理一个yaml文件多条测试用例的场景
    if len(data) <= 1:
        yaml_data = data[0]
        base_info = yaml_data.get('baseInfo')
        for ts in yaml_data.get('testCase'):
            # 把单接口的测试数据构建成完整的测试数据
            params = {'baseInfo': base_info, 'testCase': [ts]}
            testcase_list.append(params)
        return testcase_list
    else:
        return data


def write_yaml(value_dict):
    """
    yaml文件数据写入
    :param value_dict: （dict）写入的数据，必须为字典类型
    :return:
    """
    file_ptah = FILE_PATH['extract']
    if not os.path.exists(file_ptah):
        with open(file_ptah, 'w', encoding='utf-8'):
            pass
    file = None
    try:
        file = open(file_ptah, 'a', encoding='utf-8')
        if isinstance(value_dict, dict):
            # 判断yaml文件中是否存在需要写入对象的键，有则覆盖写入，无则追加写入
            yaml_dict = read_yaml(file_ptah)
            if yaml_dict:
                # 使用集合的交集来判断两个字典是否有相同的键
                common_keys = set(yaml_dict.keys()).intersection(value_dict.keys())
                # 检查交集是否非空
                if common_keys:
                    file = open(file_ptah, 'w', encoding='utf-8')
                    logs.warning(f"注意！！！提取的字典和之前的数据有相同的键:{common_keys}")
                    for key in common_keys:
                        yaml_dict[key] = value_dict[key]
                    # 将修改后的数据写回YAML文件
                    yaml.dump(yaml_dict, file, allow_unicode=True, default_flow_style=False, sort_keys=False)

                else:
                    file = open(file_ptah, 'a', encoding='utf-8')
                    write_data = yaml.dump(value_dict, allow_unicode=True, sort_keys=False)
                    file.write(write_data)
            else:
                file = open(file_ptah, 'a', encoding='utf-8')
                write_data = yaml.dump(value_dict, allow_unicode=True, sort_keys=False)
                file.write(write_data)
        else:
            logs.warning('写入的数据必须为字典类型！')
    except Exception as e:
        logs.error(f'写入yaml文件出现异常，原因：{e}')
    finally:
        file.close()


def clear_yaml():
    """
    清空extract.yaml文件数据
    :return:
    """
    with open(FILE_PATH['extract'], 'w') as f:
        f.truncate()


def get_extract_yaml(node_name, sub_node_name=None):
    """
    用于获取extract.yaml文件的数据
    :param node_name: 第一级key值
    :param sub_node_name: 下级key值
    :return:
    """
    file_ptah = FILE_PATH['extract']
    try:
        extract_data_ = read_yaml(file_ptah)
        if sub_node_name is None:
            return extract_data_[node_name]
        else:
            return extract_data_.get(node_name, {}).get(sub_node_name)
    except yaml.YAMLError as e:
        logs.error(f'Error：读取yaml文件失败，请检查格式 -{file_ptah}，{e}')
    except Exception as e:
        logs.error(f'Error:extract.yaml文件中不存在{node_name}节点')


def extract_data(testcase_extract, resp: Union[str, dict, list]):
    """
    提取单个参数，提取接口的返回参数，支持正则表达式提取和json提取器
    :param testcase_extract: （dict）yaml文件中的extract值，例如：{'token': '$.token'}
    :param resp: （str）接口的实际返回值
    :return:
    """
    extract_data = None
    """判断字符串是否是有效的json字符串,注意--->"1"数字型和'[1,2]'、'[1,"a"]'的字符串可以json.loads"""
    if isinstance(resp, list):
        # 处理传进来的无效json字符串。则构造一个有效的
        resp = {
            list(testcase_extract.keys())[0]: resp
        }
    if isinstance(resp, str):
        try:
            resp1 = json.loads(resp)
        except Exception:
            resp = {
                list(testcase_extract.keys())[0]: resp
            }
        else:
            """json.loads转成功会有3个类型 int list dict"""
            if not isinstance(resp1, dict):
                resp = {
                    list(testcase_extract.keys())[0]: resp
                }
            else:
                resp = resp1

    resp = json.dumps(resp, ensure_ascii=False)
    try:
        for _key, value in testcase_extract.items():
            if any(pat in value for pat in ['(.*?)', '(.+?)', r'(\d+)', r'(\d*)']):
                ext_list = re.search(value, resp)
                extract_data = {_key: int(ext_list.group(1)) if r'(\d+)' in value else ext_list.group(1)}
            if isinstance(resp, str):
                resp = json.loads(resp)
            # 处理自动提取数据
            if _key == 'extract_auto':
                extract_data = extract_auto(resp, _key, value)
            elif "$" in value:
                extract_json = jsonpath.jsonpath(resp, value)[0]
                extract_data = {_key: extract_json} if extract_json else {
                    _key: "未提取到数据，请检查接口返回信息或表达式！"}

            if extract_data:
                write_yaml(extract_data)

    except re.error:
        logs.error('正则表达式解析错误，请检查yaml文件extract表达式是否正确！')
    except json.JSONDecodeError:
        logs.error('JSON解析错误，请检查yaml文件extract表达式是否正确！')


def extract_data_list(testcase_extract_list, response_text):
    """
    提取多个参数，提取接口的返回参数，支持正则表达式提取和json提取器
    :param testcase_extract_list: （dict）yaml文件中的extract_list值，例如：{'token': '$.token'}
    :param response_text: （str）接口的实际返回值
    :return:
    """
    extract_data = None
    try:
        for _key, value in testcase_extract_list.items():
            if any(pat in value for pat in ['(.*?)', '(.+?)', r'(\d+)', r'(\d*)']):
                ext_list = remove_duplicates(re.findall(value, response_text, re.S))
                if ext_list:
                    extract_data = {_key: ext_list}
            elif "$" in value:
                extract_json = jsonpath.jsonpath(json.loads(response_text), value)
                if extract_json:
                    extract_data = {_key: extract_json}
                else:
                    extract_data = {_key: "未提取到数据，请检查接口返回信息或表达式！"}

            if extract_data:
                write_yaml(extract_data)

    except re.error:
        logs.error('正则表达式解析错误，请检查yaml文件extract表达式是否正确！')
    except json.JSONDecodeError:
        logs.error('JSON解析错误，请检查yaml文件extract表达式是否正确！')


def remove_duplicates(lst):
    seen = set()
    result = []
    for item in lst:
        if item not in seen:
            seen.add(item)
            result.append(item)
    return result


def get_extract_data(*args, **kwargs):
    """
    获取extract.yaml数据，首先判断out_format是否为数字类型，如果不是就获取下一个节点的value
    :param node_name: extract.yaml文件中的key
    :param args: ('landInfoZpgVO', 'projectIdentifyCode')  逐级往下取值
    :param out_format: str类型，0：随机去读取；-1：读取全部数据，返回字符串格式；-2：读取全部，返回是列表格式
                        其他值的就按顺序读取
    :return: 可以是具体的值 可以是字典 列表
    """
    if len(args) == 1:
        # 没有往下取的层级 get_extract_data('project_data')
        node_name, lst, out_format = args[0], None, None
    else:
        if is_number_string(args[-1]):
            # get_extract_data('landIds', 1)
            node_name, lst, out_format = args[0], args[1:-1], args[-1]
        else:
            node_name, lst, out_format = args[0], args[1:], None
    _data = get_extract_yaml(node_name)
    # 初始化一个变量来逐步深入字典
    current_value = _data
    if lst:
        # 遍历列表中的键
        for key in lst:
            # 检查当前值是否是字典且包含当前键
            if isinstance(current_value, dict) and key in current_value:
                # 如果是，则更新当前值为该键对应的值
                current_value = current_value[key]
                # 对于列表，我们需要检查索引是否存在（这里简单处理为整数索引）
            elif isinstance(current_value, list) and isinstance(int(key), int) and 0 <= int(key) <= len(
                    current_value):
                current_value = current_value[int(key) - 1]
            else:
                # 如果当前值不是字典或列表，或者键不存在于当前值中，则终止循环
                current_value = 'error_key'
                return current_value
    # print(current_value)
    if out_format is not None and bool(re.compile(r'^[+-]?\d+$').match(str(out_format))):
        out_format = int(out_format)
        data_value = {
            out_format: seq_read(current_value, out_format),
            0: random.choice(current_value),
            -1: ','.join([json.dumps(i) if isinstance(i, (dict, list)) else str(i) for i in current_value]),
            -2: current_value
        }
        current_value = data_value[out_format]
    return current_value


def seq_read(data, out_format):
    """获取extract.yaml，第二个参数不为0，-1，-2的情况下"""
    if out_format not in [0, -1, -2]:
        return data[out_format - 1]
    else:
        return None


def is_number_string(s):
    # 匹配整数、小数和负数（但不包括科学记数法）
    s = str(s)
    pattern = r'^-?\d+(\.\d+)?$'
    return bool(re.match(pattern, s))


if __name__ == '__main__':
    # clear_yaml()
    # res = read_yaml_case('../../testcase/lightAssets/lightAssets.yaml')
    # res = read_yaml('../../testcase/jbg/setup/jbg_setup.yaml')[:1]
    # print(res, type(res))
    # write_yaml({'token': '5001a2569ff1a41b515f066ad788888888888888888888888888888888888cf7a6f826547d2_C1'})
    data = {
        "code": 0,
        "msg": "",
        "data": {
            "ticketUnits": [
                {
                    "ticketUnitId": "214",
                    "showId": "48",
                    "name": None,
                    "ticketName": "",
                    "minBuyLimit": 1,
                    "maxBuyLimit": 6,
                    "needRealName": False,
                    "realNameLimit": 0,
                    "ticketLevel": "VVIP",
                    "rgb": "#FF4242",
                    "ticketPrice": "10",
                    "setNumber": 1,
                    "sellPriceList": [
                        11,
                        22,
                        33,
                        44,
                        55,
                        66,
                        77,
                        88,
                        99,
                        110,
                        121,
                        132,
                        143,
                        154,
                        165,
                        176,
                        187,
                        198,
                        209,
                        220
                    ],
                    "limited": True,
                    "sellStatus": 3,
                    "currentAmount": 20,
                    "baseTicketUnitId": None,
                    "thirdTicketUnitId": 16905302,
                    "thirdBaseTicketUnitId": 0
                },
                {
                    "ticketUnitId": "215",
                    "showId": "48",
                    "name": None,
                    "ticketName": "",
                    "minBuyLimit": 1,
                    "maxBuyLimit": 6,
                    "needRealName": False,
                    "realNameLimit": 0,
                    "ticketLevel": "VIP",
                    "rgb": "#FF6FE4",
                    "ticketPrice": "20",
                    "setNumber": 1,
                    "sellPriceList": [
                        22,
                        44,
                        66,
                        88,
                        110,
                        132,
                        154,
                        176,
                        198,
                        220,
                        242,
                        264,
                        286,
                        308,
                        330,
                        352,
                        374,
                        396,
                        418,
                        440
                    ],
                    "limited": True,
                    "sellStatus": 3,
                    "currentAmount": 20,
                    "baseTicketUnitId": None,
                    "thirdTicketUnitId": 16905303,
                    "thirdBaseTicketUnitId": 0
                },
                {
                    "ticketUnitId": "216",
                    "showId": "48",
                    "name": None,
                    "ticketName": "",
                    "minBuyLimit": 1,
                    "maxBuyLimit": 6,
                    "needRealName": False,
                    "realNameLimit": 0,
                    "ticketLevel": "A",
                    "rgb": "#FF8014",
                    "ticketPrice": "30",
                    "setNumber": 1,
                    "sellPriceList": [
                        33,
                        66,
                        99,
                        132,
                        165,
                        198,
                        231,
                        264,
                        297,
                        330,
                        363,
                        396,
                        429,
                        462,
                        495,
                        528,
                        561,
                        594,
                        627,
                        660
                    ],
                    "limited": True,
                    "sellStatus": 3,
                    "currentAmount": 20,
                    "baseTicketUnitId": None,
                    "thirdTicketUnitId": 16905304,
                    "thirdBaseTicketUnitId": 0
                },
                {
                    "ticketUnitId": "217",
                    "showId": "48",
                    "name": None,
                    "ticketName": "",
                    "minBuyLimit": 1,
                    "maxBuyLimit": 6,
                    "needRealName": False,
                    "realNameLimit": 0,
                    "ticketLevel": "B",
                    "rgb": "#FFD70E",
                    "ticketPrice": "40",
                    "setNumber": 1,
                    "sellPriceList": [
                        44,
                        88,
                        132,
                        176,
                        220,
                        264,
                        308,
                        352,
                        396,
                        440,
                        484,
                        528,
                        572,
                        616,
                        660,
                        704,
                        748,
                        792,
                        836,
                        880
                    ],
                    "limited": True,
                    "sellStatus": 3,
                    "currentAmount": 20,
                    "baseTicketUnitId": None,
                    "thirdTicketUnitId": 16905305,
                    "thirdBaseTicketUnitId": 0
                },
                {
                    "ticketUnitId": "218",
                    "showId": "48",
                    "name": None,
                    "ticketName": "",
                    "minBuyLimit": 1,
                    "maxBuyLimit": 6,
                    "needRealName": False,
                    "realNameLimit": 0,
                    "ticketLevel": "C",
                    "rgb": "#5A84FF",
                    "ticketPrice": "50",
                    "setNumber": 1,
                    "sellPriceList": [
                        55,
                        110,
                        165,
                        220,
                        275,
                        330,
                        385,
                        440,
                        495,
                        550,
                        605,
                        660,
                        715,
                        770,
                        825,
                        880,
                        935,
                        990,
                        1045,
                        1100
                    ],
                    "limited": True,
                    "sellStatus": 3,
                    "currentAmount": 20,
                    "baseTicketUnitId": None,
                    "thirdTicketUnitId": 16905306,
                    "thirdBaseTicketUnitId": 0
                },
                {
                    "ticketUnitId": "219",
                    "showId": "48",
                    "name": "",
                    "ticketName": "",
                    "minBuyLimit": 1,
                    "maxBuyLimit": 3,
                    "needRealName": False,
                    "realNameLimit": 0,
                    "ticketLevel": "VVIP",
                    "rgb": "#FF4242",
                    "ticketPrice": "10",
                    "setNumber": 2,
                    "sellPriceList": [
                        20,
                        40,
                        60,
                        80,
                        99,
                        119,
                        139,
                        159,
                        179,
                        198,
                        218,
                        238,
                        258,
                        278,
                        297,
                        317,
                        337,
                        357,
                        377,
                        396
                    ],
                    "limited": True,
                    "sellStatus": 3,
                    "currentAmount": 20,
                    "baseTicketUnitId": "214",
                    "thirdTicketUnitId": 16905478,
                    "thirdBaseTicketUnitId": 16905302
                }
            ]
        }
    }
    # extract_data({'777': '$..tradeId'}, data)
    extract_data({'extract_auto': 'show_ticket_units'}, data)
