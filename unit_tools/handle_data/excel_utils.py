import ast
import json
import pandas as pd
from unit_tools.log_util.recordlog import logs


def get_excel_case_data(file_path, module='轻资产'):
    """
    读取excel文件，获取指定模块的数据
    :param file_path: 文件路径
    :param module: 模块，如登录，注册，查询
    :return:
    """
    try:
        _data = pd.read_excel(file_path)
        df = _data.fillna(0)  # 处理nan值成0
    except Exception as e:
        logs.error(f'读取excel文件失败：{e}')
        raise logs.error(f'读取excel文件失败：{e}')
    else:
        return parse_case(df)


def parse_case(_data, module='轻资产'):
    """
    解析数据，把字符串类型的字典转换为字典
    :param module:
    :param _data: df格式的数据
    :return:
    """
    testCase_list = ['case_name', 'format_data', 'input_data', 'validation', 'extract', 'extract_list']
    # df = _data[_data['module'] == module]
    df = _data
    # for i in df:
    #     print(i,'---------------------------')
    #     print(df[i][0], type(df[i][0]))
    final_data = []  # 最终返回的用例数据
    start_index = 0
    for i in df.index:  # 循环行
        inner_data = {
            'baseInfo': {},
            'testCase': {}
        }
        for d in df.iloc[[start_index]]:  # 循环列
            # print("*****"+str(d))
            if d in testCase_list:
                if d in testCase_list[:2]:
                    inner_data['testCase'][d] = df[d][i]
                else:  # 处理数据 # 把字符串类型的字典转换为字典
                    try:
                        d_data = df[d][i]
                        if d_data == '' or isinstance(d_data, (int, float)) or "update_value" in d_data:
                            inner_data['testCase'][d] = d_data
                        else:
                            d_data = handle_json_string(d_data)
                            if isinstance(d_data, (dict,)):
                                inner_data['testCase'][d] = d_data
                            elif '${' in d_data or '$.' in d_data:
                                inner_data['testCase'][d] = ast.literal_eval(d_data)
                            else:
                                # print(df[d][i], type(df[d][i]))
                                inner_data['testCase'][d] = ast.literal_eval(df[d][i])
                    except Exception as e:
                        logs.error(f'处理{i + 2}行{d}列数据异常，请检查用例数据格式原因：{e}')
                        inner_data['testCase'][d] = df[d][i]
                        continue

            else:
                inner_data['baseInfo'][d] = df[d][i]
        format_data = inner_data['testCase'].pop('format_data')
        input_data = inner_data['testCase'].pop('input_data')
        inner_data['testCase'][format_data] = input_data
        final_data.append(inner_data)
        start_index += 1
    return final_data


# 定义一个处理json字符串的函数，异常处理传入的数据，能转成字典则返回字典，否则返回原字符串
def handle_json_string(in_data):
    if not in_data:
        return in_data
    if isinstance(in_data, (int, list, float)):
        return in_data
    try:
        return json.loads(in_data)
    except json.JSONDecodeError:
        return in_data


if __name__ == '__main__':
    data = get_excel_case_data('../../testcase/demo_test/测试用例.xlsx')[:1]
    print(data)
    # print('{"update_value":{"$..status": "1"}}'[16:-1])
    # print(data[1]['testCase']['validation'])
    # print(data[1]['testCase']['extract'], type(data[1]['testCase']['extract']))
    # print(data[1]['testCase']['extract_list'])
    # aa = "[{'code': 200}, {'eq': {'message': '操作成功'}}]"
    # bb = "{\"userName\":\"imooc\",\"password\":\"12345678\"}"
    # print(handle_json_string(aa), type(handle_json_string(aa)))
    # print(handle_json_string(bb), type(handle_json_string(bb)))
    # print(handle_json_string(''), type(handle_json_string('')))
    # print(handle_json_string([]), type(handle_json_string([])))
    # print(handle_json_string([1]), type(handle_json_string([1])))
    # print(handle_json_string(1), type(handle_json_string(1)))
    # print(handle_json_string(1.1), type(handle_json_string(1.1)))
    # print(handle_json_string(False), type(handle_json_string(False)))
    # print(handle_json_string(True), type(handle_json_string(True)))
    # print(handle_json_string('{"message":"$.message"}'), type(handle_json_string('{"message":"$.message"}')))
    # print(json.loads('{"message":"$.message"}'))
    # d_data = ast.literal_eval("${get_headers(phone)}")
    # d_data = ast.literal_eval("[{'code': 200}, {'eq': {'code': '0'}},{ \"message\": \"发送短信验证码成功\" }]")
    # d_data = ast.literal_eval('{"userName":"imooc","password":"123456789"}')
    # d_data = ast.literal_eval("{\"userName\":\"imooc\",\"password\":\"123456789\"}")
    # d_data = ast.literal_eval("{'msgIds': 'msgId%3D(.*?)\"'}")
    # print(d_data, type(d_data))
