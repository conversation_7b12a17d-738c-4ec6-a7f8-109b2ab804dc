import configparser
from configs.setting import api_env_dev,FILE_PATH
from configs.use_conf import ZY_SERVER
from unit_tools.log_util.recordlog import logs


class ConfigParse:
    """
    解析.ini后缀的配置文件
    """

    def __init__(self, file_path=FILE_PATH['sql_conf']):
        self.file_path = file_path
        self.config = configparser.ConfigParser()
        self.read_config()

    def read_config(self):
        self.config.read(self.file_path)

    def get_value(self, section, option):
        """
        获取配置文件的值
        :param section: 头参数
        :param option: 下级参数key值
        :return:
        """
        try:
            return self.config.get(section, option)
        except Exception as e:
            logs.error(f'解析配置文件出现异常，原因：{e}')

    @staticmethod
    def get_host(host_name):
        """
        获取对应服务host
        :return:
        """
        host_name = host_name.lower()
        if api_env_dev:
            return ZY_SERVER['DEV'][host_name]['host']
        else:
            return ZY_SERVER['SIT'][host_name]['host']

    @staticmethod
    def get_headers(host_name):
        """
        获取对应服务host
        :return:
        """
        host_name = host_name.lower()
        if api_env_dev:
            return ZY_SERVER['DEV'][host_name]['headers']
        else:
            return ZY_SERVER['SIT'][host_name]['headers']

    def get_mysql_conf(self, option):
        """
        获取MySQL数据库的配置参数值
        :return:
        """
        return self.get_value('MySQL', option)

    def mysql_conn(self):
        db_info = {
            'host': self.get_mysql_conf('host'),
            'port': int(self.get_mysql_conf('port')),
            'user': self.get_mysql_conf('user'),
            'password': self.get_mysql_conf('password'),
            'database': self.get_mysql_conf('database')
        }
        return db_info
