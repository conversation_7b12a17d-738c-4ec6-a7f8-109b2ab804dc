# -*- coding:utf-8 -*-
import copy
import json
import traceback
import requests
from unit_tools.handle_data.configParse import ConfigParse
from unit_tools.handle_data.yaml_handler import get_extract_data
from unit_tools.params_util import parse_params, parse_and_replace_variables as prv
import allure
from apis.frontend import FRONTEND_APIS
from apis.backend import BACKEND_APIS
from configs.setting import FILE_PATH, zy_serve, channel
from login.zy_app_login import ZyAppLogin
from login.zy_backend_login import session
from unit_tools.assertion_utils import Assertions
from unit_tools.debugtalk import DebugTalk
from unit_tools.handle_data.excel_utils import get_excel_case_data
from unit_tools.sendrequests import SendRequests
from unit_tools.log_util.recordlog import logs

send_request = SendRequests()
send_request_backend = SendRequests(session=session)


class RequestsBase:

    def __init__(self):
        self.asserts = Assertions()
        self.file_path = FILE_PATH['file']

    @classmethod
    def allure_attach_dict_result(cls, result):
        """
        处理结果是字典类型，就将其转换成字符串类型，并做格式化处理，否则直接返回
        :return:
        """
        if isinstance(result, dict):
            allure_response = json.dumps(result, ensure_ascii=False, indent=4)
        else:
            allure_response = result
        return allure_response

    def execute_test_cases(self, _api_info):
        """
        规范yaml接口信息，执行接口、提取结果以及断言操作
        :param _api_info: （dict）yaml里面接口信息
        :return:
        """
        api_info = copy.deepcopy(_api_info)  # 失败重跑用例不至于找不到对象，因为下面是pop操作
        # logs.info(f'测试用例信息：{api_info}')
        try:
            # print(api_info)
            old_host = api_info['baseInfo']['host']  # 保存原始host，后续用于处理特殊入参判断
            base_info = prv(api_info['baseInfo'])
            _testcase = prv(api_info['testCase'])
            try:
                skip_flag = int(base_info.get('skip_flag', 0))
            except:
                skip_flag = 0
            if skip_flag:
                logs.warning(f'接口信息里面有错误，跳过执行！')
                return
            # 处理baseInfo里面的数据
            host_tag = base_info.get('host') if base_info.get('host', None) else ""
            if 'http' in base_info['url']:
                # 如果url是完整的，则不需要处理
                url = base_info['url']
            else:
                if host_tag == ConfigParse.get_host('app'):
                    url = host_tag
                else:
                    url = host_tag + base_info['url']
            api_name = base_info['api_name']
            method = base_info['method']
            # 中影【后台】整体请求是session，用例里不需要加关键字headers了
            headers = base_info.get('headers', None)
            if headers is not None:
                headers = eval(prv(headers)) if isinstance(headers, str) else headers

            cookies = api_info['baseInfo'].get('cookies', None)
            if cookies is not None:
                cookies = eval(prv(cookies)) if isinstance(cookies, str) else cookies

            # 处理testCase下面的数据
            if isinstance(_testcase, dict):
                _testcase = [_testcase]
            for testcase in _testcase:
                case_name = testcase.pop('case_name')

                # 通过变量引用处理断言结果
                val_result = prv(testcase.get('validation'))
                testcase['validation'] = val_result
                validation = testcase.pop('validation')

                # 处理接口返回值提取部分
                extract, extract_list = testcase.pop('extract', None), testcase.pop('extract_list', None)

                param_type, request_params = None, None
                # ####################处理参数类型和请求参数#############################
                for param_type, param_value in testcase.items():
                    if param_type in ['params', 'data', 'json']:
                        if isinstance(param_value, dict) and 'update_value' not in param_value:
                            request_params = prv(param_value)
                            # testcase[param_type] = request_params
                        else:
                            if BACKEND_APIS.get(base_info['url'].strip(), None):
                                _param_value = BACKEND_APIS[base_info['url'].strip()]
                            else:
                                _param_value = FRONTEND_APIS[base_info['url'].strip()]
                            # 处理入参的替换
                            if param_value and 'update_value' in param_value:
                                # 提取默认入参
                                func_params = json.loads(param_value[16:-1])
                                for key, value in func_params.items():
                                    # 先把入参变量写入yaml文件
                                    _key = key.split('.')[-1]  # $..content
                                    DebugTalk().extract_data({f'{_key}': f'$.{_key}'}, value)
                                # 使用面向对象反射getattr调用函数
                                _param_value = getattr(DebugTalk(), 'update_value')(_param_value, func_params)

                            request_params = prv(_param_value)
                        testcase[param_type] = request_params

                # 处理中影入参
                request_params = testcase[param_type]
                if isinstance(request_params, str):
                    request_params = json.loads(request_params)
                request_params_json = copy.deepcopy(request_params)

                if host_tag in ConfigParse.get_host('backend'):  # 处理后台参数
                    request_params = parse_params(request_params)
                elif host_tag in [v['host'] for k, v in zy_serve.items() if k != 'backend']:  # app参数处理
                    token = get_extract_data('token')
                    if not token:
                        logs.warning('token未提取的，下面马上登录')
                        token = ZyAppLogin().write_token()
                    payload = {}
                    if "play" in old_host: # 演出模块入不需要扁平化处理
                        payload = request_params  # 原来的入参
                        token += f',{channel}' # 处理演出票token，后面需要+渠道编码
                    else:
                        for key, value in request_params.items():
                            # 处理value里的空格 扁平化处理
                            json_param_value = json.dumps(value, separators=(',', ':'))
                            payload[key] = json_param_value
                    payload['token'] = token
                    request_params = payload
                testcase[param_type] = request_params
                # 处理文件上传
                files = testcase.pop('files', None)
                file_list = []
                if files:
                    for fk, fv in files.items():
                        for path in fv:
                            fv = self.file_path + '\\' + path
                            file_list.append(
                                [fk, open(fv, 'rb')]
                            )
                files = file_list
                if host_tag in ConfigParse.get_host('backend'):
                    response = send_request_backend.execute_api_request(
                        api_name=api_name, url=url, method=method,
                        headers=headers,
                        case_name=case_name,
                        cookies=cookies,
                        files=files,
                        **testcase
                    )
                else:
                    response = send_request.execute_api_request(
                        api_name=api_name, url=url, method=method,
                        headers=headers,
                        case_name=case_name,
                        cookies=cookies,
                        files=files,
                        **testcase
                    )
                status_code, response_text = response.status_code, response.text
                logs.info(f'接口实际返回结果：{response_text}')

                # 安全地处理响应数据
                try:
                    response_data = response.json()
                    response_content = self.allure_attach_dict_result(response_data)
                except (ValueError, requests.exceptions.JSONDecodeError):
                    # 如果响应不是JSON格式，记录原始文本
                    response_data = None
                    response_content = f"非JSON响应 (状态码: {response.status_code}):\n{response.text}"
                    logs.warning(f"接口返回非JSON格式数据: {response.text[:200]}...")

                # 在allure报告Test body显示内容
                allure_info = {
                    '接口地址': url,
                    '接口名称': api_name,
                    '请求方式': method,
                    '请求头': self.allure_attach_dict_result(headers if headers else "无需请求头"),
                    'Cookie': self.allure_attach_dict_result(cookies if cookies else "无需Cookie"),
                    '测试用例名称': case_name,
                    '参数类型': param_type if param_type else "",
                    '请求参数json格式': self.allure_attach_dict_result(request_params_json),
                    '请求参数实际入参': self.allure_attach_dict_result(
                        request_params if request_params else "无需入参"),
                    '接口实际响应信息': response_content

                }

                for title, content in allure_info.items():
                    allure.attach(content, title, attachment_type=allure.attachment_type.JSON)

                # 先处理接口断言
                if response_data is not None:
                    self.asserts.assert_result(validation, response_data, status_code)
                else:
                    # 对于非JSON响应，只进行状态码断言
                    logs.warning(f"接口返回非JSON数据，跳过JSON内容断言，只验证状态码")
                    if validation:
                        for item in validation:
                            if 'code' in item and isinstance(item['code'], int):
                                # 只进行状态码断言
                                self.asserts.status_code_assert(status_code, item['code'])
                            else:
                                logs.warning(f"跳过非状态码断言: {item}")
                    # 如果是405错误，标记为失败
                    if status_code == 405:
                        raise AssertionError(f"接口返回405 Not Allowed错误: {response.text[:200]}")

                # 再处理接口返回值提取 0值是excel里不用提取的情况
                if extract is not None and extract != 0:
                    DebugTalk().extract_data(extract, response_text)
                    # self.extract_data(extract, response_text)
                if extract_list is not None and extract_list != 0:
                    DebugTalk().extract_data_list(extract_list, response_text)

                # 添加控制台日志到Allure报告
                self.attach_console_logs_to_allure()

        except Exception as e:
            logs.error(f'出现未知异常，-- {str(traceback.format_exc())}')
            # 即使出现异常也要添加控制台日志
            self.attach_console_logs_to_allure()
            raise e

    def attach_console_logs_to_allure(self):
        """
        将当前测试用例的控制台日志附加到Allure报告中
        """
        try:
            import os
            from datetime import datetime, timedelta

            # 获取今天的日志文件
            today = datetime.now().strftime('%Y%m%d')
            log_file = f'logs/test.{today}.log'

            if os.path.exists(log_file):
                try:
                    with open(log_file, 'r', encoding='utf-8') as f:
                        lines = f.readlines()

                    # 查找当前测试用例相关的日志
                    current_test_logs = []
                    test_start_found = False
                    test_end_found = False

                    # 从后往前查找，找到最近的测试开始和结束标记
                    for i in range(len(lines) - 1, -1, -1):
                        line = lines[i]

                        # 查找测试结束标记
                        if '---------------接口测试结束---------------' in line and not test_end_found:
                            test_end_found = True
                            current_test_logs.insert(0, line)
                            continue

                        # 如果找到了结束标记，继续收集日志直到找到开始标记
                        if test_end_found:
                            current_test_logs.insert(0, line)

                            # 查找测试开始标记
                            if '---------------接口测试开始---------------' in line:
                                test_start_found = True
                                break

                    # 如果没有找到完整的测试日志，获取最近的50行
                    if not test_start_found or not current_test_logs:
                        current_test_logs = lines[-50:] if len(lines) > 50 else lines

                    # 处理日志内容
                    if current_test_logs:
                        log_content = ''.join(current_test_logs)

                        # 过滤和格式化日志
                        filtered_lines = []
                        for line in log_content.split('\n'):
                            line = line.strip()
                            if line and not line.startswith('['):  # 过滤掉pytest的颜色代码
                                # 清理ANSI颜色代码
                                import re
                                clean_line = re.sub(r'\x1b\[[0-9;]*m', '', line)
                                filtered_lines.append(clean_line)

                        filtered_logs = '\n'.join(filtered_lines)

                        if filtered_logs:
                            # 附加到Allure报告
                            allure.attach(
                                filtered_logs,
                                name="📋 测试执行日志",
                                attachment_type=allure.attachment_type.TEXT
                            )
                            # logs.info("测试执行日志已附加到Allure报告")

                except Exception as e:
                    logs.warning(f"读取日志文件失败: {str(e)}")
            else:
                logs.warning(f"日志文件不存在: {log_file}")

        except Exception as e:
            # 如果日志附加失败，不要影响测试执行
            logs.warning(f"附加控制台日志到Allure报告失败: {str(e)}")


if __name__ == '__main__':
    api_case_data = get_excel_case_data('../testcase/play/演出接口测试用例.xlsx')[:1]
    print(api_case_data)
    req = RequestsBase()
    res = prv(api_case_data)
    print(f'解析后：{res}')
    # res1 = getattr(DebugTalk(), "get_extract_data")(*["project_data", "setupAttachments"])
    # print(type(res1))
    # print(res1)
    # req.execute_test_cases(api_case_data)
