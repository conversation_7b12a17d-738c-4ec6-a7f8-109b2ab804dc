# -*- coding:utf-8 -*-
"""
@Project    :zy_ApiAuto
@File       :time_util.py
@IDE        :PyCharm
<AUTHOR>
@Date       :2025/5/23 17:46
"""
import time
from datetime import datetime, timedelta


class TimeUtil:
    """
    时间工具类
    """

    # 获取年月日时分秒
    @staticmethod
    def get_current_time(format_str='%Y-%m-%d %H:%M:%S'):
        """
        获取当前时间 年月日时分秒
        :param format_str: 格式化字符串，默认是'%Y-%m-%d %H:%M:%S'
        :return: 示例 2025-05-23 17:46:00
        """
        return datetime.now().strftime(format_str)

    @staticmethod
    def getDate(later_days=0):
        """
        默认获取当前日期  年月日
        :param later_days: 在当前日期的基础上，往后推多少天，默认是0（负数往前推）
        :return: 示例 2025-05-23
        """
        days_later = (datetime.now() + timedelta(days=later_days)).strftime('%Y-%m-%d')
        return days_later

    @staticmethod
    def get_later_hour(later_hours=0):
        """
        默认获取当前日期  年月日时分秒
        :param later_hours:  在当前日期的基础上，往后推XX小时，默认是0（负数往前推）
        :return: 示例 2025-05-23 17:46:00
        """
        hours_later = (datetime.now() + timedelta(hours=later_hours)).strftime('%Y-%m-%d %H:%M:%S')
        return hours_later

    @staticmethod
    def get_h_m_s():
        """
        获取当前时间  时分秒
        :return: str 示例 17:46:00
        """
        return datetime.now().strftime('%H:%M:00')

    @staticmethod
    def current_timestamp():
        """
        获取当前时间戳10位，int类型
        :return: int 示例 1721846360
        """
        return int(time.time())

    @staticmethod
    def get_millisecond_timestamp():
        """
        生成毫秒时间戳 13位
        :return: int 示例 1721846360000
        """
        return int(time.time() * 1000)

    @staticmethod
    def get_days_between(date1, date2):
        """
        计算两个日期之间的天数差异
        :param date1:
        :param date2:
        :return: 示例 100
        """
        date1 = datetime.strptime(date1, '%Y-%m-%d')
        # date1 = datetime(2025, 5, 1)
        date2 = datetime.strptime(date2, '%Y-%m-%d')
        return (date2 - date1).days

    @staticmethod
    def countdown(t):
        """
        定时器
        :param t:
        :return:
        """
        while t:
            mins, secs = divmod(t, 60)
            timer = '{:02d}:{:02d}'.format(mins, secs)
            print(timer, end="\r")
            time.sleep(1)
            t -= 1
        print('Time up!')


time_util = TimeUtil()
