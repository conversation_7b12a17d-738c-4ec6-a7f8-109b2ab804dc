# -*- coding:utf-8 -*-
import logging
import os
import time
from logging.handlers import RotatingFileHandler  # 按文件大小滚动备份

import colorlog

from configs.setting import FILE_PATH

logs_path = FILE_PATH['log']
if not os.path.exists(logs_path):
    os.mkdir(logs_path)

logfile_name = logs_path + '/test.{}.log'.format(time.strftime('%Y%m%d'))


class HandleLogs:

    @classmethod
    def setting_log_color(cls):
        log_color_config = {
            'DEBUG': 'cyan',
            'INFO': 'green',
            'ERROR': 'red',
            'WARNING': 'yellow',
            'CRITICAL': 'red'
        }
        formatter = colorlog.ColoredFormatter(
            '%(log_color)s %(levelname)s - %(asctime)s - %(filename)s:%(lineno)d -[%(module)s:%(funcName)s] - '
            '%(message)s',
            log_colors=log_color_config)

        return formatter

    @classmethod
    def output_logs(cls):
        # 使用专用的日志记录器，避免与Web服务日志冲突
        logger = logging.getLogger('unit_tools.log_util.recordlog')
        steam_format = cls.setting_log_color()
        log_format = logging.Formatter(
            '%(levelname)s - %(asctime)s - %(filename)s:%(lineno)d -[%(module)s:%(funcName)s] - %(message)s'
        )
        logger.setLevel(logging.DEBUG)

        # 防止重复打印日志
        if not logger.handlers:
            # 检查是否在pytest环境中运行
            import sys
            is_pytest = 'pytest' in sys.modules

            if is_pytest:
                # 在pytest环境中，使用无颜色格式，确保Allure报告显示正确
                sh = logging.StreamHandler()
                sh.setLevel(logging.DEBUG)
                sh.setFormatter(steam_format)  # 无颜色，包含时间戳
                logger.addHandler(sh)

                # 在pytest环境中，初始设置为不传播，由conftest.py控制
                logger.propagate = False
            else:
                # 在非pytest环境中，使用带颜色的格式
                sh = logging.StreamHandler()
                sh.setLevel(logging.DEBUG)
                sh.setFormatter(steam_format)  # 带颜色
                logger.addHandler(sh)

                # 在非pytest环境中也不传播，避免重复
                logger.propagate = False

            # 把日志输出到文件里面（无颜色，包含时间戳）
            fh = RotatingFileHandler(filename=logfile_name, mode='a', maxBytes=5242880, backupCount=7, encoding='utf-8')
            fh.setLevel(logging.DEBUG)
            fh.setFormatter(log_format)
            logger.addHandler(fh)
        return logger


handle = HandleLogs()
logs = handle.output_logs()
