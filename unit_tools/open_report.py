# -*- coding: utf-8 -*-
"""
@Project    :http_server
@File       :http_server.py
@IDE        :PyCharm
<AUTHOR>
@Date       :2025/4/21 09:38
"""
import http.server
import socketserver
import os
from functools import partial
import sys
import webbrowser
import threading
import time
import socket
import random


class NoCacheHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    """自定义HTTP请求处理器，禁用缓存"""

    def end_headers(self):
        """在响应头结束前添加禁用缓存的头部"""
        self.send_header('Cache-Control', 'no-cache, no-store, must-revalidate')
        self.send_header('Pragma', 'no-cache')
        self.send_header('Expires', '0')
        super().end_headers()

    def log_message(self, format, *args):
        """重写日志方法，减少输出"""
        return


class HttpServer:
    def __init__(self, bind: str = "127.0.0.1", port: int = 8000, directory=os.getcwd()):
        """
        :param bind: 指定地址，如本地主机
        :param port: 自定义端口号, 服务器默认监听端口是 8000
        :param directory: 指定工作目录, 服务器默认工作目录为当前目录
        """
        self.bind = bind
        self.port = port

        # 处理命令行参数
        custom_dir = None
        force_current_dir = False
        args = sys.argv
        for i in range(1, len(args)):
            if args[i] == "-port" and i + 1 < len(args):
                self.port = int(args[i + 1])
            if args[i] == "-dir" and i + 1 < len(args):
                custom_dir = args[i + 1]
            if args[i] == "-bind" and i + 1 < len(args):
                self.bind = args[i + 1]
            if args[i] == "-current":
                force_current_dir = True

        # 获取资源目录路径，支持PyInstaller打包
        if custom_dir:
            # 如果指定了自定义目录，使用指定的目录
            self.directory = os.path.abspath(custom_dir)
            print(f"使用指定的目录: {self.directory}")
        elif force_current_dir:
            # 如果指定了-current参数，强制使用脚本所在目录
            self.directory = os.path.dirname(os.path.abspath(__file__))
            print(f"强制使用当前目录: {self.directory}")
        else:
            # 否则自动检测
            self.directory = self._get_resource_path(force_current_dir)
            print(f"自动检测的资源目录: {self.directory}")

        # 在处理完命令行参数后，检查并设置可用端口
        self.port = self._get_available_port(self.port)

    def _is_port_available(self, port):
        """检查端口是否可用"""
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
                sock.settimeout(1)
                # 尝试绑定端口，如果成功说明端口可用
                sock.bind((self.bind, port))
                return True
        except Exception as e:
            print(f"端口 {port} 不可用: {e}")
            return False

    def _get_available_port(self, preferred_port):
        """获取可用端口，如果首选端口被占用则使用随机端口"""
        if self._is_port_available(preferred_port):
            print(f"端口 {preferred_port} 可用")
            return preferred_port

        print(f"端口 {preferred_port} 被占用，正在寻找可用端口...")

        # 尝试一些常用端口
        common_ports = [8080, 8081, 8082, 8083, 8084, 8085, 9000, 9001, 9002]
        for port in common_ports:
            if self._is_port_available(port):
                print(f"找到可用端口: {port}")
                return port

        # 如果常用端口都被占用，使用随机端口
        for _ in range(10):  # 最多尝试10次
            random_port = random.randint(8000, 9999)
            if self._is_port_available(random_port):
                print(f"使用随机端口: {random_port}")
                return random_port

        # 如果还是找不到，让系统自动分配
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
            sock.bind(('', 0))
            auto_port = sock.getsockname()[1]
            print(f"使用系统自动分配端口: {auto_port}")
            return auto_port

    def _get_resource_path(self, force_current_dir=False):
        """获取资源目录路径，支持PyInstaller打包和开发环境"""
        # 检查是否在PyInstaller打包环境中
        if getattr(sys, 'frozen', False) and hasattr(sys, '_MEIPASS'):
            # PyInstaller打包后的环境
            print(f"检测到PyInstaller环境")

            # 优先检查exe同目录下的report文件夹（实际的报告数据）
            exe_dir = os.path.dirname(sys.executable)
            print(f"exe目录: {exe_dir}")

            # 检查exe同目录下的report文件夹
            report_dir = os.path.join(exe_dir, "report")
            if os.path.exists(os.path.join(report_dir, "index.html")):
                print(f"使用exe同目录的报告: {report_dir}")
                return report_dir

            # 检查exe同目录是否直接就是报告目录
            if os.path.exists(os.path.join(exe_dir, "index.html")):
                print(f"exe目录本身就是报告目录: {exe_dir}")
                return exe_dir

            # 检查更多可能的外部报告位置
            additional_paths = [
                # 检查exe上级目录的report文件夹
                os.path.join(os.path.dirname(exe_dir), "report"),
                # 检查当前工作目录的report文件夹
                os.path.join(os.getcwd(), "report"),
                # 检查当前工作目录本身
                os.getcwd()
            ]

            for report_path in additional_paths:
                index_path = os.path.join(report_path, "index.html")
                if os.path.exists(index_path):
                    print(f"找到外部报告文件: {index_path}")
                    return report_path

            # 如果都没找到外部报告，给出明确的错误提示
            print(f"错误: 未找到报告文件!")
            print(f"请确保以下位置之一存在index.html文件:")
            print(f"  - {os.path.join(exe_dir, 'report')}")
            print(f"  - {exe_dir}")
            print(f"  - {os.path.join(os.path.dirname(exe_dir), 'report')}")
            print(f"  - {os.path.join(os.getcwd(), 'report')}")
            print(f"  - {os.getcwd()}")

            # 返回exe目录作为默认值
            return exe_dir
        else:
            # 开发环境
            script_dir = os.path.dirname(os.path.abspath(__file__))
            print(f"开发环境，脚本目录: {script_dir}")

            # 检查脚本所在目录的文件
            current_index = os.path.join(script_dir, "index.html")
            report_subdir_index = os.path.join(script_dir, "report", "index.html")

            print(f"检查当前目录index.html: {current_index} - 存在: {os.path.exists(current_index)}")
            print(f"检查report子目录index.html: {report_subdir_index} - 存在: {os.path.exists(report_subdir_index)}")

            # 优先检查脚本所在目录是否直接就是报告目录
            if os.path.exists(current_index):
                print(f"脚本目录本身就是报告目录: {script_dir}")
                return script_dir

            # 检查是否存在report子目录下的index.html
            if os.path.exists(report_subdir_index):
                print(f"使用report子目录: {os.path.join(script_dir, 'report')}")
                return os.path.join(script_dir, "report")

            # 默认返回脚本目录
            print(f"未找到报告文件，使用脚本目录: {script_dir}")
            return script_dir

    def open_browser(self):
        """延迟打开浏览器"""
        time.sleep(1)  # 等待服务器启动
        url = f"http://{self.bind}:{self.port}/"
        print(f"正在打开浏览器: {url}")
        webbrowser.open(url)

    def run(self):
        try:
            with socketserver.TCPServer((self.bind, self.port), partial(NoCacheHTTPRequestHandler,
                                                                        directory=self.directory)) as httpd:
                print(
                    f"工作目录：{self.directory}\n"
                    f"Serving HTTP on {self.bind} port {self.port} \n"
                    f"http://{self.bind}:{self.port}/ ..."
                )

                # 在后台线程中打开浏览器
                browser_thread = threading.Thread(target=self.open_browser)
                browser_thread.daemon = True
                browser_thread.start()

                httpd.serve_forever()
        except KeyboardInterrupt:
            print("\nKeyboard interrupt received, exiting.")
            sys.exit(0)


if __name__ == '__main__':
    server = HttpServer()
    server.run()
