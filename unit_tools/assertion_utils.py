# -*- coding:utf-8 -*-
import operator
from typing import Callable, Any
import allure
import jsonpath
from unit_tools.db_connector.connectMysql import ConnectMysql
from unit_tools.exception_utils.exceptions import AssertTypeError
from unit_tools.log_util.recordlog import logs


class Assertions:
    """
    接口断言模式封装
    1） 状态码断言
    2） 包含模式断言
    3） 相等断言
    4） 不相等断言
    5） 数据库断言
    """

    @classmethod
    def status_code_assert(cls, expected_result, status_code):
        """
        接口的响应状态码断言
        :param expected_result: （int）用例code模式中的预期状态码
        :param status_code: （int）接口实际返回的状态码
        :return:
        """
        # 断言状态标识，0 表示成功，其他表示失败
        failure_count = 0
        if not isinstance(expected_result, int):
            expected_result = int(expected_result)

        if expected_result == status_code:
            logs.info(f'状态码断言成功：接口实际返回状态码 {status_code} == {expected_result}')
            allure.attach(f"预期结果：{str(expected_result)}\n实际结果：{str(status_code)}", "状态码断言结果：成功",
                          attachment_type=allure.attachment_type.TEXT)
        else:
            logs.error(f'状态码断言失败：接口实际返回状态码 {status_code} != {expected_result}')
            failure_count += 1
            allure.attach(f"预期结果：{str(expected_result)}\n实际结果：{str(status_code)}", "状态码断言结果：失败",
                          attachment_type=allure.attachment_type.TEXT)

        return failure_count

    @classmethod
    def contain_assert(cls, expected_result, response):
        """
        字符串包含模式，断言预期结果字符串是否包含在接口的实际响应返回信息中
        :param expected_result: （dict）用例里面contain模式的数据
        :param response: （dict）接口的实际响应信息
        :return:
        """
        # 断言状态标识，0 表示成功，其他表示失败
        failure_count = 0
        for assert_key, assert_value in expected_result.items():
            response_list = jsonpath.jsonpath(response, f'$..{assert_key}')
            response_str = ''.join(response_list) if response_list else ''

            if response_list and isinstance(response_list[0], str):
                success_message = f"包含模式断言成功：预期结果【{assert_value}】存在于实际结果【{response_str}】中"
                if assert_value in response_str:
                    logs.info(success_message)
                    allure.attach(f"预期结果：{str(assert_value)}\n实际结果：{response_str}",
                                  "包含断言结果：成功",
                                  attachment_type=allure.attachment_type.TEXT)
            else:
                failure_message = f"包含模式断言失败：预期结果【{assert_value}】未在实际结果【{response_str}】中找到"
                failure_count += 1
                logs.error(failure_message)
                allure.attach(f"预期结果：{str(assert_value)}\n实际结果：{response_str}",
                              "包含断言结果：失败",
                              attachment_type=allure.attachment_type.TEXT)
        return failure_count

    @classmethod
    def equal_assert(cls, expected_result, response):
        """
        相等断言，根据yaml里面的validation关键词下面的eq模式数据去跟接口实际响应信息对比
        :param expected_result: （dict）yaml里面的eq值
        :param response: （dict）接口实际响应结果
        :return:
        """
        failure_count = 0
        if isinstance(response, dict) and isinstance(expected_result, dict):
            # 找出实际结果与预期结果共同的key值
            common_key = list(cls.get_all_keys(response) & set(expected_result.keys()))
            if common_key:
                common_key = common_key[0]
                # 根据相同的key值去实际结果中获取，并重新生成一个实际结果的字典
                new_actual_result = {common_key: jsonpath.jsonpath(response, f'$..{common_key}')[0]}
                eq_assert = operator.eq(new_actual_result, expected_result)
                if eq_assert:
                    logs.info(f"相等断言成功：接口实际结果 {new_actual_result} == 预期结果：{expected_result}")
                    allure.attach(f"预期结果：{str(expected_result)}\n实际结果：{str(new_actual_result)}",
                                  "相等断言结果：成功",
                                  attachment_type=allure.attachment_type.JSON)
                else:
                    failure_count += 1
                    logs.error(f"相等断言失败：接口实际结果 {new_actual_result} != 预期结果：{expected_result}")
                    allure.attach(f"预期结果：{str(expected_result)}\n实际结果：{str(new_actual_result)}",
                                  "相等断言结果：失败",
                                  attachment_type=allure.attachment_type.JSON)
            else:
                failure_count += 1
                logs.error('相等断言失败，请检查用例eq模式的预期结果或接口返回值是否正确')

        return failure_count

    @classmethod
    def not_equal_assert(cls, expected_result, response):
        """
        不相等断言，根据yaml里面的validation关键词下面的ne模式数据去跟接口实际响应信息对比
        :param expected_result: （dict）yaml里面的eq值
        :param response: （dict）接口实际响应结果
        :return:
        """
        failure_count = 0
        if isinstance(response, dict) and isinstance(expected_result, dict):
            # 找出实际结果与预期结果共同的key值
            common_key = list(cls.get_all_keys(response) & set(expected_result.keys()))
            if common_key:
                common_key = common_key[0]
                # 根据相同的key值去实际结果中获取，并重新生成一个实际结果的字典
                new_actual_result = {common_key: jsonpath.jsonpath(response, f'$..{common_key}')[0]}
                eq_assert = operator.ne(new_actual_result, expected_result)
                if eq_assert:
                    logs.info(f"不相等断言成功：接口实际结果 {new_actual_result} != 预期结果：{expected_result}")
                    allure.attach(f"预期结果：{str(expected_result)}\n实际结果：{str(response)}",
                                  "不相等断言结果：成功",
                                  attachment_type=allure.attachment_type.TEXT)
                else:
                    failure_count += 1
                    logs.error(f"不相等断言失败：接口实际结果 {new_actual_result} == 预期结果：{expected_result}")
                    allure.attach(f"预期结果：{str(expected_result)}\n实际结果：{str(response)}",
                                  "不相等断言结果：失败",
                                  attachment_type=allure.attachment_type.TEXT)
            else:
                failure_count += 1
                logs.error('不相等断言失败，请检查用例eq模式的预期结果或接口返回值是否正确')

        return failure_count

    @classmethod
    def database_assert(cls, expected_result, status_code=None):
        """
        数据库断言
        :param expected_result: 用例db模式中的SQL语句预期结果
        :param status_code: 不做任何操作
        :return:
        """
        failure_count = 0
        conn = ConnectMysql()
        db_value = conn.query(expected_result)
        if db_value is not None:
            logs.info('数据库断言成功')
            allure.attach(f"预期结果：{str(expected_result)}\n实际结果：{db_value}",
                          "数据库断言结果：成功",
                          attachment_type=allure.attachment_type.TEXT)
        else:
            failure_count += 1
            logs.error('数据库断言失败，请检查数据库是否存在该数据')
            allure.attach(f"预期结果：{str(expected_result)}\n实际结果：{db_value}",
                          "数据库断言结果：失败",
                          attachment_type=allure.attachment_type.TEXT)
        return failure_count

    @classmethod
    def assert_result(cls, expected_result, response, status_code):
        """
        断言主函数，通过all_flag标记，如all_flag == 0表示测试成功，否则为失败
        :param expected_result: （list）用例validation关键词下面的预期结果
        :param response: （dict）接口的实际响应信息
        :param status_code: （int）接口的实际响应状态码
        :return:
        """
        all_flag = 0
        # 通过字典映射方式管理不同的断言方式
        assert_methods = {
            'code': cls.status_code_assert,
            'contain': cls.contain_assert,
            'eq': cls.equal_assert,
            'ne': cls.not_equal_assert,
            'db': cls.database_assert
        }

        try:
            for yq in expected_result:
                for assert_mode, assert_value in yq.items():
                    # 表示assert_method是一个接受两个参数，类型为Any表示可以是任意类型，并返回整数的可调用对象
                    assert_method: Callable[[Any, Any], int] = assert_methods.get(assert_mode)
                    if assert_method:
                        # 调用对应的断言方法，传递适当的参数
                        if assert_mode in ['code', 'db']:
                            flag = assert_method(assert_value, status_code)
                        else:
                            flag = assert_method(assert_value, response)
                        all_flag += flag
                    else:
                        raise AssertTypeError(f'不支持{assert_mode}该断言模式')

        except Exception as exceptions:
            raise exceptions

        assert all_flag == 0, '测试失败'
        logs.info('测试成功')

    @classmethod
    def get_all_keys(cls, data, keys=None):
        """
        需要递归获取所有的key,字典还可能是列表嵌套字典
        :param data: 字典 dict
        :param keys:
        :return: set集合  {'a', 'b', 'c', 'd'}
        """

        if keys is None:
            keys = set()  # 使用集合避免重复键
        if isinstance(data, dict):
            for key, value in data.items():
                keys.add(key)
                cls.get_all_keys(value, keys)  # 递归处理值
        elif isinstance(data, list):
            for item in data:
                cls.get_all_keys(item, keys)  # 递归处理列表中的每个元素
        return keys




if __name__ == '__main__':
    ass = Assertions()
    res = {
        "head": {
            "errCode": "0",
            "errMsg": "操作成功。",
            "tradeId": "getNewsList",
            "timestamp": "20250416104917"
        }
    }
    print(ass.contain_assert({"errCode": "0"}, res))
    print(ass.contain_assert({"errMsg": "操作成功。"}, res))
