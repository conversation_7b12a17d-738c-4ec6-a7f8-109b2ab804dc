# -*- coding:utf-8 -*-
"""
@Project    :zy_ApiAuto
@File       :use_conf.py
@IDE        :PyCharm
<AUTHOR>
@Date       :2025/6/18 15:29
@Description: 存放项目的常用配置信息
"""

# 测试账号信息
Users = {
    'dev': {
        'user': 'qinqingrong',
        'password': 'Qqr_9986'
    },
    'sit': {
        'user': 'WEI',
        'password': '123456'
    }
}

# 项目渠道密钥
Channels = {
    'sit': {
        'android': [
            '888888',
            'C10000027'
        ],
    },
    'dev': {
        'android': [
            'zyandriod',
            'C10000007',
        ],
    }
}
# 区域编码
AREA_CODE = {
    '北京': '110000',
    '上海': '310000',
    '广州': '440100',
    '深圳': '440300',
}

# 中影服务器地址和请求头
header = {
    # "User-Agent": "Android/HUAWEI-BRA-AL00-marlin-android9-2.49.0-1080*1920-1080.0*1920.0",
    "Content-Type": "application/x-www-form-urlencoded",
    "Connection": "Keep-Alive",
    "Accept-Encoding": "gzip"
}
ZY_SERVER = {
    'SIT': {  # 后台
        'backend': {
            'host': 'http://*************:40',
            'headers': None
        },
        # 前端C端
        'app': {
            'host': 'http://*************:1984/api/app/handler',
            'headers': header
        },
        # 前台演出模块
        'play': {
            'host': 'http://*************',
            'headers': header
        },
    },
    'DEV': {
        # 后台
        'backend': {
            'host': 'https://cloud.cfc.com.cn',
            'headers': None
        },
        # 前端C端
        'app': {
            'host': 'https://ownapi.cfc.com.cn/api/app/handler.do',
            'headers': header
        },
        # 前台演出模块
        'play': {
            'host': 'https://mallxcx.cfc.com.cn/',
            'headers': header
        },
        # 商城
        'mall':{
            'host': 'https://mallxcx.cfc.com.cn',
            'headers': header
        }
    }

}
# print([v['ip'] for k, v in zy_serve.items() if k != 'backend'])
