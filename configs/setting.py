import os
import sys

from configs.use_conf import Users, Channels, ZY_SERVER, AREA_CODE

DIR_PATH = os.path.dirname(os.path.dirname(__file__))
sys.path.append(DIR_PATH)

api_env_dev = False  # 设置接口环境，默认是False-sit True-dev


def join_path(args):
    """需要传入列表，元组，可迭代对象"""
    if isinstance(args, (list, tuple)):
        return os.path.join(DIR_PATH, *args)
    else:
        print('传入参数有误，请检查类型')
        return False


FILE_PATH = {
    'extract': join_path(['files', 'extract.yaml']),
    'sql_conf': join_path(('configs', 'sql_config.ini')),
    'log': join_path(['logs']),
    'file': join_path(['files']),
    'js': join_path(['login/js']),
}

if api_env_dev:
    test_users = (Users['dev']['user'], Users['dev']['password'])
else:
    test_users = (Users['sit']['user'], Users['sit']['password'])
# 测试区域编码
area_code = AREA_CODE['深圳']
# 固定经纬度
lon = "114.12024"
lat = "22.55125"

# 中影服务器地址和请求
zy_serve = ZY_SERVER['DEV'] if api_env_dev else ZY_SERVER['SIT']
# print([v['host'] for k, v in zy_serve.items() if k != 'backend'])

key = Channels['dev']['android'][0] if api_env_dev else Channels['sit']['android'][0]
channel = Channels['dev']['android'][1] if api_env_dev else Channels['sit']['android'][1]

phone = '19877292090'  # 测试环境-查询数据库获取验证码  生产直接用特殊渠道登录（不需要验证码）

# 设置是否发送邮件，默认为False则不会发送
is_qq_msg = True
# is_qq_msg = False
mail_host = 'smtp.qq.com'
sender = '<EMAIL>'  # 发件人的地址
sender_password = 'xqqesnhnuqxybbbg'  # 此处是我们刚刚在邮箱中获取的授权码
receivers = '<EMAIL>'  # 邮件接受方邮箱地址，可以配置多个，实现群发，注意这里要是字符串

# 设置是否发送钉钉群消息，默认为False则不会发送
is_dd_msg = False

# 是否发送企业微信消息，默认为False则不会发送
is_qw_msg = True
# 企业微信机器人key
qw_key = '24947eb7-32ee-4568-b8f2-11d614edd789'

# 钉钉机器人签名和地址
secret = 'SECb163daa45904540212492d8ad7bf7c3ce428fae5211c2e94b1f0926be0778191'
webhook = 'https://oapi.dingtalk.com/robot/send?access_token=' \
          'df849617e1f9593fd9c31f75ce4fdf2fea8fec39c7b714a65f20413444f5cea5'
