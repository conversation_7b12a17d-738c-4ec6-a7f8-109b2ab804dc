headers:
  Content-Type: application/x-www-form-urlencoded
login_info:
  birthday: ''
  lastTime: '20250707095014'
  isCurrentRegister: '0'
  zipCode: ''
  sign: 156fde85f143d4440e51aeb931c22eb6
  imageNo: 0
  points: '0'
  cityName: ''
  areaName: ''
  areaNo: ''
  imageUrl: ''
  provinceNo: ''
  email: ''
  QQ: ''
  cityNo: ''
  address: ''
  level: '2'
  nickName: ''
  sex: 2
  mobile: '19877292090'
  MSN: ''
  identityCard: ''
  sessionId: '346518532528558681'
  userId: '346516136741756608'
  isMobileValid: 1
  loginCount: 0
  token: 0sVf/9TsF/vUNfiRV10Rd78j9/KxSzElenZLaHZPfBXYeSVK8p+YV6865yT2Gq5WkbHRxS9K/4EOw2l8VxrP9AfQjRHVXZ3Q2LRUpzjehTn5b5j2V/3NtNIx0Jw1sIdK
  realName: ''
  createTime: '1751613674000'
  provinceName: ''
  username: ''
token: 0sVf/9TsF/vUNfiRV10Rd78j9/KxSzElenZLaHZPfBXYeSVK8p+YV6865yT2Gq5WkbHRxS9K/4EOw2l8VxrP9AfQjRHVXZ3Q2LRUpzjehTn5b5j2V/3NtNIx0Jw1sIdK
performance_id: '26'
showInfo:
  showUnusualStatus: 0
  showId: '48'
  name: 2025-12-01 周一 00:00
ticketInfo:
  currentAmount: 20
  ticketUnitId: '214'
  price: 11
fetchTicketWayId: 4386733
fetchType: 5
orderId: '2025070709543516470'
article_id: 292
summary: "\n    测试环境！！！！！\n    详情请查看附件~~~\n    自动化测试结果简单汇总，通知如下，执行结果如下：\n    测试用例总数：14\
  \  \n    测试用例通过数：14\n    通过率：100%\n    测试用例失败数：0\n    失败率：0%\n    错误数量：0\n    错误率：0%\
  \       \n    跳过执行数量：0\n    执行总时长：29.43s (0:00:29)\n    "
