headers:
  Content-Type: application/x-www-form-urlencoded
login_info:
  birthday: ''
  lastTime: '20250707092628'
  isCurrentRegister: '0'
  zipCode: ''
  sign: ac28348381b0f65b0a9aa26be513336e
  imageNo: 0
  points: '0'
  cityName: ''
  areaName: ''
  areaNo: ''
  imageUrl: ''
  provinceNo: ''
  email: ''
  QQ: ''
  cityNo: ''
  address: ''
  level: '2'
  nickName: ''
  sex: 2
  mobile: '19877292090'
  MSN: ''
  identityCard: ''
  sessionId: '346518524795538677'
  userId: '346516136741756608'
  isMobileValid: 1
  loginCount: 0
  token: 8y4eoCKpp9tcMjp51DV4UEmI1hegk73JSMjjz1bMf39BjiKzdJqOMBsgTCwgY7EYCzAR1uOc6Yqxj4jNblZyacedjpTnhEFWf6eGZZnSrZzreIYsloXPIsHpej7HdUL6
  realName: ''
  createTime: '1751613674000'
  provinceName: ''
  username: ''
token: 8y4eoCKpp9tcMjp51DV4UEmI1hegk73JSMjjz1bMf39BjiKzdJqOMBsgTCwgY7EYCzAR1uOc6Yqxj4jNblZyacedjpTnhEFWf6eGZZnSrZzreIYsloXPIsHpej7HdUL6
performance_id: '26'
showInfo:
  showUnusualStatus: 0
  showId: '48'
  name: 2025-12-01 周一 00:00
ticketInfo:
  currentAmount: 20
  ticketUnitId: '214'
  price: 11
fetchTicketWayId: 4386733
fetchType: 5
orderId: '2025070709414228360'
summary: "\n    测试环境！！！！！\n    详情请查看附件~~~\n    自动化测试结果简单汇总，通知如下，执行结果如下：\n    测试用例总数：13\
  \  \n    测试用例通过数：13\n    通过率：100%\n    测试用例失败数：0\n    失败率：0%\n    错误数量：0\n    错误率：0%\
  \       \n    跳过执行数量：0\n    执行总时长：30.94s (0:00:30)\n    "
