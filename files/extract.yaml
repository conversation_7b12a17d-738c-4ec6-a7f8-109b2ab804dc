headers:
  Content-Type: application/x-www-form-urlencoded
login_info:
  birthday: ''
  lastTime: '20250707100230'
  isCurrentRegister: '0'
  zipCode: ''
  sign: 00281f86c0f8cd548137135dd217f9de
  imageNo: 0
  points: '0'
  cityName: ''
  areaName: ''
  areaNo: ''
  imageUrl: ''
  provinceNo: ''
  email: ''
  QQ: ''
  cityNo: ''
  address: ''
  level: '1'
  nickName: ''
  sex: 2
  mobile: '19877292898'
  MSN: ''
  identityCard: ''
  sessionId: '346518539094358685'
  userId: '346422044320518083'
  isMobileValid: 1
  loginCount: 0
  token: Qe6Ksfgh3qb79B03Du4KRxqmaA/hYC1+xsEsPwf8whRUz+WKDKmpzWT26buO2HqW0XOCuwkxwoYJamrcDoQPy0lJKXp3E5fBDtbroNS2GzraxiiEAdZ8BrEeHrFsgXGM
  realName: ''
  createTime: '1742204432000'
  provinceName: ''
  username: ''
token: Qe6Ksfgh3qb79B03Du4KRxqmaA/hYC1+xsEsPwf8whRUz+WKDKmpzWT26buO2HqW0XOCuwkxwoYJamrcDoQPy0lJKXp3E5fBDtbroNS2GzraxiiEAdZ8BrEeHrFsgXGM
performance_id: '26'
showInfo:
  showUnusualStatus: 0
  showId: '48'
  name: 2025-12-01 周一 00:00
ticketInfo:
  currentAmount: 20
  ticketUnitId: '214'
  price: 11
fetchTicketWayId: 4386733
fetchType: 5
orderId: '2025070710053194911'
article_id: 292
summary: "\n    测试环境！！！！！\n    详情请查看附件~~~\n    自动化测试结果简单汇总，通知如下，执行结果如下：\n    测试用例总数：14\
  \  \n    测试用例通过数：14\n    通过率：100%\n    测试用例失败数：0\n    失败率：0%\n    错误数量：0\n    错误率：0%\
  \       \n    跳过执行数量：0\n    执行总时长：30.47s (0:00:30)\n    "
