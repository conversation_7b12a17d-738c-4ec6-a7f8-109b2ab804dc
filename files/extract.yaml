headers:
  Content-Type: application/x-www-form-urlencoded
login_info:
  birthday: ''
  lastTime: '20250707110705'
  isCurrentRegister: '0'
  zipCode: ''
  sign: 238e47b873fcdd4c125d2101d5bc3cae
  imageNo: 0
  points: '0'
  cityName: ''
  areaName: ''
  areaNo: ''
  imageUrl: ''
  provinceNo: ''
  email: ''
  QQ: ''
  cityNo: ''
  address: ''
  level: '1'
  nickName: ''
  sex: 2
  mobile: '19877292898'
  MSN: ''
  identityCard: ''
  sessionId: '346518664341478696'
  userId: '346422044320518083'
  isMobileValid: 1
  loginCount: 0
  token: lrDqVLEc8jnKaORIKiJB+y6nN1Io3lnKNueVwmjECnPs3mUEcTEbKdyma0pUIUY8s2ItjSU2QLLYFxywf0Grw9Hfdp3svbaytqb6Sc0oYJ/tO8wd56XQTybpwRkS8Qt8
  realName: ''
  createTime: '1742204432000'
  provinceName: ''
  username: ''
token: lrDqVLEc8jnKaORIKiJB+y6nN1Io3lnKNueVwmjECnPs3mUEcTEbKdyma0pUIUY8s2ItjSU2QLLYFxywf0Grw9Hfdp3svbaytqb6Sc0oYJ/tO8wd56XQTybpwRkS8Qt8
performance_id: '26'
showInfo:
  showUnusualStatus: 0
  showId: '48'
  name: 2025-12-01 周一 00:00
ticketInfo:
  currentAmount: 20
  ticketUnitId: '214'
  price: 11
fetchTicketWayId: 4386733
fetchType: 5
orderId: '2025070713341762949'
article_id: 292
summary: "\n    测试环境！！！！！\n    详情请查看附件~~~\n    自动化测试结果简单汇总，通知如下，执行结果如下：\n    测试用例总数：14\
  \  \n    测试用例通过数：14\n    通过率：100%\n    测试用例失败数：0\n    失败率：0%\n    错误数量：0\n    错误率：0%\
  \       \n    跳过执行数量：0\n    执行总时长：30.74s (0:00:30)\n    "
