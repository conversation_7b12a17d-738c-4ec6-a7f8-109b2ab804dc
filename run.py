import os
import shutil
import sys
import time
import pytest  # pip install pytest==8.0.2
from configs.setting import DIR_PATH, is_qq_msg, is_qw_msg, FILE_PATH
from unit_tools.handle_data.yaml_handler import get_extract_yaml
from unit_tools.log_util.recordlog import logs
from unit_tools.other_util.qw_robot import send_qw_msg
from unit_tools.other_util.qq_email import qq_email

# import execjs  # pip install PyExecJS2==1.6.1
# # print(execjs.get().name)  # 安装node配置环境变量后需要重启电脑才是 V8

if __name__ == '__main__':
    # 记录环境信息
    logs.info(f"Python版本: {sys.version}")
    logs.info(f"Python可执行文件: {sys.executable}")
    logs.info(f"当前工作目录: {os.getcwd()}")
    logs.info(f"脚本参数: {sys.argv}")

    testpaths = []
    if len(sys.argv) > 1:
        args = sys.argv[1:]
        for arg in args:
            if '/' in arg or '\\' in arg:
                testpaths.append(arg)
            else:
                testpaths.append(f'./testcase/{arg}')
    else:  # 没有命令行参数，默认执行所有
        testpaths.append('./testcase/demo_test')
    # testpaths = ['./testcase/demo_test', './testcase/play']
    # print(testpaths)
    # #用例执行入口
    if all([1 if os.path.exists(i) else 0 for i in testpaths]):
        logs.info(f'执行路径【{testpaths}】')

        # 彻底清理所有旧文件，确保生成全新报告
        logs.info('开始清理所有旧的报告和结果文件...')

        # 1. 删除整个report目录下的report文件夹
        report_dir = './report/report'
        if os.path.exists(report_dir):
            logs.info('删除旧的报告目录...')
            shutil.rmtree(report_dir, ignore_errors=True)

        # 2. 删除temp目录下的所有文件（除了environment.xml）
        temp_dir = './report/result/temp'
        if os.path.exists(temp_dir):
            logs.info('清理temp目录下的所有旧文件...')
            for file in os.listdir(temp_dir):
                if file != 'environment.xml':
                    file_path = os.path.join(temp_dir, file)
                    try:
                        if os.path.isfile(file_path):
                            os.remove(file_path)
                        elif os.path.isdir(file_path):
                            shutil.rmtree(file_path, ignore_errors=True)
                    except Exception as e:
                        logs.warning(f'删除文件失败 {file_path}: {e}')

        # 3. 等待文件系统完成删除操作
        time.sleep(2)
        logs.info('旧文件清理完成，开始执行测试...')

        # 执行测试，添加更严格的参数确保只执行指定路径
        pytest_args = testpaths + [
            '--tb=short',  # 简化错误输出
            '--strict-markers',  # 严格标记模式
            '--disable-warnings'  # 禁用警告
        ]
        logs.info(f'pytest参数: {pytest_args}')
        pytest.main(pytest_args)
        # os.system('allure serve ./report/temp')  # 测试完成自动打开报告  jenkins运行需要注释掉

        # 生成新报告
        logs.info('生成Allure报告...')
        result = os.system("allure generate ./report/result/temp -o ./report/report --clean")
        if result != 0:
            logs.error('Allure报告生成失败')
        else:
            logs.info('Allure报告生成成功')

        shutil.copy('report/result/environment.xml', './report/result/temp')

        # # 这里打包报告---打包report文件夹
        folder_path = f'{DIR_PATH}/report'
        zip_name = os.path.basename(folder_path)  # 使用文件夹名作为压缩包名
        zip_path = os.path.join(FILE_PATH['file'], zip_name)  # 压缩包的完整路径，不含扩展名
        # 创建压缩包
        time.sleep(1)
        shutil.make_archive(zip_path, 'zip', folder_path)
        logs.info(f'{zip_name}.zip 已经创建在 {os.path.dirname(zip_path)}')

        summary = get_extract_yaml('summary')
        # if is_qw_msg:
        #     send_qw_msg()
        if is_qq_msg:
            qq_email(summary)
    else:
        logs.error(f'【{testpaths}】用例路径不存在，请检查路径入参是否正确！')
        exit()

