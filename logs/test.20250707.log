INFO - 2025-07-07 11:06:28,982 - web_service.py:147 -[web_service:check_existing_process] - 从PID文件读取到进程ID: 35724
INFO - 2025-07-07 11:06:29,153 - web_service.py:160 -[web_service:check_existing_process] - 进程 35724 不存在，删除过期的PID文件
INFO - 2025-07-07 11:06:29,154 - web_service.py:133 -[web_service:remove_pid_file] - PID文件已删除: C:\vae\python_project\zy_ApiAuto\webserver\web_service.pid
INFO - 2025-07-07 11:06:29,154 - web_service.py:929 -[web_service:start_web_service] - 启动Windows后台服务...
INFO - 2025-07-07 11:06:29,246 - web_service.py:317 -[web_service:run_as_windows_service] - 后台服务进程已启动，PID: 7244
INFO - 2025-07-07 11:06:29,247 - web_service.py:931 -[web_service:start_web_service] - 后台服务已启动，PID: 7244
INFO - 2025-07-07 11:06:29,248 - web_service.py:233 -[web_service:cleanup_on_exit] - Web服务正在关闭...
INFO - 2025-07-07 11:06:29,522 - web_service.py:140 -[web_service:check_existing_process] - PID文件不存在: C:\vae\python_project\zy_ApiAuto\webserver\web_service.pid
INFO - 2025-07-07 11:06:29,522 - web_service.py:945 -[web_service:start_web_service] - 检测到现有服务 (PID: None)，正在停止旧服务并启动新服务...
INFO - 2025-07-07 11:06:29,522 - web_service.py:140 -[web_service:check_existing_process] - PID文件不存在: C:\vae\python_project\zy_ApiAuto\webserver\web_service.pid
INFO - 2025-07-07 11:06:29,522 - web_service.py:951 -[web_service:start_web_service] - 旧服务已停止，正在启动新服务...
INFO - 2025-07-07 11:06:30,524 - web_service.py:124 -[web_service:write_pid_file] - PID文件已创建: C:\vae\python_project\zy_ApiAuto\webserver\web_service.pid, PID: 36100
INFO - 2025-07-07 11:06:30,534 - web_service.py:966 -[web_service:start_web_service] - ==================================================
INFO - 2025-07-07 11:06:30,535 - web_service.py:967 -[web_service:start_web_service] - Python项目Web服务已启动!
INFO - 2025-07-07 11:06:30,535 - web_service.py:968 -[web_service:start_web_service] - ==================================================
INFO - 2025-07-07 11:06:30,535 - web_service.py:969 -[web_service:start_web_service] - 访问地址:
INFO - 2025-07-07 11:06:30,535 - web_service.py:970 -[web_service:start_web_service] -   主页: http://*************:5000/
INFO - 2025-07-07 11:06:30,535 - web_service.py:971 -[web_service:start_web_service] -   监控页面: http://*************:5000/monitor
INFO - 2025-07-07 11:06:30,535 - web_service.py:972 -[web_service:start_web_service] -   执行demo_test示例: http://*************:5000/project/demo_test
INFO - 2025-07-07 11:06:30,535 - web_service.py:973 -[web_service:start_web_service] -   查看可用测试用例: http://*************:5000/testcases
INFO - 2025-07-07 11:06:30,535 - web_service.py:974 -[web_service:start_web_service] - ==================================================
INFO - 2025-07-07 11:06:30,535 - web_service.py:975 -[web_service:start_web_service] - 服务PID: 36100
INFO - 2025-07-07 11:06:30,535 - web_service.py:976 -[web_service:start_web_service] - 按 Ctrl+C 停止服务
INFO - 2025-07-07 11:06:30,535 - web_service.py:977 -[web_service:start_web_service] - ==================================================
INFO - 2025-07-07 11:06:37,563 - web_service.py:50 -[web_service:log_request_info] - HTTP请求 - POST http://*************:5000/project/play&demo_test - 客户端IP: *************
INFO - 2025-07-07 11:06:37,563 - web_service.py:80 -[web_service:log_response_info] - HTTP响应 - 状态码: 200 - 处理时间: 0.0ms
INFO - 2025-07-07 11:06:37,564 - web_service.py:426 -[web_service:execute_command_simple] - 开始执行命令: python run.py play demo_test
INFO - 2025-07-07 11:06:37,564 - web_service.py:86 -[web_service:log_response_info] - 响应类型: application/json
INFO - 2025-07-07 11:06:37,564 - web_service.py:427 -[web_service:execute_command_simple] - 当前工作目录: C:\vae\python_project\zy_ApiAuto
INFO - 2025-07-07 11:06:37,564 - web_service.py:92 -[web_service:log_response_info] - 响应内容: {"batch_id": "batch_1751857597", "command": "python run.py play demo_test", "message": "开始批量执行 2 个测试用例", "start_time": "2025-07-07 11:06:37", "status_url": "/status/batch_1751857597", "testcases": ["play", "demo_test"]}
INFO - 2025-07-07 11:06:37,564 - web_service.py:428 -[web_service:execute_command_simple] - 项目根目录: C:\vae\python_project\zy_ApiAuto
INFO - 2025-07-07 11:06:37,564 - web_service.py:429 -[web_service:execute_command_simple] - Python可执行文件: C:\vae\python_project\zy_ApiAuto\.venv\Scripts\pythonw.exe
INFO - 2025-07-07 11:06:37,564 - web_service.py:430 -[web_service:execute_command_simple] - 环境变量PATH: C:\vae\python_project\zy_ApiAuto\.venv/Scripts;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\windows\system32;C:\windows;C:\windows\System32\Wbem;C:\windows\System32\WindowsPowerShell\v1.0\;C:\windows\System32\OpenSSH\;C:\Program Files\nodejs\;C:\Program Files\nodejs\node_global\node_modules;C:\Program Files\Java\jdk1.8.0_281\bin;C:\Program Files\Java\jdk1.8.0_281\jre;C:\Program Files\Git\cmd;C:\vae\python3\3.12.6;C:\vae\python3\3.12.6\Scripts\;C:\Program Files\allure-2.13.8\bin;C:\Program Files\apache-jmeter-5.4.3\bin;C:\Program Files\platform-tools_r31.0.3-windows\platform-tools;C:\Program Files\dotnet\;C:\Program Files\Docker\Docker\resources\bin;C:\vae\python3\3.12.6\Scripts\;C:\vae\python3\3.12.6\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Program Files\JetBrains\PyCharm 2024.3.5\bin;;C:\Program Files\nodejs\node_global;
INFO - 2025-07-07 11:06:37,564 - web_service.py:434 -[web_service:execute_command_simple] - run.py文件是否存在: True
INFO - 2025-07-07 11:06:37,644 - web_service.py:439 -[web_service:execute_command_simple] - pytest版本: 8.0.2
INFO - 2025-07-07 11:06:37,644 - web_service.py:450 -[web_service:execute_command_simple] - 转换后的完整命令: "C:\vae\python_project\zy_ApiAuto\.venv\Scripts\pythonw.exe" "C:\vae\python_project\zy_ApiAuto\run.py" play demo_test
INFO - 2025-07-07 11:06:37,644 - web_service.py:454 -[web_service:execute_command_simple] - 准备启动subprocess进程...
INFO - 2025-07-07 11:06:37,655 - web_service.py:489 -[web_service:execute_command_simple] - subprocess进程已启动，PID: 30608
INFO - 2025-07-07 11:06:37,655 - web_service.py:503 -[web_service:execute_command_simple] - 开始读取进程输出...
INFO - 2025-07-07 11:06:38,373 - run.py:17 -[run:<module>] - Python版本: 3.12.6 (tags/v3.12.6:a4a2d2b, Sep  6 2024, 20:11:23) [MSC v.1940 64 bit (AMD64)]
INFO - 2025-07-07 11:06:38,373 - run.py:18 -[run:<module>] - Python可执行文件: C:\vae\python_project\zy_ApiAuto\.venv\Scripts\pythonw.exe
INFO - 2025-07-07 11:06:38,373 - run.py:19 -[run:<module>] - 当前工作目录: C:\vae\python_project\zy_ApiAuto
INFO - 2025-07-07 11:06:38,373 - run.py:20 -[run:<module>] - 脚本参数: ['C:\\vae\\python_project\\zy_ApiAuto\\run.py', 'play', 'demo_test']
INFO - 2025-07-07 11:06:38,373 - run.py:36 -[run:<module>] - 执行路径【['./testcase/play', './testcase/demo_test']】
INFO - 2025-07-07 11:06:38,373 - run.py:39 -[run:<module>] - 开始清理所有旧的报告和结果文件...
INFO - 2025-07-07 11:06:38,374 - run.py:44 -[run:<module>] - 删除旧的报告目录...
INFO - 2025-07-07 11:06:38,403 - run.py:50 -[run:<module>] - 清理temp目录下的所有旧文件...
INFO - 2025-07-07 11:06:40,437 - run.py:64 -[run:<module>] - 旧文件清理完成，开始执行测试...
INFO - 2025-07-07 11:06:40,437 - run.py:72 -[run:<module>] - pytest参数: ['./testcase/play', './testcase/demo_test', '--tb=short', '--strict-markers', '--disable-warnings']
INFO - 2025-07-07 11:06:40,437 - web_service.py:517 -[web_service:execute_command_simple] - [batch_1751857597] 第10行: [32m INFO - 2025-07-07 11:06:40,437 - run.py:72 -[run:<module>] - pytest参数: ['./testcase/play', './testcase/demo_test', '--tb=short', '--strict-markers', '--disable-warnings'][0m
INFO - 2025-07-07 11:06:40,788 - web_service.py:517 -[web_service:execute_command_simple] - [batch_1751857597] 第12行: platform win32 -- Python 3.12.6, pytest-8.0.2, pluggy-1.5.0 -- C:\vae\python_project\zy_ApiAuto\.venv\Scripts\pythonw.exe
INFO - 2025-07-07 11:06:40,788 - web_service.py:517 -[web_service:execute_command_simple] - [batch_1751857597] 第13行: cachedir: .pytest_cache
INFO - 2025-07-07 11:06:40,788 - web_service.py:517 -[web_service:execute_command_simple] - [batch_1751857597] 第15行: configfile: pytest.ini
INFO - 2025-07-07 11:06:40,788 - web_service.py:517 -[web_service:execute_command_simple] - [batch_1751857597] 第16行: plugins: allure-pytest-2.9.41, Faker-37.1.0, rerunfailures-14.0
INFO - 2025-07-07 11:06:41,297 - zy_backend_login.py:57 -[zy_backend_login:login] - 登录成功！{'msg': '登录成功', 'uid': '1942057751428562944', 'code': '0', 'data': {'name': '管理员', 'roleName': '租户管理员', 'isDefaultPwd': '0', 'userType': '1'}}
INFO - 2025-07-07 11:06:41,822 - connectMysql.py:22 -[connectMysql:__init__] - 成功连接到数据库：数据库ip：*************
INFO - 2025-07-07 11:06:41,822 - web_service.py:517 -[web_service:execute_command_simple] - [batch_1751857597] 第20行: testcase/play/test_play.py::TestPlay::test_play[api_info0] [32m INFO - 2025-07-07 11:06:41,822 - connectMysql.py:22 -[connectMysql:__init__] - 成功连接到数据库：数据库ip：*************[0m
INFO - 2025-07-07 11:06:41,846 - connectMysql.py:43 -[connectMysql:insert] - 插入数据库SQL--INSERT INTO `wei_sms_code_log` (`id`, `phone`, `channel_code`, `code`, `code_status`, `type`, `create_by`, `create_date`, `update_by`, `update_date`) VALUES (271515014630425548, '19877292898', '*********', '123456', 0, 1, NULL, '2025-07-07 11:06:41', NULL, '2025-07-07 11:06:41');
INFO - 2025-07-07 11:06:41,846 - connectMysql.py:44 -[connectMysql:insert] - 数据库数据插入成功
INFO - 2025-07-07 11:06:42,117 - zy_app_login.py:103 -[zy_app_login:login] - 【++登录成功++】{'head': {'errCode': '0', 'errMsg': '', 'tradeId': 'login', 'timestamp': '20250707110705'}, 'uid': '1942057755794771968', 'body': {'birthday': '', 'lastTime': '20250707101920', 'isCurrentRegister': '0', 'zipCode': '', 'sign': 'eacd9a26b76fa2783a70e8a4661733e4', 'imageNo': 0, 'points': '0', 'cityName': '', 'areaName': '', 'areaNo': '', 'imageUrl': '', 'provinceNo': '', 'email': '', 'QQ': '', 'cityNo': '', 'address': '', 'level': '1', 'nickName': '', 'sex': 2, 'mobile': '19877292898', 'MSN': '', 'identityCard': '', 'sessionId': '346518576256328694', 'userId': '346422044320518083', 'isMobileValid': 1, 'loginCount': 0, 'token': 'HIx4GcZCV9rNSVvCDHxLnTd9lqe4T+yvqwm3lMQTCY7gdc7GaMgINq64QVyeD0XxEC+AHo9ONgxjBFMyfqlPosl+78aoOuYgI9gLHmRnbRGmlT2bSdpg/kI22Kc6vOUF', 'realName': '', 'createTime': '1742204432000', 'provinceName': '', 'username': ''}}
INFO - 2025-07-07 11:06:42,877 - conftest.py:7 -[conftest:print_info] - ---------------接口测试开始---------------
INFO - 2025-07-07 11:06:42,916 - sendrequests.py:57 -[sendrequests:execute_api_request] - 接口名称：演出分类
INFO - 2025-07-07 11:06:42,916 - sendrequests.py:58 -[sendrequests:execute_api_request] - 请求地址：http://*************/api/show/info/category_list
INFO - 2025-07-07 11:06:42,916 - sendrequests.py:59 -[sendrequests:execute_api_request] - 请求方式：POST
INFO - 2025-07-07 11:06:42,916 - sendrequests.py:60 -[sendrequests:execute_api_request] - 请求头：{'Content-Type': 'application/x-www-form-urlencoded', 'Connection': 'Keep-Alive', 'Accept-Encoding': 'gzip'}
INFO - 2025-07-07 11:06:42,916 - sendrequests.py:61 -[sendrequests:execute_api_request] - 测试用例名：获取猫眼演出分类
INFO - 2025-07-07 11:06:42,916 - sendrequests.py:62 -[sendrequests:execute_api_request] - cookies值：None
INFO - 2025-07-07 11:06:42,916 - sendrequests.py:67 -[sendrequests:execute_api_request] - 参数类型：data
INFO - 2025-07-07 11:06:42,916 - sendrequests.py:69 -[sendrequests:execute_api_request] - 请求参数：{"channelCode": "*********", "merchant_code": "", "token": "HIx4GcZCV9rNSVvCDHxLnTd9lqe4T+yvqwm3lMQTCY7gdc7GaMgINq64QVyeD0XxEC+AHo9ONgxjBFMyfqlPosl+78aoOuYgI9gLHmRnbRGmlT2bSdpg/kI22Kc6vOUF,*********"}
INFO - 2025-07-07 11:06:42,916 - web_service.py:517 -[web_service:execute_command_simple] - [batch_1751857597] 第30行: [32m INFO - 2025-07-07 11:06:42,916 - sendrequests.py:62 -[sendrequests:execute_api_request] - cookies值：None[0m
INFO - 2025-07-07 11:06:43,176 - apiutils_business.py:179 -[apiutils_business:execute_test_cases] - 接口实际返回结果：{"code":0,"msg":"","data":[{"id":"10001","name":"演唱会","image":"http:\/\/*************\/resource\/play\/upload\/20250421\/175345K0n_6qP6Pi.jpg","tag_list":["测试文案","演出演唱会12"]},{"id":"10002","name":"体育赛事","image":"http:\/\/*************\/resource\/play\/upload\/20250408\/144744oaSxDsD3Qu.png","tag_list":["孙颖莎","全红婵"]},{"id":"10003","name":"戏曲艺术","image":"http:\/\/*************\/resource\/play\/upload\/20250408\/1448079S7mHpY2QU.png","tag_list":[]},{"id":"10017","name":"Livehouse","image":"http:\/\/*************\/resource\/play\/upload\/20250408\/1449279xNEIQlg9D.png","tag_list":[]},{"id":"10005","name":"舞蹈芭蕾","image":"http:\/\/*************\/resource\/play\/upload\/20250408\/145113dfAkJoKwCg.png","tag_list":[]},{"id":"10006","name":"音乐会","image":"http:\/\/*************\/resource\/play\/upload\/20250408\/1448369SbU_7vykE.png","tag_list":[]},{"id":"10007","name":"亲子演出","image":"http:\/\/*************\/resource\/play\/upload\/20250408\/144825FTdAI7yPy_.png","tag_list":[]},{"id":"10010","name":"音乐节","image":"http:\/\/*************\/resource\/play\/upload\/20250422\/145418ZAvXkbQMhU.png","tag_list":[]},{"id":"10014","name":"沉浸剧场","image":"http:\/\/*************\/resource\/play\/upload\/20250408\/144959tWsxiYBDA-.png","tag_list":[]},{"id":"10016","name":"相声","image":"http:\/\/*************\/resource\/play\/upload\/20250408\/144939LP2Ruhsuki.png","tag_list":[]},{"id":"10015","name":"脱口秀","image":"http:\/\/*************\/resource\/play\/upload\/20250408\/144949GXhLPNXxUa.png","tag_list":[]},{"id":"10009","name":"休闲展览","image":"http:\/\/*************\/resource\/play\/upload\/20250408\/144907oWhMCZrrG_.png","tag_list":[]},{"id":"10004","name":"话剧音乐剧","image":"http:\/\/*************\/resource\/play\/upload\/20250408\/145102PT7jed5WkQ.png","tag_list":[]},{"id":"10008","name":"其他","image":"http:\/\/*************\/resource\/play\/upload\/20250331\/164501dyI8a3idSN.png","tag_list":["标签 一","标签二"]}]}
INFO - 2025-07-07 11:06:43,181 - assertion_utils.py:35 -[assertion_utils:status_code_assert] - 状态码断言成功：接口实际返回状态码 200 == 200
INFO - 2025-07-07 11:06:43,182 - assertion_utils.py:94 -[assertion_utils:equal_assert] - 相等断言成功：接口实际结果 {'code': 0} == 预期结果：{'code': 0}
INFO - 2025-07-07 11:06:43,183 - assertion_utils.py:206 -[assertion_utils:assert_result] - 测试成功
INFO - 2025-07-07 11:06:43,197 - conftest.py:9 -[conftest:print_info] - ---------------接口测试结束---------------
INFO - 2025-07-07 11:06:44,710 - conftest.py:7 -[conftest:print_info] - ---------------接口测试开始---------------
INFO - 2025-07-07 11:06:44,717 - sendrequests.py:57 -[sendrequests:execute_api_request] - 接口名称：今日必抢
INFO - 2025-07-07 11:06:44,717 - sendrequests.py:58 -[sendrequests:execute_api_request] - 请求地址：http://*************/api/show/info/home_today_sale_list
INFO - 2025-07-07 11:06:44,717 - web_service.py:517 -[web_service:execute_command_simple] - [batch_1751857597] 第40行: [32m INFO - 2025-07-07 11:06:44,717 - sendrequests.py:57 -[sendrequests:execute_api_request] - 接口名称：今日必抢[0m
INFO - 2025-07-07 11:06:44,717 - sendrequests.py:59 -[sendrequests:execute_api_request] - 请求方式：POST
INFO - 2025-07-07 11:06:44,717 - sendrequests.py:60 -[sendrequests:execute_api_request] - 请求头：{'Content-Type': 'application/x-www-form-urlencoded', 'Connection': 'Keep-Alive', 'Accept-Encoding': 'gzip'}
INFO - 2025-07-07 11:06:44,717 - sendrequests.py:61 -[sendrequests:execute_api_request] - 测试用例名：获取今日必抢演出列表
INFO - 2025-07-07 11:06:44,718 - sendrequests.py:62 -[sendrequests:execute_api_request] - cookies值：None
INFO - 2025-07-07 11:06:44,718 - sendrequests.py:67 -[sendrequests:execute_api_request] - 参数类型：data
INFO - 2025-07-07 11:06:44,718 - sendrequests.py:69 -[sendrequests:execute_api_request] - 请求参数：{"page": 1, "pageSize": 20, "channelCode": "*********", "token": "HIx4GcZCV9rNSVvCDHxLnTd9lqe4T+yvqwm3lMQTCY7gdc7GaMgINq64QVyeD0XxEC+AHo9ONgxjBFMyfqlPosl+78aoOuYgI9gLHmRnbRGmlT2bSdpg/kI22Kc6vOUF,*********"}
INFO - 2025-07-07 11:06:45,004 - apiutils_business.py:179 -[apiutils_business:execute_test_cases] - 接口实际返回结果：{"code":0,"msg":"","data":{"total":65,"list":[{"id":"26","name":"老无座-0313","category_id":"10001","venue_id":"8","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/d7db73c1934ce8fcb5c4003afe3522b594040.jpg","ticket_status":"3","unusual_status":"0","need_real_name":"0","first_show_time":"2025-08-30 20:00:00","last_show_time":"2025-12-01 02:00:00","on_sale_time":"2025-03-13 19:00:00","max_buy_limit_per_id":"0","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"4.00","max_sale_price":"55.00","shelve_status":"1","third_shelve_status":"1","sort":"210","venue_info":{"venue_id":"8","venue_name":"梅赛德斯-奔驰文化中心","province_title":"上海市","city_title":"浦东新区","address":"世博大道1200号","latitude":"31.188952","longitude":"121.493201"},"show_info":{"show_id":"217","performance_id":"26","show_name":"2025-08-30 周六 20:00","show_start_time":"2025-08-30 20:00:00","show_end_time":"2025-08-30 22:00:00","show_type":"1","show_seat_type":"0","show_real_name_limit":"0","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2025-05-20 16:46:26","off_sale_time":"2025-08-30 20:00:00"},"category_info":{"category_id":"10001","category_name":"演唱会"}},{"id":"22","name":"【外部分销对接专用，不可编辑】编辑请联系上单-新选座-实名制","category_id":"10001","venue_id":"8","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/d7db73c1934ce8fcb5c4003afe3522b594040.jpg","ticket_status":"3","unusual_status":"0","need_real_name":"1","first_show_time":"2025-12-31 01:00:00","last_show_time":"2028-01-01 01:00:00","on_sale_time":"2025-04-22 16:05:43","max_buy_limit_per_id":"6","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"2.00","max_sale_price":"6.00","shelve_status":"1","third_shelve_status":"1","sort":"90","venue_info":{"venue_id":"8","venue_name":"梅赛德斯-奔驰文化中心","province_title":"上海市","city_title":"浦东新区","address":"世博大道1200号","latitude":"31.188952","longitude":"121.493201"},"show_info":{"show_id":"40","performance_id":"22","show_name":"2025-12-31 周三 01:00","show_start_time":"2025-12-31 01:00:00","show_end_time":"2025-12-31 02:00:00","show_type":"1","show_seat_type":"1","show_real_name_limit":"1","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2024-12-10 13:18:31","off_sale_time":"2025-12-31 01:00:00"},"category_info":{"category_id":"10001","category_name":"演唱会"}},{"id":"75","name":"【禁止修改】4.1  自营选座项目---日历模式（优惠混搭）+非实名","category_id":"10004","venue_id":"7","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/fa0fa284da6c8246a179c7400e8d132e59521.png","ticket_status":"12","unusual_status":"6","need_real_name":"0","first_show_time":"2025-09-01 08:00:00","last_show_time":"2026-04-11 23:59:00","on_sale_time":"2025-04-01 10:49:52","max_buy_limit_per_id":"6","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"110.00","max_sale_price":"2200.00","shelve_status":"1","third_shelve_status":"1","sort":"80","venue_info":{"venue_id":"7","venue_name":"凯迪拉克·上海音乐厅","province_title":"上海市","city_title":"黄浦区","address":"延安东路523号（近西藏南路）","latitude":"31.226854","longitude":"121.477977"},"show_info":{"show_id":"250","performance_id":"75","show_name":"2025-09-01 周一 08:00","show_start_time":"2025-09-01 08:00:00","show_end_time":"2025-09-01 10:00:00","show_type":"1","show_seat_type":"1","show_real_name_limit":"0","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2025-04-01 10:46:15","off_sale_time":"2025-08-31 00:00:00"},"category_info":{"category_id":"10004","category_name":"话剧音乐剧"}},{"id":"34","name":"朱磊业老无座线下搬单","category_id":"10002","venue_id":"7","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/84d2c832ab35b5514e8962a287d69c59631.png","ticket_status":"3","unusual_status":"0","need_real_name":"0","first_show_time":"2025-12-01 00:00:00","last_show_time":"2026-01-14 07:00:00","on_sale_time":"2025-03-11 19:23:31","max_buy_limit_per_id":"0","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"5","min_sale_price":"2.00","max_sale_price":"3.00","shelve_status":"1","third_shelve_status":"1","sort":"70","venue_info":{"venue_id":"7","venue_name":"凯迪拉克·上海音乐厅","province_title":"上海市","city_title":"黄浦区","address":"延安东路523号（近西藏南路）","latitude":"31.226854","longitude":"121.477977"},"show_info":{"show_id":"94","performance_id":"34","show_name":"2025-12-01 周一 00:00","show_start_time":"2025-12-01 00:00:00","show_end_time":"2025-12-01 17:00:00","show_type":"1","show_seat_type":"0","show_real_name_limit":"0","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2024-11-20 12:15:50","off_sale_time":"2025-11-30 00:00:00"},"category_info":{"category_id":"10002","category_name":"体育赛事"}},{"id":"8","name":"测试发票说明05","category_id":"10002","venue_id":"12","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/9353f3ac3ba4b47bd4642fcbdc393bbf101705.jpg","ticket_status":"3","unusual_status":"0","need_real_name":"0","first_show_time":"2026-01-03 00:00:00","last_show_time":"2026-01-03 02:00:00","on_sale_time":"2025-02-28 19:20:54","max_buy_limit_per_id":"6","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"33.00","max_sale_price":"33.00","shelve_status":"1","third_shelve_status":"1","sort":"60","venue_info":{"venue_id":"12","venue_name":"上海展览中心","province_title":"上海市","city_title":"静安区","address":"延安中路1000号","latitude":"31.224714","longitude":"121.452558"},"show_info":{"show_id":"16","performance_id":"8","show_name":"2026-01-03 周六 00:00","show_start_time":"2026-01-03 00:00:00","show_end_time":"2026-01-03 02:00:00","show_type":"1","show_seat_type":"0","show_real_name_limit":"0","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2025-01-14 15:12:06","off_sale_time":"2026-01-03 00:00:00"},"category_info":{"category_id":"10002","category_name":"体育赛事"}},{"id":"23","name":"【外部分销对接专用，不可编辑】编辑请联系上单-新选座-非实名制","category_id":"10007","venue_id":"8","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/d7db73c1934ce8fcb5c4003afe3522b594040.jpg","ticket_status":"3","unusual_status":"0","need_real_name":"1","first_show_time":"2028-01-01 00:00:00","last_show_time":"2028-01-01 01:00:00","on_sale_time":"2024-07-29 16:43:00","max_buy_limit_per_id":"6","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"50.00","max_sale_price":"165.00","shelve_status":"1","third_shelve_status":"1","sort":"60","venue_info":{"venue_id":"8","venue_name":"梅赛德斯-奔驰文化中心","province_title":"上海市","city_title":"浦东新区","address":"世博大道1200号","latitude":"31.188952","longitude":"121.493201"},"show_info":{"show_id":"42","performance_id":"23","show_name":"2028-01-01 周六 00:00","show_start_time":"2028-01-01 00:00:00","show_end_time":"2028-01-01 01:00:00","show_type":"1","show_seat_type":"1","show_real_name_limit":"1","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2024-07-29 16:40:00","off_sale_time":"2028-01-01 00:00:00"},"category_info":{"category_id":"10007","category_name":"亲子演出"}},{"id":"48","name":"朱磊业新配座套票配座","category_id":"10001","venue_id":"7","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/84d2c832ab35b5514e8962a287d69c59631.png","ticket_status":"3","unusual_status":"0","need_real_name":"0","first_show_time":"2026-01-02 00:00:00","last_show_time":"2026-01-02 04:00:00","on_sale_time":"2025-01-09 11:28:59","max_buy_limit_per_id":"0","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"2.00","max_sale_price":"3.00","shelve_status":"1","third_shelve_status":"1","sort":"60","venue_info":{"venue_id":"7","venue_name":"凯迪拉克·上海音乐厅","province_title":"上海市","city_title":"黄浦区","address":"延安东路523号（近西藏南路）","latitude":"31.226854","longitude":"121.477977"},"show_info":{"show_id":"179","performance_id":"48","show_name":"2026-01-02 周五 00:00","show_start_time":"2026-01-02 00:00:00","show_end_time":"2026-01-02 04:00:00","show_type":"1","show_seat_type":"1","show_real_name_limit":"9999","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2025-01-20 16:24:00","off_sale_time":"2026-01-02 00:00:00"},"category_info":{"category_id":"10001","category_name":"演唱会"}},{"id":"51","name":"朱磊业新无座快递配送转其它","category_id":"10001","venue_id":"13","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/84d2c832ab35b5514e8962a287d69c59631.png","ticket_status":"12","unusual_status":"3","need_real_name":"0","first_show_time":"2026-01-01 00:00:00","last_show_time":"2028-12-15 02:00:00","on_sale_time":"2024-12-13 17:47:21","max_buy_limit_per_id":"0","max_buy_limit_per_order":"20","min_buy_limit_per_order":"0","max_buy_limit_per_user":"1","min_sale_price":"2.00","max_sale_price":"6.00","shelve_status":"1","third_shelve_status":"1","sort":"60","venue_info":{"venue_id":"13","venue_name":"虹桥天地演艺中心","province_title":"上海市","city_title":"闵行区","address":"绍虹路33号","latitude":"31.19331","longitude":"121.31583"},"show_info":{"show_id":"182","performance_id":"51","show_name":"2026-01-01 周四 00:00","show_start_time":"2026-01-01 00:00:00","show_end_time":"2026-01-01 03:00:00","show_type":"1","show_seat_type":"0","show_real_name_limit":"0","max_buy_limit_per_order":"20","show_unusual_status":"1","on_sale_time":"2024-12-24 15:57:38","off_sale_time":"2026-01-01 00:00:00"},"category_info":{"category_id":"10001","category_name":"演唱会"}},{"id":"53","name":"小红书推送商品验证（老项目喽）","category_id":"10001","venue_id":"7","poster_url":"https:\/\/p1.meituan.net\/myvideodistribute\/6563cbda84d54511adb254e69b0c8001239854.jpg","ticket_status":"3","unusual_status":"0","need_real_name":"0","first_show_time":"2025-07-30 11:00:00","last_show_time":"2025-09-30 20:00:00","on_sale_time":"2025-02-11 13:00:00","max_buy_limit_per_id":"0","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"11.00","max_sale_price":"367.00","shelve_status":"1","third_shelve_status":"1","sort":"60","venue_info":{"venue_id":"7","venue_name":"凯迪拉克·上海音乐厅","province_title":"上海市","city_title":"黄浦区","address":"延安东路523号（近西藏南路）","latitude":"31.226854","longitude":"121.477977"},"show_info":{"show_id":"312","performance_id":"53","show_name":"2025-07-30 周三 11:00","show_start_time":"2025-07-30 11:00:00","show_end_time":"2025-07-30 19:00:00","show_type":"1","show_seat_type":"0","show_real_name_limit":"0","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2025-06-26 14:33:53","off_sale_time":"2025-07-30 11:00:00"},"category_info":{"category_id":"10001","category_name":"演唱会"}},{"id":"108","name":"场馆---项目开售时间校验优化","category_id":"10002","venue_id":"13","poster_url":"https:\/\/p1.meituan.net\/myvideodistribute\/54c4fc8913710b6cbd587f52bce3cb4a305726.jpg","ticket_status":"3","unusual_status":"0","need_real_name":"0","first_show_time":"2025-07-08 00:00:00","last_show_time":"2025-07-15 01:00:00","on_sale_time":"2025-06-10 19:45:00","max_buy_limit_per_id":"6","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"110.00","max_sale_price":"110.00","shelve_status":"1","third_shelve_status":"1","sort":"60","venue_info":{"venue_id":"13","venue_name":"虹桥天地演艺中心","province_title":"上海市","city_title":"闵行区","address":"绍虹路33号","latitude":"31.19331","longitude":"121.31583"},"show_info":{"show_id":"341","performance_id":"108","show_name":"2025-07-08 周二 00:00","show_start_time":"2025-07-08 00:00:00","show_end_time":"2025-07-08 01:00:00","show_type":"1","show_seat_type":"0","show_real_name_limit":"0","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2025-06-10 19:20:54","off_sale_time":"2025-07-08 00:00:00"},"category_info":{"category_id":"10002","category_name":"体育赛事"}}]}}
INFO - 2025-07-07 11:06:45,009 - assertion_utils.py:35 -[assertion_utils:status_code_assert] - 状态码断言成功：接口实际返回状态码 200 == 200
INFO - 2025-07-07 11:06:45,010 - assertion_utils.py:94 -[assertion_utils:equal_assert] - 相等断言成功：接口实际结果 {'code': 0} == 预期结果：{'code': 0}
INFO - 2025-07-07 11:06:45,011 - web_service.py:517 -[web_service:execute_command_simple] - [batch_1751857597] 第50行: [32m INFO - 2025-07-07 11:06:45,010 - assertion_utils.py:94 -[assertion_utils:equal_assert] - 相等断言成功：接口实际结果 {'code': 0} == 预期结果：{'code': 0}[0m
INFO - 2025-07-07 11:06:45,011 - assertion_utils.py:206 -[assertion_utils:assert_result] - 测试成功
INFO - 2025-07-07 11:06:45,023 - conftest.py:9 -[conftest:print_info] - ---------------接口测试结束---------------
INFO - 2025-07-07 11:06:46,536 - conftest.py:7 -[conftest:print_info] - ---------------接口测试开始---------------
INFO - 2025-07-07 11:06:46,540 - sendrequests.py:57 -[sendrequests:execute_api_request] - 接口名称：近期特惠
INFO - 2025-07-07 11:06:46,541 - sendrequests.py:58 -[sendrequests:execute_api_request] - 请求地址：http://*************/api/show/info/home_near_show_list
INFO - 2025-07-07 11:06:46,541 - sendrequests.py:59 -[sendrequests:execute_api_request] - 请求方式：POST
INFO - 2025-07-07 11:06:46,541 - sendrequests.py:60 -[sendrequests:execute_api_request] - 请求头：{'Content-Type': 'application/x-www-form-urlencoded', 'Connection': 'Keep-Alive', 'Accept-Encoding': 'gzip'}
INFO - 2025-07-07 11:06:46,541 - sendrequests.py:61 -[sendrequests:execute_api_request] - 测试用例名：获取近期特惠演出列表
INFO - 2025-07-07 11:06:46,541 - sendrequests.py:62 -[sendrequests:execute_api_request] - cookies值：None
INFO - 2025-07-07 11:06:46,541 - sendrequests.py:67 -[sendrequests:execute_api_request] - 参数类型：data
INFO - 2025-07-07 11:06:46,541 - web_service.py:517 -[web_service:execute_command_simple] - [batch_1751857597] 第60行: [32m INFO - 2025-07-07 11:06:46,541 - sendrequests.py:62 -[sendrequests:execute_api_request] - cookies值：None[0m
INFO - 2025-07-07 11:06:46,541 - sendrequests.py:69 -[sendrequests:execute_api_request] - 请求参数：{"page": 1, "pageSize": 20, "channelCode": "*********", "token": "HIx4GcZCV9rNSVvCDHxLnTd9lqe4T+yvqwm3lMQTCY7gdc7GaMgINq64QVyeD0XxEC+AHo9ONgxjBFMyfqlPosl+78aoOuYgI9gLHmRnbRGmlT2bSdpg/kI22Kc6vOUF,*********"}
INFO - 2025-07-07 11:06:46,823 - apiutils_business.py:179 -[apiutils_business:execute_test_cases] - 接口实际返回结果：{"code":0,"msg":"","data":{"total":65,"list":[{"id":"26","name":"老无座-0313","category_id":"10001","venue_id":"8","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/d7db73c1934ce8fcb5c4003afe3522b594040.jpg","ticket_status":"3","unusual_status":"0","need_real_name":"0","first_show_time":"2025-08-30 20:00:00","last_show_time":"2025-12-01 02:00:00","on_sale_time":"2025-03-13 19:00:00","max_buy_limit_per_id":"0","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"4.00","max_sale_price":"55.00","shelve_status":"1","third_shelve_status":"1","sort":"210","venue_info":{"venue_id":"8","venue_name":"梅赛德斯-奔驰文化中心","province_title":"上海市","city_title":"浦东新区","address":"世博大道1200号","latitude":"31.188952","longitude":"121.493201"},"show_info":{"show_id":"217","performance_id":"26","show_name":"2025-08-30 周六 20:00","show_start_time":"2025-08-30 20:00:00","show_end_time":"2025-08-30 22:00:00","show_type":"1","show_seat_type":"0","show_real_name_limit":"0","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2025-05-20 16:46:26","off_sale_time":"2025-08-30 20:00:00"},"category_info":{"category_id":"10001","category_name":"演唱会"}},{"id":"22","name":"【外部分销对接专用，不可编辑】编辑请联系上单-新选座-实名制","category_id":"10001","venue_id":"8","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/d7db73c1934ce8fcb5c4003afe3522b594040.jpg","ticket_status":"3","unusual_status":"0","need_real_name":"1","first_show_time":"2025-12-31 01:00:00","last_show_time":"2028-01-01 01:00:00","on_sale_time":"2025-04-22 16:05:43","max_buy_limit_per_id":"6","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"2.00","max_sale_price":"6.00","shelve_status":"1","third_shelve_status":"1","sort":"90","venue_info":{"venue_id":"8","venue_name":"梅赛德斯-奔驰文化中心","province_title":"上海市","city_title":"浦东新区","address":"世博大道1200号","latitude":"31.188952","longitude":"121.493201"},"show_info":{"show_id":"40","performance_id":"22","show_name":"2025-12-31 周三 01:00","show_start_time":"2025-12-31 01:00:00","show_end_time":"2025-12-31 02:00:00","show_type":"1","show_seat_type":"1","show_real_name_limit":"1","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2024-12-10 13:18:31","off_sale_time":"2025-12-31 01:00:00"},"category_info":{"category_id":"10001","category_name":"演唱会"}},{"id":"75","name":"【禁止修改】4.1  自营选座项目---日历模式（优惠混搭）+非实名","category_id":"10004","venue_id":"7","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/fa0fa284da6c8246a179c7400e8d132e59521.png","ticket_status":"12","unusual_status":"6","need_real_name":"0","first_show_time":"2025-09-01 08:00:00","last_show_time":"2026-04-11 23:59:00","on_sale_time":"2025-04-01 10:49:52","max_buy_limit_per_id":"6","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"110.00","max_sale_price":"2200.00","shelve_status":"1","third_shelve_status":"1","sort":"80","venue_info":{"venue_id":"7","venue_name":"凯迪拉克·上海音乐厅","province_title":"上海市","city_title":"黄浦区","address":"延安东路523号（近西藏南路）","latitude":"31.226854","longitude":"121.477977"},"show_info":{"show_id":"250","performance_id":"75","show_name":"2025-09-01 周一 08:00","show_start_time":"2025-09-01 08:00:00","show_end_time":"2025-09-01 10:00:00","show_type":"1","show_seat_type":"1","show_real_name_limit":"0","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2025-04-01 10:46:15","off_sale_time":"2025-08-31 00:00:00"},"category_info":{"category_id":"10004","category_name":"话剧音乐剧"}},{"id":"34","name":"朱磊业老无座线下搬单","category_id":"10002","venue_id":"7","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/84d2c832ab35b5514e8962a287d69c59631.png","ticket_status":"3","unusual_status":"0","need_real_name":"0","first_show_time":"2025-12-01 00:00:00","last_show_time":"2026-01-14 07:00:00","on_sale_time":"2025-03-11 19:23:31","max_buy_limit_per_id":"0","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"5","min_sale_price":"2.00","max_sale_price":"3.00","shelve_status":"1","third_shelve_status":"1","sort":"70","venue_info":{"venue_id":"7","venue_name":"凯迪拉克·上海音乐厅","province_title":"上海市","city_title":"黄浦区","address":"延安东路523号（近西藏南路）","latitude":"31.226854","longitude":"121.477977"},"show_info":{"show_id":"94","performance_id":"34","show_name":"2025-12-01 周一 00:00","show_start_time":"2025-12-01 00:00:00","show_end_time":"2025-12-01 17:00:00","show_type":"1","show_seat_type":"0","show_real_name_limit":"0","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2024-11-20 12:15:50","off_sale_time":"2025-11-30 00:00:00"},"category_info":{"category_id":"10002","category_name":"体育赛事"}},{"id":"8","name":"测试发票说明05","category_id":"10002","venue_id":"12","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/9353f3ac3ba4b47bd4642fcbdc393bbf101705.jpg","ticket_status":"3","unusual_status":"0","need_real_name":"0","first_show_time":"2026-01-03 00:00:00","last_show_time":"2026-01-03 02:00:00","on_sale_time":"2025-02-28 19:20:54","max_buy_limit_per_id":"6","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"33.00","max_sale_price":"33.00","shelve_status":"1","third_shelve_status":"1","sort":"60","venue_info":{"venue_id":"12","venue_name":"上海展览中心","province_title":"上海市","city_title":"静安区","address":"延安中路1000号","latitude":"31.224714","longitude":"121.452558"},"show_info":{"show_id":"16","performance_id":"8","show_name":"2026-01-03 周六 00:00","show_start_time":"2026-01-03 00:00:00","show_end_time":"2026-01-03 02:00:00","show_type":"1","show_seat_type":"0","show_real_name_limit":"0","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2025-01-14 15:12:06","off_sale_time":"2026-01-03 00:00:00"},"category_info":{"category_id":"10002","category_name":"体育赛事"}},{"id":"23","name":"【外部分销对接专用，不可编辑】编辑请联系上单-新选座-非实名制","category_id":"10007","venue_id":"8","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/d7db73c1934ce8fcb5c4003afe3522b594040.jpg","ticket_status":"3","unusual_status":"0","need_real_name":"1","first_show_time":"2028-01-01 00:00:00","last_show_time":"2028-01-01 01:00:00","on_sale_time":"2024-07-29 16:43:00","max_buy_limit_per_id":"6","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"50.00","max_sale_price":"165.00","shelve_status":"1","third_shelve_status":"1","sort":"60","venue_info":{"venue_id":"8","venue_name":"梅赛德斯-奔驰文化中心","province_title":"上海市","city_title":"浦东新区","address":"世博大道1200号","latitude":"31.188952","longitude":"121.493201"},"show_info":{"show_id":"42","performance_id":"23","show_name":"2028-01-01 周六 00:00","show_start_time":"2028-01-01 00:00:00","show_end_time":"2028-01-01 01:00:00","show_type":"1","show_seat_type":"1","show_real_name_limit":"1","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2024-07-29 16:40:00","off_sale_time":"2028-01-01 00:00:00"},"category_info":{"category_id":"10007","category_name":"亲子演出"}},{"id":"48","name":"朱磊业新配座套票配座","category_id":"10001","venue_id":"7","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/84d2c832ab35b5514e8962a287d69c59631.png","ticket_status":"3","unusual_status":"0","need_real_name":"0","first_show_time":"2026-01-02 00:00:00","last_show_time":"2026-01-02 04:00:00","on_sale_time":"2025-01-09 11:28:59","max_buy_limit_per_id":"0","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"2.00","max_sale_price":"3.00","shelve_status":"1","third_shelve_status":"1","sort":"60","venue_info":{"venue_id":"7","venue_name":"凯迪拉克·上海音乐厅","province_title":"上海市","city_title":"黄浦区","address":"延安东路523号（近西藏南路）","latitude":"31.226854","longitude":"121.477977"},"show_info":{"show_id":"179","performance_id":"48","show_name":"2026-01-02 周五 00:00","show_start_time":"2026-01-02 00:00:00","show_end_time":"2026-01-02 04:00:00","show_type":"1","show_seat_type":"1","show_real_name_limit":"9999","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2025-01-20 16:24:00","off_sale_time":"2026-01-02 00:00:00"},"category_info":{"category_id":"10001","category_name":"演唱会"}},{"id":"51","name":"朱磊业新无座快递配送转其它","category_id":"10001","venue_id":"13","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/84d2c832ab35b5514e8962a287d69c59631.png","ticket_status":"12","unusual_status":"3","need_real_name":"0","first_show_time":"2026-01-01 00:00:00","last_show_time":"2028-12-15 02:00:00","on_sale_time":"2024-12-13 17:47:21","max_buy_limit_per_id":"0","max_buy_limit_per_order":"20","min_buy_limit_per_order":"0","max_buy_limit_per_user":"1","min_sale_price":"2.00","max_sale_price":"6.00","shelve_status":"1","third_shelve_status":"1","sort":"60","venue_info":{"venue_id":"13","venue_name":"虹桥天地演艺中心","province_title":"上海市","city_title":"闵行区","address":"绍虹路33号","latitude":"31.19331","longitude":"121.31583"},"show_info":{"show_id":"182","performance_id":"51","show_name":"2026-01-01 周四 00:00","show_start_time":"2026-01-01 00:00:00","show_end_time":"2026-01-01 03:00:00","show_type":"1","show_seat_type":"0","show_real_name_limit":"0","max_buy_limit_per_order":"20","show_unusual_status":"1","on_sale_time":"2024-12-24 15:57:38","off_sale_time":"2026-01-01 00:00:00"},"category_info":{"category_id":"10001","category_name":"演唱会"}},{"id":"53","name":"小红书推送商品验证（老项目喽）","category_id":"10001","venue_id":"7","poster_url":"https:\/\/p1.meituan.net\/myvideodistribute\/6563cbda84d54511adb254e69b0c8001239854.jpg","ticket_status":"3","unusual_status":"0","need_real_name":"0","first_show_time":"2025-07-30 11:00:00","last_show_time":"2025-09-30 20:00:00","on_sale_time":"2025-02-11 13:00:00","max_buy_limit_per_id":"0","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"11.00","max_sale_price":"367.00","shelve_status":"1","third_shelve_status":"1","sort":"60","venue_info":{"venue_id":"7","venue_name":"凯迪拉克·上海音乐厅","province_title":"上海市","city_title":"黄浦区","address":"延安东路523号（近西藏南路）","latitude":"31.226854","longitude":"121.477977"},"show_info":{"show_id":"312","performance_id":"53","show_name":"2025-07-30 周三 11:00","show_start_time":"2025-07-30 11:00:00","show_end_time":"2025-07-30 19:00:00","show_type":"1","show_seat_type":"0","show_real_name_limit":"0","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2025-06-26 14:33:53","off_sale_time":"2025-07-30 11:00:00"},"category_info":{"category_id":"10001","category_name":"演唱会"}},{"id":"108","name":"场馆---项目开售时间校验优化","category_id":"10002","venue_id":"13","poster_url":"https:\/\/p1.meituan.net\/myvideodistribute\/54c4fc8913710b6cbd587f52bce3cb4a305726.jpg","ticket_status":"3","unusual_status":"0","need_real_name":"0","first_show_time":"2025-07-08 00:00:00","last_show_time":"2025-07-15 01:00:00","on_sale_time":"2025-06-10 19:45:00","max_buy_limit_per_id":"6","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"110.00","max_sale_price":"110.00","shelve_status":"1","third_shelve_status":"1","sort":"60","venue_info":{"venue_id":"13","venue_name":"虹桥天地演艺中心","province_title":"上海市","city_title":"闵行区","address":"绍虹路33号","latitude":"31.19331","longitude":"121.31583"},"show_info":{"show_id":"341","performance_id":"108","show_name":"2025-07-08 周二 00:00","show_start_time":"2025-07-08 00:00:00","show_end_time":"2025-07-08 01:00:00","show_type":"1","show_seat_type":"0","show_real_name_limit":"0","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2025-06-10 19:20:54","off_sale_time":"2025-07-08 00:00:00"},"category_info":{"category_id":"10002","category_name":"体育赛事"}}]}}
INFO - 2025-07-07 11:06:46,829 - assertion_utils.py:35 -[assertion_utils:status_code_assert] - 状态码断言成功：接口实际返回状态码 200 == 200
INFO - 2025-07-07 11:06:46,830 - assertion_utils.py:94 -[assertion_utils:equal_assert] - 相等断言成功：接口实际结果 {'code': 0} == 预期结果：{'code': 0}
INFO - 2025-07-07 11:06:46,831 - assertion_utils.py:206 -[assertion_utils:assert_result] - 测试成功
INFO - 2025-07-07 11:06:46,840 - conftest.py:9 -[conftest:print_info] - ---------------接口测试结束---------------
INFO - 2025-07-07 11:06:48,361 - conftest.py:7 -[conftest:print_info] - ---------------接口测试开始---------------
INFO - 2025-07-07 11:06:48,366 - sendrequests.py:57 -[sendrequests:execute_api_request] - 接口名称：为你推荐
INFO - 2025-07-07 11:06:48,366 - sendrequests.py:58 -[sendrequests:execute_api_request] - 请求地址：http://*************/api/show/info/home_recommend_list
INFO - 2025-07-07 11:06:48,366 - web_service.py:517 -[web_service:execute_command_simple] - [batch_1751857597] 第70行: [32m INFO - 2025-07-07 11:06:48,366 - sendrequests.py:57 -[sendrequests:execute_api_request] - 接口名称：为你推荐[0m
INFO - 2025-07-07 11:06:48,366 - sendrequests.py:59 -[sendrequests:execute_api_request] - 请求方式：POST
INFO - 2025-07-07 11:06:48,366 - sendrequests.py:60 -[sendrequests:execute_api_request] - 请求头：{'Content-Type': 'application/x-www-form-urlencoded', 'Connection': 'Keep-Alive', 'Accept-Encoding': 'gzip'}
INFO - 2025-07-07 11:06:48,366 - sendrequests.py:61 -[sendrequests:execute_api_request] - 测试用例名：获取为你推荐演出列表
INFO - 2025-07-07 11:06:48,367 - sendrequests.py:62 -[sendrequests:execute_api_request] - cookies值：None
INFO - 2025-07-07 11:06:48,367 - sendrequests.py:67 -[sendrequests:execute_api_request] - 参数类型：data
INFO - 2025-07-07 11:06:48,367 - sendrequests.py:69 -[sendrequests:execute_api_request] - 请求参数：{"page": 1, "pageSize": 20, "channelCode": "*********", "token": "HIx4GcZCV9rNSVvCDHxLnTd9lqe4T+yvqwm3lMQTCY7gdc7GaMgINq64QVyeD0XxEC+AHo9ONgxjBFMyfqlPosl+78aoOuYgI9gLHmRnbRGmlT2bSdpg/kI22Kc6vOUF,*********"}
INFO - 2025-07-07 11:06:48,647 - apiutils_business.py:179 -[apiutils_business:execute_test_cases] - 接口实际返回结果：{"code":0,"msg":"","data":{"total":65,"list":[{"id":"26","name":"老无座-0313","category_id":"10001","venue_id":"8","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/d7db73c1934ce8fcb5c4003afe3522b594040.jpg","ticket_status":"3","unusual_status":"0","need_real_name":"0","first_show_time":"2025-08-30 20:00:00","last_show_time":"2025-12-01 02:00:00","on_sale_time":"2025-03-13 19:00:00","max_buy_limit_per_id":"0","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"4.00","max_sale_price":"55.00","shelve_status":"1","third_shelve_status":"1","sort":"210","venue_info":{"venue_id":"8","venue_name":"梅赛德斯-奔驰文化中心","province_title":"上海市","city_title":"浦东新区","address":"世博大道1200号","latitude":"31.188952","longitude":"121.493201"},"show_info":{"show_id":"217","performance_id":"26","show_name":"2025-08-30 周六 20:00","show_start_time":"2025-08-30 20:00:00","show_end_time":"2025-08-30 22:00:00","show_type":"1","show_seat_type":"0","show_real_name_limit":"0","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2025-05-20 16:46:26","off_sale_time":"2025-08-30 20:00:00"},"category_info":{"category_id":"10001","category_name":"演唱会"}},{"id":"22","name":"【外部分销对接专用，不可编辑】编辑请联系上单-新选座-实名制","category_id":"10001","venue_id":"8","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/d7db73c1934ce8fcb5c4003afe3522b594040.jpg","ticket_status":"3","unusual_status":"0","need_real_name":"1","first_show_time":"2025-12-31 01:00:00","last_show_time":"2028-01-01 01:00:00","on_sale_time":"2025-04-22 16:05:43","max_buy_limit_per_id":"6","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"2.00","max_sale_price":"6.00","shelve_status":"1","third_shelve_status":"1","sort":"90","venue_info":{"venue_id":"8","venue_name":"梅赛德斯-奔驰文化中心","province_title":"上海市","city_title":"浦东新区","address":"世博大道1200号","latitude":"31.188952","longitude":"121.493201"},"show_info":{"show_id":"40","performance_id":"22","show_name":"2025-12-31 周三 01:00","show_start_time":"2025-12-31 01:00:00","show_end_time":"2025-12-31 02:00:00","show_type":"1","show_seat_type":"1","show_real_name_limit":"1","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2024-12-10 13:18:31","off_sale_time":"2025-12-31 01:00:00"},"category_info":{"category_id":"10001","category_name":"演唱会"}},{"id":"75","name":"【禁止修改】4.1  自营选座项目---日历模式（优惠混搭）+非实名","category_id":"10004","venue_id":"7","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/fa0fa284da6c8246a179c7400e8d132e59521.png","ticket_status":"12","unusual_status":"6","need_real_name":"0","first_show_time":"2025-09-01 08:00:00","last_show_time":"2026-04-11 23:59:00","on_sale_time":"2025-04-01 10:49:52","max_buy_limit_per_id":"6","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"110.00","max_sale_price":"2200.00","shelve_status":"1","third_shelve_status":"1","sort":"80","venue_info":{"venue_id":"7","venue_name":"凯迪拉克·上海音乐厅","province_title":"上海市","city_title":"黄浦区","address":"延安东路523号（近西藏南路）","latitude":"31.226854","longitude":"121.477977"},"show_info":{"show_id":"250","performance_id":"75","show_name":"2025-09-01 周一 08:00","show_start_time":"2025-09-01 08:00:00","show_end_time":"2025-09-01 10:00:00","show_type":"1","show_seat_type":"1","show_real_name_limit":"0","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2025-04-01 10:46:15","off_sale_time":"2025-08-31 00:00:00"},"category_info":{"category_id":"10004","category_name":"话剧音乐剧"}},{"id":"34","name":"朱磊业老无座线下搬单","category_id":"10002","venue_id":"7","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/84d2c832ab35b5514e8962a287d69c59631.png","ticket_status":"3","unusual_status":"0","need_real_name":"0","first_show_time":"2025-12-01 00:00:00","last_show_time":"2026-01-14 07:00:00","on_sale_time":"2025-03-11 19:23:31","max_buy_limit_per_id":"0","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"5","min_sale_price":"2.00","max_sale_price":"3.00","shelve_status":"1","third_shelve_status":"1","sort":"70","venue_info":{"venue_id":"7","venue_name":"凯迪拉克·上海音乐厅","province_title":"上海市","city_title":"黄浦区","address":"延安东路523号（近西藏南路）","latitude":"31.226854","longitude":"121.477977"},"show_info":{"show_id":"94","performance_id":"34","show_name":"2025-12-01 周一 00:00","show_start_time":"2025-12-01 00:00:00","show_end_time":"2025-12-01 17:00:00","show_type":"1","show_seat_type":"0","show_real_name_limit":"0","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2024-11-20 12:15:50","off_sale_time":"2025-11-30 00:00:00"},"category_info":{"category_id":"10002","category_name":"体育赛事"}},{"id":"8","name":"测试发票说明05","category_id":"10002","venue_id":"12","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/9353f3ac3ba4b47bd4642fcbdc393bbf101705.jpg","ticket_status":"3","unusual_status":"0","need_real_name":"0","first_show_time":"2026-01-03 00:00:00","last_show_time":"2026-01-03 02:00:00","on_sale_time":"2025-02-28 19:20:54","max_buy_limit_per_id":"6","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"33.00","max_sale_price":"33.00","shelve_status":"1","third_shelve_status":"1","sort":"60","venue_info":{"venue_id":"12","venue_name":"上海展览中心","province_title":"上海市","city_title":"静安区","address":"延安中路1000号","latitude":"31.224714","longitude":"121.452558"},"show_info":{"show_id":"16","performance_id":"8","show_name":"2026-01-03 周六 00:00","show_start_time":"2026-01-03 00:00:00","show_end_time":"2026-01-03 02:00:00","show_type":"1","show_seat_type":"0","show_real_name_limit":"0","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2025-01-14 15:12:06","off_sale_time":"2026-01-03 00:00:00"},"category_info":{"category_id":"10002","category_name":"体育赛事"}},{"id":"23","name":"【外部分销对接专用，不可编辑】编辑请联系上单-新选座-非实名制","category_id":"10007","venue_id":"8","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/d7db73c1934ce8fcb5c4003afe3522b594040.jpg","ticket_status":"3","unusual_status":"0","need_real_name":"1","first_show_time":"2028-01-01 00:00:00","last_show_time":"2028-01-01 01:00:00","on_sale_time":"2024-07-29 16:43:00","max_buy_limit_per_id":"6","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"50.00","max_sale_price":"165.00","shelve_status":"1","third_shelve_status":"1","sort":"60","venue_info":{"venue_id":"8","venue_name":"梅赛德斯-奔驰文化中心","province_title":"上海市","city_title":"浦东新区","address":"世博大道1200号","latitude":"31.188952","longitude":"121.493201"},"show_info":{"show_id":"42","performance_id":"23","show_name":"2028-01-01 周六 00:00","show_start_time":"2028-01-01 00:00:00","show_end_time":"2028-01-01 01:00:00","show_type":"1","show_seat_type":"1","show_real_name_limit":"1","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2024-07-29 16:40:00","off_sale_time":"2028-01-01 00:00:00"},"category_info":{"category_id":"10007","category_name":"亲子演出"}},{"id":"48","name":"朱磊业新配座套票配座","category_id":"10001","venue_id":"7","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/84d2c832ab35b5514e8962a287d69c59631.png","ticket_status":"3","unusual_status":"0","need_real_name":"0","first_show_time":"2026-01-02 00:00:00","last_show_time":"2026-01-02 04:00:00","on_sale_time":"2025-01-09 11:28:59","max_buy_limit_per_id":"0","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"2.00","max_sale_price":"3.00","shelve_status":"1","third_shelve_status":"1","sort":"60","venue_info":{"venue_id":"7","venue_name":"凯迪拉克·上海音乐厅","province_title":"上海市","city_title":"黄浦区","address":"延安东路523号（近西藏南路）","latitude":"31.226854","longitude":"121.477977"},"show_info":{"show_id":"179","performance_id":"48","show_name":"2026-01-02 周五 00:00","show_start_time":"2026-01-02 00:00:00","show_end_time":"2026-01-02 04:00:00","show_type":"1","show_seat_type":"1","show_real_name_limit":"9999","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2025-01-20 16:24:00","off_sale_time":"2026-01-02 00:00:00"},"category_info":{"category_id":"10001","category_name":"演唱会"}},{"id":"51","name":"朱磊业新无座快递配送转其它","category_id":"10001","venue_id":"13","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/84d2c832ab35b5514e8962a287d69c59631.png","ticket_status":"12","unusual_status":"3","need_real_name":"0","first_show_time":"2026-01-01 00:00:00","last_show_time":"2028-12-15 02:00:00","on_sale_time":"2024-12-13 17:47:21","max_buy_limit_per_id":"0","max_buy_limit_per_order":"20","min_buy_limit_per_order":"0","max_buy_limit_per_user":"1","min_sale_price":"2.00","max_sale_price":"6.00","shelve_status":"1","third_shelve_status":"1","sort":"60","venue_info":{"venue_id":"13","venue_name":"虹桥天地演艺中心","province_title":"上海市","city_title":"闵行区","address":"绍虹路33号","latitude":"31.19331","longitude":"121.31583"},"show_info":{"show_id":"182","performance_id":"51","show_name":"2026-01-01 周四 00:00","show_start_time":"2026-01-01 00:00:00","show_end_time":"2026-01-01 03:00:00","show_type":"1","show_seat_type":"0","show_real_name_limit":"0","max_buy_limit_per_order":"20","show_unusual_status":"1","on_sale_time":"2024-12-24 15:57:38","off_sale_time":"2026-01-01 00:00:00"},"category_info":{"category_id":"10001","category_name":"演唱会"}},{"id":"53","name":"小红书推送商品验证（老项目喽）","category_id":"10001","venue_id":"7","poster_url":"https:\/\/p1.meituan.net\/myvideodistribute\/6563cbda84d54511adb254e69b0c8001239854.jpg","ticket_status":"3","unusual_status":"0","need_real_name":"0","first_show_time":"2025-07-30 11:00:00","last_show_time":"2025-09-30 20:00:00","on_sale_time":"2025-02-11 13:00:00","max_buy_limit_per_id":"0","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"11.00","max_sale_price":"367.00","shelve_status":"1","third_shelve_status":"1","sort":"60","venue_info":{"venue_id":"7","venue_name":"凯迪拉克·上海音乐厅","province_title":"上海市","city_title":"黄浦区","address":"延安东路523号（近西藏南路）","latitude":"31.226854","longitude":"121.477977"},"show_info":{"show_id":"312","performance_id":"53","show_name":"2025-07-30 周三 11:00","show_start_time":"2025-07-30 11:00:00","show_end_time":"2025-07-30 19:00:00","show_type":"1","show_seat_type":"0","show_real_name_limit":"0","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2025-06-26 14:33:53","off_sale_time":"2025-07-30 11:00:00"},"category_info":{"category_id":"10001","category_name":"演唱会"}},{"id":"108","name":"场馆---项目开售时间校验优化","category_id":"10002","venue_id":"13","poster_url":"https:\/\/p1.meituan.net\/myvideodistribute\/54c4fc8913710b6cbd587f52bce3cb4a305726.jpg","ticket_status":"3","unusual_status":"0","need_real_name":"0","first_show_time":"2025-07-08 00:00:00","last_show_time":"2025-07-15 01:00:00","on_sale_time":"2025-06-10 19:45:00","max_buy_limit_per_id":"6","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"110.00","max_sale_price":"110.00","shelve_status":"1","third_shelve_status":"1","sort":"60","venue_info":{"venue_id":"13","venue_name":"虹桥天地演艺中心","province_title":"上海市","city_title":"闵行区","address":"绍虹路33号","latitude":"31.19331","longitude":"121.31583"},"show_info":{"show_id":"341","performance_id":"108","show_name":"2025-07-08 周二 00:00","show_start_time":"2025-07-08 00:00:00","show_end_time":"2025-07-08 01:00:00","show_type":"1","show_seat_type":"0","show_real_name_limit":"0","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2025-06-10 19:20:54","off_sale_time":"2025-07-08 00:00:00"},"category_info":{"category_id":"10002","category_name":"体育赛事"}},{"id":"4","name":"三方导码","category_id":"10001","venue_id":"8","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/680352446992ad3846ab6542da1d1400288377.jpg","ticket_status":"3","unusual_status":"0","need_real_name":"1","first_show_time":"2025-11-06 00:00:00","last_show_time":"2025-11-06 01:00:00","on_sale_time":"2024-11-06 15:30:47","max_buy_limit_per_id":"6","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"2.00","max_sale_price":"3.00","shelve_status":"1","third_shelve_status":"1","sort":"50","venue_info":{"venue_id":"8","venue_name":"梅赛德斯-奔驰文化中心","province_title":"上海市","city_title":"浦东新区","address":"世博大道1200号","latitude":"31.188952","longitude":"121.493201"},"show_info":{"show_id":"10","performance_id":"4","show_name":"2025-11-06 周四 00:00","show_start_time":"2025-11-06 00:00:00","show_end_time":"2025-11-06 01:00:00","show_type":"1","show_seat_type":"0","show_real_name_limit":"0","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2024-11-06 15:29:42","off_sale_time":"2025-11-06 00:00:00"},"category_info":{"category_id":"10001","category_name":"演唱会"}},{"id":"9","name":"20241202分销配座测试","category_id":"10002","venue_id":"13","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/bfb0c3a9cc0695646bbee5de5288ed9c950360.jpg","ticket_status":"3","unusual_status":"0","need_real_name":"0","first_show_time":"2025-12-01 00:00:00","last_show_time":"2025-12-11 02:00:00","on_sale_time":"2025-02-14 15:46:50","max_buy_limit_per_id":"0","max_buy_limit_per_order":"20","min_buy_limit_per_order":"0","max_buy_limit_per_user":"99999","min_sale_price":"0.00","max_sale_price":"1056.00","shelve_status":"1","third_shelve_status":"1","sort":"50","venue_info":{"venue_id":"13","venue_name":"虹桥天地演艺中心","province_title":"上海市","city_title":"闵行区","address":"绍虹路33号","latitude":"31.19331","longitude":"121.31583"},"show_info":{"show_id":"17","performance_id":"9","show_name":"2025-12-01 周一 00:00","show_start_time":"2025-12-01 00:00:00","show_end_time":"2025-12-01 01:00:00","show_type":"1","show_seat_type":"1","show_real_name_limit":"0","max_buy_limit_per_order":"20","show_unusual_status":"0","on_sale_time":"2024-12-02 14:05:56","off_sale_time":"2025-12-01 00:00:00"},"category_info":{"category_id":"10002","category_name":"体育赛事"}},{"id":"10","name":"老上单实名制","category_id":"10001","venue_id":"7","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/cabb76c33aad7247623a0a827cec6a7e8066.jpg","ticket_status":"3","unusual_status":"0","need_real_name":"1","first_show_time":"2025-09-01 02:00:00","last_show_time":"2025-09-01 04:00:00","on_sale_time":"2025-04-30 23:00:00","max_buy_limit_per_id":"6","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"2.00","max_sale_price":"2.00","shelve_status":"1","third_shelve_status":"1","sort":"50","venue_info":{"venue_id":"7","venue_name":"凯迪拉克·上海音乐厅","province_title":"上海市","city_title":"黄浦区","address":"延安东路523号（近西藏南路）","latitude":"31.226854","longitude":"121.477977"},"show_info":{"show_id":"28","performance_id":"10","show_name":"2025-09-01 周一 02:00","show_start_time":"2025-09-01 02:00:00","show_end_time":"2025-09-01 04:00:00","show_type":"1","show_seat_type":"0","show_real_name_limit":"1","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2025-02-24 10:33:25","off_sale_time":"2025-09-01 02:00:00"},"category_info":{"category_id":"10001","category_name":"演唱会"}},{"id":"12","name":"【售卖场】2.11无座-2026.12.28","category_id":"10001","venue_id":"7","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/03fdb9f41adcf253ff9211097ab444421598915.png","ticket_status":"3","unusual_status":"0","need_real_name":"1","first_show_time":"2026-12-28 19:00:00","last_show_time":"2026-12-31 23:00:00","on_sale_time":"2025-02-11 14:53:19","max_buy_limit_per_id":"10","max_buy_limit_per_order":"4","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"748.00","max_sale_price":"2178.00","shelve_status":"1","third_shelve_status":"1","sort":"50","venue_info":{"venue_id":"7","venue_name":"凯迪拉克·上海音乐厅","province_title":"上海市","city_title":"黄浦区","address":"延安东路523号（近西藏南路）","latitude":"31.226854","longitude":"121.477977"},"show_info":{"show_id":"30","performance_id":"12","show_name":"2026-12-28 周一 19:00","show_start_time":"2026-12-28 19:00:00","show_end_time":"2026-12-28 23:00:00","show_type":"1","show_seat_type":"0","show_real_name_limit":"1","max_buy_limit_per_order":"2","show_unusual_status":"0","on_sale_time":"2025-02-11 11:33:38","off_sale_time":"2026-12-28 00:00:00"},"category_info":{"category_id":"10001","category_name":"演唱会"}},{"id":"14","name":"zly测试渠道","category_id":"10001","venue_id":"10","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/84d2c832ab35b5514e8962a287d69c59631.png","ticket_status":"3","unusual_status":"0","need_real_name":"0","first_show_time":"2026-02-06 00:00:00","last_show_time":"2026-02-06 05:00:00","on_sale_time":"2025-03-07 00:00:00","max_buy_limit_per_id":"6","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"2.00","max_sale_price":"2.00","shelve_status":"1","third_shelve_status":"1","sort":"50","venue_info":{"venue_id":"10","venue_name":"猫眼演出321","province_title":"北京市","city_title":"朝阳区","address":"望京sohoT3B栋2楼","latitude":"39.996126","longitude":"116.480553"},"show_info":{"show_id":"34","performance_id":"14","show_name":"2026-02-06 周五 00:00","show_start_time":"2026-02-06 00:00:00","show_end_time":"2026-02-06 05:00:00","show_type":"1","show_seat_type":"1","show_real_name_limit":"0","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2025-02-17 15:57:48","off_sale_time":"2026-02-06 00:00:00"},"category_info":{"category_id":"10001","category_name":"演唱会"}},{"id":"17","name":"老无座-非大演-0227","category_id":"10004","venue_id":"8","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/d7db73c1934ce8fcb5c4003afe3522b594040.jpg","ticket_status":"3","unusual_status":"0","need_real_name":"0","first_show_time":"2025-12-31 00:00:00","last_show_time":"2025-12-31 01:00:00","on_sale_time":"2025-02-27 19:55:09","max_buy_limit_per_id":"0","max_buy_limit_per_order":"10","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"11.00","max_sale_price":"33.00","shelve_status":"1","third_shelve_status":"1","sort":"50","venue_info":{"venue_id":"8","venue_name":"梅赛德斯-奔驰文化中心","province_title":"上海市","city_title":"浦东新区","address":"世博大道1200号","latitude":"31.188952","longitude":"121.493201"},"show_info":{"show_id":"36","performance_id":"17","show_name":"2025-12-31 周三 00:00","show_start_time":"2025-12-31 00:00:00","show_end_time":"2025-12-31 01:00:00","show_type":"1","show_seat_type":"0","show_real_name_limit":"0","max_buy_limit_per_order":"10","show_unusual_status":"0","on_sale_time":"2025-02-27 19:54:03","off_sale_time":"2025-12-31 00:00:00"},"category_info":{"category_id":"10004","category_name":"话剧音乐剧"}},{"id":"19","name":"和净子账号测试项目2-0641","category_id":"10001","venue_id":"15","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/7f13fafffa5464aec7c728efca22be0a24721.jpg","ticket_status":"12","unusual_status":"1","need_real_name":"0","first_show_time":"2025-11-01 00:00:00","last_show_time":"2025-11-01 01:00:00","on_sale_time":"2025-03-07 18:35:00","max_buy_limit_per_id":"0","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"4.00","max_sale_price":"5.00","shelve_status":"1","third_shelve_status":"1","sort":"50","venue_info":{"venue_id":"15","venue_name":"北京展览馆剧场","province_title":"北京市","city_title":"西城区","address":"西直门外大街135号","latitude":"39.942007","longitude":"116.343911"},"show_info":{"show_id":"38","performance_id":"19","show_name":"2025-11-01 周六 00:00","show_start_time":"2025-11-01 00:00:00","show_end_time":"2025-11-01 01:00:00","show_type":"1","show_seat_type":"0","show_real_name_limit":"0","max_buy_limit_per_order":"6","show_unusual_status":"2","on_sale_time":"2024-12-26 19:40:48","off_sale_time":"2025-11-01 00:00:00"},"category_info":{"category_id":"10001","category_name":"演唱会"}},{"id":"21","name":"商家上单","category_id":"10001","venue_id":"17","poster_url":"https:\/\/p1.meituan.net\/myvideodistribute\/78c1cb8f72c5a9d08f7879bd1cb0f6d4279046.jpg","ticket_status":"3","unusual_status":"0","need_real_name":"0","first_show_time":"2025-11-01 00:00:00","last_show_time":"2025-11-01 05:00:00","on_sale_time":"2025-03-31 19:56:37","max_buy_limit_per_id":"0","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"136.00","max_sale_price":"136.00","shelve_status":"1","third_shelve_status":"1","sort":"50","venue_info":{"venue_id":"17","venue_name":"上海体育馆","province_title":"上海市","city_title":"徐汇区","address":"漕溪北路1111号","latitude":"31.181511","longitude":"121.438358"},"show_info":{"show_id":"39","performance_id":"21","show_name":"2025-11-01 周六 00:00","show_start_time":"2025-11-01 00:00:00","show_end_time":"2025-11-01 05:00:00","show_type":"1","show_seat_type":"0","show_real_name_limit":"6","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2025-03-03 19:27:53","off_sale_time":"2025-11-01 00:00:00"},"category_info":{"category_id":"10001","category_name":"演唱会"}},{"id":"24","name":"【外部分销对接专用，不可编辑】编辑请联系上单-选座-实名制","category_id":"10001","venue_id":"17","poster_url":"https:\/\/p1.meituan.net\/myvideodistribute\/d69af400577449d5ea329a53cbe0bb8d1185076.jpg","ticket_status":"3","unusual_status":"0","need_real_name":"1","first_show_time":"2027-01-01 19:00:00","last_show_time":"2029-01-01 21:00:00","on_sale_time":"2024-04-18 14:49:55","max_buy_limit_per_id":"6","max_buy_limit_per_order":"3","min_buy_limit_per_order":"0","max_buy_limit_per_user":"10","min_sale_price":"6.00","max_sale_price":"11.00","shelve_status":"1","third_shelve_status":"1","sort":"50","venue_info":{"venue_id":"17","venue_name":"上海体育馆","province_title":"上海市","city_title":"徐汇区","address":"漕溪北路1111号","latitude":"31.181511","longitude":"121.438358"},"show_info":{"show_id":"170","performance_id":"24","show_name":"2025国庆专场","show_start_time":"2025-10-01 15:00:00","show_end_time":"2025-10-01 18:00:00","show_type":"1","show_seat_type":"1","show_real_name_limit":"0","max_buy_limit_per_order":"3","show_unusual_status":"0","on_sale_time":"2025-04-28 14:46:21","off_sale_time":"2025-10-07 19:00:00"},"category_info":{"category_id":"10001","category_name":"演唱会"}},{"id":"29","name":"老无座-日历-0318","category_id":"10004","venue_id":"8","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/d7db73c1934ce8fcb5c4003afe3522b594040.jpg","ticket_status":"3","unusual_status":"0","need_real_name":"0","first_show_time":"2025-11-27 01:00:00","last_show_time":"2025-12-31 02:00:00","on_sale_time":"2025-03-18 11:30:41","max_buy_limit_per_id":"0","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"11.00","max_sale_price":"55.00","shelve_status":"1","third_shelve_status":"1","sort":"50","venue_info":{"venue_id":"8","venue_name":"梅赛德斯-奔驰文化中心","province_title":"上海市","city_title":"浦东新区","address":"世博大道1200号","latitude":"31.188952","longitude":"121.493201"},"show_info":{"show_id":"51","performance_id":"29","show_name":"2025-11-27 周四 01:00","show_start_time":"2025-11-27 01:00:00","show_end_time":"2025-11-27 02:00:00","show_type":"1","show_seat_type":"0","show_real_name_limit":"0","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2025-03-18 11:29:39","off_sale_time":"2025-11-27 01:00:00"},"category_info":{"category_id":"10004","category_name":"话剧音乐剧"}}]}}
INFO - 2025-07-07 11:06:48,653 - assertion_utils.py:35 -[assertion_utils:status_code_assert] - 状态码断言成功：接口实际返回状态码 200 == 200
INFO - 2025-07-07 11:06:48,656 - assertion_utils.py:94 -[assertion_utils:equal_assert] - 相等断言成功：接口实际结果 {'code': 0} == 预期结果：{'code': 0}
INFO - 2025-07-07 11:06:48,656 - web_service.py:517 -[web_service:execute_command_simple] - [batch_1751857597] 第80行: [32m INFO - 2025-07-07 11:06:48,656 - assertion_utils.py:94 -[assertion_utils:equal_assert] - 相等断言成功：接口实际结果 {'code': 0} == 预期结果：{'code': 0}[0m
INFO - 2025-07-07 11:06:48,656 - assertion_utils.py:206 -[assertion_utils:assert_result] - 测试成功
INFO - 2025-07-07 11:06:48,663 - conftest.py:9 -[conftest:print_info] - ---------------接口测试结束---------------
INFO - 2025-07-07 11:06:50,178 - conftest.py:7 -[conftest:print_info] - ---------------接口测试开始---------------
INFO - 2025-07-07 11:06:50,183 - sendrequests.py:57 -[sendrequests:execute_api_request] - 接口名称：首页广告位
INFO - 2025-07-07 11:06:50,183 - sendrequests.py:58 -[sendrequests:execute_api_request] - 请求地址：http://*************/api/show/ad/select_list
INFO - 2025-07-07 11:06:50,183 - sendrequests.py:59 -[sendrequests:execute_api_request] - 请求方式：POST
INFO - 2025-07-07 11:06:50,183 - sendrequests.py:60 -[sendrequests:execute_api_request] - 请求头：{'Content-Type': 'application/x-www-form-urlencoded', 'Connection': 'Keep-Alive', 'Accept-Encoding': 'gzip'}
INFO - 2025-07-07 11:06:50,183 - sendrequests.py:61 -[sendrequests:execute_api_request] - 测试用例名：获取首页广告列表
INFO - 2025-07-07 11:06:50,183 - sendrequests.py:62 -[sendrequests:execute_api_request] - cookies值：None
INFO - 2025-07-07 11:06:50,183 - sendrequests.py:67 -[sendrequests:execute_api_request] - 参数类型：data
INFO - 2025-07-07 11:06:50,183 - web_service.py:517 -[web_service:execute_command_simple] - [batch_1751857597] 第90行: [32m INFO - 2025-07-07 11:06:50,183 - sendrequests.py:62 -[sendrequests:execute_api_request] - cookies值：None[0m
INFO - 2025-07-07 11:06:50,184 - sendrequests.py:69 -[sendrequests:execute_api_request] - 请求参数：{"channelCode": "*********", "token": "HIx4GcZCV9rNSVvCDHxLnTd9lqe4T+yvqwm3lMQTCY7gdc7GaMgINq64QVyeD0XxEC+AHo9ONgxjBFMyfqlPosl+78aoOuYgI9gLHmRnbRGmlT2bSdpg/kI22Kc6vOUF,*********"}
INFO - 2025-07-07 11:06:50,471 - apiutils_business.py:179 -[apiutils_business:execute_test_cases] - 接口实际返回结果：{"code":0,"msg":"","data":[{"id":"31","name":"端午活动","position_type":"home_middle","image_url":"upload\/20250529\/102446xt4IfG0Kf0.jpg","link_type":"customize","link_value":"https:\/\/www.msn.cn\/zh-cn\/news\/other\/%E7%AB%AF%E5%8D%88%E6%B0%9B%E5%9B%B4%E6%84%9F%E6%8B%89%E6%BB%A1-%E8%B5%9B%E9%BE%99%E8%88%9F-%E7%B2%BD%E5%AD%90-%E9%A6%99%E5%8C%85%E9%83%BD%E6%9C%89%E4%BA%86%E6%96%B0%E8%8A%B1%E6%A0%B7\/ar-AA1Fz2TU?ocid=msedgntp&pc=CNNDDB","sort_value":"99","link_type_desc":"自定义","image_url_show":"http:\/\/*************\/resource\/play\/upload\/20250529\/102446xt4IfG0Kf0.jpg"},{"id":"32","name":"陈慧娴","position_type":"home_middle","image_url":"upload\/20250529\/105313SLTl4LjSBR.png","link_type":"performance","link_value":"34","sort_value":"67","link_type_desc":"演出项目","image_url_show":"http:\/\/*************\/resource\/play\/upload\/20250529\/105313SLTl4LjSBR.png"},{"id":"40","name":"【专用】深圳大运中心体育场","position_type":"home_middle","image_url":"upload\/20250529\/13382349PX4yl_Dk.png","link_type":"performance","link_value":"69","sort_value":"66","link_type_desc":"演出项目","image_url_show":"http:\/\/*************\/resource\/play\/upload\/20250529\/13382349PX4yl_Dk.png"},{"id":"45","name":"luowt","position_type":"home_middle","image_url":"upload\/20250630\/144818XuaAA54TKR.png","link_type":"performance","link_value":"106","sort_value":"23","link_type_desc":"演出项目","image_url_show":"http:\/\/*************\/resource\/play\/upload\/20250630\/144818XuaAA54TKR.png"},{"id":"42","name":"wk新建的没有推广时间的","position_type":"home_middle","image_url":"upload\/20250529\/1732116kcMEpIoAV.png","link_type":"customize","link_value":"http:\/\/www.baoLY.com","sort_value":"22","link_type_desc":"自定义","image_url_show":"http:\/\/*************\/resource\/play\/upload\/20250529\/1732116kcMEpIoAV.png"},{"id":"30","name":"【勿动】测试城市","position_type":"home_middle","image_url":"upload\/20250528\/174625-aUjLRKWr1.jpg","link_type":"category","link_value":"10002","sort_value":"0","link_type_desc":"演出分类","image_url_show":"http:\/\/*************\/resource\/play\/upload\/20250528\/174625-aUjLRKWr1.jpg"},{"id":"29","name":"【勿动】QQR测试","position_type":"home_middle","image_url":"upload\/20250528\/173340QP-ZIAp90D.png","link_type":"performance","link_value":"26","sort_value":"-1","link_type_desc":"演出项目","image_url_show":"http:\/\/*************\/resource\/play\/upload\/20250528\/173340QP-ZIAp90D.png"}]}
INFO - 2025-07-07 11:06:50,480 - assertion_utils.py:35 -[assertion_utils:status_code_assert] - 状态码断言成功：接口实际返回状态码 200 == 200
INFO - 2025-07-07 11:06:50,481 - assertion_utils.py:94 -[assertion_utils:equal_assert] - 相等断言成功：接口实际结果 {'code': 0} == 预期结果：{'code': 0}
INFO - 2025-07-07 11:06:50,482 - assertion_utils.py:206 -[assertion_utils:assert_result] - 测试成功
INFO - 2025-07-07 11:06:50,492 - conftest.py:9 -[conftest:print_info] - ---------------接口测试结束---------------
INFO - 2025-07-07 11:06:52,004 - conftest.py:7 -[conftest:print_info] - ---------------接口测试开始---------------
INFO - 2025-07-07 11:06:52,027 - sendrequests.py:57 -[sendrequests:execute_api_request] - 接口名称：演唱会分类
INFO - 2025-07-07 11:06:52,027 - sendrequests.py:58 -[sendrequests:execute_api_request] - 请求地址：http://*************/api/show/info/performance_search
INFO - 2025-07-07 11:06:52,027 - web_service.py:517 -[web_service:execute_command_simple] - [batch_1751857597] 第100行: [32m INFO - 2025-07-07 11:06:52,027 - sendrequests.py:57 -[sendrequests:execute_api_request] - 接口名称：演唱会分类[0m
INFO - 2025-07-07 11:06:52,027 - sendrequests.py:59 -[sendrequests:execute_api_request] - 请求方式：POST
INFO - 2025-07-07 11:06:52,027 - sendrequests.py:60 -[sendrequests:execute_api_request] - 请求头：{'Content-Type': 'application/x-www-form-urlencoded', 'Connection': 'Keep-Alive', 'Accept-Encoding': 'gzip'}
INFO - 2025-07-07 11:06:52,028 - sendrequests.py:61 -[sendrequests:execute_api_request] - 测试用例名：按类型搜索=演唱会
INFO - 2025-07-07 11:06:52,028 - sendrequests.py:62 -[sendrequests:execute_api_request] - cookies值：None
INFO - 2025-07-07 11:06:52,028 - sendrequests.py:67 -[sendrequests:execute_api_request] - 参数类型：data
INFO - 2025-07-07 11:06:52,028 - sendrequests.py:69 -[sendrequests:execute_api_request] - 请求参数：{"category_id": "10001", "channelCode": "*********", "token": "HIx4GcZCV9rNSVvCDHxLnTd9lqe4T+yvqwm3lMQTCY7gdc7GaMgINq64QVyeD0XxEC+AHo9ONgxjBFMyfqlPosl+78aoOuYgI9gLHmRnbRGmlT2bSdpg/kI22Kc6vOUF,*********"}
INFO - 2025-07-07 11:06:52,264 - apiutils_business.py:179 -[apiutils_business:execute_test_cases] - 接口实际返回结果：{"code":0,"msg":"","data":{"total":27,"list":[{"id":"26","name":"老无座-0313","category_id":"10001","venue_id":"8","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/d7db73c1934ce8fcb5c4003afe3522b594040.jpg","ticket_status":"3","unusual_status":"0","need_real_name":"0","first_show_time":"2025-08-30 20:00:00","last_show_time":"2025-12-01 02:00:00","on_sale_time":"2025-03-13 19:00:00","max_buy_limit_per_id":"0","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"4.00","max_sale_price":"55.00","shelve_status":"1","third_shelve_status":"1","sort":"210","venue_info":{"venue_id":"8","venue_name":"梅赛德斯-奔驰文化中心","province_title":"上海市","city_title":"浦东新区","address":"世博大道1200号","latitude":"31.188952","longitude":"121.493201"},"show_info":{"show_id":"217","performance_id":"26","show_name":"2025-08-30 周六 20:00","show_start_time":"2025-08-30 20:00:00","show_end_time":"2025-08-30 22:00:00","show_type":"1","show_seat_type":"0","show_real_name_limit":"0","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2025-05-20 16:46:26","off_sale_time":"2025-08-30 20:00:00"},"category_info":{"category_id":"10001","category_name":"演唱会"}},{"id":"22","name":"【外部分销对接专用，不可编辑】编辑请联系上单-新选座-实名制","category_id":"10001","venue_id":"8","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/d7db73c1934ce8fcb5c4003afe3522b594040.jpg","ticket_status":"3","unusual_status":"0","need_real_name":"1","first_show_time":"2025-12-31 01:00:00","last_show_time":"2028-01-01 01:00:00","on_sale_time":"2025-04-22 16:05:43","max_buy_limit_per_id":"6","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"2.00","max_sale_price":"6.00","shelve_status":"1","third_shelve_status":"1","sort":"90","venue_info":{"venue_id":"8","venue_name":"梅赛德斯-奔驰文化中心","province_title":"上海市","city_title":"浦东新区","address":"世博大道1200号","latitude":"31.188952","longitude":"121.493201"},"show_info":{"show_id":"40","performance_id":"22","show_name":"2025-12-31 周三 01:00","show_start_time":"2025-12-31 01:00:00","show_end_time":"2025-12-31 02:00:00","show_type":"1","show_seat_type":"1","show_real_name_limit":"1","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2024-12-10 13:18:31","off_sale_time":"2025-12-31 01:00:00"},"category_info":{"category_id":"10001","category_name":"演唱会"}},{"id":"48","name":"朱磊业新配座套票配座","category_id":"10001","venue_id":"7","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/84d2c832ab35b5514e8962a287d69c59631.png","ticket_status":"3","unusual_status":"0","need_real_name":"0","first_show_time":"2026-01-02 00:00:00","last_show_time":"2026-01-02 04:00:00","on_sale_time":"2025-01-09 11:28:59","max_buy_limit_per_id":"0","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"2.00","max_sale_price":"3.00","shelve_status":"1","third_shelve_status":"1","sort":"60","venue_info":{"venue_id":"7","venue_name":"凯迪拉克·上海音乐厅","province_title":"上海市","city_title":"黄浦区","address":"延安东路523号（近西藏南路）","latitude":"31.226854","longitude":"121.477977"},"show_info":{"show_id":"179","performance_id":"48","show_name":"2026-01-02 周五 00:00","show_start_time":"2026-01-02 00:00:00","show_end_time":"2026-01-02 04:00:00","show_type":"1","show_seat_type":"1","show_real_name_limit":"9999","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2025-01-20 16:24:00","off_sale_time":"2026-01-02 00:00:00"},"category_info":{"category_id":"10001","category_name":"演唱会"}},{"id":"51","name":"朱磊业新无座快递配送转其它","category_id":"10001","venue_id":"13","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/84d2c832ab35b5514e8962a287d69c59631.png","ticket_status":"12","unusual_status":"3","need_real_name":"0","first_show_time":"2026-01-01 00:00:00","last_show_time":"2028-12-15 02:00:00","on_sale_time":"2024-12-13 17:47:21","max_buy_limit_per_id":"0","max_buy_limit_per_order":"20","min_buy_limit_per_order":"0","max_buy_limit_per_user":"1","min_sale_price":"2.00","max_sale_price":"6.00","shelve_status":"1","third_shelve_status":"1","sort":"60","venue_info":{"venue_id":"13","venue_name":"虹桥天地演艺中心","province_title":"上海市","city_title":"闵行区","address":"绍虹路33号","latitude":"31.19331","longitude":"121.31583"},"show_info":{"show_id":"182","performance_id":"51","show_name":"2026-01-01 周四 00:00","show_start_time":"2026-01-01 00:00:00","show_end_time":"2026-01-01 03:00:00","show_type":"1","show_seat_type":"0","show_real_name_limit":"0","max_buy_limit_per_order":"20","show_unusual_status":"1","on_sale_time":"2024-12-24 15:57:38","off_sale_time":"2026-01-01 00:00:00"},"category_info":{"category_id":"10001","category_name":"演唱会"}},{"id":"53","name":"小红书推送商品验证（老项目喽）","category_id":"10001","venue_id":"7","poster_url":"https:\/\/p1.meituan.net\/myvideodistribute\/6563cbda84d54511adb254e69b0c8001239854.jpg","ticket_status":"3","unusual_status":"0","need_real_name":"0","first_show_time":"2025-07-30 11:00:00","last_show_time":"2025-09-30 20:00:00","on_sale_time":"2025-02-11 13:00:00","max_buy_limit_per_id":"0","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"11.00","max_sale_price":"367.00","shelve_status":"1","third_shelve_status":"1","sort":"60","venue_info":{"venue_id":"7","venue_name":"凯迪拉克·上海音乐厅","province_title":"上海市","city_title":"黄浦区","address":"延安东路523号（近西藏南路）","latitude":"31.226854","longitude":"121.477977"},"show_info":{"show_id":"312","performance_id":"53","show_name":"2025-07-30 周三 11:00","show_start_time":"2025-07-30 11:00:00","show_end_time":"2025-07-30 19:00:00","show_type":"1","show_seat_type":"0","show_real_name_limit":"0","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2025-06-26 14:33:53","off_sale_time":"2025-07-30 11:00:00"},"category_info":{"category_id":"10001","category_name":"演唱会"}},{"id":"4","name":"三方导码","category_id":"10001","venue_id":"8","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/680352446992ad3846ab6542da1d1400288377.jpg","ticket_status":"3","unusual_status":"0","need_real_name":"1","first_show_time":"2025-11-06 00:00:00","last_show_time":"2025-11-06 01:00:00","on_sale_time":"2024-11-06 15:30:47","max_buy_limit_per_id":"6","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"2.00","max_sale_price":"3.00","shelve_status":"1","third_shelve_status":"1","sort":"50","venue_info":{"venue_id":"8","venue_name":"梅赛德斯-奔驰文化中心","province_title":"上海市","city_title":"浦东新区","address":"世博大道1200号","latitude":"31.188952","longitude":"121.493201"},"show_info":{"show_id":"10","performance_id":"4","show_name":"2025-11-06 周四 00:00","show_start_time":"2025-11-06 00:00:00","show_end_time":"2025-11-06 01:00:00","show_type":"1","show_seat_type":"0","show_real_name_limit":"0","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2024-11-06 15:29:42","off_sale_time":"2025-11-06 00:00:00"},"category_info":{"category_id":"10001","category_name":"演唱会"}},{"id":"10","name":"老上单实名制","category_id":"10001","venue_id":"7","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/cabb76c33aad7247623a0a827cec6a7e8066.jpg","ticket_status":"3","unusual_status":"0","need_real_name":"1","first_show_time":"2025-09-01 02:00:00","last_show_time":"2025-09-01 04:00:00","on_sale_time":"2025-04-30 23:00:00","max_buy_limit_per_id":"6","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"2.00","max_sale_price":"2.00","shelve_status":"1","third_shelve_status":"1","sort":"50","venue_info":{"venue_id":"7","venue_name":"凯迪拉克·上海音乐厅","province_title":"上海市","city_title":"黄浦区","address":"延安东路523号（近西藏南路）","latitude":"31.226854","longitude":"121.477977"},"show_info":{"show_id":"28","performance_id":"10","show_name":"2025-09-01 周一 02:00","show_start_time":"2025-09-01 02:00:00","show_end_time":"2025-09-01 04:00:00","show_type":"1","show_seat_type":"0","show_real_name_limit":"1","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2025-02-24 10:33:25","off_sale_time":"2025-09-01 02:00:00"},"category_info":{"category_id":"10001","category_name":"演唱会"}},{"id":"12","name":"【售卖场】2.11无座-2026.12.28","category_id":"10001","venue_id":"7","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/03fdb9f41adcf253ff9211097ab444421598915.png","ticket_status":"3","unusual_status":"0","need_real_name":"1","first_show_time":"2026-12-28 19:00:00","last_show_time":"2026-12-31 23:00:00","on_sale_time":"2025-02-11 14:53:19","max_buy_limit_per_id":"10","max_buy_limit_per_order":"4","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"748.00","max_sale_price":"2178.00","shelve_status":"1","third_shelve_status":"1","sort":"50","venue_info":{"venue_id":"7","venue_name":"凯迪拉克·上海音乐厅","province_title":"上海市","city_title":"黄浦区","address":"延安东路523号（近西藏南路）","latitude":"31.226854","longitude":"121.477977"},"show_info":{"show_id":"30","performance_id":"12","show_name":"2026-12-28 周一 19:00","show_start_time":"2026-12-28 19:00:00","show_end_time":"2026-12-28 23:00:00","show_type":"1","show_seat_type":"0","show_real_name_limit":"1","max_buy_limit_per_order":"2","show_unusual_status":"0","on_sale_time":"2025-02-11 11:33:38","off_sale_time":"2026-12-28 00:00:00"},"category_info":{"category_id":"10001","category_name":"演唱会"}},{"id":"14","name":"zly测试渠道","category_id":"10001","venue_id":"10","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/84d2c832ab35b5514e8962a287d69c59631.png","ticket_status":"3","unusual_status":"0","need_real_name":"0","first_show_time":"2026-02-06 00:00:00","last_show_time":"2026-02-06 05:00:00","on_sale_time":"2025-03-07 00:00:00","max_buy_limit_per_id":"6","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"2.00","max_sale_price":"2.00","shelve_status":"1","third_shelve_status":"1","sort":"50","venue_info":{"venue_id":"10","venue_name":"猫眼演出321","province_title":"北京市","city_title":"朝阳区","address":"望京sohoT3B栋2楼","latitude":"39.996126","longitude":"116.480553"},"show_info":{"show_id":"34","performance_id":"14","show_name":"2026-02-06 周五 00:00","show_start_time":"2026-02-06 00:00:00","show_end_time":"2026-02-06 05:00:00","show_type":"1","show_seat_type":"1","show_real_name_limit":"0","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2025-02-17 15:57:48","off_sale_time":"2026-02-06 00:00:00"},"category_info":{"category_id":"10001","category_name":"演唱会"}},{"id":"19","name":"和净子账号测试项目2-0641","category_id":"10001","venue_id":"15","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/7f13fafffa5464aec7c728efca22be0a24721.jpg","ticket_status":"12","unusual_status":"1","need_real_name":"0","first_show_time":"2025-11-01 00:00:00","last_show_time":"2025-11-01 01:00:00","on_sale_time":"2025-03-07 18:35:00","max_buy_limit_per_id":"0","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"4.00","max_sale_price":"5.00","shelve_status":"1","third_shelve_status":"1","sort":"50","venue_info":{"venue_id":"15","venue_name":"北京展览馆剧场","province_title":"北京市","city_title":"西城区","address":"西直门外大街135号","latitude":"39.942007","longitude":"116.343911"},"show_info":{"show_id":"38","performance_id":"19","show_name":"2025-11-01 周六 00:00","show_start_time":"2025-11-01 00:00:00","show_end_time":"2025-11-01 01:00:00","show_type":"1","show_seat_type":"0","show_real_name_limit":"0","max_buy_limit_per_order":"6","show_unusual_status":"2","on_sale_time":"2024-12-26 19:40:48","off_sale_time":"2025-11-01 00:00:00"},"category_info":{"category_id":"10001","category_name":"演唱会"}}]}}
INFO - 2025-07-07 11:06:52,268 - assertion_utils.py:35 -[assertion_utils:status_code_assert] - 状态码断言成功：接口实际返回状态码 200 == 200
INFO - 2025-07-07 11:06:52,269 - assertion_utils.py:94 -[assertion_utils:equal_assert] - 相等断言成功：接口实际结果 {'code': 0} == 预期结果：{'code': 0}
INFO - 2025-07-07 11:06:52,269 - web_service.py:517 -[web_service:execute_command_simple] - [batch_1751857597] 第110行: [32m INFO - 2025-07-07 11:06:52,269 - assertion_utils.py:94 -[assertion_utils:equal_assert] - 相等断言成功：接口实际结果 {'code': 0} == 预期结果：{'code': 0}[0m
INFO - 2025-07-07 11:06:52,270 - assertion_utils.py:206 -[assertion_utils:assert_result] - 测试成功
INFO - 2025-07-07 11:06:52,277 - conftest.py:9 -[conftest:print_info] - ---------------接口测试结束---------------
INFO - 2025-07-07 11:06:53,796 - conftest.py:7 -[conftest:print_info] - ---------------接口测试开始---------------
INFO - 2025-07-07 11:06:53,802 - sendrequests.py:57 -[sendrequests:execute_api_request] - 接口名称：**搜索**
INFO - 2025-07-07 11:06:53,802 - sendrequests.py:58 -[sendrequests:execute_api_request] - 请求地址：http://*************/api/show/info/performance_search
INFO - 2025-07-07 11:06:53,802 - sendrequests.py:59 -[sendrequests:execute_api_request] - 请求方式：POST
INFO - 2025-07-07 11:06:53,802 - sendrequests.py:60 -[sendrequests:execute_api_request] - 请求头：{'Content-Type': 'application/x-www-form-urlencoded', 'Connection': 'Keep-Alive', 'Accept-Encoding': 'gzip'}
INFO - 2025-07-07 11:06:53,802 - sendrequests.py:61 -[sendrequests:execute_api_request] - 测试用例名：按演出名称关键字搜索
INFO - 2025-07-07 11:06:53,802 - sendrequests.py:62 -[sendrequests:execute_api_request] - cookies值：None
INFO - 2025-07-07 11:06:53,802 - sendrequests.py:67 -[sendrequests:execute_api_request] - 参数类型：data
INFO - 2025-07-07 11:06:53,802 - web_service.py:517 -[web_service:execute_command_simple] - [batch_1751857597] 第120行: [32m INFO - 2025-07-07 11:06:53,802 - sendrequests.py:62 -[sendrequests:execute_api_request] - cookies值：None[0m
INFO - 2025-07-07 11:06:53,802 - sendrequests.py:69 -[sendrequests:execute_api_request] - 请求参数：{"keyword": "演", "channelCode": "*********", "token": "HIx4GcZCV9rNSVvCDHxLnTd9lqe4T+yvqwm3lMQTCY7gdc7GaMgINq64QVyeD0XxEC+AHo9ONgxjBFMyfqlPosl+78aoOuYgI9gLHmRnbRGmlT2bSdpg/kI22Kc6vOUF,*********"}
INFO - 2025-07-07 11:06:54,080 - apiutils_business.py:179 -[apiutils_business:execute_test_cases] - 接口实际返回结果：{"code":0,"msg":"","data":{"total":12,"list":[{"id":"17","name":"老无座-非大演-0227","category_id":"10004","venue_id":"8","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/d7db73c1934ce8fcb5c4003afe3522b594040.jpg","ticket_status":"3","unusual_status":"0","need_real_name":"0","first_show_time":"2025-12-31 00:00:00","last_show_time":"2025-12-31 01:00:00","on_sale_time":"2025-02-27 19:55:09","max_buy_limit_per_id":"0","max_buy_limit_per_order":"10","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"11.00","max_sale_price":"33.00","shelve_status":"1","third_shelve_status":"1","sort":"50","venue_info":{"venue_id":"8","venue_name":"梅赛德斯-奔驰文化中心","province_title":"上海市","city_title":"浦东新区","address":"世博大道1200号","latitude":"31.188952","longitude":"121.493201"},"show_info":{"show_id":"36","performance_id":"17","show_name":"2025-12-31 周三 00:00","show_start_time":"2025-12-31 00:00:00","show_end_time":"2025-12-31 01:00:00","show_type":"1","show_seat_type":"0","show_real_name_limit":"0","max_buy_limit_per_order":"10","show_unusual_status":"0","on_sale_time":"2025-02-27 19:54:03","off_sale_time":"2025-12-31 00:00:00"},"category_info":{"category_id":"10004","category_name":"话剧音乐剧"}},{"id":"36","name":"新创建2.0项目wt0402--大演1","category_id":"10001","venue_id":"13","poster_url":"https:\/\/p0.pipi.cn\/mediaplus\/fantasy_perform_fe\/0fa3343d52905119bf8457350de54ce83cc10.jpeg?imageMogr2\/quality\/80","ticket_status":"3","unusual_status":"0","need_real_name":"1","first_show_time":"2026-04-01 00:00:00","last_show_time":"2026-04-10 03:00:00","on_sale_time":null,"max_buy_limit_per_id":"6","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"25.00","max_sale_price":"123.00","shelve_status":"1","third_shelve_status":"1","sort":"50","venue_info":{"venue_id":"13","venue_name":"虹桥天地演艺中心","province_title":"上海市","city_title":"闵行区","address":"绍虹路33号","latitude":"31.19331","longitude":"121.31583"},"show_info":{"show_id":"98","performance_id":"36","show_name":"2026-04-01 周三 00:00","show_start_time":"2026-04-01 00:00:00","show_end_time":"2026-04-01 01:00:00","show_type":"1","show_seat_type":"0","show_real_name_limit":"1","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2025-04-02 17:54:44","off_sale_time":"2026-04-01 00:00:00"},"category_info":{"category_id":"10001","category_name":"演唱会"}},{"id":"37","name":"新创建2.0项目wt0402--大演","category_id":"10001","venue_id":"13","poster_url":"https:\/\/p0.pipi.cn\/mediaplus\/fantasy_perform_fe\/0fa3343d52905119bf8457350de54ce83cc10.jpeg?imageMogr2\/quality\/80","ticket_status":"3","unusual_status":"0","need_real_name":"1","first_show_time":"2026-04-01 00:00:00","last_show_time":"2026-05-03 02:00:00","on_sale_time":"2025-04-02 16:34:30","max_buy_limit_per_id":"6","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"13.00","max_sale_price":"17.00","shelve_status":"1","third_shelve_status":"1","sort":"50","venue_info":{"venue_id":"13","venue_name":"虹桥天地演艺中心","province_title":"上海市","city_title":"闵行区","address":"绍虹路33号","latitude":"31.19331","longitude":"121.31583"},"show_info":{"show_id":"108","performance_id":"37","show_name":"2026-04-01 周三 00:00","show_start_time":"2026-04-01 00:00:00","show_end_time":"2026-04-01 02:00:00","show_type":"1","show_seat_type":"0","show_real_name_limit":"1","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2025-04-02 16:33:14","off_sale_time":"2026-04-01 00:00:00"},"category_info":{"category_id":"10001","category_name":"演唱会"}},{"id":"65","name":"李胜素、于魁智联袂中国戏曲学院名家教授演绎梅派名剧《穆桂英挂帅》+丁蕾","category_id":"10003","venue_id":"25","poster_url":"https:\/\/p0.pipi.cn\/mediaplus\/fantasy_perform_fe\/0fa3345bc8b4a115d5f87bbcda03261e22dfb.jpg?imageMogr2\/quality\/80","ticket_status":"3","unusual_status":"0","need_real_name":"0","first_show_time":"2025-09-06 19:30:00","last_show_time":"2025-09-06 21:00:00","on_sale_time":"2025-05-22 18:20:54","max_buy_limit_per_id":"0","max_buy_limit_per_order":"9999","min_buy_limit_per_order":"0","max_buy_limit_per_user":"9999","min_sale_price":"198.00","max_sale_price":"968.00","shelve_status":"1","third_shelve_status":"1","sort":"50","venue_info":{"venue_id":"25","venue_name":"北京音乐厅","province_title":"北京市","city_title":"西城区","address":"北新华街1号","latitude":"39.906784","longitude":"116.382384"},"show_info":{"show_id":"219","performance_id":"65","show_name":"2025-09-06 周六 19:30","show_start_time":"2025-09-06 19:30:00","show_end_time":"2025-09-06 21:00:00","show_type":"1","show_seat_type":"0","show_real_name_limit":"1","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2025-05-22 12:00:00","off_sale_time":"2025-09-06 19:30:00"},"category_info":{"category_id":"10003","category_name":"戏曲艺术"}},{"id":"66","name":"陈奕迅《FEAR AND DREAMS世界巡回演唱会》-北京站+丁蕾","category_id":"10001","venue_id":"25","poster_url":"https:\/\/p0.pipi.cn\/mediaplus\/fantasy_perform_fe\/0fa3345bc8be328dd477e1f0196d9d6164381.png?imageMogr2\/quality\/80","ticket_status":"1","unusual_status":"0","need_real_name":"1","first_show_time":"2025-07-11 19:00:00","last_show_time":"2025-07-20 22:00:00","on_sale_time":null,"max_buy_limit_per_id":"6","max_buy_limit_per_order":"9999","min_buy_limit_per_order":"0","max_buy_limit_per_user":"24","min_sale_price":"748.00","max_sale_price":"2838.00","shelve_status":"1","third_shelve_status":"1","sort":"50","venue_info":{"venue_id":"25","venue_name":"北京音乐厅","province_title":"北京市","city_title":"西城区","address":"北新华街1号","latitude":"39.906784","longitude":"116.382384"},"show_info":{"show_id":"220","performance_id":"66","show_name":"2025-07-11 周五 19:00","show_start_time":"2025-07-11 19:00:00","show_end_time":"2025-07-11 22:00:00","show_type":"1","show_seat_type":"1","show_real_name_limit":"1","max_buy_limit_per_order":"4","show_unusual_status":"0","on_sale_time":"2025-05-22 17:53:40","off_sale_time":"2025-07-11 22:00:00"},"category_info":{"category_id":"10001","category_name":"演唱会"}},{"id":"80","name":"杨丽萍导演舞剧《春之祭》+dinglei","category_id":"10005","venue_id":"26","poster_url":"https:\/\/p0.pipi.cn\/mediaplus\/fantasy_perform_fe\/0fa33425e328d3d8a1ea40daaf1b74ca02eaa.png?imageMogr2\/quality\/80","ticket_status":"3","unusual_status":"0","need_real_name":"1","first_show_time":"2025-07-26 19:30:00","last_show_time":"2025-07-26 21:00:00","on_sale_time":"2025-06-17 14:31:23","max_buy_limit_per_id":"9999","max_buy_limit_per_order":"9999","min_buy_limit_per_order":"0","max_buy_limit_per_user":"9999","min_sale_price":"1.00","max_sale_price":"1162.00","shelve_status":"1","third_shelve_status":"1","sort":"50","venue_info":{"venue_id":"26","venue_name":"工人体育场","province_title":"北京市","city_title":"朝阳区","address":"工体北路与新东街交叉口西南角","latitude":"39.93069","longitude":"116.447211"},"show_info":{"show_id":"279","performance_id":"80","show_name":"2025-07-26 周六 19:30","show_start_time":"2025-07-26 19:30:00","show_end_time":"2025-07-26 21:00:00","show_type":"1","show_seat_type":"1","show_real_name_limit":"1","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2025-06-17 00:30:00","off_sale_time":"2025-07-26 19:30:00"},"category_info":{"category_id":"10005","category_name":"舞蹈芭蕾"}},{"id":"83","name":"次元盛典.热门二次元动漫ACG乐队番演唱会+丁蕾","category_id":"10006","venue_id":"25","poster_url":"https:\/\/p0.pipi.cn\/mediaplus\/fantasy_perform_fe\/0fa33425e3277e25ce25cbab374b995fae36f.png?imageMogr2\/quality\/80","ticket_status":"3","unusual_status":"0","need_real_name":"0","first_show_time":"2025-07-26 19:30:00","last_show_time":"2025-07-26 21:00:00","on_sale_time":"2025-06-17 13:47:18","max_buy_limit_per_id":"0","max_buy_limit_per_order":"9999","min_buy_limit_per_order":"0","max_buy_limit_per_user":"9999","min_sale_price":"1.00","max_sale_price":"1.00","shelve_status":"1","third_shelve_status":"1","sort":"50","venue_info":{"venue_id":"25","venue_name":"北京音乐厅","province_title":"北京市","city_title":"西城区","address":"北新华街1号","latitude":"39.906784","longitude":"116.382384"},"show_info":{"show_id":"283","performance_id":"83","show_name":"2025-07-26 周六 19:30","show_start_time":"2025-07-26 19:30:00","show_end_time":"2025-07-26 21:00:00","show_type":"1","show_seat_type":"1","show_real_name_limit":"1","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2025-06-17 00:30:00","off_sale_time":"2025-07-26 19:30:00"},"category_info":{"category_id":"10006","category_name":"音乐会"}},{"id":"85","name":"2025虚拟歌姬 IA 中国巡演13周年纪念版PARTY A GOGO巡游派对「轨迹与奇迹」广州站","category_id":"10017","venue_id":"25","poster_url":"https:\/\/p0.pipi.cn\/mediaplus\/fantasy_perform_fe\/0fa334254a177e14dca219726706cf7bca309.jpg?imageMogr2\/quality\/80","ticket_status":"2","unusual_status":"0","need_real_name":"1","first_show_time":"2025-08-10 20:00:00","last_show_time":"2025-08-10 21:40:00","on_sale_time":"2025-06-18 19:18:00","max_buy_limit_per_id":"1","max_buy_limit_per_order":"9999","min_buy_limit_per_order":"0","max_buy_limit_per_user":"2","min_sale_price":"418.00","max_sale_price":"968.00","shelve_status":"1","third_shelve_status":"1","sort":"50","venue_info":{"venue_id":"25","venue_name":"北京音乐厅","province_title":"北京市","city_title":"西城区","address":"北新华街1号","latitude":"39.906784","longitude":"116.382384"},"show_info":{"show_id":"285","performance_id":"85","show_name":"2025-08-10 周日 20:00","show_start_time":"2025-08-10 20:00:00","show_end_time":"2025-08-10 21:40:00","show_type":"1","show_seat_type":"0","show_real_name_limit":"1","max_buy_limit_per_order":"2","show_unusual_status":"0","on_sale_time":"2025-06-18 00:00:00","off_sale_time":"2025-08-10 20:00:00"},"category_info":{"category_id":"10017","category_name":"Livehouse"}},{"id":"88","name":"哈瓦那的偶像演唱团中国巡回演唱会（赖）","category_id":"10001","venue_id":"25","poster_url":"https:\/\/p0.pipi.cn\/mediaplus\/fantasy_perform_fe\/0fa334254a1ea46b1b374cf3266bdd1cc1245.jpg?imageMogr2\/quality\/80","ticket_status":"3","unusual_status":"0","need_real_name":"0","first_show_time":"2025-07-23 19:00:00","last_show_time":"2025-07-23 20:30:00","on_sale_time":"2025-06-18 15:34:46","max_buy_limit_per_id":"0","max_buy_limit_per_order":"9999","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"132.00","max_sale_price":"220.00","shelve_status":"1","third_shelve_status":"1","sort":"50","venue_info":{"venue_id":"25","venue_name":"北京音乐厅","province_title":"北京市","city_title":"西城区","address":"北新华街1号","latitude":"39.906784","longitude":"116.382384"},"show_info":{"show_id":"287","performance_id":"88","show_name":"2025-07-23 周三 19:00","show_start_time":"2025-07-23 19:00:00","show_end_time":"2025-07-23 20:30:00","show_type":"1","show_seat_type":"1","show_real_name_limit":"1","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2025-06-18 15:36:22","off_sale_time":"2025-07-23 19:00:00"},"category_info":{"category_id":"10001","category_name":"演唱会"}},{"id":"93","name":"有我·天使MAX成团演唱会+罗媛","category_id":"10017","venue_id":"25","poster_url":"https:\/\/p0.pipi.cn\/mediaplus\/fantasy_perform_fe\/0fa3342577ee323cbca2194cb125aba838a8d.jpg?imageMogr2\/quality\/80","ticket_status":"3","unusual_status":"0","need_real_name":"1","first_show_time":"2025-07-18 19:30:00","last_show_time":"2025-07-18 21:00:00","on_sale_time":"2025-06-19 20:00:00","max_buy_limit_per_id":"9999","max_buy_limit_per_order":"9999","min_buy_limit_per_order":"0","max_buy_limit_per_user":"9999","min_sale_price":"418.00","max_sale_price":"748.00","shelve_status":"1","third_shelve_status":"1","sort":"50","venue_info":{"venue_id":"25","venue_name":"北京音乐厅","province_title":"北京市","city_title":"西城区","address":"北新华街1号","latitude":"39.906784","longitude":"116.382384"},"show_info":{"show_id":"293","performance_id":"93","show_name":"2025-07-18 周五 19:30","show_start_time":"2025-07-18 19:30:00","show_end_time":"2025-07-18 21:00:00","show_type":"1","show_seat_type":"0","show_real_name_limit":"6","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2025-06-19 17:01:30","off_sale_time":"2025-07-18 19:30:00"},"category_info":{"category_id":"10017","category_name":"Livehouse"}}]}}
INFO - 2025-07-07 11:06:54,085 - assertion_utils.py:35 -[assertion_utils:status_code_assert] - 状态码断言成功：接口实际返回状态码 200 == 200
INFO - 2025-07-07 11:06:54,087 - assertion_utils.py:94 -[assertion_utils:equal_assert] - 相等断言成功：接口实际结果 {'code': 0} == 预期结果：{'code': 0}
INFO - 2025-07-07 11:06:54,087 - assertion_utils.py:206 -[assertion_utils:assert_result] - 测试成功
INFO - 2025-07-07 11:06:54,096 - conftest.py:9 -[conftest:print_info] - ---------------接口测试结束---------------
INFO - 2025-07-07 11:06:55,611 - conftest.py:7 -[conftest:print_info] - ---------------接口测试开始---------------
INFO - 2025-07-07 11:06:55,619 - sendrequests.py:57 -[sendrequests:execute_api_request] - 接口名称：演出项目详情
INFO - 2025-07-07 11:06:55,619 - sendrequests.py:58 -[sendrequests:execute_api_request] - 请求地址：http://*************/api/show/info/performance_info
INFO - 2025-07-07 11:06:55,619 - sendrequests.py:59 -[sendrequests:execute_api_request] - 请求方式：GET
INFO - 2025-07-07 11:06:55,619 - sendrequests.py:60 -[sendrequests:execute_api_request] - 请求头：{'Content-Type': 'application/x-www-form-urlencoded', 'Connection': 'Keep-Alive', 'Accept-Encoding': 'gzip'}
INFO - 2025-07-07 11:06:55,619 - web_service.py:517 -[web_service:execute_command_simple] - [batch_1751857597] 第130行: [32m INFO - 2025-07-07 11:06:55,619 - sendrequests.py:57 -[sendrequests:execute_api_request] - 接口名称：演出项目详情[0m
INFO - 2025-07-07 11:06:55,619 - sendrequests.py:61 -[sendrequests:execute_api_request] - 测试用例名：获取演出项目详情信息
INFO - 2025-07-07 11:06:55,619 - sendrequests.py:62 -[sendrequests:execute_api_request] - cookies值：None
INFO - 2025-07-07 11:06:55,619 - sendrequests.py:67 -[sendrequests:execute_api_request] - 参数类型：params
INFO - 2025-07-07 11:06:55,619 - sendrequests.py:69 -[sendrequests:execute_api_request] - 请求参数：{"performanceId": "26", "channelCode": "*********", "token": "HIx4GcZCV9rNSVvCDHxLnTd9lqe4T+yvqwm3lMQTCY7gdc7GaMgINq64QVyeD0XxEC+AHo9ONgxjBFMyfqlPosl+78aoOuYgI9gLHmRnbRGmlT2bSdpg/kI22Kc6vOUF,*********"}
INFO - 2025-07-07 11:06:56,141 - apiutils_business.py:179 -[apiutils_business:execute_test_cases] - 接口实际返回结果：{"code":0,"msg":"","data":{"performanceId":"26","city":{"cityId":1,"cityName":"上海"},"category":{"categoryId":1,"categoryName":"演唱会"},"venue":{"venueId":"8","name":"梅赛德斯-奔驰文化中心","address":"世博大道1200号","cityId":1,"latitude":31.188952,"longitude":121.493201},"name":"老无座-0313","posterUrl":"https:\/\/p0.meituan.net\/myvideodistribute\/d7db73c1934ce8fcb5c4003afe3522b594040.jpg","seatUrl":"","ticketNotes":"<p><strong>√演出\/活动时长<\/strong><\/p><p>以现场为准<\/p><p><br><\/p><p><strong>√最低演出曲目<\/strong><\/p><p>以现场为准<\/p><p><br><\/p><p><strong>√主要演员<\/strong><\/p><p>以现场为准<\/p><p><br><\/p><p><strong>√最低演出\/活动时长<\/strong><\/p><p>以现场为准<\/p><p><br><\/p><p><strong>√预约说明<\/strong><\/p><p>无需预约<\/p><p><br><\/p><p><strong>√儿童说明<\/strong><\/p><p>1.2米以下儿童谢绝入场，1.2米以上儿童需持票入场<\/p><p><br><\/p><p><strong>√发票说明<\/strong><\/p><p>该项目支持开具电子发票，发票由第三方主办提供，请在演出结束前通过订单详情页提交发票申请，一般演出结束后1个月左右开具，开票方名称以实际收到的发票为准。<\/p><p><br><\/p><p><strong>√异常购票<\/strong><\/p><p>为了确保广大消费者的利益，对于异常订购行为，猫眼演出有权取消相应订单并且通过系统原路退回该订单下全部票款，不予配票。异常订购行为包括但不限于：<br>（1）同一用户订购超出限购张数的订单；<br>（2）经合理判断为非真实消费者的订购行为，包括但不限于使用软件、爬虫技术等进行恶意刷票；<br>（3）通过批量相同或虚构的支付账号、收货地址、收货人、电话号码订购超出限购张数的订单。<\/p><p><br><\/p><p><strong>√禁止携带物品<\/strong><\/p><p>由于安保和版权的原因，大多数演出、展览及比赛场所禁止携带食品、饮料、专业摄录设备、打火机等物品，请您注意现场工作人员和广播的提示，予以配合。<\/p><p><br><\/p><p><strong>√付款时效提醒<\/strong><\/p><p>下单成功后需在指定时间内完成支付，未支付成功的订单，将在下单指定时间后系统自动取消，请及时刷新购票页面进行购买。<\/p><p><br><\/p><p><strong>√特殊提示<\/strong><\/p><p>因市场变化、不可抗力等客观情形以及演出票随订随售的性质，可能会出现演出变更、取消、或正式出票时票品库存不足等情况，该等情形下猫眼客服会及时与您联系并尽快退款。<\/p><p><br><\/p><p><strong>√退换政策<\/strong><\/p><p>退款<\/p><p><br><\/p><p><strong>√实名制购票<\/strong><\/p><p>无需实名制购票<\/p><p><br><\/p><p><strong>√限购说明<\/strong><\/p><p>每笔订单最多购买6张，以实际购票情况为准<\/p><p><br><\/p><p><strong>√入场规则<\/strong><\/p><p>·电子票：购买电子票的用户，可凭二维码直接入场，无需纸质门票<br><\/p><p><br><\/p>","detail":"<p>11111<\/p>","celebrityList":[],"ticketStatus":3,"shelveStatus":1,"needRealName":false,"firstShowTime":"2025-08-30 20:00:00","lastShowTime":"2025-12-01 02:00:00","sort":50,"createTime":"2025-03-13 14:19:59","onSaleTime":"","openBuyProjectLimit":{"maxBuyLimitPerId":0,"maxBuyLimitPerOrder":6,"minBuyLimitPerOrder":0,"maxBuyLimitPerUser":20},"exclusiveSales":0,"soleAgent":0,"seatType":0,"unusualStatus":0,"performanceDetailUrl":null,"minPrice":"4.00","maxPrice":"55.00","seatMode":0,"fetchTicketWayList":["电子票"],"idTypeList":null,"minDiscount":null,"payExpireTime":15,"priceRangeType":0,"conditionRefund":1,"isNewMode":0,"showInfo":[]}}
INFO - 2025-07-07 11:06:56,144 - assertion_utils.py:35 -[assertion_utils:status_code_assert] - 状态码断言成功：接口实际返回状态码 200 == 200
INFO - 2025-07-07 11:06:56,145 - assertion_utils.py:94 -[assertion_utils:equal_assert] - 相等断言成功：接口实际结果 {'code': 0} == 预期结果：{'code': 0}
INFO - 2025-07-07 11:06:56,146 - web_service.py:517 -[web_service:execute_command_simple] - [batch_1751857597] 第140行: [32m INFO - 2025-07-07 11:06:56,145 - assertion_utils.py:94 -[assertion_utils:equal_assert] - 相等断言成功：接口实际结果 {'code': 0} == 预期结果：{'code': 0}[0m
INFO - 2025-07-07 11:06:56,146 - assertion_utils.py:206 -[assertion_utils:assert_result] - 测试成功
INFO - 2025-07-07 11:06:56,156 - conftest.py:9 -[conftest:print_info] - ---------------接口测试结束---------------
INFO - 2025-07-07 11:06:57,670 - conftest.py:7 -[conftest:print_info] - ---------------接口测试开始---------------
INFO - 2025-07-07 11:06:57,677 - sendrequests.py:57 -[sendrequests:execute_api_request] - 接口名称：演出场次列表
INFO - 2025-07-07 11:06:57,677 - sendrequests.py:58 -[sendrequests:execute_api_request] - 请求地址：http://*************/api/show/info/performance_shows
INFO - 2025-07-07 11:06:57,677 - sendrequests.py:59 -[sendrequests:execute_api_request] - 请求方式：GET
INFO - 2025-07-07 11:06:57,677 - sendrequests.py:60 -[sendrequests:execute_api_request] - 请求头：{'Content-Type': 'application/x-www-form-urlencoded', 'Connection': 'Keep-Alive', 'Accept-Encoding': 'gzip'}
INFO - 2025-07-07 11:06:57,677 - sendrequests.py:61 -[sendrequests:execute_api_request] - 测试用例名：获取演出场次列表信息
INFO - 2025-07-07 11:06:57,677 - sendrequests.py:62 -[sendrequests:execute_api_request] - cookies值：None
INFO - 2025-07-07 11:06:57,677 - sendrequests.py:67 -[sendrequests:execute_api_request] - 参数类型：params
INFO - 2025-07-07 11:06:57,677 - sendrequests.py:69 -[sendrequests:execute_api_request] - 请求参数：{"performanceId": "26", "channelCode": "*********", "token": "HIx4GcZCV9rNSVvCDHxLnTd9lqe4T+yvqwm3lMQTCY7gdc7GaMgINq64QVyeD0XxEC+AHo9ONgxjBFMyfqlPosl+78aoOuYgI9gLHmRnbRGmlT2bSdpg/kI22Kc6vOUF,*********"}
INFO - 2025-07-07 11:06:57,677 - web_service.py:517 -[web_service:execute_command_simple] - [batch_1751857597] 第150行: [32m INFO - 2025-07-07 11:06:57,677 - sendrequests.py:62 -[sendrequests:execute_api_request] - cookies值：None[0m
INFO - 2025-07-07 11:06:58,332 - apiutils_business.py:179 -[apiutils_business:execute_test_cases] - 接口实际返回结果：{"code":0,"msg":"","data":{"shows":[{"showId":"217","performanceId":"26","name":"2025-08-30 周六 20:00","showNote":"","startTime":"2025-08-30 20:00:00","endTime":"2025-08-30 22:00:00","showType":1,"showSeatType":0,"areaSvg":null,"areaUrl":null,"showUnusualStatus":0,"showRealNameLimit":null,"maxBuyLimitPerUser":null,"maxBuyLimitPerOrder":6,"onSaleTime":"2025-05-20 16:46:26","offSaleTime":"2025-08-30 20:00:00","saleable":1},{"showId":"48","performanceId":"26","name":"2025-12-01 周一 00:00","showNote":"","startTime":"2025-12-01 00:00:00","endTime":"2025-12-01 02:00:00","showType":1,"showSeatType":0,"areaSvg":null,"areaUrl":null,"showUnusualStatus":0,"showRealNameLimit":null,"maxBuyLimitPerUser":null,"maxBuyLimitPerOrder":6,"onSaleTime":"2025-03-13 14:21:40","offSaleTime":"2025-12-01 00:00:00","saleable":1}]}}
INFO - 2025-07-07 11:06:58,335 - assertion_utils.py:35 -[assertion_utils:status_code_assert] - 状态码断言成功：接口实际返回状态码 200 == 200
INFO - 2025-07-07 11:06:58,336 - assertion_utils.py:94 -[assertion_utils:equal_assert] - 相等断言成功：接口实际结果 {'code': 0} == 预期结果：{'code': 0}
INFO - 2025-07-07 11:06:58,336 - assertion_utils.py:206 -[assertion_utils:assert_result] - 测试成功
INFO - 2025-07-07 11:06:58,348 - conftest.py:9 -[conftest:print_info] - ---------------接口测试结束---------------
INFO - 2025-07-07 11:06:59,864 - conftest.py:7 -[conftest:print_info] - ---------------接口测试开始---------------
INFO - 2025-07-07 11:06:59,872 - sendrequests.py:57 -[sendrequests:execute_api_request] - 接口名称：项目下场次票品列表
INFO - 2025-07-07 11:06:59,872 - sendrequests.py:58 -[sendrequests:execute_api_request] - 请求地址：http://*************/api/show/info/show_ticket_units
INFO - 2025-07-07 11:06:59,872 - web_service.py:517 -[web_service:execute_command_simple] - [batch_1751857597] 第160行: [32m INFO - 2025-07-07 11:06:59,872 - sendrequests.py:57 -[sendrequests:execute_api_request] - 接口名称：项目下场次票品列表[0m
INFO - 2025-07-07 11:06:59,872 - sendrequests.py:59 -[sendrequests:execute_api_request] - 请求方式：GET
INFO - 2025-07-07 11:06:59,872 - sendrequests.py:60 -[sendrequests:execute_api_request] - 请求头：{'Content-Type': 'application/x-www-form-urlencoded', 'Connection': 'Keep-Alive', 'Accept-Encoding': 'gzip'}
INFO - 2025-07-07 11:06:59,872 - sendrequests.py:61 -[sendrequests:execute_api_request] - 测试用例名：获取项目下场次票品列表信息
INFO - 2025-07-07 11:06:59,872 - sendrequests.py:62 -[sendrequests:execute_api_request] - cookies值：None
INFO - 2025-07-07 11:06:59,872 - sendrequests.py:67 -[sendrequests:execute_api_request] - 参数类型：params
INFO - 2025-07-07 11:06:59,872 - sendrequests.py:69 -[sendrequests:execute_api_request] - 请求参数：{"showId": "48", "channelCode": "*********", "token": "HIx4GcZCV9rNSVvCDHxLnTd9lqe4T+yvqwm3lMQTCY7gdc7GaMgINq64QVyeD0XxEC+AHo9ONgxjBFMyfqlPosl+78aoOuYgI9gLHmRnbRGmlT2bSdpg/kI22Kc6vOUF,*********"}
INFO - 2025-07-07 11:07:00,397 - apiutils_business.py:179 -[apiutils_business:execute_test_cases] - 接口实际返回结果：{"code":0,"msg":"","data":{"ticketUnits":[{"ticketUnitId":"214","showId":"48","name":null,"ticketName":"","minBuyLimit":1,"maxBuyLimit":6,"needRealName":false,"realNameLimit":0,"ticketLevel":"VVIP","rgb":"#FF4242","ticketPrice":"10","setNumber":1,"sellPriceList":[11,22,33,44,55,66,77,88,99,110,121,132,143,154,165,176,187,198,209,220],"limited":true,"sellStatus":3,"currentAmount":20,"baseTicketUnitId":null,"thirdTicketUnitId":16905302,"thirdBaseTicketUnitId":0},{"ticketUnitId":"215","showId":"48","name":null,"ticketName":"","minBuyLimit":1,"maxBuyLimit":6,"needRealName":false,"realNameLimit":0,"ticketLevel":"VIP","rgb":"#FF6FE4","ticketPrice":"20","setNumber":1,"sellPriceList":[22,44,66,88,110,132,154,176,198,220,242,264,286,308,330,352,374,396,418,440],"limited":true,"sellStatus":3,"currentAmount":20,"baseTicketUnitId":null,"thirdTicketUnitId":16905303,"thirdBaseTicketUnitId":0},{"ticketUnitId":"216","showId":"48","name":null,"ticketName":"","minBuyLimit":1,"maxBuyLimit":6,"needRealName":false,"realNameLimit":0,"ticketLevel":"A","rgb":"#FF8014","ticketPrice":"30","setNumber":1,"sellPriceList":[33,66,99,132,165,198,231,264,297,330,363,396,429,462,495,528,561,594,627,660],"limited":true,"sellStatus":3,"currentAmount":20,"baseTicketUnitId":null,"thirdTicketUnitId":16905304,"thirdBaseTicketUnitId":0},{"ticketUnitId":"217","showId":"48","name":null,"ticketName":"","minBuyLimit":1,"maxBuyLimit":6,"needRealName":false,"realNameLimit":0,"ticketLevel":"B","rgb":"#FFD70E","ticketPrice":"40","setNumber":1,"sellPriceList":[44,88,132,176,220,264,308,352,396,440,484,528,572,616,660,704,748,792,836,880],"limited":true,"sellStatus":3,"currentAmount":20,"baseTicketUnitId":null,"thirdTicketUnitId":16905305,"thirdBaseTicketUnitId":0},{"ticketUnitId":"218","showId":"48","name":null,"ticketName":"","minBuyLimit":1,"maxBuyLimit":6,"needRealName":false,"realNameLimit":0,"ticketLevel":"C","rgb":"#5A84FF","ticketPrice":"50","setNumber":1,"sellPriceList":[55,110,165,220,275,330,385,440,495,550,605,660,715,770,825,880,935,990,1045,1100],"limited":true,"sellStatus":3,"currentAmount":20,"baseTicketUnitId":null,"thirdTicketUnitId":16905306,"thirdBaseTicketUnitId":0},{"ticketUnitId":"219","showId":"48","name":"","ticketName":"","minBuyLimit":1,"maxBuyLimit":3,"needRealName":false,"realNameLimit":0,"ticketLevel":"VVIP","rgb":"#FF4242","ticketPrice":"10","setNumber":2,"sellPriceList":[20,40,60,80,99,119,139,159,179,198,218,238,258,278,297,317,337,357,377,396],"limited":true,"sellStatus":3,"currentAmount":20,"baseTicketUnitId":"214","thirdTicketUnitId":16905478,"thirdBaseTicketUnitId":16905302}]}}
INFO - 2025-07-07 11:07:00,400 - assertion_utils.py:35 -[assertion_utils:status_code_assert] - 状态码断言成功：接口实际返回状态码 200 == 200
INFO - 2025-07-07 11:07:00,401 - assertion_utils.py:94 -[assertion_utils:equal_assert] - 相等断言成功：接口实际结果 {'code': 0} == 预期结果：{'code': 0}
INFO - 2025-07-07 11:07:00,401 - web_service.py:517 -[web_service:execute_command_simple] - [batch_1751857597] 第170行: [32m INFO - 2025-07-07 11:07:00,401 - assertion_utils.py:94 -[assertion_utils:equal_assert] - 相等断言成功：接口实际结果 {'code': 0} == 预期结果：{'code': 0}[0m
INFO - 2025-07-07 11:07:00,402 - assertion_utils.py:206 -[assertion_utils:assert_result] - 测试成功
INFO - 2025-07-07 11:07:00,412 - conftest.py:9 -[conftest:print_info] - ---------------接口测试结束---------------
INFO - 2025-07-07 11:07:01,926 - conftest.py:7 -[conftest:print_info] - ---------------接口测试开始---------------
INFO - 2025-07-07 11:07:01,935 - sendrequests.py:57 -[sendrequests:execute_api_request] - 接口名称：根据场次获取取票方式
INFO - 2025-07-07 11:07:01,935 - sendrequests.py:58 -[sendrequests:execute_api_request] - 请求地址：http://*************/api/show/info/show_fetch_ticket_ways
INFO - 2025-07-07 11:07:01,935 - sendrequests.py:59 -[sendrequests:execute_api_request] - 请求方式：GET
INFO - 2025-07-07 11:07:01,935 - sendrequests.py:60 -[sendrequests:execute_api_request] - 请求头：{'Content-Type': 'application/x-www-form-urlencoded', 'Connection': 'Keep-Alive', 'Accept-Encoding': 'gzip'}
INFO - 2025-07-07 11:07:01,935 - sendrequests.py:61 -[sendrequests:execute_api_request] - 测试用例名：获取该场次的取票方式
INFO - 2025-07-07 11:07:01,936 - sendrequests.py:62 -[sendrequests:execute_api_request] - cookies值：None
INFO - 2025-07-07 11:07:01,936 - sendrequests.py:67 -[sendrequests:execute_api_request] - 参数类型：params
INFO - 2025-07-07 11:07:01,936 - web_service.py:517 -[web_service:execute_command_simple] - [batch_1751857597] 第180行: [32m INFO - 2025-07-07 11:07:01,936 - sendrequests.py:62 -[sendrequests:execute_api_request] - cookies值：None[0m
INFO - 2025-07-07 11:07:01,936 - sendrequests.py:69 -[sendrequests:execute_api_request] - 请求参数：{"showId": "48", "channelCode": "*********", "token": "HIx4GcZCV9rNSVvCDHxLnTd9lqe4T+yvqwm3lMQTCY7gdc7GaMgINq64QVyeD0XxEC+AHo9ONgxjBFMyfqlPosl+78aoOuYgI9gLHmRnbRGmlT2bSdpg/kI22Kc6vOUF,*********"}
INFO - 2025-07-07 11:07:02,378 - apiutils_business.py:179 -[apiutils_business:execute_test_cases] - 接口实际返回结果：{"code":0,"msg":"","data":{"fetchTicketWays":[{"fetchTicketWayId":4386733,"showId":"48","fetchType":5,"needIdCard":false,"tips":"","postage":"0","onTime":"2025-03-13 14:21:40","offTime":"2025-12-01 00:00:00","fetchAddress":null,"contactMobile":null,"fetchTime":null,"feePayType":0,"feeType":0}]}}
INFO - 2025-07-07 11:07:02,380 - assertion_utils.py:35 -[assertion_utils:status_code_assert] - 状态码断言成功：接口实际返回状态码 200 == 200
INFO - 2025-07-07 11:07:02,381 - assertion_utils.py:94 -[assertion_utils:equal_assert] - 相等断言成功：接口实际结果 {'code': 0} == 预期结果：{'code': 0}
INFO - 2025-07-07 11:07:02,381 - assertion_utils.py:206 -[assertion_utils:assert_result] - 测试成功
INFO - 2025-07-07 11:07:02,400 - conftest.py:9 -[conftest:print_info] - ---------------接口测试结束---------------
INFO - 2025-07-07 11:07:03,914 - conftest.py:7 -[conftest:print_info] - ---------------接口测试开始---------------
INFO - 2025-07-07 11:07:03,937 - sendrequests.py:57 -[sendrequests:execute_api_request] - 接口名称：下单
INFO - 2025-07-07 11:07:03,937 - sendrequests.py:58 -[sendrequests:execute_api_request] - 请求地址：http://*************/api/show/order/create
INFO - 2025-07-07 11:07:03,937 - web_service.py:517 -[web_service:execute_command_simple] - [batch_1751857597] 第190行: [32m INFO - 2025-07-07 11:07:03,937 - sendrequests.py:57 -[sendrequests:execute_api_request] - 接口名称：下单[0m
INFO - 2025-07-07 11:07:03,937 - sendrequests.py:59 -[sendrequests:execute_api_request] - 请求方式：POST
INFO - 2025-07-07 11:07:03,937 - sendrequests.py:60 -[sendrequests:execute_api_request] - 请求头：{'Content-Type': 'application/x-www-form-urlencoded', 'Connection': 'Keep-Alive', 'Accept-Encoding': 'gzip'}
INFO - 2025-07-07 11:07:03,937 - sendrequests.py:61 -[sendrequests:execute_api_request] - 测试用例名：创建订单
INFO - 2025-07-07 11:07:03,937 - sendrequests.py:62 -[sendrequests:execute_api_request] - cookies值：None
INFO - 2025-07-07 11:07:03,937 - sendrequests.py:67 -[sendrequests:execute_api_request] - 参数类型：data
INFO - 2025-07-07 11:07:03,937 - sendrequests.py:69 -[sendrequests:execute_api_request] - 请求参数：{"recipientMobile": "19877292898", "recipientName": "购票人", "recipientIdNo": "", "fetchTicketWayId": 4386733, "recipientAddressId": "", "fetchTicketWayType": 5, "performanceId": "26", "showId": "48", "salesPlanId": "214", "salesPlanCount": "1", "totalTicketPrice": 11, "deliveryPrice": "0", "orderPrice": 11, "realNameIds": "", "seatRequest": "", "channelCode": "*********", "merchant_code": "", "token": "HIx4GcZCV9rNSVvCDHxLnTd9lqe4T+yvqwm3lMQTCY7gdc7GaMgINq64QVyeD0XxEC+AHo9ONgxjBFMyfqlPosl+78aoOuYgI9gLHmRnbRGmlT2bSdpg/kI22Kc6vOUF,*********"}
INFO - 2025-07-07 11:07:05,332 - apiutils_business.py:179 -[apiutils_business:execute_test_cases] - 接口实际返回结果：{"code":0,"msg":"","data":{"orderId":"2025070711072766223"}}
INFO - 2025-07-07 11:07:05,337 - assertion_utils.py:35 -[assertion_utils:status_code_assert] - 状态码断言成功：接口实际返回状态码 200 == 200
INFO - 2025-07-07 11:07:05,338 - assertion_utils.py:94 -[assertion_utils:equal_assert] - 相等断言成功：接口实际结果 {'code': 0} == 预期结果：{'code': 0}
INFO - 2025-07-07 11:07:05,338 - web_service.py:517 -[web_service:execute_command_simple] - [batch_1751857597] 第200行: [32m INFO - 2025-07-07 11:07:05,338 - assertion_utils.py:94 -[assertion_utils:equal_assert] - 相等断言成功：接口实际结果 {'code': 0} == 预期结果：{'code': 0}[0m
INFO - 2025-07-07 11:07:05,339 - assertion_utils.py:206 -[assertion_utils:assert_result] - 测试成功
INFO - 2025-07-07 11:07:05,352 - conftest.py:9 -[conftest:print_info] - ---------------接口测试结束---------------
INFO - 2025-07-07 11:07:06,864 - conftest.py:7 -[conftest:print_info] - ---------------接口测试开始---------------
INFO - 2025-07-07 11:07:06,871 - sendrequests.py:57 -[sendrequests:execute_api_request] - 接口名称：取消订单
INFO - 2025-07-07 11:07:06,872 - sendrequests.py:58 -[sendrequests:execute_api_request] - 请求地址：http://*************/api/show/order/cancel
INFO - 2025-07-07 11:07:06,872 - sendrequests.py:59 -[sendrequests:execute_api_request] - 请求方式：POST
INFO - 2025-07-07 11:07:06,872 - sendrequests.py:60 -[sendrequests:execute_api_request] - 请求头：{'Content-Type': 'application/x-www-form-urlencoded', 'Connection': 'Keep-Alive', 'Accept-Encoding': 'gzip'}
INFO - 2025-07-07 11:07:06,872 - sendrequests.py:61 -[sendrequests:execute_api_request] - 测试用例名：取消订单
INFO - 2025-07-07 11:07:06,872 - sendrequests.py:62 -[sendrequests:execute_api_request] - cookies值：None
INFO - 2025-07-07 11:07:06,872 - sendrequests.py:67 -[sendrequests:execute_api_request] - 参数类型：data
INFO - 2025-07-07 11:07:06,872 - web_service.py:517 -[web_service:execute_command_simple] - [batch_1751857597] 第210行: [32m INFO - 2025-07-07 11:07:06,872 - sendrequests.py:62 -[sendrequests:execute_api_request] - cookies值：None[0m
INFO - 2025-07-07 11:07:06,872 - sendrequests.py:69 -[sendrequests:execute_api_request] - 请求参数：{"orderId": "2025070711072766223", "channelCode": "*********", "token": "HIx4GcZCV9rNSVvCDHxLnTd9lqe4T+yvqwm3lMQTCY7gdc7GaMgINq64QVyeD0XxEC+AHo9ONgxjBFMyfqlPosl+78aoOuYgI9gLHmRnbRGmlT2bSdpg/kI22Kc6vOUF,*********"}
INFO - 2025-07-07 11:07:08,128 - apiutils_business.py:179 -[apiutils_business:execute_test_cases] - 接口实际返回结果：{"code":0,"msg":"","data":[]}
INFO - 2025-07-07 11:07:08,131 - assertion_utils.py:35 -[assertion_utils:status_code_assert] - 状态码断言成功：接口实际返回状态码 200 == 200
INFO - 2025-07-07 11:07:08,132 - assertion_utils.py:94 -[assertion_utils:equal_assert] - 相等断言成功：接口实际结果 {'code': 0} == 预期结果：{'code': 0}
INFO - 2025-07-07 11:07:08,132 - assertion_utils.py:206 -[assertion_utils:assert_result] - 测试成功
INFO - 2025-07-07 11:07:08,142 - conftest.py:9 -[conftest:print_info] - ---------------接口测试结束---------------
INFO - 2025-07-07 11:07:09,658 - conftest.py:7 -[conftest:print_info] - ---------------接口测试开始---------------
INFO - 2025-07-07 11:07:09,660 - sendrequests.py:57 -[sendrequests:execute_api_request] - 接口名称：查询影讯接口
INFO - 2025-07-07 11:07:09,660 - sendrequests.py:58 -[sendrequests:execute_api_request] - 请求地址：http://*************:40/proxy/cnews/article/list
INFO - 2025-07-07 11:07:09,660 - web_service.py:517 -[web_service:execute_command_simple] - [batch_1751857597] 第220行: [32m INFO - 2025-07-07 11:07:09,660 - sendrequests.py:57 -[sendrequests:execute_api_request] - 接口名称：查询影讯接口[0m
INFO - 2025-07-07 11:07:09,660 - sendrequests.py:59 -[sendrequests:execute_api_request] - 请求方式：POST
INFO - 2025-07-07 11:07:09,661 - sendrequests.py:60 -[sendrequests:execute_api_request] - 请求头：None
INFO - 2025-07-07 11:07:09,661 - sendrequests.py:61 -[sendrequests:execute_api_request] - 测试用例名：查询上架状态的影讯
INFO - 2025-07-07 11:07:09,661 - sendrequests.py:62 -[sendrequests:execute_api_request] - cookies值：None
INFO - 2025-07-07 11:07:09,661 - sendrequests.py:67 -[sendrequests:execute_api_request] - 参数类型：data
INFO - 2025-07-07 11:07:09,661 - sendrequests.py:69 -[sendrequests:execute_api_request] - 请求参数："pagination=%7B%22pageNumber%22%3A1%2C%22pageSize%22%3A50%2C%22queryParams%22%3A%7B%22title%22%3A%22%22%2C%22source%22%3A%22%22%2C%22tag_id%22%3A%22%22%2C%22status%22%3A%221%22%2C%22istop%22%3A%22%22%2C%22film_name%22%3A%22%22%2C%22startintime%22%3A%22%22%2C%22endinttime%22%3A%22%22%2C%22showtype%22%3A%22%22%2C%22startshowtime%22%3A%22%22%2C%22endshowttime%22%3A%22%22%2C%22min_program_video_status%22%3A%22%22%2C%22isplat%22%3A0%7D%7D"
INFO - 2025-07-07 11:07:09,885 - apiutils_business.py:179 -[apiutils_business:execute_test_cases] - 接口实际返回结果：{"code":0,"msg":"操作成功","data":{"totalCount":"111","records":[{"id":292,"title":"看一下资讯缩进1","author":"11","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":579,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2023\/09\/18\/6507a64bd42b1\/o.png"},"showtime":"2023-09-18 09:21:00","uptime":"2025-06-27 14:42:31","intime":"2023-09-18 09:22:24","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":1,"ui_type":"文章","type":0,"min_program_video_status":0},{"id":291,"title":"看一下文章缩进","author":"测试","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":578,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2023\/09\/18\/6507a5c183a72\/o.jpg"},"showtime":"2023-09-18 09:19:00","uptime":"2025-06-27 14:41:44","intime":"2023-09-18 09:20:11","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":2,"ui_type":"文章","type":0,"min_program_video_status":0},{"id":299,"title":"NS2","author":"百度","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":591,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2025\/06\/19\/6853c4adeca12\/o.jpg"},"showtime":"2025-06-19 16:04:00","uptime":"2025-06-19 16:05:20","intime":"2025-06-19 16:05:20","status":1,"ui_status":"上架","film_code":"001X02212020","film_name":"图兰朵：魔咒缘起","isplat":0,"showtype":1,"ui_type":"文章","type":0,"min_program_video_status":0},{"id":277,"title":"测试通看播放按钮不显示","author":"测试12121","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":588,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2025\/04\/11\/67f8e2be2112b\/o.jpg"},"showtime":"2022-07-29 09:45:00","uptime":"2025-04-11 17:47:32","intime":"2022-07-29 09:47:49","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":2,"ui_type":"小视频","type":1,"min_program_video_status":0},{"id":276,"title":"文章类","author":"luowt","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":589,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2025\/04\/11\/67f8e4a211f3d\/o.jpg"},"showtime":"2022-07-29 09:45:00","uptime":"2025-04-11 17:45:06","intime":"2022-07-29 09:46:15","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":1,"ui_type":"文章","type":0,"min_program_video_status":0},{"id":295,"title":"测试云点播上传","author":"liean","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":583,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2024\/04\/24\/66286143f28be\/o.jpg"},"showtime":"2024-04-24 09:30:00","uptime":"2024-10-22 10:58:25","intime":"2024-04-24 09:33:14","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":1,"ui_type":"小视频","type":1,"min_program_video_status":1},{"id":248,"title":"华纳发布新片片花混剪","author":"中国电影通1","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":525,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2022\/07\/18\/62d5061ab8754\/o.png"},"showtime":"2021-01-28 10:17:00","uptime":"2024-01-25 14:43:59","intime":"2021-01-28 10:19:58","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":1,"ui_type":"小视频","type":1,"min_program_video_status":0},{"id":294,"title":"智桐反馈资讯问题","author":"测试","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":582,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2023\/09\/18\/6507a7cb1e6fb\/o.gif"},"showtime":"2023-09-18 09:27:00","uptime":"2023-11-24 15:39:09","intime":"2023-09-18 09:28:44","status":1,"ui_status":"上架","film_code":"001X04052021","film_name":"金手指","isplat":0,"showtype":2,"ui_type":"文章","type":0,"min_program_video_status":0},{"id":293,"title":"智桐周末反馈问题1","author":"测试","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":580,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2023\/09\/18\/6507a6ebdec5a\/o.png"},"showtime":"2023-09-18 09:24:00","uptime":"2023-09-18 09:27:40","intime":"2023-09-18 09:25:08","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":2,"ui_type":"文章","type":0,"min_program_video_status":0},{"id":260,"title":"type=2","author":"dd","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":540,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2022\/07\/18\/62d530a7801eb\/o.png"},"showtime":"2021-04-25 10:58:00","uptime":"2023-09-18 09:18:32","intime":"2021-04-25 10:52:29","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":2,"ui_type":"文章","type":0,"min_program_video_status":0},{"id":287,"title":"写潘多拉传奇！","author":"中影电影通","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":574,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2022\/11\/25\/638066dde28eb\/o.png"},"showtime":"2022-11-25 14:54:00","uptime":"2022-11-30 11:23:04","intime":"2022-11-25 14:55:28","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":2,"ui_type":"小视频","type":1,"min_program_video_status":1},{"id":288,"title":"主题曲《无名英雄》MV，由周笔畅深情献声致敬生活中默默无闻的平凡人。凡人微光，星火成炬！","author":"中国电影","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":575,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2022\/11\/25\/638067df3606f\/o.jpeg"},"showtime":"2022-11-25 14:59:00","uptime":"2022-11-25 14:59:46","intime":"2022-11-25 14:59:46","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":2,"ui_type":"小视频","type":1,"min_program_video_status":1},{"id":285,"title":"七十一岁的老鱼塘淤泥太深 不过今天挖机没陷车 确把公鸡陷得很深","author":"@棒哥带你开挖机确把公鸡陷得很","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":572,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2022\/11\/25\/6380634e3f558\/o.png"},"showtime":"2022-11-25 14:39:00","uptime":"2022-11-25 14:41:54","intime":"2022-11-25 14:40:20","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":2,"ui_type":"小视频","type":1,"min_program_video_status":0},{"id":284,"title":"你可以怀疑","author":"@one_live","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":571,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2022\/11\/25\/63805fe81244f\/o.png"},"showtime":"2022-11-25 14:24:00","uptime":"2022-11-25 14:26:42","intime":"2022-11-25 14:25:59","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":2,"ui_type":"小视频","type":1,"min_program_video_status":0},{"id":283,"title":"高清重温：邓卓翔世界波，国足破32年恐韩症！","author":"@尚足球","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":570,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2022\/11\/25\/6380583ac513b\/o.png"},"showtime":"2022-11-25 13:52:00","uptime":"2022-11-25 13:54:11","intime":"2022-11-25 13:53:03","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":2,"ui_type":"小视频","type":1,"min_program_video_status":0},{"id":282,"title":"李云龙成功伏击鬼子观摩团，全歼200名鬼子军官!","author":"@特务兔说剧","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":569,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2022\/11\/25\/63803b694352f\/o.png"},"showtime":"2022-11-25 11:49:00","uptime":"2022-11-25 11:50:07","intime":"2022-11-25 11:50:07","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":2,"ui_type":"小视频","type":1,"min_program_video_status":0},{"id":281,"title":"跌宕起伏！C罗点射创纪录！葡萄牙3比2加纳取开门红","author":"@小鱼人足球","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":568,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2022\/11\/25\/63803a7390a4b\/o.jpg"},"showtime":"2022-11-24 11:45:00","uptime":"2022-11-25 11:47:03","intime":"2022-11-25 11:47:03","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":2,"ui_type":"小视频","type":1,"min_program_video_status":0},{"id":280,"title":"test-logo","author":"seif","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":567,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2022\/08\/16\/62fb3000c40df\/o.jpeg"},"showtime":"2022-08-16 13:46:00","uptime":"2022-08-16 13:50:34","intime":"2022-08-16 13:50:34","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":1,"ui_type":"小视频","type":1,"min_program_video_status":1},{"id":278,"title":"视频","author":"luowt","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":565,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2022\/07\/29\/62e33ea6b2740\/o.jpg"},"showtime":"2022-07-29 09:57:00","uptime":"2022-08-16 13:45:59","intime":"2022-07-29 09:58:20","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":1,"ui_type":"小视频","type":1,"min_program_video_status":1},{"id":279,"title":"GIF","author":"luowt","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":566,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2022\/07\/29\/62e33fca0b1a5\/o.gif"},"showtime":"2022-07-29 10:01:00","uptime":"2022-07-29 10:02:56","intime":"2022-07-29 10:02:56","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":1,"ui_type":"小视频","type":1,"min_program_video_status":0},{"id":247,"title":"测试数据","author":"测试数据","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":561,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2022\/07\/28\/62e2077f83eba\/o.png"},"showtime":"2021-01-28 09:56:00","uptime":"2022-07-28 11:50:24","intime":"2021-01-28 09:56:50","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":1,"ui_type":"小视频","type":1,"min_program_video_status":0},{"id":245,"title":"压缩测试","author":"aa","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":560,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2022\/07\/28\/62e2075cabcb2\/o.png"},"showtime":"2021-01-27 18:42:00","uptime":"2022-07-28 11:49:49","intime":"2021-01-27 18:44:05","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":2,"ui_type":"小视频","type":1,"min_program_video_status":0},{"id":271,"title":"深圳晚霞","author":"luowt","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":559,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2022\/07\/28\/62e206e09f4d3\/o.png"},"showtime":"2022-07-15 15:14:00","uptime":"2022-07-28 11:47:45","intime":"2022-07-15 15:18:27","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":1,"ui_type":"小视频","type":1,"min_program_video_status":0},{"id":261,"title":"辣妹又辣了辣妹又辣了辣妹又辣了辣妹又辣了辣妹又辣了辣妹又辣了辣妹又辣了辣妹又辣了辣妹又辣了","author":"luowt","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":548,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2022\/07\/20\/62d79aeca8720\/o.jpg"},"showtime":"2021-07-07 17:01:00","uptime":"2022-07-22 09:54:53","intime":"2021-07-07 17:02:32","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":3,"ui_type":"小视频","type":1,"min_program_video_status":0},{"id":235,"title":"《疯狂原始人2》发布中文配音版预告","author":"中影电影通","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":539,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2022\/07\/18\/62d51fe1c5c6d\/o.png"},"showtime":"2020-11-13 13:50:00","uptime":"2022-07-18 18:07:31","intime":"2020-11-13 13:55:06","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":1,"ui_type":"文章","type":0,"min_program_video_status":0},{"id":236,"title":"《疯狂原始人2》发布中文配音版预告","author":"中影电影通","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":537,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2022\/07\/18\/62d506b230614\/o.png"},"showtime":"2020-11-13 13:55:00","uptime":"2022-07-18 15:07:31","intime":"2020-11-13 13:56:15","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":1,"ui_type":"小视频","type":1,"min_program_video_status":0},{"id":237,"title":"《古董局中局》曝光先导预告","author":"中影电影通","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":536,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2022\/07\/18\/62d5068c1bd56\/o.png"},"showtime":"2020-11-23 13:36:00","uptime":"2022-07-18 15:06:57","intime":"2020-11-23 13:39:28","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":1,"ui_type":"小视频","type":1,"min_program_video_status":0},{"id":238,"title":"《外太空的莫扎特》曝光概念海报","author":"中影电影通","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":535,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2022\/07\/18\/62d5068212d25\/o.png"},"showtime":"2020-11-23 13:39:00","uptime":"2022-07-18 15:06:42","intime":"2020-11-23 13:40:50","status":1,"ui_status":"上架","film_code":"001X03752019","film_name":"征途","isplat":0,"showtype":2,"ui_type":"小视频","type":1,"min_program_video_status":0},{"id":239,"title":"范娟测试1123-1","author":"范娟","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":534,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2022\/07\/18\/62d5067691601\/o.png"},"showtime":"2020-11-23 15:46:00","uptime":"2022-07-18 15:06:31","intime":"2020-11-23 15:44:03","status":1,"ui_status":"上架","film_code":"001X02042019","film_name":"悟空奇遇记","isplat":0,"showtype":1,"ui_type":"小视频","type":1,"min_program_video_status":0},{"id":240,"title":"范娟测试1126-1","author":"范娟","tag_title":"test","tag_id":17,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":533,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2022\/07\/18\/62d506669b96f\/o.png"},"showtime":"2020-11-26 11:44:00","uptime":"2022-07-18 15:06:15","intime":"2020-11-26 11:44:50","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":2,"ui_type":"小视频","type":1,"min_program_video_status":0},{"id":242,"title":"fj-12.10短视频","author":"范娟","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":532,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2022\/07\/18\/62d50657a543c\/o.png"},"showtime":"2020-12-10 14:30:00","uptime":"2022-07-18 15:06:00","intime":"2020-12-10 14:27:56","status":1,"ui_status":"上架","film_code":"001X05262020","film_name":"送你一朵小红花","isplat":0,"showtype":1,"ui_type":"小视频","type":1,"min_program_video_status":0},{"id":243,"title":"xxxxx","author":"xxxx","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":531,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2022\/07\/18\/62d5064cdc5b5\/o.png"},"showtime":"2021-01-11 15:51:00","uptime":"2022-07-18 15:05:49","intime":"2021-01-11 15:52:05","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":2,"ui_type":"小视频","type":1,"min_program_video_status":0},{"id":246,"title":"360分辨率","author":"11","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":527,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2022\/07\/18\/62d5063129a25\/o.png"},"showtime":"2021-01-27 18:51:00","uptime":"2022-07-18 15:05:21","intime":"2021-01-27 18:52:56","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":2,"ui_type":"小视频","type":1,"min_program_video_status":0},{"id":274,"title":"上班那点小事type=1","author":"zkf","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":524,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2022\/07\/18\/62d5011d5278d\/o.png"},"showtime":"2022-07-18 14:42:00","uptime":"2022-07-18 14:44:55","intime":"2022-07-18 14:44:55","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":1,"ui_type":"小视频","type":1,"min_program_video_status":0},{"id":273,"title":"type=1","author":"中国电影","tag_title":"幕后","tag_id":4,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":523,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2022\/07\/18\/62d4ff5d004fe\/o.jpg"},"showtime":"2022-07-18 14:35:00","uptime":"2022-07-18 14:36:17","intime":"2022-07-18 14:36:17","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":1,"ui_type":"文章","type":0,"min_program_video_status":0},{"id":258,"title":"showtype=3","author":"范娟","tag_title":"幕后","tag_id":4,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":518,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2022\/07\/18\/62d4fc939b274\/o.jpg"},"showtime":"2021-04-23 12:05:00","uptime":"2022-07-18 14:34:19","intime":"2021-04-23 11:59:55","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":3,"ui_type":"文章","type":0,"min_program_video_status":0},{"id":263,"title":"张同学幕后团队","author":"张同学","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":507,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2022\/07\/15\/62d103f1bcf47\/o.jpeg"},"showtime":"2021-11-30 14:48:00","uptime":"2022-07-15 14:06:46","intime":"2021-11-30 14:50:06","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":1,"ui_type":"文章","type":0,"min_program_video_status":0},{"id":270,"title":"测试原图1.2M-liugx","author":"刘庚鑫","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":503,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2022\/07\/05\/62c400711f272\/o.jpeg"},"showtime":"2022-07-05 16:38:00","uptime":"2022-07-05 17:12:18","intime":"2022-07-05 17:02:07","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":1,"ui_type":"文章","type":0,"min_program_video_status":0},{"id":269,"title":"lance小视频","author":"lance","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":496,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2022\/01\/10\/61dbab2e749c3\/o.gif"},"showtime":"2022-01-10 11:41:00","uptime":"2022-01-10 11:46:06","intime":"2022-01-10 11:43:37","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":1,"ui_type":"小视频","type":1,"min_program_video_status":0},{"id":232,"title":"范娟测试","author":"范娟","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":390,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2020\/10\/29\/5f9a368e09299\/o.png"},"showtime":"2020-10-29 11:29:00","uptime":"2020-11-13 10:29:05","intime":"2020-10-29 11:27:12","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":2,"ui_type":"文章","type":0,"min_program_video_status":0},{"id":223,"title":"建行H5","author":"范德萨","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":389,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2020\/10\/29\/5f9a3611f093e\/o.png"},"showtime":"2020-10-15 10:08:00","uptime":"2020-10-29 11:25:19","intime":"2020-10-15 10:10:38","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":1,"ui_type":"文章","type":0,"min_program_video_status":0},{"id":224,"title":"测试视频10.22","author":"测试","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":378,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2020\/10\/22\/5f913f651a4f5\/o.png"},"showtime":"2020-10-22 11:07:00","uptime":"2020-10-23 13:50:31","intime":"2020-10-22 11:10:43","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":1,"ui_type":"文章","type":0,"min_program_video_status":0},{"id":222,"title":"范娟-短视频","author":"范娟","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":376,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2020\/10\/15\/5f8813933246e\/o.png"},"showtime":"2020-10-14 09:37:00","uptime":"2020-10-16 11:13:17","intime":"2020-10-15 09:37:07","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":1,"ui_type":"文章","type":0,"min_program_video_status":0},{"id":221,"title":"范娟-纯文字","author":"范娟","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":364,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2020\/10\/15\/5f87a68067430\/o.png"},"showtime":"2020-10-14 09:33:00","uptime":"2020-10-15 09:31:52","intime":"2020-10-15 09:31:52","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":1,"ui_type":"文章","type":0,"min_program_video_status":0},{"id":296,"title":"测试测试","author":"呵呵","tag_title":"推荐","tag_id":3,"istop":0,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":584,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2024\/10\/17\/671072cb02393\/o.jpg"},"showtime":"2024-10-17 10:10:00","uptime":"2024-10-17 10:14:06","intime":"2024-10-17 10:14:06","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":2,"ui_type":"小视频","type":1,"min_program_video_status":1},{"id":289,"title":"这是视频标题","author":"丹丹","tag_title":"推荐","tag_id":3,"istop":0,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":576,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2022\/11\/29\/63857ecde065c\/o.png"},"showtime":"2022-11-29 11:34:00","uptime":"2022-11-29 11:39:27","intime":"2022-11-29 11:39:27","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":1,"ui_type":"小视频","type":1,"min_program_video_status":1},{"id":275,"title":"测试播放按钮是否显示","author":"测试12121","tag_title":"推荐","tag_id":3,"istop":0,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":562,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2022\/07\/29\/62e33bc2d2560\/o.jpg"},"showtime":"2022-07-29 09:44:00","uptime":"2022-07-29 09:45:47","intime":"2022-07-29 09:45:47","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":2,"ui_type":"文章","type":0,"min_program_video_status":0},{"id":272,"title":"showtype=1","author":"中国电影","tag_title":"推荐","tag_id":3,"istop":0,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":521,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2022\/07\/18\/62d4fe866fe15\/o.jpg"},"showtime":"2022-07-18 14:27:00","uptime":"2022-07-18 14:32:47","intime":"2022-07-18 14:32:47","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":1,"ui_type":"文章","type":0,"min_program_video_status":0},{"id":267,"title":"测试GIF-0105","author":"测试GIF-0105","tag_title":"推荐","tag_id":3,"istop":0,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":472,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2022\/01\/05\/61d536d46cc0f\/o.gif"},"showtime":"2022-01-05 14:11:00","uptime":"2022-01-07 10:25:45","intime":"2022-01-05 14:12:37","status":1,"ui_status":"上架","film_code":"001X05442020","film_name":"村里来了个洋媳妇","isplat":0,"showtype":2,"ui_type":"文章","type":0,"min_program_video_status":0},{"id":266,"title":"tea.0102","author":"tea.0102","tag_title":"推荐","tag_id":3,"istop":0,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":464,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2022\/01\/04\/61d3a5c58bac3\/o.gif"},"showtime":"2022-01-04 09:40:00","uptime":"2022-01-04 09:42:01","intime":"2022-01-04 09:42:01","status":1,"ui_status":"上架","film_code":"001X04142021","film_name":"农民院士","isplat":0,"showtype":2,"ui_type":"小视频","type":1,"min_program_video_status":0}]}}
INFO - 2025-07-07 11:07:09,891 - assertion_utils.py:35 -[assertion_utils:status_code_assert] - 状态码断言成功：接口实际返回状态码 200 == 200
INFO - 2025-07-07 11:07:09,894 - assertion_utils.py:94 -[assertion_utils:equal_assert] - 相等断言成功：接口实际结果 {'code': 0} == 预期结果：{'code': 0}
INFO - 2025-07-07 11:07:09,894 - web_service.py:517 -[web_service:execute_command_simple] - [batch_1751857597] 第230行: [32m INFO - 2025-07-07 11:07:09,894 - assertion_utils.py:94 -[assertion_utils:equal_assert] - 相等断言成功：接口实际结果 {'code': 0} == 预期结果：{'code': 0}[0m
INFO - 2025-07-07 11:07:09,897 - assertion_utils.py:94 -[assertion_utils:equal_assert] - 相等断言成功：接口实际结果 {'msg': '操作成功'} == 预期结果：{'msg': '操作成功'}
INFO - 2025-07-07 11:07:09,898 - assertion_utils.py:206 -[assertion_utils:assert_result] - 测试成功
INFO - 2025-07-07 11:07:09,913 - conftest.py:9 -[conftest:print_info] - ---------------接口测试结束---------------
INFO - 2025-07-07 11:07:10,697 - conftest.py:201 -[conftest:pytest_terminal_summary] - 
    测试环境！！！！！
    详情请查看附件~~~
    自动化测试结果简单汇总，通知如下，执行结果如下：
    测试用例总数：14  
    测试用例通过数：14
    通过率：100%
    测试用例失败数：0
    失败率：0%
    错误数量：0
    错误率：0%       
    跳过执行数量：0
    执行总时长：29.91s (0:00:29)
    
INFO - 2025-07-07 11:07:10,698 - web_service.py:517 -[web_service:execute_command_simple] - [batch_1751857597] 第234行: [32m INFO - 2025-07-07 11:07:10,697 - conftest.py:201 -[conftest:pytest_terminal_summary] -
INFO - 2025-07-07 11:07:10,699 - web_service.py:517 -[web_service:execute_command_simple] - [batch_1751857597] 第240行: 通过率：100%
INFO - 2025-07-07 11:07:10,704 - run.py:77 -[run:<module>] - 生成Allure报告...
INFO - 2025-07-07 11:07:13,900 - run.py:82 -[run:<module>] - Allure报告生成成功
INFO - 2025-07-07 11:07:13,900 - web_service.py:517 -[web_service:execute_command_simple] - [batch_1751857597] 第250行: [32m INFO - 2025-07-07 11:07:13,900 - run.py:82 -[run:<module>] - Allure报告生成成功[0m
INFO - 2025-07-07 11:07:15,639 - run.py:93 -[run:<module>] - report.zip 已经创建在 C:\vae\python_project\zy_ApiAuto\files
INFO - 2025-07-07 11:07:19,962 - qq_email.py:54 -[qq_email:qq_email] - 邮件发送成功！
INFO - 2025-07-07 11:07:20,076 - web_service.py:521 -[web_service:execute_command_simple] - 进程执行完成，返回码: 0，总输出行数: 255
INFO - 2025-07-07 11:07:20,076 - web_service.py:530 -[web_service:execute_command_simple] - 命令输出: [32m INFO - 2025-07-07 11:06:38,373 - run.py:17 -[run:<module>] - Python版本: 3.12.6 (tags/v3.12.6:a4a2d2b, Sep  6 2024, 20:11:23) [MSC v.1940 64 bit (AMD64)][0m [32m INFO - 2025-07-07 11:06:38,373 - run.py:18 -[run:<module>] - Python可执行文件: C:\vae\python_project\zy_ApiAuto\.venv\Scripts\pythonw.exe[0m [32m INFO - 2025-07-07 11:06:38,373 - run.py:19 -[run:<module>] - 当前工作目录: C:\vae\python_project\zy_ApiAuto[0m [32m INFO - 2025-07-07 11:06:38,373 - run.py:20 -[run:<module>] - 脚本参数: ['C:\\vae\\python_project\\zy_ApiAuto\\run.py', 'play', 'demo_test'][0m [32m INFO - 2025-07-07 11:06:38,373 - run.py:36 -[run:<module>] - 执行路径【['./testcase/play', './testcase/demo_test']】[0m [32m INFO - 2025-07-07 11:06:38,373 - run.py:39 -[run:<module>] - 开始清理所有旧的报告和结果文件...[0m [32m INFO - 2025-07-07 11:06:38,374 - run.py:44 -[run:<module>] - 删除旧的报告目录...[0m [32m INFO - 2025-07-07 11:06:38,403 - run.py:50 -[run:<module>] - 清理temp目录下的所有旧文件...[0m [32m INFO - 2025-07-07 11:06:40,437 - run.py:64 -[run:<module>] - 旧文件清理完成，开始执行测试...[0m [32m INFO - 2025-07-07 11:06:40,437 - run.py:72 -[run:<module>] - pytest参数: ['./testcase/play', './testcase/demo_test', '--tb=short', '--strict-markers', '--disable-warnings'][0m ============================= test session starts ============================= platform win32 -- Python 3.12.6, pytest-8.0.2, pluggy-1.5.0 -- C:\vae\python_project\zy_ApiAuto\.venv\Scripts\pythonw.exe cachedir: .pytest_cache rootdir: C:\vae\python_project\zy_ApiAuto configfile: pytest.ini plugins: allure-pytest-2.9.41, Faker-37.1.0, rerunfailures-14.0 collecting ... [32m INFO - 2025-07-07 11:06:41,297 - zy_backend_login.py:57 -[zy_backend_login:login] - 登录成功！{'msg': '登录成功', 'uid': '1942057751428562944', 'code': '0', 'data': {'name': '管理员', 'roleName': '租户管理员', 'isDefaultPwd': '0', 'userType': '1'}}[0m collected 14 items  testcase/play/test_play.py::TestPlay::test_play[api_info0] [32m INFO - 2025-07-07 11:06:41,822 - connectMysql.py:22 -[connectMysql:__init__] - 成功连接到数据库：数据库ip：*************[0m [32m INFO - 2025-07-07 11:06:41,846 - connectMysql.py:43 -[connectMysql:insert] - 插入数据库SQL--INSERT INTO `wei_sms_code_log` (`id`, `phone`, `channel_code`, `code`, `code_status`, `type`, `create_by`, `create_date`, `update_by`, `update_date`) VALUES (271515014630425548, '19877292898', '*********', '123456', 0, 1, NULL, '2025-07-07 11:06:41', NULL, '2025-07-07 11:06:41');[0m [32m INFO - 2025-07-07 11:06:41,846 - connectMysql.py:44 -[connectMysql:insert] - 数据库数据插入成功[0m [32m INFO - 2025-07-07 11:06:42,117 - zy_app_login.py:103 -[zy_app_login:login] - 【++登录成功++】{'head': {'errCode': '0', 'errMsg': '', 'tradeId': 'login', 'timestamp': '20250707110705'}, 'uid': '1942057755794771968', 'body': {'birthday': '', 'lastTime': '20250707101920', 'isCurrentRegister': '0', 'zipCode': '', 'sign': 'eacd9a26b76fa2783a70e8a4661733e4', 'imageNo': 0, 'points': '0', 'cityName': '', 'areaName': '', 'areaNo': '', 'imageUrl': '', 'provinceNo': '', 'email': '', 'QQ': '', 'cityNo': '', 'address': '', 'level': '1', 'nickName': '', 'sex': 2, 'mobile': '19877292898', 'MSN': '', 'identityCard': '', 'sessionId': '346518576256328694', 'userId': '346422044320518083', 'isMobileValid': 1, 'loginCount': 0, 'token': 'HIx4GcZCV9rNSVvCDHxLnTd9lqe4T+yvqwm3lMQTCY7gdc7GaMgINq64QVyeD0XxEC+AHo9ONgxjBFMyfqlPosl+78aoOuYgI9gLHmRnbRGmlT2bSdpg/kI22Kc6vOUF', 'realName': '', 'createTime': '1742204432000', 'provinceName': '', 'username': ''}}[0m [32m INFO - 2025-07-07 11:06:42,877 - conftest.py:7 -[conftest:print_info] - ---------------接口测试开始---------------[0m [32m INFO - 2025-07-07 11:06:42,916 - sendrequests.py:57 -[sendrequests:execute_api_request] - 接口名称：演出分类[0m [32m INFO - 2025-07-07 11:06:42,916 - sendrequests.py:58 -[sendrequests:execute_api_request] - 请求地址：http://*************/api/show/info/category_list[0m [32m INFO - 2025-07-07 11:06:42,916 - sendrequests.py:59 -[sendrequests:execute_api_request] - 请求方式：POST[0m [32m INFO - 2025-07-07 11:06:42,916 - sendrequests.py:60 -[sendrequests:execute_api_request] - 请求头：{'Content-Type': 'application/x-www-form-urlencoded', 'Connection': 'Keep-Alive', 'Accept-Encoding': 'gzip'}[0m [32m INFO - 2025-07-07 11:06:42,916 - sendrequests.py:61 -[sendrequests:execute_api_request] - 测试用例名：获取猫眼演出分类[0m [32m INFO - 2025-07-07 11:06:42,916 - sendrequests.py:62 -[sendrequests:execute_api_request] - cookies值：None[0m [32m INFO - 2025-07-07 11:06:42,916 - sendrequests.py:67 -[sendrequests:execute_api_request] - 参数类型：data[0m [32m INFO - 2025-07-07 11:06:42,916 - sendrequests.py:69 -[sendrequests:execute_api_request] - 请求参数：{"channelCode": "*********", "merchant_code": "", "token": "HIx4GcZCV9rNSVvCDHxLnTd9lqe4T+yvqwm3lMQTCY7gdc7GaMgINq64QVyeD0XxEC+AHo9ONgxjBFMyfqlPosl+78aoOuYgI9gLHmRnbRGmlT2bSdpg/kI22Kc6vOUF,*********"}[0m [32m INFO - 2025-07-07 11:06:43,176 - apiutils_business.py:179 -[apiutils_business:execute_test_cases] - 接口实际返回结果：{"code":0,"msg":"","data":[{"id":"10001","name":"演唱会","image":"http:\/\/*************\/resource\/play\/upload\/20250421\/175345K0n_6qP6Pi.jpg","tag_list":["测试文案","演出演唱会12"]},{"id":"10002","name":"体育赛事","image":"http:\/\/*************\/resource\/play\/upload\/20250408\/144744oaSxDsD3Qu.png","tag_list":["孙颖莎","全红婵"]},{"id":"10003","name":"戏曲艺术","image":"http:\/\/*************\/resource\/play\/upload\/20250408\/1448079S7mHpY2QU.png","tag_list":[]},{"id":"10017","name":"Livehouse","image":"http:\/\/*************\/resource\/play\/upload\/20250408\/1449279xNEIQlg9D.png","tag_list":[]},{"id":"10005","name":"舞蹈芭蕾","image":"http:\/\/*************\/resource\/play\/upload\/20250408\/145113dfAkJoKwCg.png","tag_list":[]},{"id":"10006","name":"音乐会","image":"http:\/\/*************\/resource\/play\/upload\/20250408\/1448369SbU_7vykE.png","tag_list":[]},{"id":"10007","name":"亲子演出","image":"http:\/\/*************\/resource\/play\/upload\/20250408\/144825FTdAI7yPy_.png","tag_list":[]},{"id":"10010","name":"音乐节","image":"http:\/\/*************\/resource\/play\/upload\/20250422\/145418ZAvXkbQMhU.png","tag_list":[]},{"id":"10014","name":"沉浸剧场","image":"http:\/\/*************\/resource\/play\/upload\/20250408\/144959tWsxiYBDA-.png","tag_list":[]},{"id":"10016","name":"相声","image":"http:\/\/*************\/resource\/play\/upload\/20250408\/144939LP2Ruhsuki.png","tag_list":[]},{"id":"10015","name":"脱口秀","image":"http:\/\/*************\/resource\/play\/upload\/20250408\/144949GXhLPNXxUa.png","tag_list":[]},{"id":"10009","name":"休闲展览","image":"http:\/\/*************\/resource\/play\/upload\/20250408\/144907oWhMCZrrG_.png","tag_list":[]},{"id":"10004","name":"话剧音乐剧","image":"http:\/\/*************\/resource\/play\/upload\/20250408\/145102PT7jed5WkQ.png","tag_list":[]},{"id":"10008","name":"其他","image":"http:\/\/*************\/resource\/play\/upload\/20250331\/164501dyI8a3idSN.png","tag_list":["标签 一","标签二"]}]}[0m [32m INFO - 2025-07-07 11:06:43,181 - assertion_utils.py:35 -[assertion_utils:status_code_assert] - 状态码断言成功：接口实际返回状态码 200 == 200[0m [32m INFO - 2025-07-07 11:06:43,182 - assertion_utils.py:94 -[assertion_utils:equal_assert] - 相等断言成功：接口实际结果 {'code': 0} == 预期结果：{'code': 0}[0m [32m INFO - 2025-07-07 11:06:43,183 - assertion_utils.py:206 -[assertion_utils:assert_result] - 测试成功[0m PASSED[32m INFO - 2025-07-07 11:06:43,197 - conftest.py:9 -[conftest:print_info] - ---------------接口测试结束---------------[0m  testcase/play/test_play.py::TestPlay::test_play[api_info1] [32m INFO - 2025-07-07 11:06:44,710 - conftest.py:7 -[conftest:print_info] - ---------------接口测试开始---------------[0m [32m INFO - 2025-07-07 11:06:44,717 - sendrequests.py:57 -[sendrequests:execute_api_request] - 接口名称：今日必抢[0m [32m INFO - 2025-07-07 11:06:44,717 - sendrequests.py:58 -[sendrequests:execute_api_request] - 请求地址：http://*************/api/show/info/home_today_sale_list[0m [32m INFO - 2025-07-07 11:06:44,717 - sendrequests.py:59 -[sendrequests:execute_api_request] - 请求方式：POST[0m [32m INFO - 2025-07-07 11:06:44,717 - sendrequests.py:60 -[sendrequests:execute_api_request] - 请求头：{'Content-Type': 'application/x-www-form-urlencoded', 'Connection': 'Keep-Alive', 'Accept-Encoding': 'gzip'}[0m [32m INFO - 2025-07-07 11:06:44,717 - sendrequests.py:61 -[sendrequests:execute_api_request] - 测试用例名：获取今日必抢演出列表[0m [32m INFO - 2025-07-07 11:06:44,718 - sendrequests.py:62 -[sendrequests:execute_api_request] - cookies值：None[0m [32m INFO - 2025-07-07 11:06:44,718 - sendrequests.py:67 -[sendrequests:execute_api_request] - 参数类型：data[0m [32m INFO - 2025-07-07 11:06:44,718 - sendrequests.py:69 -[sendrequests:execute_api_request] - 请求参数：{"page": 1, "pageSize": 20, "channelCode": "*********", "token": "HIx4GcZCV9rNSVvCDHxLnTd9lqe4T+yvqwm3lMQTCY7gdc7GaMgINq64QVyeD0XxEC+AHo9ONgxjBFMyfqlPosl+78aoOuYgI9gLHmRnbRGmlT2bSdpg/kI22Kc6vOUF,*********"}[0m [32m INFO - 2025-07-07 11:06:45,004 - apiutils_business.py:179 -[apiutils_business:execute_test_cases] - 接口实际返回结果：{"code":0,"msg":"","data":{"total":65,"list":[{"id":"26","name":"老无座-0313","category_id":"10001","venue_id":"8","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/d7db73c1934ce8fcb5c4003afe3522b594040.jpg","ticket_status":"3","unusual_status":"0","need_real_name":"0","first_show_time":"2025-08-30 20:00:00","last_show_time":"2025-12-01 02:00:00","on_sale_time":"2025-03-13 19:00:00","max_buy_limit_per_id":"0","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"4.00","max_sale_price":"55.00","shelve_status":"1","third_shelve_status":"1","sort":"210","venue_info":{"venue_id":"8","venue_name":"梅赛德斯-奔驰文化中心","province_title":"上海市","city_title":"浦东新区","address":"世博大道1200号","latitude":"31.188952","longitude":"121.493201"},"show_info":{"show_id":"217","performance_id":"26","show_name":"2025-08-30 周六 20:00","show_start_time":"2025-08-30 20:00:00","show_end_time":"2025-08-30 22:00:00","show_type":"1","show_seat_type":"0","show_real_name_limit":"0","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2025-05-20 16:46:26","off_sale_time":"2025-08-30 20:00:00"},"category_info":{"category_id":"10001","category_name":"演唱会"}},{"id":"22","name":"【外部分销对接专用，不可编辑】编辑请联系上单-新选座-实名制","category_id":"10001","venue_id":"8","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/d7db73c1934ce8fcb5c4003afe3522b594040.jpg","ticket_status":"3","unusual_status":"0","need_real_name":"1","first_show_time":"2025-12-31 01:00:00","last_show_time":"2028-01-01 01:00:00","on_sale_time":"2025-04-22 16:05:43","max_buy_limit_per_id":"6","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"2.00","max_sale_price":"6.00","shelve_status":"1","third_shelve_status":"1","sort":"90","venue_info":{"venue_id":"8","venue_name":"梅赛德斯-奔驰文化中心","province_title":"上海市","city_title":"浦东新区","address":"世博大道1200号","latitude":"31.188952","longitude":"121.493201"},"show_info":{"show_id":"40","performance_id":"22","show_name":"2025-12-31 周三 01:00","show_start_time":"2025-12-31 01:00:00","show_end_time":"2025-12-31 02:00:00","show_type":"1","show_seat_type":"1","show_real_name_limit":"1","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2024-12-10 13:18:31","off_sale_time":"2025-12-31 01:00:00"},"category_info":{"category_id":"10001","category_name":"演唱会"}},{"id":"75","name":"【禁止修改】4.1  自营选座项目---日历模式（优惠混搭）+非实名","category_id":"10004","venue_id":"7","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/fa0fa284da6c8246a179c7400e8d132e59521.png","ticket_status":"12","unusual_status":"6","need_real_name":"0","first_show_time":"2025-09-01 08:00:00","last_show_time":"2026-04-11 23:59:00","on_sale_time":"2025-04-01 10:49:52","max_buy_limit_per_id":"6","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"110.00","max_sale_price":"2200.00","shelve_status":"1","third_shelve_status":"1","sort":"80","venue_info":{"venue_id":"7","venue_name":"凯迪拉克·上海音乐厅","province_title":"上海市","city_title":"黄浦区","address":"延安东路523号（近西藏南路）","latitude":"31.226854","longitude":"121.477977"},"show_info":{"show_id":"250","performance_id":"75","show_name":"2025-09-01 周一 08:00","show_start_time":"2025-09-01 08:00:00","show_end_time":"2025-09-01 10:00:00","show_type":"1","show_seat_type":"1","show_real_name_limit":"0","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2025-04-01 10:46:15","off_sale_time":"2025-08-31 00:00:00"},"category_info":{"category_id":"10004","category_name":"话剧音乐剧"}},{"id":"34","name":"朱磊业老无座线下搬单","category_id":"10002","venue_id":"7","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/84d2c832ab35b5514e8962a287d69c59631.png","ticket_status":"3","unusual_status":"0","need_real_name":"0","first_show_time":"2025-12-01 00:00:00","last_show_time":"2026-01-14 07:00:00","on_sale_time":"2025-03-11 19:23:31","max_buy_limit_per_id":"0","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"5","min_sale_price":"2.00","max_sale_price":"3.00","shelve_status":"1","third_shelve_status":"1","sort":"70","venue_info":{"venue_id":"7","venue_name":"凯迪拉克·上海音乐厅","province_title":"上海市","city_title":"黄浦区","address":"延安东路523号（近西藏南路）","latitude":"31.226854","longitude":"121.477977"},"show_info":{"show_id":"94","performance_id":"34","show_name":"2025-12-01 周一 00:00","show_start_time":"2025-12-01 00:00:00","show_end_time":"2025-12-01 17:00:00","show_type":"1","show_seat_type":"0","show_real_name_limit":"0","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2024-11-20 12:15:50","off_sale_time":"2025-11-30 00:00:00"},"category_info":{"category_id":"10002","category_name":"体育赛事"}},{"id":"8","name":"测试发票说明05","category_id":"10002","venue_id":"12","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/9353f3ac3ba4b47bd4642fcbdc393bbf101705.jpg","ticket_status":"3","unusual_status":"0","need_real_name":"0","first_show_time":"2026-01-03 00:00:00","last_show_time":"2026-01-03 02:00:00","on_sale_time":"2025-02-28 19:20:54","max_buy_limit_per_id":"6","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"33.00","max_sale_price":"33.00","shelve_status":"1","third_shelve_status":"1","sort":"60","venue_info":{"venue_id":"12","venue_name":"上海展览中心","province_title":"上海市","city_title":"静安区","address":"延安中路1000号","latitude":"31.224714","longitude":"121.452558"},"show_info":{"show_id":"16","performance_id":"8","show_name":"2026-01-03 周六 00:00","show_start_time":"2026-01-03 00:00:00","show_end_time":"2026-01-03 02:00:00","show_type":"1","show_seat_type":"0","show_real_name_limit":"0","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2025-01-14 15:12:06","off_sale_time":"2026-01-03 00:00:00"},"category_info":{"category_id":"10002","category_name":"体育赛事"}},{"id":"23","name":"【外部分销对接专用，不可编辑】编辑请联系上单-新选座-非实名制","category_id":"10007","venue_id":"8","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/d7db73c1934ce8fcb5c4003afe3522b594040.jpg","ticket_status":"3","unusual_status":"0","need_real_name":"1","first_show_time":"2028-01-01 00:00:00","last_show_time":"2028-01-01 01:00:00","on_sale_time":"2024-07-29 16:43:00","max_buy_limit_per_id":"6","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"50.00","max_sale_price":"165.00","shelve_status":"1","third_shelve_status":"1","sort":"60","venue_info":{"venue_id":"8","venue_name":"梅赛德斯-奔驰文化中心","province_title":"上海市","city_title":"浦东新区","address":"世博大道1200号","latitude":"31.188952","longitude":"121.493201"},"show_info":{"show_id":"42","performance_id":"23","show_name":"2028-01-01 周六 00:00","show_start_time":"2028-01-01 00:00:00","show_end_time":"2028-01-01 01:00:00","show_type":"1","show_seat_type":"1","show_real_name_limit":"1","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2024-07-29 16:40:00","off_sale_time":"2028-01-01 00:00:00"},"category_info":{"category_id":"10007","category_name":"亲子演出"}},{"id":"48","name":"朱磊业新配座套票配座","category_id":"10001","venue_id":"7","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/84d2c832ab35b5514e8962a287d69c59631.png","ticket_status":"3","unusual_status":"0","need_real_name":"0","first_show_time":"2026-01-02 00:00:00","last_show_time":"2026-01-02 04:00:00","on_sale_time":"2025-01-09 11:28:59","max_buy_limit_per_id":"0","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"2.00","max_sale_price":"3.00","shelve_status":"1","third_shelve_status":"1","sort":"60","venue_info":{"venue_id":"7","venue_name":"凯迪拉克·上海音乐厅","province_title":"上海市","city_title":"黄浦区","address":"延安东路523号（近西藏南路）","latitude":"31.226854","longitude":"121.477977"},"show_info":{"show_id":"179","performance_id":"48","show_name":"2026-01-02 周五 00:00","show_start_time":"2026-01-02 00:00:00","show_end_time":"2026-01-02 04:00:00","show_type":"1","show_seat_type":"1","show_real_name_limit":"9999","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2025-01-20 16:24:00","off_sale_time":"2026-01-02 00:00:00"},"category_info":{"category_id":"10001","category_name":"演唱会"}},{"id":"51","name":"朱磊业新无座快递配送转其它","category_id":"10001","venue_id":"13","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/84d2c832ab35b5514e8962a287d69c59631.png","ticket_status":"12","unusual_status":"3","need_real_name":"0","first_show_time":"2026-01-01 00:00:00","last_show_time":"2028-12-15 02:00:00","on_sale_time":"2024-12-13 17:47:21","max_buy_limit_per_id":"0","max_buy_limit_per_order":"20","min_buy_limit_per_order":"0","max_buy_limit_per_user":"1","min_sale_price":"2.00","max_sale_price":"6.00","shelve_status":"1","third_shelve_status":"1","sort":"60","venue_info":{"venue_id":"13","venue_name":"虹桥天地演艺中心","province_title":"上海市","city_title":"闵行区","address":"绍虹路33号","latitude":"31.19331","longitude":"121.31583"},"show_info":{"show_id":"182","performance_id":"51","show_name":"2026-01-01 周四 00:00","show_start_time":"2026-01-01 00:00:00","show_end_time":"2026-01-01 03:00:00","show_type":"1","show_seat_type":"0","show_real_name_limit":"0","max_buy_limit_per_order":"20","show_unusual_status":"1","on_sale_time":"2024-12-24 15:57:38","off_sale_time":"2026-01-01 00:00:00"},"category_info":{"category_id":"10001","category_name":"演唱会"}},{"id":"53","name":"小红书推送商品验证（老项目喽）","category_id":"10001","venue_id":"7","poster_url":"https:\/\/p1.meituan.net\/myvideodistribute\/6563cbda84d54511adb254e69b0c8001239854.jpg","ticket_status":"3","unusual_status":"0","need_real_name":"0","first_show_time":"2025-07-30 11:00:00","last_show_time":"2025-09-30 20:00:00","on_sale_time":"2025-02-11 13:00:00","max_buy_limit_per_id":"0","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"11.00","max_sale_price":"367.00","shelve_status":"1","third_shelve_status":"1","sort":"60","venue_info":{"venue_id":"7","venue_name":"凯迪拉克·上海音乐厅","province_title":"上海市","city_title":"黄浦区","address":"延安东路523号（近西藏南路）","latitude":"31.226854","longitude":"121.477977"},"show_info":{"show_id":"312","performance_id":"53","show_name":"2025-07-30 周三 11:00","show_start_time":"2025-07-30 11:00:00","show_end_time":"2025-07-30 19:00:00","show_type":"1","show_seat_type":"0","show_real_name_limit":"0","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2025-06-26 14:33:53","off_sale_time":"2025-07-30 11:00:00"},"category_info":{"category_id":"10001","category_name":"演唱会"}},{"id":"108","name":"场馆---项目开售时间校验优化","category_id":"10002","venue_id":"13","poster_url":"https:\/\/p1.meituan.net\/myvideodistribute\/54c4fc8913710b6cbd587f52bce3cb4a305726.jpg","ticket_status":"3","unusual_status":"0","need_real_name":"0","first_show_time":"2025-07-08 00:00:00","last_show_time":"2025-07-15 01:00:00","on_sale_time":"2025-06-10 19:45:00","max_buy_limit_per_id":"6","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"110.00","max_sale_price":"110.00","shelve_status":"1","third_shelve_status":"1","sort":"60","venue_info":{"venue_id":"13","venue_name":"虹桥天地演艺中心","province_title":"上海市","city_title":"闵行区","address":"绍虹路33号","latitude":"31.19331","longitude":"121.31583"},"show_info":{"show_id":"341","performance_id":"108","show_name":"2025-07-08 周二 00:00","show_start_time":"2025-07-08 00:00:00","show_end_time":"2025-07-08 01:00:00","show_type":"1","show_seat_type":"0","show_real_name_limit":"0","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2025-06-10 19:20:54","off_sale_time":"2025-07-08 00:00:00"},"category_info":{"category_id":"10002","category_name":"体育赛事"}}]}}[0m [32m INFO - 2025-07-07 11:06:45,009 - assertion_utils.py:35 -[assertion_utils:status_code_assert] - 状态码断言成功：接口实际返回状态码 200 == 200[0m [32m INFO - 2025-07-07 11:06:45,010 - assertion_utils.py:94 -[assertion_utils:equal_assert] - 相等断言成功：接口实际结果 {'code': 0} == 预期结果：{'code': 0}[0m [32m INFO - 2025-07-07 11:06:45,011 - assertion_utils.py:206 -[assertion_utils:assert_result] - 测试成功[0m PASSED[32m INFO - 2025-07-07 11:06:45,023 - conftest.py:9 -[conftest:print_info] - ---------------接口测试结束---------------[0m  testcase/play/test_play.py::TestPlay::test_play[api_info2] [32m INFO - 2025-07-07 11:06:46,536 - conftest.py:7 -[conftest:print_info] - ---------------接口测试开始---------------[0m [32m INFO - 2025-07-07 11:06:46,540 - sendrequests.py:57 -[sendrequests:execute_api_request] - 接口名称：近期特惠[0m [32m INFO - 2025-07-07 11:06:46,541 - sendrequests.py:58 -[sendrequests:execute_api_request] - 请求地址：http://*************/api/show/info/home_near_show_list[0m [32m INFO - 2025-07-07 11:06:46,541 - sendrequests.py:59 -[sendrequests:execute_api_request] - 请求方式：POST[0m [32m INFO - 2025-07-07 11:06:46,541 - sendrequests.py:60 -[sendrequests:execute_api_request] - 请求头：{'Content-Type': 'application/x-www-form-urlencoded', 'Connection': 'Keep-Alive', 'Accept-Encoding': 'gzip'}[0m [32m INFO - 2025-07-07 11:06:46,541 - sendrequests.py:61 -[sendrequests:execute_api_request] - 测试用例名：获取近期特惠演出列表[0m [32m INFO - 2025-07-07 11:06:46,541 - sendrequests.py:62 -[sendrequests:execute_api_request] - cookies值：None[0m [32m INFO - 2025-07-07 11:06:46,541 - sendrequests.py:67 -[sendrequests:execute_api_request] - 参数类型：data[0m [32m INFO - 2025-07-07 11:06:46,541 - sendrequests.py:69 -[sendrequests:execute_api_request] - 请求参数：{"page": 1, "pageSize": 20, "channelCode": "*********", "token": "HIx4GcZCV9rNSVvCDHxLnTd9lqe4T+yvqwm3lMQTCY7gdc7GaMgINq64QVyeD0XxEC+AHo9ONgxjBFMyfqlPosl+78aoOuYgI9gLHmRnbRGmlT2bSdpg/kI22Kc6vOUF,*********"}[0m [32m INFO - 2025-07-07 11:06:46,823 - apiutils_business.py:179 -[apiutils_business:execute_test_cases] - 接口实际返回结果：{"code":0,"msg":"","data":{"total":65,"list":[{"id":"26","name":"老无座-0313","category_id":"10001","venue_id":"8","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/d7db73c1934ce8fcb5c4003afe3522b594040.jpg","ticket_status":"3","unusual_status":"0","need_real_name":"0","first_show_time":"2025-08-30 20:00:00","last_show_time":"2025-12-01 02:00:00","on_sale_time":"2025-03-13 19:00:00","max_buy_limit_per_id":"0","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"4.00","max_sale_price":"55.00","shelve_status":"1","third_shelve_status":"1","sort":"210","venue_info":{"venue_id":"8","venue_name":"梅赛德斯-奔驰文化中心","province_title":"上海市","city_title":"浦东新区","address":"世博大道1200号","latitude":"31.188952","longitude":"121.493201"},"show_info":{"show_id":"217","performance_id":"26","show_name":"2025-08-30 周六 20:00","show_start_time":"2025-08-30 20:00:00","show_end_time":"2025-08-30 22:00:00","show_type":"1","show_seat_type":"0","show_real_name_limit":"0","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2025-05-20 16:46:26","off_sale_time":"2025-08-30 20:00:00"},"category_info":{"category_id":"10001","category_name":"演唱会"}},{"id":"22","name":"【外部分销对接专用，不可编辑】编辑请联系上单-新选座-实名制","category_id":"10001","venue_id":"8","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/d7db73c1934ce8fcb5c4003afe3522b594040.jpg","ticket_status":"3","unusual_status":"0","need_real_name":"1","first_show_time":"2025-12-31 01:00:00","last_show_time":"2028-01-01 01:00:00","on_sale_time":"2025-04-22 16:05:43","max_buy_limit_per_id":"6","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"2.00","max_sale_price":"6.00","shelve_status":"1","third_shelve_status":"1","sort":"90","venue_info":{"venue_id":"8","venue_name":"梅赛德斯-奔驰文化中心","province_title":"上海市","city_title":"浦东新区","address":"世博大道1200号","latitude":"31.188952","longitude":"121.493201"},"show_info":{"show_id":"40","performance_id":"22","show_name":"2025-12-31 周三 01:00","show_start_time":"2025-12-31 01:00:00","show_end_time":"2025-12-31 02:00:00","show_type":"1","show_seat_type":"1","show_real_name_limit":"1","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2024-12-10 13:18:31","off_sale_time":"2025-12-31 01:00:00"},"category_info":{"category_id":"10001","category_name":"演唱会"}},{"id":"75","name":"【禁止修改】4.1  自营选座项目---日历模式（优惠混搭）+非实名","category_id":"10004","venue_id":"7","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/fa0fa284da6c8246a179c7400e8d132e59521.png","ticket_status":"12","unusual_status":"6","need_real_name":"0","first_show_time":"2025-09-01 08:00:00","last_show_time":"2026-04-11 23:59:00","on_sale_time":"2025-04-01 10:49:52","max_buy_limit_per_id":"6","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"110.00","max_sale_price":"2200.00","shelve_status":"1","third_shelve_status":"1","sort":"80","venue_info":{"venue_id":"7","venue_name":"凯迪拉克·上海音乐厅","province_title":"上海市","city_title":"黄浦区","address":"延安东路523号（近西藏南路）","latitude":"31.226854","longitude":"121.477977"},"show_info":{"show_id":"250","performance_id":"75","show_name":"2025-09-01 周一 08:00","show_start_time":"2025-09-01 08:00:00","show_end_time":"2025-09-01 10:00:00","show_type":"1","show_seat_type":"1","show_real_name_limit":"0","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2025-04-01 10:46:15","off_sale_time":"2025-08-31 00:00:00"},"category_info":{"category_id":"10004","category_name":"话剧音乐剧"}},{"id":"34","name":"朱磊业老无座线下搬单","category_id":"10002","venue_id":"7","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/84d2c832ab35b5514e8962a287d69c59631.png","ticket_status":"3","unusual_status":"0","need_real_name":"0","first_show_time":"2025-12-01 00:00:00","last_show_time":"2026-01-14 07:00:00","on_sale_time":"2025-03-11 19:23:31","max_buy_limit_per_id":"0","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"5","min_sale_price":"2.00","max_sale_price":"3.00","shelve_status":"1","third_shelve_status":"1","sort":"70","venue_info":{"venue_id":"7","venue_name":"凯迪拉克·上海音乐厅","province_title":"上海市","city_title":"黄浦区","address":"延安东路523号（近西藏南路）","latitude":"31.226854","longitude":"121.477977"},"show_info":{"show_id":"94","performance_id":"34","show_name":"2025-12-01 周一 00:00","show_start_time":"2025-12-01 00:00:00","show_end_time":"2025-12-01 17:00:00","show_type":"1","show_seat_type":"0","show_real_name_limit":"0","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2024-11-20 12:15:50","off_sale_time":"2025-11-30 00:00:00"},"category_info":{"category_id":"10002","category_name":"体育赛事"}},{"id":"8","name":"测试发票说明05","category_id":"10002","venue_id":"12","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/9353f3ac3ba4b47bd4642fcbdc393bbf101705.jpg","ticket_status":"3","unusual_status":"0","need_real_name":"0","first_show_time":"2026-01-03 00:00:00","last_show_time":"2026-01-03 02:00:00","on_sale_time":"2025-02-28 19:20:54","max_buy_limit_per_id":"6","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"33.00","max_sale_price":"33.00","shelve_status":"1","third_shelve_status":"1","sort":"60","venue_info":{"venue_id":"12","venue_name":"上海展览中心","province_title":"上海市","city_title":"静安区","address":"延安中路1000号","latitude":"31.224714","longitude":"121.452558"},"show_info":{"show_id":"16","performance_id":"8","show_name":"2026-01-03 周六 00:00","show_start_time":"2026-01-03 00:00:00","show_end_time":"2026-01-03 02:00:00","show_type":"1","show_seat_type":"0","show_real_name_limit":"0","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2025-01-14 15:12:06","off_sale_time":"2026-01-03 00:00:00"},"category_info":{"category_id":"10002","category_name":"体育赛事"}},{"id":"23","name":"【外部分销对接专用，不可编辑】编辑请联系上单-新选座-非实名制","category_id":"10007","venue_id":"8","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/d7db73c1934ce8fcb5c4003afe3522b594040.jpg","ticket_status":"3","unusual_status":"0","need_real_name":"1","first_show_time":"2028-01-01 00:00:00","last_show_time":"2028-01-01 01:00:00","on_sale_time":"2024-07-29 16:43:00","max_buy_limit_per_id":"6","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"50.00","max_sale_price":"165.00","shelve_status":"1","third_shelve_status":"1","sort":"60","venue_info":{"venue_id":"8","venue_name":"梅赛德斯-奔驰文化中心","province_title":"上海市","city_title":"浦东新区","address":"世博大道1200号","latitude":"31.188952","longitude":"121.493201"},"show_info":{"show_id":"42","performance_id":"23","show_name":"2028-01-01 周六 00:00","show_start_time":"2028-01-01 00:00:00","show_end_time":"2028-01-01 01:00:00","show_type":"1","show_seat_type":"1","show_real_name_limit":"1","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2024-07-29 16:40:00","off_sale_time":"2028-01-01 00:00:00"},"category_info":{"category_id":"10007","category_name":"亲子演出"}},{"id":"48","name":"朱磊业新配座套票配座","category_id":"10001","venue_id":"7","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/84d2c832ab35b5514e8962a287d69c59631.png","ticket_status":"3","unusual_status":"0","need_real_name":"0","first_show_time":"2026-01-02 00:00:00","last_show_time":"2026-01-02 04:00:00","on_sale_time":"2025-01-09 11:28:59","max_buy_limit_per_id":"0","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"2.00","max_sale_price":"3.00","shelve_status":"1","third_shelve_status":"1","sort":"60","venue_info":{"venue_id":"7","venue_name":"凯迪拉克·上海音乐厅","province_title":"上海市","city_title":"黄浦区","address":"延安东路523号（近西藏南路）","latitude":"31.226854","longitude":"121.477977"},"show_info":{"show_id":"179","performance_id":"48","show_name":"2026-01-02 周五 00:00","show_start_time":"2026-01-02 00:00:00","show_end_time":"2026-01-02 04:00:00","show_type":"1","show_seat_type":"1","show_real_name_limit":"9999","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2025-01-20 16:24:00","off_sale_time":"2026-01-02 00:00:00"},"category_info":{"category_id":"10001","category_name":"演唱会"}},{"id":"51","name":"朱磊业新无座快递配送转其它","category_id":"10001","venue_id":"13","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/84d2c832ab35b5514e8962a287d69c59631.png","ticket_status":"12","unusual_status":"3","need_real_name":"0","first_show_time":"2026-01-01 00:00:00","last_show_time":"2028-12-15 02:00:00","on_sale_time":"2024-12-13 17:47:21","max_buy_limit_per_id":"0","max_buy_limit_per_order":"20","min_buy_limit_per_order":"0","max_buy_limit_per_user":"1","min_sale_price":"2.00","max_sale_price":"6.00","shelve_status":"1","third_shelve_status":"1","sort":"60","venue_info":{"venue_id":"13","venue_name":"虹桥天地演艺中心","province_title":"上海市","city_title":"闵行区","address":"绍虹路33号","latitude":"31.19331","longitude":"121.31583"},"show_info":{"show_id":"182","performance_id":"51","show_name":"2026-01-01 周四 00:00","show_start_time":"2026-01-01 00:00:00","show_end_time":"2026-01-01 03:00:00","show_type":"1","show_seat_type":"0","show_real_name_limit":"0","max_buy_limit_per_order":"20","show_unusual_status":"1","on_sale_time":"2024-12-24 15:57:38","off_sale_time":"2026-01-01 00:00:00"},"category_info":{"category_id":"10001","category_name":"演唱会"}},{"id":"53","name":"小红书推送商品验证（老项目喽）","category_id":"10001","venue_id":"7","poster_url":"https:\/\/p1.meituan.net\/myvideodistribute\/6563cbda84d54511adb254e69b0c8001239854.jpg","ticket_status":"3","unusual_status":"0","need_real_name":"0","first_show_time":"2025-07-30 11:00:00","last_show_time":"2025-09-30 20:00:00","on_sale_time":"2025-02-11 13:00:00","max_buy_limit_per_id":"0","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"11.00","max_sale_price":"367.00","shelve_status":"1","third_shelve_status":"1","sort":"60","venue_info":{"venue_id":"7","venue_name":"凯迪拉克·上海音乐厅","province_title":"上海市","city_title":"黄浦区","address":"延安东路523号（近西藏南路）","latitude":"31.226854","longitude":"121.477977"},"show_info":{"show_id":"312","performance_id":"53","show_name":"2025-07-30 周三 11:00","show_start_time":"2025-07-30 11:00:00","show_end_time":"2025-07-30 19:00:00","show_type":"1","show_seat_type":"0","show_real_name_limit":"0","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2025-06-26 14:33:53","off_sale_time":"2025-07-30 11:00:00"},"category_info":{"category_id":"10001","category_name":"演唱会"}},{"id":"108","name":"场馆---项目开售时间校验优化","category_id":"10002","venue_id":"13","poster_url":"https:\/\/p1.meituan.net\/myvideodistribute\/54c4fc8913710b6cbd587f52bce3cb4a305726.jpg","ticket_status":"3","unusual_status":"0","need_real_name":"0","first_show_time":"2025-07-08 00:00:00","last_show_time":"2025-07-15 01:00:00","on_sale_time":"2025-06-10 19:45:00","max_buy_limit_per_id":"6","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"110.00","max_sale_price":"110.00","shelve_status":"1","third_shelve_status":"1","sort":"60","venue_info":{"venue_id":"13","venue_name":"虹桥天地演艺中心","province_title":"上海市","city_title":"闵行区","address":"绍虹路33号","latitude":"31.19331","longitude":"121.31583"},"show_info":{"show_id":"341","performance_id":"108","show_name":"2025-07-08 周二 00:00","show_start_time":"2025-07-08 00:00:00","show_end_time":"2025-07-08 01:00:00","show_type":"1","show_seat_type":"0","show_real_name_limit":"0","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2025-06-10 19:20:54","off_sale_time":"2025-07-08 00:00:00"},"category_info":{"category_id":"10002","category_name":"体育赛事"}}]}}[0m [32m INFO - 2025-07-07 11:06:46,829 - assertion_utils.py:35 -[assertion_utils:status_code_assert] - 状态码断言成功：接口实际返回状态码 200 == 200[0m [32m INFO - 2025-07-07 11:06:46,830 - assertion_utils.py:94 -[assertion_utils:equal_assert] - 相等断言成功：接口实际结果 {'code': 0} == 预期结果：{'code': 0}[0m [32m INFO - 2025-07-07 11:06:46,831 - assertion_utils.py:206 -[assertion_utils:assert_result] - 测试成功[0m PASSED[32m INFO - 2025-07-07 11:06:46,840 - conftest.py:9 -[conftest:print_info] - ---------------接口测试结束---------------[0m  testcase/play/test_play.py::TestPlay::test_play[api_info3] [32m INFO - 2025-07-07 11:06:48,361 - conftest.py:7 -[conftest:print_info] - ---------------接口测试开始---------------[0m [32m INFO - 2025-07-07 11:06:48,366 - sendrequests.py:57 -[sendrequests:execute_api_request] - 接口名称：为你推荐[0m [32m INFO - 2025-07-07 11:06:48,366 - sendrequests.py:58 -[sendrequests:execute_api_request] - 请求地址：http://*************/api/show/info/home_recommend_list[0m [32m INFO - 2025-07-07 11:06:48,366 - sendrequests.py:59 -[sendrequests:execute_api_request] - 请求方式：POST[0m [32m INFO - 2025-07-07 11:06:48,366 - sendrequests.py:60 -[sendrequests:execute_api_request] - 请求头：{'Content-Type': 'application/x-www-form-urlencoded', 'Connection': 'Keep-Alive', 'Accept-Encoding': 'gzip'}[0m [32m INFO - 2025-07-07 11:06:48,366 - sendrequests.py:61 -[sendrequests:execute_api_request] - 测试用例名：获取为你推荐演出列表[0m [32m INFO - 2025-07-07 11:06:48,367 - sendrequests.py:62 -[sendrequests:execute_api_request] - cookies值：None[0m [32m INFO - 2025-07-07 11:06:48,367 - sendrequests.py:67 -[sendrequests:execute_api_request] - 参数类型：data[0m [32m INFO - 2025-07-07 11:06:48,367 - sendrequests.py:69 -[sendrequests:execute_api_request] - 请求参数：{"page": 1, "pageSize": 20, "channelCode": "*********", "token": "HIx4GcZCV9rNSVvCDHxLnTd9lqe4T+yvqwm3lMQTCY7gdc7GaMgINq64QVyeD0XxEC+AHo9ONgxjBFMyfqlPosl+78aoOuYgI9gLHmRnbRGmlT2bSdpg/kI22Kc6vOUF,*********"}[0m [32m INFO - 2025-07-07 11:06:48,647 - apiutils_business.py:179 -[apiutils_business:execute_test_cases] - 接口实际返回结果：{"code":0,"msg":"","data":{"total":65,"list":[{"id":"26","name":"老无座-0313","category_id":"10001","venue_id":"8","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/d7db73c1934ce8fcb5c4003afe3522b594040.jpg","ticket_status":"3","unusual_status":"0","need_real_name":"0","first_show_time":"2025-08-30 20:00:00","last_show_time":"2025-12-01 02:00:00","on_sale_time":"2025-03-13 19:00:00","max_buy_limit_per_id":"0","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"4.00","max_sale_price":"55.00","shelve_status":"1","third_shelve_status":"1","sort":"210","venue_info":{"venue_id":"8","venue_name":"梅赛德斯-奔驰文化中心","province_title":"上海市","city_title":"浦东新区","address":"世博大道1200号","latitude":"31.188952","longitude":"121.493201"},"show_info":{"show_id":"217","performance_id":"26","show_name":"2025-08-30 周六 20:00","show_start_time":"2025-08-30 20:00:00","show_end_time":"2025-08-30 22:00:00","show_type":"1","show_seat_type":"0","show_real_name_limit":"0","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2025-05-20 16:46:26","off_sale_time":"2025-08-30 20:00:00"},"category_info":{"category_id":"10001","category_name":"演唱会"}},{"id":"22","name":"【外部分销对接专用，不可编辑】编辑请联系上单-新选座-实名制","category_id":"10001","venue_id":"8","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/d7db73c1934ce8fcb5c4003afe3522b594040.jpg","ticket_status":"3","unusual_status":"0","need_real_name":"1","first_show_time":"2025-12-31 01:00:00","last_show_time":"2028-01-01 01:00:00","on_sale_time":"2025-04-22 16:05:43","max_buy_limit_per_id":"6","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"2.00","max_sale_price":"6.00","shelve_status":"1","third_shelve_status":"1","sort":"90","venue_info":{"venue_id":"8","venue_name":"梅赛德斯-奔驰文化中心","province_title":"上海市","city_title":"浦东新区","address":"世博大道1200号","latitude":"31.188952","longitude":"121.493201"},"show_info":{"show_id":"40","performance_id":"22","show_name":"2025-12-31 周三 01:00","show_start_time":"2025-12-31 01:00:00","show_end_time":"2025-12-31 02:00:00","show_type":"1","show_seat_type":"1","show_real_name_limit":"1","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2024-12-10 13:18:31","off_sale_time":"2025-12-31 01:00:00"},"category_info":{"category_id":"10001","category_name":"演唱会"}},{"id":"75","name":"【禁止修改】4.1  自营选座项目---日历模式（优惠混搭）+非实名","category_id":"10004","venue_id":"7","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/fa0fa284da6c8246a179c7400e8d132e59521.png","ticket_status":"12","unusual_status":"6","need_real_name":"0","first_show_time":"2025-09-01 08:00:00","last_show_time":"2026-04-11 23:59:00","on_sale_time":"2025-04-01 10:49:52","max_buy_limit_per_id":"6","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"110.00","max_sale_price":"2200.00","shelve_status":"1","third_shelve_status":"1","sort":"80","venue_info":{"venue_id":"7","venue_name":"凯迪拉克·上海音乐厅","province_title":"上海市","city_title":"黄浦区","address":"延安东路523号（近西藏南路）","latitude":"31.226854","longitude":"121.477977"},"show_info":{"show_id":"250","performance_id":"75","show_name":"2025-09-01 周一 08:00","show_start_time":"2025-09-01 08:00:00","show_end_time":"2025-09-01 10:00:00","show_type":"1","show_seat_type":"1","show_real_name_limit":"0","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2025-04-01 10:46:15","off_sale_time":"2025-08-31 00:00:00"},"category_info":{"category_id":"10004","category_name":"话剧音乐剧"}},{"id":"34","name":"朱磊业老无座线下搬单","category_id":"10002","venue_id":"7","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/84d2c832ab35b5514e8962a287d69c59631.png","ticket_status":"3","unusual_status":"0","need_real_name":"0","first_show_time":"2025-12-01 00:00:00","last_show_time":"2026-01-14 07:00:00","on_sale_time":"2025-03-11 19:23:31","max_buy_limit_per_id":"0","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"5","min_sale_price":"2.00","max_sale_price":"3.00","shelve_status":"1","third_shelve_status":"1","sort":"70","venue_info":{"venue_id":"7","venue_name":"凯迪拉克·上海音乐厅","province_title":"上海市","city_title":"黄浦区","address":"延安东路523号（近西藏南路）","latitude":"31.226854","longitude":"121.477977"},"show_info":{"show_id":"94","performance_id":"34","show_name":"2025-12-01 周一 00:00","show_start_time":"2025-12-01 00:00:00","show_end_time":"2025-12-01 17:00:00","show_type":"1","show_seat_type":"0","show_real_name_limit":"0","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2024-11-20 12:15:50","off_sale_time":"2025-11-30 00:00:00"},"category_info":{"category_id":"10002","category_name":"体育赛事"}},{"id":"8","name":"测试发票说明05","category_id":"10002","venue_id":"12","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/9353f3ac3ba4b47bd4642fcbdc393bbf101705.jpg","ticket_status":"3","unusual_status":"0","need_real_name":"0","first_show_time":"2026-01-03 00:00:00","last_show_time":"2026-01-03 02:00:00","on_sale_time":"2025-02-28 19:20:54","max_buy_limit_per_id":"6","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"33.00","max_sale_price":"33.00","shelve_status":"1","third_shelve_status":"1","sort":"60","venue_info":{"venue_id":"12","venue_name":"上海展览中心","province_title":"上海市","city_title":"静安区","address":"延安中路1000号","latitude":"31.224714","longitude":"121.452558"},"show_info":{"show_id":"16","performance_id":"8","show_name":"2026-01-03 周六 00:00","show_start_time":"2026-01-03 00:00:00","show_end_time":"2026-01-03 02:00:00","show_type":"1","show_seat_type":"0","show_real_name_limit":"0","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2025-01-14 15:12:06","off_sale_time":"2026-01-03 00:00:00"},"category_info":{"category_id":"10002","category_name":"体育赛事"}},{"id":"23","name":"【外部分销对接专用，不可编辑】编辑请联系上单-新选座-非实名制","category_id":"10007","venue_id":"8","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/d7db73c1934ce8fcb5c4003afe3522b594040.jpg","ticket_status":"3","unusual_status":"0","need_real_name":"1","first_show_time":"2028-01-01 00:00:00","last_show_time":"2028-01-01 01:00:00","on_sale_time":"2024-07-29 16:43:00","max_buy_limit_per_id":"6","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"50.00","max_sale_price":"165.00","shelve_status":"1","third_shelve_status":"1","sort":"60","venue_info":{"venue_id":"8","venue_name":"梅赛德斯-奔驰文化中心","province_title":"上海市","city_title":"浦东新区","address":"世博大道1200号","latitude":"31.188952","longitude":"121.493201"},"show_info":{"show_id":"42","performance_id":"23","show_name":"2028-01-01 周六 00:00","show_start_time":"2028-01-01 00:00:00","show_end_time":"2028-01-01 01:00:00","show_type":"1","show_seat_type":"1","show_real_name_limit":"1","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2024-07-29 16:40:00","off_sale_time":"2028-01-01 00:00:00"},"category_info":{"category_id":"10007","category_name":"亲子演出"}},{"id":"48","name":"朱磊业新配座套票配座","category_id":"10001","venue_id":"7","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/84d2c832ab35b5514e8962a287d69c59631.png","ticket_status":"3","unusual_status":"0","need_real_name":"0","first_show_time":"2026-01-02 00:00:00","last_show_time":"2026-01-02 04:00:00","on_sale_time":"2025-01-09 11:28:59","max_buy_limit_per_id":"0","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"2.00","max_sale_price":"3.00","shelve_status":"1","third_shelve_status":"1","sort":"60","venue_info":{"venue_id":"7","venue_name":"凯迪拉克·上海音乐厅","province_title":"上海市","city_title":"黄浦区","address":"延安东路523号（近西藏南路）","latitude":"31.226854","longitude":"121.477977"},"show_info":{"show_id":"179","performance_id":"48","show_name":"2026-01-02 周五 00:00","show_start_time":"2026-01-02 00:00:00","show_end_time":"2026-01-02 04:00:00","show_type":"1","show_seat_type":"1","show_real_name_limit":"9999","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2025-01-20 16:24:00","off_sale_time":"2026-01-02 00:00:00"},"category_info":{"category_id":"10001","category_name":"演唱会"}},{"id":"51","name":"朱磊业新无座快递配送转其它","category_id":"10001","venue_id":"13","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/84d2c832ab35b5514e8962a287d69c59631.png","ticket_status":"12","unusual_status":"3","need_real_name":"0","first_show_time":"2026-01-01 00:00:00","last_show_time":"2028-12-15 02:00:00","on_sale_time":"2024-12-13 17:47:21","max_buy_limit_per_id":"0","max_buy_limit_per_order":"20","min_buy_limit_per_order":"0","max_buy_limit_per_user":"1","min_sale_price":"2.00","max_sale_price":"6.00","shelve_status":"1","third_shelve_status":"1","sort":"60","venue_info":{"venue_id":"13","venue_name":"虹桥天地演艺中心","province_title":"上海市","city_title":"闵行区","address":"绍虹路33号","latitude":"31.19331","longitude":"121.31583"},"show_info":{"show_id":"182","performance_id":"51","show_name":"2026-01-01 周四 00:00","show_start_time":"2026-01-01 00:00:00","show_end_time":"2026-01-01 03:00:00","show_type":"1","show_seat_type":"0","show_real_name_limit":"0","max_buy_limit_per_order":"20","show_unusual_status":"1","on_sale_time":"2024-12-24 15:57:38","off_sale_time":"2026-01-01 00:00:00"},"category_info":{"category_id":"10001","category_name":"演唱会"}},{"id":"53","name":"小红书推送商品验证（老项目喽）","category_id":"10001","venue_id":"7","poster_url":"https:\/\/p1.meituan.net\/myvideodistribute\/6563cbda84d54511adb254e69b0c8001239854.jpg","ticket_status":"3","unusual_status":"0","need_real_name":"0","first_show_time":"2025-07-30 11:00:00","last_show_time":"2025-09-30 20:00:00","on_sale_time":"2025-02-11 13:00:00","max_buy_limit_per_id":"0","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"11.00","max_sale_price":"367.00","shelve_status":"1","third_shelve_status":"1","sort":"60","venue_info":{"venue_id":"7","venue_name":"凯迪拉克·上海音乐厅","province_title":"上海市","city_title":"黄浦区","address":"延安东路523号（近西藏南路）","latitude":"31.226854","longitude":"121.477977"},"show_info":{"show_id":"312","performance_id":"53","show_name":"2025-07-30 周三 11:00","show_start_time":"2025-07-30 11:00:00","show_end_time":"2025-07-30 19:00:00","show_type":"1","show_seat_type":"0","show_real_name_limit":"0","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2025-06-26 14:33:53","off_sale_time":"2025-07-30 11:00:00"},"category_info":{"category_id":"10001","category_name":"演唱会"}},{"id":"108","name":"场馆---项目开售时间校验优化","category_id":"10002","venue_id":"13","poster_url":"https:\/\/p1.meituan.net\/myvideodistribute\/54c4fc8913710b6cbd587f52bce3cb4a305726.jpg","ticket_status":"3","unusual_status":"0","need_real_name":"0","first_show_time":"2025-07-08 00:00:00","last_show_time":"2025-07-15 01:00:00","on_sale_time":"2025-06-10 19:45:00","max_buy_limit_per_id":"6","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"110.00","max_sale_price":"110.00","shelve_status":"1","third_shelve_status":"1","sort":"60","venue_info":{"venue_id":"13","venue_name":"虹桥天地演艺中心","province_title":"上海市","city_title":"闵行区","address":"绍虹路33号","latitude":"31.19331","longitude":"121.31583"},"show_info":{"show_id":"341","performance_id":"108","show_name":"2025-07-08 周二 00:00","show_start_time":"2025-07-08 00:00:00","show_end_time":"2025-07-08 01:00:00","show_type":"1","show_seat_type":"0","show_real_name_limit":"0","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2025-06-10 19:20:54","off_sale_time":"2025-07-08 00:00:00"},"category_info":{"category_id":"10002","category_name":"体育赛事"}},{"id":"4","name":"三方导码","category_id":"10001","venue_id":"8","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/680352446992ad3846ab6542da1d1400288377.jpg","ticket_status":"3","unusual_status":"0","need_real_name":"1","first_show_time":"2025-11-06 00:00:00","last_show_time":"2025-11-06 01:00:00","on_sale_time":"2024-11-06 15:30:47","max_buy_limit_per_id":"6","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"2.00","max_sale_price":"3.00","shelve_status":"1","third_shelve_status":"1","sort":"50","venue_info":{"venue_id":"8","venue_name":"梅赛德斯-奔驰文化中心","province_title":"上海市","city_title":"浦东新区","address":"世博大道1200号","latitude":"31.188952","longitude":"121.493201"},"show_info":{"show_id":"10","performance_id":"4","show_name":"2025-11-06 周四 00:00","show_start_time":"2025-11-06 00:00:00","show_end_time":"2025-11-06 01:00:00","show_type":"1","show_seat_type":"0","show_real_name_limit":"0","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2024-11-06 15:29:42","off_sale_time":"2025-11-06 00:00:00"},"category_info":{"category_id":"10001","category_name":"演唱会"}},{"id":"9","name":"20241202分销配座测试","category_id":"10002","venue_id":"13","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/bfb0c3a9cc0695646bbee5de5288ed9c950360.jpg","ticket_status":"3","unusual_status":"0","need_real_name":"0","first_show_time":"2025-12-01 00:00:00","last_show_time":"2025-12-11 02:00:00","on_sale_time":"2025-02-14 15:46:50","max_buy_limit_per_id":"0","max_buy_limit_per_order":"20","min_buy_limit_per_order":"0","max_buy_limit_per_user":"99999","min_sale_price":"0.00","max_sale_price":"1056.00","shelve_status":"1","third_shelve_status":"1","sort":"50","venue_info":{"venue_id":"13","venue_name":"虹桥天地演艺中心","province_title":"上海市","city_title":"闵行区","address":"绍虹路33号","latitude":"31.19331","longitude":"121.31583"},"show_info":{"show_id":"17","performance_id":"9","show_name":"2025-12-01 周一 00:00","show_start_time":"2025-12-01 00:00:00","show_end_time":"2025-12-01 01:00:00","show_type":"1","show_seat_type":"1","show_real_name_limit":"0","max_buy_limit_per_order":"20","show_unusual_status":"0","on_sale_time":"2024-12-02 14:05:56","off_sale_time":"2025-12-01 00:00:00"},"category_info":{"category_id":"10002","category_name":"体育赛事"}},{"id":"10","name":"老上单实名制","category_id":"10001","venue_id":"7","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/cabb76c33aad7247623a0a827cec6a7e8066.jpg","ticket_status":"3","unusual_status":"0","need_real_name":"1","first_show_time":"2025-09-01 02:00:00","last_show_time":"2025-09-01 04:00:00","on_sale_time":"2025-04-30 23:00:00","max_buy_limit_per_id":"6","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"2.00","max_sale_price":"2.00","shelve_status":"1","third_shelve_status":"1","sort":"50","venue_info":{"venue_id":"7","venue_name":"凯迪拉克·上海音乐厅","province_title":"上海市","city_title":"黄浦区","address":"延安东路523号（近西藏南路）","latitude":"31.226854","longitude":"121.477977"},"show_info":{"show_id":"28","performance_id":"10","show_name":"2025-09-01 周一 02:00","show_start_time":"2025-09-01 02:00:00","show_end_time":"2025-09-01 04:00:00","show_type":"1","show_seat_type":"0","show_real_name_limit":"1","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2025-02-24 10:33:25","off_sale_time":"2025-09-01 02:00:00"},"category_info":{"category_id":"10001","category_name":"演唱会"}},{"id":"12","name":"【售卖场】2.11无座-2026.12.28","category_id":"10001","venue_id":"7","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/03fdb9f41adcf253ff9211097ab444421598915.png","ticket_status":"3","unusual_status":"0","need_real_name":"1","first_show_time":"2026-12-28 19:00:00","last_show_time":"2026-12-31 23:00:00","on_sale_time":"2025-02-11 14:53:19","max_buy_limit_per_id":"10","max_buy_limit_per_order":"4","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"748.00","max_sale_price":"2178.00","shelve_status":"1","third_shelve_status":"1","sort":"50","venue_info":{"venue_id":"7","venue_name":"凯迪拉克·上海音乐厅","province_title":"上海市","city_title":"黄浦区","address":"延安东路523号（近西藏南路）","latitude":"31.226854","longitude":"121.477977"},"show_info":{"show_id":"30","performance_id":"12","show_name":"2026-12-28 周一 19:00","show_start_time":"2026-12-28 19:00:00","show_end_time":"2026-12-28 23:00:00","show_type":"1","show_seat_type":"0","show_real_name_limit":"1","max_buy_limit_per_order":"2","show_unusual_status":"0","on_sale_time":"2025-02-11 11:33:38","off_sale_time":"2026-12-28 00:00:00"},"category_info":{"category_id":"10001","category_name":"演唱会"}},{"id":"14","name":"zly测试渠道","category_id":"10001","venue_id":"10","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/84d2c832ab35b5514e8962a287d69c59631.png","ticket_status":"3","unusual_status":"0","need_real_name":"0","first_show_time":"2026-02-06 00:00:00","last_show_time":"2026-02-06 05:00:00","on_sale_time":"2025-03-07 00:00:00","max_buy_limit_per_id":"6","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"2.00","max_sale_price":"2.00","shelve_status":"1","third_shelve_status":"1","sort":"50","venue_info":{"venue_id":"10","venue_name":"猫眼演出321","province_title":"北京市","city_title":"朝阳区","address":"望京sohoT3B栋2楼","latitude":"39.996126","longitude":"116.480553"},"show_info":{"show_id":"34","performance_id":"14","show_name":"2026-02-06 周五 00:00","show_start_time":"2026-02-06 00:00:00","show_end_time":"2026-02-06 05:00:00","show_type":"1","show_seat_type":"1","show_real_name_limit":"0","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2025-02-17 15:57:48","off_sale_time":"2026-02-06 00:00:00"},"category_info":{"category_id":"10001","category_name":"演唱会"}},{"id":"17","name":"老无座-非大演-0227","category_id":"10004","venue_id":"8","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/d7db73c1934ce8fcb5c4003afe3522b594040.jpg","ticket_status":"3","unusual_status":"0","need_real_name":"0","first_show_time":"2025-12-31 00:00:00","last_show_time":"2025-12-31 01:00:00","on_sale_time":"2025-02-27 19:55:09","max_buy_limit_per_id":"0","max_buy_limit_per_order":"10","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"11.00","max_sale_price":"33.00","shelve_status":"1","third_shelve_status":"1","sort":"50","venue_info":{"venue_id":"8","venue_name":"梅赛德斯-奔驰文化中心","province_title":"上海市","city_title":"浦东新区","address":"世博大道1200号","latitude":"31.188952","longitude":"121.493201"},"show_info":{"show_id":"36","performance_id":"17","show_name":"2025-12-31 周三 00:00","show_start_time":"2025-12-31 00:00:00","show_end_time":"2025-12-31 01:00:00","show_type":"1","show_seat_type":"0","show_real_name_limit":"0","max_buy_limit_per_order":"10","show_unusual_status":"0","on_sale_time":"2025-02-27 19:54:03","off_sale_time":"2025-12-31 00:00:00"},"category_info":{"category_id":"10004","category_name":"话剧音乐剧"}},{"id":"19","name":"和净子账号测试项目2-0641","category_id":"10001","venue_id":"15","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/7f13fafffa5464aec7c728efca22be0a24721.jpg","ticket_status":"12","unusual_status":"1","need_real_name":"0","first_show_time":"2025-11-01 00:00:00","last_show_time":"2025-11-01 01:00:00","on_sale_time":"2025-03-07 18:35:00","max_buy_limit_per_id":"0","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"4.00","max_sale_price":"5.00","shelve_status":"1","third_shelve_status":"1","sort":"50","venue_info":{"venue_id":"15","venue_name":"北京展览馆剧场","province_title":"北京市","city_title":"西城区","address":"西直门外大街135号","latitude":"39.942007","longitude":"116.343911"},"show_info":{"show_id":"38","performance_id":"19","show_name":"2025-11-01 周六 00:00","show_start_time":"2025-11-01 00:00:00","show_end_time":"2025-11-01 01:00:00","show_type":"1","show_seat_type":"0","show_real_name_limit":"0","max_buy_limit_per_order":"6","show_unusual_status":"2","on_sale_time":"2024-12-26 19:40:48","off_sale_time":"2025-11-01 00:00:00"},"category_info":{"category_id":"10001","category_name":"演唱会"}},{"id":"21","name":"商家上单","category_id":"10001","venue_id":"17","poster_url":"https:\/\/p1.meituan.net\/myvideodistribute\/78c1cb8f72c5a9d08f7879bd1cb0f6d4279046.jpg","ticket_status":"3","unusual_status":"0","need_real_name":"0","first_show_time":"2025-11-01 00:00:00","last_show_time":"2025-11-01 05:00:00","on_sale_time":"2025-03-31 19:56:37","max_buy_limit_per_id":"0","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"136.00","max_sale_price":"136.00","shelve_status":"1","third_shelve_status":"1","sort":"50","venue_info":{"venue_id":"17","venue_name":"上海体育馆","province_title":"上海市","city_title":"徐汇区","address":"漕溪北路1111号","latitude":"31.181511","longitude":"121.438358"},"show_info":{"show_id":"39","performance_id":"21","show_name":"2025-11-01 周六 00:00","show_start_time":"2025-11-01 00:00:00","show_end_time":"2025-11-01 05:00:00","show_type":"1","show_seat_type":"0","show_real_name_limit":"6","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2025-03-03 19:27:53","off_sale_time":"2025-11-01 00:00:00"},"category_info":{"category_id":"10001","category_name":"演唱会"}},{"id":"24","name":"【外部分销对接专用，不可编辑】编辑请联系上单-选座-实名制","category_id":"10001","venue_id":"17","poster_url":"https:\/\/p1.meituan.net\/myvideodistribute\/d69af400577449d5ea329a53cbe0bb8d1185076.jpg","ticket_status":"3","unusual_status":"0","need_real_name":"1","first_show_time":"2027-01-01 19:00:00","last_show_time":"2029-01-01 21:00:00","on_sale_time":"2024-04-18 14:49:55","max_buy_limit_per_id":"6","max_buy_limit_per_order":"3","min_buy_limit_per_order":"0","max_buy_limit_per_user":"10","min_sale_price":"6.00","max_sale_price":"11.00","shelve_status":"1","third_shelve_status":"1","sort":"50","venue_info":{"venue_id":"17","venue_name":"上海体育馆","province_title":"上海市","city_title":"徐汇区","address":"漕溪北路1111号","latitude":"31.181511","longitude":"121.438358"},"show_info":{"show_id":"170","performance_id":"24","show_name":"2025国庆专场","show_start_time":"2025-10-01 15:00:00","show_end_time":"2025-10-01 18:00:00","show_type":"1","show_seat_type":"1","show_real_name_limit":"0","max_buy_limit_per_order":"3","show_unusual_status":"0","on_sale_time":"2025-04-28 14:46:21","off_sale_time":"2025-10-07 19:00:00"},"category_info":{"category_id":"10001","category_name":"演唱会"}},{"id":"29","name":"老无座-日历-0318","category_id":"10004","venue_id":"8","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/d7db73c1934ce8fcb5c4003afe3522b594040.jpg","ticket_status":"3","unusual_status":"0","need_real_name":"0","first_show_time":"2025-11-27 01:00:00","last_show_time":"2025-12-31 02:00:00","on_sale_time":"2025-03-18 11:30:41","max_buy_limit_per_id":"0","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"11.00","max_sale_price":"55.00","shelve_status":"1","third_shelve_status":"1","sort":"50","venue_info":{"venue_id":"8","venue_name":"梅赛德斯-奔驰文化中心","province_title":"上海市","city_title":"浦东新区","address":"世博大道1200号","latitude":"31.188952","longitude":"121.493201"},"show_info":{"show_id":"51","performance_id":"29","show_name":"2025-11-27 周四 01:00","show_start_time":"2025-11-27 01:00:00","show_end_time":"2025-11-27 02:00:00","show_type":"1","show_seat_type":"0","show_real_name_limit":"0","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2025-03-18 11:29:39","off_sale_time":"2025-11-27 01:00:00"},"category_info":{"category_id":"10004","category_name":"话剧音乐剧"}}]}}[0m [32m INFO - 2025-07-07 11:06:48,653 - assertion_utils.py:35 -[assertion_utils:status_code_assert] - 状态码断言成功：接口实际返回状态码 200 == 200[0m [32m INFO - 2025-07-07 11:06:48,656 - assertion_utils.py:94 -[assertion_utils:equal_assert] - 相等断言成功：接口实际结果 {'code': 0} == 预期结果：{'code': 0}[0m [32m INFO - 2025-07-07 11:06:48,656 - assertion_utils.py:206 -[assertion_utils:assert_result] - 测试成功[0m PASSED[32m INFO - 2025-07-07 11:06:48,663 - conftest.py:9 -[conftest:print_info] - ---------------接口测试结束---------------[0m  testcase/play/test_play.py::TestPlay::test_play[api_info4] [32m INFO - 2025-07-07 11:06:50,178 - conftest.py:7 -[conftest:print_info] - ---------------接口测试开始---------------[0m [32m INFO - 2025-07-07 11:06:50,183 - sendrequests.py:57 -[sendrequests:execute_api_request] - 接口名称：首页广告位[0m [32m INFO - 2025-07-07 11:06:50,183 - sendrequests.py:58 -[sendrequests:execute_api_request] - 请求地址：http://*************/api/show/ad/select_list[0m [32m INFO - 2025-07-07 11:06:50,183 - sendrequests.py:59 -[sendrequests:execute_api_request] - 请求方式：POST[0m [32m INFO - 2025-07-07 11:06:50,183 - sendrequests.py:60 -[sendrequests:execute_api_request] - 请求头：{'Content-Type': 'application/x-www-form-urlencoded', 'Connection': 'Keep-Alive', 'Accept-Encoding': 'gzip'}[0m [32m INFO - 2025-07-07 11:06:50,183 - sendrequests.py:61 -[sendrequests:execute_api_request] - 测试用例名：获取首页广告列表[0m [32m INFO - 2025-07-07 11:06:50,183 - sendrequests.py:62 -[sendrequests:execute_api_request] - cookies值：None[0m [32m INFO - 2025-07-07 11:06:50,183 - sendrequests.py:67 -[sendrequests:execute_api_request] - 参数类型：data[0m [32m INFO - 2025-07-07 11:06:50,184 - sendrequests.py:69 -[sendrequests:execute_api_request] - 请求参数：{"channelCode": "*********", "token": "HIx4GcZCV9rNSVvCDHxLnTd9lqe4T+yvqwm3lMQTCY7gdc7GaMgINq64QVyeD0XxEC+AHo9ONgxjBFMyfqlPosl+78aoOuYgI9gLHmRnbRGmlT2bSdpg/kI22Kc6vOUF,*********"}[0m [32m INFO - 2025-07-07 11:06:50,471 - apiutils_business.py:179 -[apiutils_business:execute_test_cases] - 接口实际返回结果：{"code":0,"msg":"","data":[{"id":"31","name":"端午活动","position_type":"home_middle","image_url":"upload\/20250529\/102446xt4IfG0Kf0.jpg","link_type":"customize","link_value":"https:\/\/www.msn.cn\/zh-cn\/news\/other\/%E7%AB%AF%E5%8D%88%E6%B0%9B%E5%9B%B4%E6%84%9F%E6%8B%89%E6%BB%A1-%E8%B5%9B%E9%BE%99%E8%88%9F-%E7%B2%BD%E5%AD%90-%E9%A6%99%E5%8C%85%E9%83%BD%E6%9C%89%E4%BA%86%E6%96%B0%E8%8A%B1%E6%A0%B7\/ar-AA1Fz2TU?ocid=msedgntp&pc=CNNDDB","sort_value":"99","link_type_desc":"自定义","image_url_show":"http:\/\/*************\/resource\/play\/upload\/20250529\/102446xt4IfG0Kf0.jpg"},{"id":"32","name":"陈慧娴","position_type":"home_middle","image_url":"upload\/20250529\/105313SLTl4LjSBR.png","link_type":"performance","link_value":"34","sort_value":"67","link_type_desc":"演出项目","image_url_show":"http:\/\/*************\/resource\/play\/upload\/20250529\/105313SLTl4LjSBR.png"},{"id":"40","name":"【专用】深圳大运中心体育场","position_type":"home_middle","image_url":"upload\/20250529\/13382349PX4yl_Dk.png","link_type":"performance","link_value":"69","sort_value":"66","link_type_desc":"演出项目","image_url_show":"http:\/\/*************\/resource\/play\/upload\/20250529\/13382349PX4yl_Dk.png"},{"id":"45","name":"luowt","position_type":"home_middle","image_url":"upload\/20250630\/144818XuaAA54TKR.png","link_type":"performance","link_value":"106","sort_value":"23","link_type_desc":"演出项目","image_url_show":"http:\/\/*************\/resource\/play\/upload\/20250630\/144818XuaAA54TKR.png"},{"id":"42","name":"wk新建的没有推广时间的","position_type":"home_middle","image_url":"upload\/20250529\/1732116kcMEpIoAV.png","link_type":"customize","link_value":"http:\/\/www.baoLY.com","sort_value":"22","link_type_desc":"自定义","image_url_show":"http:\/\/*************\/resource\/play\/upload\/20250529\/1732116kcMEpIoAV.png"},{"id":"30","name":"【勿动】测试城市","position_type":"home_middle","image_url":"upload\/20250528\/174625-aUjLRKWr1.jpg","link_type":"category","link_value":"10002","sort_value":"0","link_type_desc":"演出分类","image_url_show":"http:\/\/*************\/resource\/play\/upload\/20250528\/174625-aUjLRKWr1.jpg"},{"id":"29","name":"【勿动】QQR测试","position_type":"home_middle","image_url":"upload\/20250528\/173340QP-ZIAp90D.png","link_type":"performance","link_value":"26","sort_value":"-1","link_type_desc":"演出项目","image_url_show":"http:\/\/*************\/resource\/play\/upload\/20250528\/173340QP-ZIAp90D.png"}]}[0m [32m INFO - 2025-07-07 11:06:50,480 - assertion_utils.py:35 -[assertion_utils:status_code_assert] - 状态码断言成功：接口实际返回状态码 200 == 200[0m [32m INFO - 2025-07-07 11:06:50,481 - assertion_utils.py:94 -[assertion_utils:equal_assert] - 相等断言成功：接口实际结果 {'code': 0} == 预期结果：{'code': 0}[0m [32m INFO - 2025-07-07 11:06:50,482 - assertion_utils.py:206 -[assertion_utils:assert_result] - 测试成功[0m PASSED[32m INFO - 2025-07-07 11:06:50,492 - conftest.py:9 -[conftest:print_info] - ---------------接口测试结束---------------[0m  testcase/play/test_play.py::TestPlay::test_play[api_info5] [32m INFO - 2025-07-07 11:06:52,004 - conftest.py:7 -[conftest:print_info] - ---------------接口测试开始---------------[0m [32m INFO - 2025-07-07 11:06:52,027 - sendrequests.py:57 -[sendrequests:execute_api_request] - 接口名称：演唱会分类[0m [32m INFO - 2025-07-07 11:06:52,027 - sendrequests.py:58 -[sendrequests:execute_api_request] - 请求地址：http://*************/api/show/info/performance_search[0m [32m INFO - 2025-07-07 11:06:52,027 - sendrequests.py:59 -[sendrequests:execute_api_request] - 请求方式：POST[0m [32m INFO - 2025-07-07 11:06:52,027 - sendrequests.py:60 -[sendrequests:execute_api_request] - 请求头：{'Content-Type': 'application/x-www-form-urlencoded', 'Connection': 'Keep-Alive', 'Accept-Encoding': 'gzip'}[0m [32m INFO - 2025-07-07 11:06:52,028 - sendrequests.py:61 -[sendrequests:execute_api_request] - 测试用例名：按类型搜索=演唱会[0m [32m INFO - 2025-07-07 11:06:52,028 - sendrequests.py:62 -[sendrequests:execute_api_request] - cookies值：None[0m [32m INFO - 2025-07-07 11:06:52,028 - sendrequests.py:67 -[sendrequests:execute_api_request] - 参数类型：data[0m [32m INFO - 2025-07-07 11:06:52,028 - sendrequests.py:69 -[sendrequests:execute_api_request] - 请求参数：{"category_id": "10001", "channelCode": "*********", "token": "HIx4GcZCV9rNSVvCDHxLnTd9lqe4T+yvqwm3lMQTCY7gdc7GaMgINq64QVyeD0XxEC+AHo9ONgxjBFMyfqlPosl+78aoOuYgI9gLHmRnbRGmlT2bSdpg/kI22Kc6vOUF,*********"}[0m [32m INFO - 2025-07-07 11:06:52,264 - apiutils_business.py:179 -[apiutils_business:execute_test_cases] - 接口实际返回结果：{"code":0,"msg":"","data":{"total":27,"list":[{"id":"26","name":"老无座-0313","category_id":"10001","venue_id":"8","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/d7db73c1934ce8fcb5c4003afe3522b594040.jpg","ticket_status":"3","unusual_status":"0","need_real_name":"0","first_show_time":"2025-08-30 20:00:00","last_show_time":"2025-12-01 02:00:00","on_sale_time":"2025-03-13 19:00:00","max_buy_limit_per_id":"0","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"4.00","max_sale_price":"55.00","shelve_status":"1","third_shelve_status":"1","sort":"210","venue_info":{"venue_id":"8","venue_name":"梅赛德斯-奔驰文化中心","province_title":"上海市","city_title":"浦东新区","address":"世博大道1200号","latitude":"31.188952","longitude":"121.493201"},"show_info":{"show_id":"217","performance_id":"26","show_name":"2025-08-30 周六 20:00","show_start_time":"2025-08-30 20:00:00","show_end_time":"2025-08-30 22:00:00","show_type":"1","show_seat_type":"0","show_real_name_limit":"0","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2025-05-20 16:46:26","off_sale_time":"2025-08-30 20:00:00"},"category_info":{"category_id":"10001","category_name":"演唱会"}},{"id":"22","name":"【外部分销对接专用，不可编辑】编辑请联系上单-新选座-实名制","category_id":"10001","venue_id":"8","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/d7db73c1934ce8fcb5c4003afe3522b594040.jpg","ticket_status":"3","unusual_status":"0","need_real_name":"1","first_show_time":"2025-12-31 01:00:00","last_show_time":"2028-01-01 01:00:00","on_sale_time":"2025-04-22 16:05:43","max_buy_limit_per_id":"6","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"2.00","max_sale_price":"6.00","shelve_status":"1","third_shelve_status":"1","sort":"90","venue_info":{"venue_id":"8","venue_name":"梅赛德斯-奔驰文化中心","province_title":"上海市","city_title":"浦东新区","address":"世博大道1200号","latitude":"31.188952","longitude":"121.493201"},"show_info":{"show_id":"40","performance_id":"22","show_name":"2025-12-31 周三 01:00","show_start_time":"2025-12-31 01:00:00","show_end_time":"2025-12-31 02:00:00","show_type":"1","show_seat_type":"1","show_real_name_limit":"1","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2024-12-10 13:18:31","off_sale_time":"2025-12-31 01:00:00"},"category_info":{"category_id":"10001","category_name":"演唱会"}},{"id":"48","name":"朱磊业新配座套票配座","category_id":"10001","venue_id":"7","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/84d2c832ab35b5514e8962a287d69c59631.png","ticket_status":"3","unusual_status":"0","need_real_name":"0","first_show_time":"2026-01-02 00:00:00","last_show_time":"2026-01-02 04:00:00","on_sale_time":"2025-01-09 11:28:59","max_buy_limit_per_id":"0","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"2.00","max_sale_price":"3.00","shelve_status":"1","third_shelve_status":"1","sort":"60","venue_info":{"venue_id":"7","venue_name":"凯迪拉克·上海音乐厅","province_title":"上海市","city_title":"黄浦区","address":"延安东路523号（近西藏南路）","latitude":"31.226854","longitude":"121.477977"},"show_info":{"show_id":"179","performance_id":"48","show_name":"2026-01-02 周五 00:00","show_start_time":"2026-01-02 00:00:00","show_end_time":"2026-01-02 04:00:00","show_type":"1","show_seat_type":"1","show_real_name_limit":"9999","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2025-01-20 16:24:00","off_sale_time":"2026-01-02 00:00:00"},"category_info":{"category_id":"10001","category_name":"演唱会"}},{"id":"51","name":"朱磊业新无座快递配送转其它","category_id":"10001","venue_id":"13","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/84d2c832ab35b5514e8962a287d69c59631.png","ticket_status":"12","unusual_status":"3","need_real_name":"0","first_show_time":"2026-01-01 00:00:00","last_show_time":"2028-12-15 02:00:00","on_sale_time":"2024-12-13 17:47:21","max_buy_limit_per_id":"0","max_buy_limit_per_order":"20","min_buy_limit_per_order":"0","max_buy_limit_per_user":"1","min_sale_price":"2.00","max_sale_price":"6.00","shelve_status":"1","third_shelve_status":"1","sort":"60","venue_info":{"venue_id":"13","venue_name":"虹桥天地演艺中心","province_title":"上海市","city_title":"闵行区","address":"绍虹路33号","latitude":"31.19331","longitude":"121.31583"},"show_info":{"show_id":"182","performance_id":"51","show_name":"2026-01-01 周四 00:00","show_start_time":"2026-01-01 00:00:00","show_end_time":"2026-01-01 03:00:00","show_type":"1","show_seat_type":"0","show_real_name_limit":"0","max_buy_limit_per_order":"20","show_unusual_status":"1","on_sale_time":"2024-12-24 15:57:38","off_sale_time":"2026-01-01 00:00:00"},"category_info":{"category_id":"10001","category_name":"演唱会"}},{"id":"53","name":"小红书推送商品验证（老项目喽）","category_id":"10001","venue_id":"7","poster_url":"https:\/\/p1.meituan.net\/myvideodistribute\/6563cbda84d54511adb254e69b0c8001239854.jpg","ticket_status":"3","unusual_status":"0","need_real_name":"0","first_show_time":"2025-07-30 11:00:00","last_show_time":"2025-09-30 20:00:00","on_sale_time":"2025-02-11 13:00:00","max_buy_limit_per_id":"0","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"11.00","max_sale_price":"367.00","shelve_status":"1","third_shelve_status":"1","sort":"60","venue_info":{"venue_id":"7","venue_name":"凯迪拉克·上海音乐厅","province_title":"上海市","city_title":"黄浦区","address":"延安东路523号（近西藏南路）","latitude":"31.226854","longitude":"121.477977"},"show_info":{"show_id":"312","performance_id":"53","show_name":"2025-07-30 周三 11:00","show_start_time":"2025-07-30 11:00:00","show_end_time":"2025-07-30 19:00:00","show_type":"1","show_seat_type":"0","show_real_name_limit":"0","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2025-06-26 14:33:53","off_sale_time":"2025-07-30 11:00:00"},"category_info":{"category_id":"10001","category_name":"演唱会"}},{"id":"4","name":"三方导码","category_id":"10001","venue_id":"8","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/680352446992ad3846ab6542da1d1400288377.jpg","ticket_status":"3","unusual_status":"0","need_real_name":"1","first_show_time":"2025-11-06 00:00:00","last_show_time":"2025-11-06 01:00:00","on_sale_time":"2024-11-06 15:30:47","max_buy_limit_per_id":"6","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"2.00","max_sale_price":"3.00","shelve_status":"1","third_shelve_status":"1","sort":"50","venue_info":{"venue_id":"8","venue_name":"梅赛德斯-奔驰文化中心","province_title":"上海市","city_title":"浦东新区","address":"世博大道1200号","latitude":"31.188952","longitude":"121.493201"},"show_info":{"show_id":"10","performance_id":"4","show_name":"2025-11-06 周四 00:00","show_start_time":"2025-11-06 00:00:00","show_end_time":"2025-11-06 01:00:00","show_type":"1","show_seat_type":"0","show_real_name_limit":"0","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2024-11-06 15:29:42","off_sale_time":"2025-11-06 00:00:00"},"category_info":{"category_id":"10001","category_name":"演唱会"}},{"id":"10","name":"老上单实名制","category_id":"10001","venue_id":"7","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/cabb76c33aad7247623a0a827cec6a7e8066.jpg","ticket_status":"3","unusual_status":"0","need_real_name":"1","first_show_time":"2025-09-01 02:00:00","last_show_time":"2025-09-01 04:00:00","on_sale_time":"2025-04-30 23:00:00","max_buy_limit_per_id":"6","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"2.00","max_sale_price":"2.00","shelve_status":"1","third_shelve_status":"1","sort":"50","venue_info":{"venue_id":"7","venue_name":"凯迪拉克·上海音乐厅","province_title":"上海市","city_title":"黄浦区","address":"延安东路523号（近西藏南路）","latitude":"31.226854","longitude":"121.477977"},"show_info":{"show_id":"28","performance_id":"10","show_name":"2025-09-01 周一 02:00","show_start_time":"2025-09-01 02:00:00","show_end_time":"2025-09-01 04:00:00","show_type":"1","show_seat_type":"0","show_real_name_limit":"1","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2025-02-24 10:33:25","off_sale_time":"2025-09-01 02:00:00"},"category_info":{"category_id":"10001","category_name":"演唱会"}},{"id":"12","name":"【售卖场】2.11无座-2026.12.28","category_id":"10001","venue_id":"7","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/03fdb9f41adcf253ff9211097ab444421598915.png","ticket_status":"3","unusual_status":"0","need_real_name":"1","first_show_time":"2026-12-28 19:00:00","last_show_time":"2026-12-31 23:00:00","on_sale_time":"2025-02-11 14:53:19","max_buy_limit_per_id":"10","max_buy_limit_per_order":"4","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"748.00","max_sale_price":"2178.00","shelve_status":"1","third_shelve_status":"1","sort":"50","venue_info":{"venue_id":"7","venue_name":"凯迪拉克·上海音乐厅","province_title":"上海市","city_title":"黄浦区","address":"延安东路523号（近西藏南路）","latitude":"31.226854","longitude":"121.477977"},"show_info":{"show_id":"30","performance_id":"12","show_name":"2026-12-28 周一 19:00","show_start_time":"2026-12-28 19:00:00","show_end_time":"2026-12-28 23:00:00","show_type":"1","show_seat_type":"0","show_real_name_limit":"1","max_buy_limit_per_order":"2","show_unusual_status":"0","on_sale_time":"2025-02-11 11:33:38","off_sale_time":"2026-12-28 00:00:00"},"category_info":{"category_id":"10001","category_name":"演唱会"}},{"id":"14","name":"zly测试渠道","category_id":"10001","venue_id":"10","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/84d2c832ab35b5514e8962a287d69c59631.png","ticket_status":"3","unusual_status":"0","need_real_name":"0","first_show_time":"2026-02-06 00:00:00","last_show_time":"2026-02-06 05:00:00","on_sale_time":"2025-03-07 00:00:00","max_buy_limit_per_id":"6","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"2.00","max_sale_price":"2.00","shelve_status":"1","third_shelve_status":"1","sort":"50","venue_info":{"venue_id":"10","venue_name":"猫眼演出321","province_title":"北京市","city_title":"朝阳区","address":"望京sohoT3B栋2楼","latitude":"39.996126","longitude":"116.480553"},"show_info":{"show_id":"34","performance_id":"14","show_name":"2026-02-06 周五 00:00","show_start_time":"2026-02-06 00:00:00","show_end_time":"2026-02-06 05:00:00","show_type":"1","show_seat_type":"1","show_real_name_limit":"0","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2025-02-17 15:57:48","off_sale_time":"2026-02-06 00:00:00"},"category_info":{"category_id":"10001","category_name":"演唱会"}},{"id":"19","name":"和净子账号测试项目2-0641","category_id":"10001","venue_id":"15","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/7f13fafffa5464aec7c728efca22be0a24721.jpg","ticket_status":"12","unusual_status":"1","need_real_name":"0","first_show_time":"2025-11-01 00:00:00","last_show_time":"2025-11-01 01:00:00","on_sale_time":"2025-03-07 18:35:00","max_buy_limit_per_id":"0","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"4.00","max_sale_price":"5.00","shelve_status":"1","third_shelve_status":"1","sort":"50","venue_info":{"venue_id":"15","venue_name":"北京展览馆剧场","province_title":"北京市","city_title":"西城区","address":"西直门外大街135号","latitude":"39.942007","longitude":"116.343911"},"show_info":{"show_id":"38","performance_id":"19","show_name":"2025-11-01 周六 00:00","show_start_time":"2025-11-01 00:00:00","show_end_time":"2025-11-01 01:00:00","show_type":"1","show_seat_type":"0","show_real_name_limit":"0","max_buy_limit_per_order":"6","show_unusual_status":"2","on_sale_time":"2024-12-26 19:40:48","off_sale_time":"2025-11-01 00:00:00"},"category_info":{"category_id":"10001","category_name":"演唱会"}}]}}[0m [32m INFO - 2025-07-07 11:06:52,268 - assertion_utils.py:35 -[assertion_utils:status_code_assert] - 状态码断言成功：接口实际返回状态码 200 == 200[0m [32m INFO - 2025-07-07 11:06:52,269 - assertion_utils.py:94 -[assertion_utils:equal_assert] - 相等断言成功：接口实际结果 {'code': 0} == 预期结果：{'code': 0}[0m [32m INFO - 2025-07-07 11:06:52,270 - assertion_utils.py:206 -[assertion_utils:assert_result] - 测试成功[0m PASSED[32m INFO - 2025-07-07 11:06:52,277 - conftest.py:9 -[conftest:print_info] - ---------------接口测试结束---------------[0m  testcase/play/test_play.py::TestPlay::test_play[api_info6] [32m INFO - 2025-07-07 11:06:53,796 - conftest.py:7 -[conftest:print_info] - ---------------接口测试开始---------------[0m [32m INFO - 2025-07-07 11:06:53,802 - sendrequests.py:57 -[sendrequests:execute_api_request] - 接口名称：**搜索**[0m [32m INFO - 2025-07-07 11:06:53,802 - sendrequests.py:58 -[sendrequests:execute_api_request] - 请求地址：http://*************/api/show/info/performance_search[0m [32m INFO - 2025-07-07 11:06:53,802 - sendrequests.py:59 -[sendrequests:execute_api_request] - 请求方式：POST[0m [32m INFO - 2025-07-07 11:06:53,802 - sendrequests.py:60 -[sendrequests:execute_api_request] - 请求头：{'Content-Type': 'application/x-www-form-urlencoded', 'Connection': 'Keep-Alive', 'Accept-Encoding': 'gzip'}[0m [32m INFO - 2025-07-07 11:06:53,802 - sendrequests.py:61 -[sendrequests:execute_api_request] - 测试用例名：按演出名称关键字搜索[0m [32m INFO - 2025-07-07 11:06:53,802 - sendrequests.py:62 -[sendrequests:execute_api_request] - cookies值：None[0m [32m INFO - 2025-07-07 11:06:53,802 - sendrequests.py:67 -[sendrequests:execute_api_request] - 参数类型：data[0m [32m INFO - 2025-07-07 11:06:53,802 - sendrequests.py:69 -[sendrequests:execute_api_request] - 请求参数：{"keyword": "演", "channelCode": "*********", "token": "HIx4GcZCV9rNSVvCDHxLnTd9lqe4T+yvqwm3lMQTCY7gdc7GaMgINq64QVyeD0XxEC+AHo9ONgxjBFMyfqlPosl+78aoOuYgI9gLHmRnbRGmlT2bSdpg/kI22Kc6vOUF,*********"}[0m [32m INFO - 2025-07-07 11:06:54,080 - apiutils_business.py:179 -[apiutils_business:execute_test_cases] - 接口实际返回结果：{"code":0,"msg":"","data":{"total":12,"list":[{"id":"17","name":"老无座-非大演-0227","category_id":"10004","venue_id":"8","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/d7db73c1934ce8fcb5c4003afe3522b594040.jpg","ticket_status":"3","unusual_status":"0","need_real_name":"0","first_show_time":"2025-12-31 00:00:00","last_show_time":"2025-12-31 01:00:00","on_sale_time":"2025-02-27 19:55:09","max_buy_limit_per_id":"0","max_buy_limit_per_order":"10","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"11.00","max_sale_price":"33.00","shelve_status":"1","third_shelve_status":"1","sort":"50","venue_info":{"venue_id":"8","venue_name":"梅赛德斯-奔驰文化中心","province_title":"上海市","city_title":"浦东新区","address":"世博大道1200号","latitude":"31.188952","longitude":"121.493201"},"show_info":{"show_id":"36","performance_id":"17","show_name":"2025-12-31 周三 00:00","show_start_time":"2025-12-31 00:00:00","show_end_time":"2025-12-31 01:00:00","show_type":"1","show_seat_type":"0","show_real_name_limit":"0","max_buy_limit_per_order":"10","show_unusual_status":"0","on_sale_time":"2025-02-27 19:54:03","off_sale_time":"2025-12-31 00:00:00"},"category_info":{"category_id":"10004","category_name":"话剧音乐剧"}},{"id":"36","name":"新创建2.0项目wt0402--大演1","category_id":"10001","venue_id":"13","poster_url":"https:\/\/p0.pipi.cn\/mediaplus\/fantasy_perform_fe\/0fa3343d52905119bf8457350de54ce83cc10.jpeg?imageMogr2\/quality\/80","ticket_status":"3","unusual_status":"0","need_real_name":"1","first_show_time":"2026-04-01 00:00:00","last_show_time":"2026-04-10 03:00:00","on_sale_time":null,"max_buy_limit_per_id":"6","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"25.00","max_sale_price":"123.00","shelve_status":"1","third_shelve_status":"1","sort":"50","venue_info":{"venue_id":"13","venue_name":"虹桥天地演艺中心","province_title":"上海市","city_title":"闵行区","address":"绍虹路33号","latitude":"31.19331","longitude":"121.31583"},"show_info":{"show_id":"98","performance_id":"36","show_name":"2026-04-01 周三 00:00","show_start_time":"2026-04-01 00:00:00","show_end_time":"2026-04-01 01:00:00","show_type":"1","show_seat_type":"0","show_real_name_limit":"1","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2025-04-02 17:54:44","off_sale_time":"2026-04-01 00:00:00"},"category_info":{"category_id":"10001","category_name":"演唱会"}},{"id":"37","name":"新创建2.0项目wt0402--大演","category_id":"10001","venue_id":"13","poster_url":"https:\/\/p0.pipi.cn\/mediaplus\/fantasy_perform_fe\/0fa3343d52905119bf8457350de54ce83cc10.jpeg?imageMogr2\/quality\/80","ticket_status":"3","unusual_status":"0","need_real_name":"1","first_show_time":"2026-04-01 00:00:00","last_show_time":"2026-05-03 02:00:00","on_sale_time":"2025-04-02 16:34:30","max_buy_limit_per_id":"6","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"13.00","max_sale_price":"17.00","shelve_status":"1","third_shelve_status":"1","sort":"50","venue_info":{"venue_id":"13","venue_name":"虹桥天地演艺中心","province_title":"上海市","city_title":"闵行区","address":"绍虹路33号","latitude":"31.19331","longitude":"121.31583"},"show_info":{"show_id":"108","performance_id":"37","show_name":"2026-04-01 周三 00:00","show_start_time":"2026-04-01 00:00:00","show_end_time":"2026-04-01 02:00:00","show_type":"1","show_seat_type":"0","show_real_name_limit":"1","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2025-04-02 16:33:14","off_sale_time":"2026-04-01 00:00:00"},"category_info":{"category_id":"10001","category_name":"演唱会"}},{"id":"65","name":"李胜素、于魁智联袂中国戏曲学院名家教授演绎梅派名剧《穆桂英挂帅》+丁蕾","category_id":"10003","venue_id":"25","poster_url":"https:\/\/p0.pipi.cn\/mediaplus\/fantasy_perform_fe\/0fa3345bc8b4a115d5f87bbcda03261e22dfb.jpg?imageMogr2\/quality\/80","ticket_status":"3","unusual_status":"0","need_real_name":"0","first_show_time":"2025-09-06 19:30:00","last_show_time":"2025-09-06 21:00:00","on_sale_time":"2025-05-22 18:20:54","max_buy_limit_per_id":"0","max_buy_limit_per_order":"9999","min_buy_limit_per_order":"0","max_buy_limit_per_user":"9999","min_sale_price":"198.00","max_sale_price":"968.00","shelve_status":"1","third_shelve_status":"1","sort":"50","venue_info":{"venue_id":"25","venue_name":"北京音乐厅","province_title":"北京市","city_title":"西城区","address":"北新华街1号","latitude":"39.906784","longitude":"116.382384"},"show_info":{"show_id":"219","performance_id":"65","show_name":"2025-09-06 周六 19:30","show_start_time":"2025-09-06 19:30:00","show_end_time":"2025-09-06 21:00:00","show_type":"1","show_seat_type":"0","show_real_name_limit":"1","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2025-05-22 12:00:00","off_sale_time":"2025-09-06 19:30:00"},"category_info":{"category_id":"10003","category_name":"戏曲艺术"}},{"id":"66","name":"陈奕迅《FEAR AND DREAMS世界巡回演唱会》-北京站+丁蕾","category_id":"10001","venue_id":"25","poster_url":"https:\/\/p0.pipi.cn\/mediaplus\/fantasy_perform_fe\/0fa3345bc8be328dd477e1f0196d9d6164381.png?imageMogr2\/quality\/80","ticket_status":"1","unusual_status":"0","need_real_name":"1","first_show_time":"2025-07-11 19:00:00","last_show_time":"2025-07-20 22:00:00","on_sale_time":null,"max_buy_limit_per_id":"6","max_buy_limit_per_order":"9999","min_buy_limit_per_order":"0","max_buy_limit_per_user":"24","min_sale_price":"748.00","max_sale_price":"2838.00","shelve_status":"1","third_shelve_status":"1","sort":"50","venue_info":{"venue_id":"25","venue_name":"北京音乐厅","province_title":"北京市","city_title":"西城区","address":"北新华街1号","latitude":"39.906784","longitude":"116.382384"},"show_info":{"show_id":"220","performance_id":"66","show_name":"2025-07-11 周五 19:00","show_start_time":"2025-07-11 19:00:00","show_end_time":"2025-07-11 22:00:00","show_type":"1","show_seat_type":"1","show_real_name_limit":"1","max_buy_limit_per_order":"4","show_unusual_status":"0","on_sale_time":"2025-05-22 17:53:40","off_sale_time":"2025-07-11 22:00:00"},"category_info":{"category_id":"10001","category_name":"演唱会"}},{"id":"80","name":"杨丽萍导演舞剧《春之祭》+dinglei","category_id":"10005","venue_id":"26","poster_url":"https:\/\/p0.pipi.cn\/mediaplus\/fantasy_perform_fe\/0fa33425e328d3d8a1ea40daaf1b74ca02eaa.png?imageMogr2\/quality\/80","ticket_status":"3","unusual_status":"0","need_real_name":"1","first_show_time":"2025-07-26 19:30:00","last_show_time":"2025-07-26 21:00:00","on_sale_time":"2025-06-17 14:31:23","max_buy_limit_per_id":"9999","max_buy_limit_per_order":"9999","min_buy_limit_per_order":"0","max_buy_limit_per_user":"9999","min_sale_price":"1.00","max_sale_price":"1162.00","shelve_status":"1","third_shelve_status":"1","sort":"50","venue_info":{"venue_id":"26","venue_name":"工人体育场","province_title":"北京市","city_title":"朝阳区","address":"工体北路与新东街交叉口西南角","latitude":"39.93069","longitude":"116.447211"},"show_info":{"show_id":"279","performance_id":"80","show_name":"2025-07-26 周六 19:30","show_start_time":"2025-07-26 19:30:00","show_end_time":"2025-07-26 21:00:00","show_type":"1","show_seat_type":"1","show_real_name_limit":"1","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2025-06-17 00:30:00","off_sale_time":"2025-07-26 19:30:00"},"category_info":{"category_id":"10005","category_name":"舞蹈芭蕾"}},{"id":"83","name":"次元盛典.热门二次元动漫ACG乐队番演唱会+丁蕾","category_id":"10006","venue_id":"25","poster_url":"https:\/\/p0.pipi.cn\/mediaplus\/fantasy_perform_fe\/0fa33425e3277e25ce25cbab374b995fae36f.png?imageMogr2\/quality\/80","ticket_status":"3","unusual_status":"0","need_real_name":"0","first_show_time":"2025-07-26 19:30:00","last_show_time":"2025-07-26 21:00:00","on_sale_time":"2025-06-17 13:47:18","max_buy_limit_per_id":"0","max_buy_limit_per_order":"9999","min_buy_limit_per_order":"0","max_buy_limit_per_user":"9999","min_sale_price":"1.00","max_sale_price":"1.00","shelve_status":"1","third_shelve_status":"1","sort":"50","venue_info":{"venue_id":"25","venue_name":"北京音乐厅","province_title":"北京市","city_title":"西城区","address":"北新华街1号","latitude":"39.906784","longitude":"116.382384"},"show_info":{"show_id":"283","performance_id":"83","show_name":"2025-07-26 周六 19:30","show_start_time":"2025-07-26 19:30:00","show_end_time":"2025-07-26 21:00:00","show_type":"1","show_seat_type":"1","show_real_name_limit":"1","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2025-06-17 00:30:00","off_sale_time":"2025-07-26 19:30:00"},"category_info":{"category_id":"10006","category_name":"音乐会"}},{"id":"85","name":"2025虚拟歌姬 IA 中国巡演13周年纪念版PARTY A GOGO巡游派对「轨迹与奇迹」广州站","category_id":"10017","venue_id":"25","poster_url":"https:\/\/p0.pipi.cn\/mediaplus\/fantasy_perform_fe\/0fa334254a177e14dca219726706cf7bca309.jpg?imageMogr2\/quality\/80","ticket_status":"2","unusual_status":"0","need_real_name":"1","first_show_time":"2025-08-10 20:00:00","last_show_time":"2025-08-10 21:40:00","on_sale_time":"2025-06-18 19:18:00","max_buy_limit_per_id":"1","max_buy_limit_per_order":"9999","min_buy_limit_per_order":"0","max_buy_limit_per_user":"2","min_sale_price":"418.00","max_sale_price":"968.00","shelve_status":"1","third_shelve_status":"1","sort":"50","venue_info":{"venue_id":"25","venue_name":"北京音乐厅","province_title":"北京市","city_title":"西城区","address":"北新华街1号","latitude":"39.906784","longitude":"116.382384"},"show_info":{"show_id":"285","performance_id":"85","show_name":"2025-08-10 周日 20:00","show_start_time":"2025-08-10 20:00:00","show_end_time":"2025-08-10 21:40:00","show_type":"1","show_seat_type":"0","show_real_name_limit":"1","max_buy_limit_per_order":"2","show_unusual_status":"0","on_sale_time":"2025-06-18 00:00:00","off_sale_time":"2025-08-10 20:00:00"},"category_info":{"category_id":"10017","category_name":"Livehouse"}},{"id":"88","name":"哈瓦那的偶像演唱团中国巡回演唱会（赖）","category_id":"10001","venue_id":"25","poster_url":"https:\/\/p0.pipi.cn\/mediaplus\/fantasy_perform_fe\/0fa334254a1ea46b1b374cf3266bdd1cc1245.jpg?imageMogr2\/quality\/80","ticket_status":"3","unusual_status":"0","need_real_name":"0","first_show_time":"2025-07-23 19:00:00","last_show_time":"2025-07-23 20:30:00","on_sale_time":"2025-06-18 15:34:46","max_buy_limit_per_id":"0","max_buy_limit_per_order":"9999","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"132.00","max_sale_price":"220.00","shelve_status":"1","third_shelve_status":"1","sort":"50","venue_info":{"venue_id":"25","venue_name":"北京音乐厅","province_title":"北京市","city_title":"西城区","address":"北新华街1号","latitude":"39.906784","longitude":"116.382384"},"show_info":{"show_id":"287","performance_id":"88","show_name":"2025-07-23 周三 19:00","show_start_time":"2025-07-23 19:00:00","show_end_time":"2025-07-23 20:30:00","show_type":"1","show_seat_type":"1","show_real_name_limit":"1","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2025-06-18 15:36:22","off_sale_time":"2025-07-23 19:00:00"},"category_info":{"category_id":"10001","category_name":"演唱会"}},{"id":"93","name":"有我·天使MAX成团演唱会+罗媛","category_id":"10017","venue_id":"25","poster_url":"https:\/\/p0.pipi.cn\/mediaplus\/fantasy_perform_fe\/0fa3342577ee323cbca2194cb125aba838a8d.jpg?imageMogr2\/quality\/80","ticket_status":"3","unusual_status":"0","need_real_name":"1","first_show_time":"2025-07-18 19:30:00","last_show_time":"2025-07-18 21:00:00","on_sale_time":"2025-06-19 20:00:00","max_buy_limit_per_id":"9999","max_buy_limit_per_order":"9999","min_buy_limit_per_order":"0","max_buy_limit_per_user":"9999","min_sale_price":"418.00","max_sale_price":"748.00","shelve_status":"1","third_shelve_status":"1","sort":"50","venue_info":{"venue_id":"25","venue_name":"北京音乐厅","province_title":"北京市","city_title":"西城区","address":"北新华街1号","latitude":"39.906784","longitude":"116.382384"},"show_info":{"show_id":"293","performance_id":"93","show_name":"2025-07-18 周五 19:30","show_start_time":"2025-07-18 19:30:00","show_end_time":"2025-07-18 21:00:00","show_type":"1","show_seat_type":"0","show_real_name_limit":"6","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2025-06-19 17:01:30","off_sale_time":"2025-07-18 19:30:00"},"category_info":{"category_id":"10017","category_name":"Livehouse"}}]}}[0m [32m INFO - 2025-07-07 11:06:54,085 - assertion_utils.py:35 -[assertion_utils:status_code_assert] - 状态码断言成功：接口实际返回状态码 200 == 200[0m [32m INFO - 2025-07-07 11:06:54,087 - assertion_utils.py:94 -[assertion_utils:equal_assert] - 相等断言成功：接口实际结果 {'code': 0} == 预期结果：{'code': 0}[0m [32m INFO - 2025-07-07 11:06:54,087 - assertion_utils.py:206 -[assertion_utils:assert_result] - 测试成功[0m PASSED[32m INFO - 2025-07-07 11:06:54,096 - conftest.py:9 -[conftest:print_info] - ---------------接口测试结束---------------[0m  testcase/play/test_play.py::TestPlay::test_play[api_info7] [32m INFO - 2025-07-07 11:06:55,611 - conftest.py:7 -[conftest:print_info] - ---------------接口测试开始---------------[0m [32m INFO - 2025-07-07 11:06:55,619 - sendrequests.py:57 -[sendrequests:execute_api_request] - 接口名称：演出项目详情[0m [32m INFO - 2025-07-07 11:06:55,619 - sendrequests.py:58 -[sendrequests:execute_api_request] - 请求地址：http://*************/api/show/info/performance_info[0m [32m INFO - 2025-07-07 11:06:55,619 - sendrequests.py:59 -[sendrequests:execute_api_request] - 请求方式：GET[0m [32m INFO - 2025-07-07 11:06:55,619 - sendrequests.py:60 -[sendrequests:execute_api_request] - 请求头：{'Content-Type': 'application/x-www-form-urlencoded', 'Connection': 'Keep-Alive', 'Accept-Encoding': 'gzip'}[0m [32m INFO - 2025-07-07 11:06:55,619 - sendrequests.py:61 -[sendrequests:execute_api_request] - 测试用例名：获取演出项目详情信息[0m [32m INFO - 2025-07-07 11:06:55,619 - sendrequests.py:62 -[sendrequests:execute_api_request] - cookies值：None[0m [32m INFO - 2025-07-07 11:06:55,619 - sendrequests.py:67 -[sendrequests:execute_api_request] - 参数类型：params[0m [32m INFO - 2025-07-07 11:06:55,619 - sendrequests.py:69 -[sendrequests:execute_api_request] - 请求参数：{"performanceId": "26", "channelCode": "*********", "token": "HIx4GcZCV9rNSVvCDHxLnTd9lqe4T+yvqwm3lMQTCY7gdc7GaMgINq64QVyeD0XxEC+AHo9ONgxjBFMyfqlPosl+78aoOuYgI9gLHmRnbRGmlT2bSdpg/kI22Kc6vOUF,*********"}[0m [32m INFO - 2025-07-07 11:06:56,141 - apiutils_business.py:179 -[apiutils_business:execute_test_cases] - 接口实际返回结果：{"code":0,"msg":"","data":{"performanceId":"26","city":{"cityId":1,"cityName":"上海"},"category":{"categoryId":1,"categoryName":"演唱会"},"venue":{"venueId":"8","name":"梅赛德斯-奔驰文化中心","address":"世博大道1200号","cityId":1,"latitude":31.188952,"longitude":121.493201},"name":"老无座-0313","posterUrl":"https:\/\/p0.meituan.net\/myvideodistribute\/d7db73c1934ce8fcb5c4003afe3522b594040.jpg","seatUrl":"","ticketNotes":"<p><strong>√演出\/活动时长<\/strong><\/p><p>以现场为准<\/p><p><br><\/p><p><strong>√最低演出曲目<\/strong><\/p><p>以现场为准<\/p><p><br><\/p><p><strong>√主要演员<\/strong><\/p><p>以现场为准<\/p><p><br><\/p><p><strong>√最低演出\/活动时长<\/strong><\/p><p>以现场为准<\/p><p><br><\/p><p><strong>√预约说明<\/strong><\/p><p>无需预约<\/p><p><br><\/p><p><strong>√儿童说明<\/strong><\/p><p>1.2米以下儿童谢绝入场，1.2米以上儿童需持票入场<\/p><p><br><\/p><p><strong>√发票说明<\/strong><\/p><p>该项目支持开具电子发票，发票由第三方主办提供，请在演出结束前通过订单详情页提交发票申请，一般演出结束后1个月左右开具，开票方名称以实际收到的发票为准。<\/p><p><br><\/p><p><strong>√异常购票<\/strong><\/p><p>为了确保广大消费者的利益，对于异常订购行为，猫眼演出有权取消相应订单并且通过系统原路退回该订单下全部票款，不予配票。异常订购行为包括但不限于：<br>（1）同一用户订购超出限购张数的订单；<br>（2）经合理判断为非真实消费者的订购行为，包括但不限于使用软件、爬虫技术等进行恶意刷票；<br>（3）通过批量相同或虚构的支付账号、收货地址、收货人、电话号码订购超出限购张数的订单。<\/p><p><br><\/p><p><strong>√禁止携带物品<\/strong><\/p><p>由于安保和版权的原因，大多数演出、展览及比赛场所禁止携带食品、饮料、专业摄录设备、打火机等物品，请您注意现场工作人员和广播的提示，予以配合。<\/p><p><br><\/p><p><strong>√付款时效提醒<\/strong><\/p><p>下单成功后需在指定时间内完成支付，未支付成功的订单，将在下单指定时间后系统自动取消，请及时刷新购票页面进行购买。<\/p><p><br><\/p><p><strong>√特殊提示<\/strong><\/p><p>因市场变化、不可抗力等客观情形以及演出票随订随售的性质，可能会出现演出变更、取消、或正式出票时票品库存不足等情况，该等情形下猫眼客服会及时与您联系并尽快退款。<\/p><p><br><\/p><p><strong>√退换政策<\/strong><\/p><p>退款<\/p><p><br><\/p><p><strong>√实名制购票<\/strong><\/p><p>无需实名制购票<\/p><p><br><\/p><p><strong>√限购说明<\/strong><\/p><p>每笔订单最多购买6张，以实际购票情况为准<\/p><p><br><\/p><p><strong>√入场规则<\/strong><\/p><p>·电子票：购买电子票的用户，可凭二维码直接入场，无需纸质门票<br><\/p><p><br><\/p>","detail":"<p>11111<\/p>","celebrityList":[],"ticketStatus":3,"shelveStatus":1,"needRealName":false,"firstShowTime":"2025-08-30 20:00:00","lastShowTime":"2025-12-01 02:00:00","sort":50,"createTime":"2025-03-13 14:19:59","onSaleTime":"","openBuyProjectLimit":{"maxBuyLimitPerId":0,"maxBuyLimitPerOrder":6,"minBuyLimitPerOrder":0,"maxBuyLimitPerUser":20},"exclusiveSales":0,"soleAgent":0,"seatType":0,"unusualStatus":0,"performanceDetailUrl":null,"minPrice":"4.00","maxPrice":"55.00","seatMode":0,"fetchTicketWayList":["电子票"],"idTypeList":null,"minDiscount":null,"payExpireTime":15,"priceRangeType":0,"conditionRefund":1,"isNewMode":0,"showInfo":[]}}[0m [32m INFO - 2025-07-07 11:06:56,144 - assertion_utils.py:35 -[assertion_utils:status_code_assert] - 状态码断言成功：接口实际返回状态码 200 == 200[0m [32m INFO - 2025-07-07 11:06:56,145 - assertion_utils.py:94 -[assertion_utils:equal_assert] - 相等断言成功：接口实际结果 {'code': 0} == 预期结果：{'code': 0}[0m [32m INFO - 2025-07-07 11:06:56,146 - assertion_utils.py:206 -[assertion_utils:assert_result] - 测试成功[0m PASSED[32m INFO - 2025-07-07 11:06:56,156 - conftest.py:9 -[conftest:print_info] - ---------------接口测试结束---------------[0m  testcase/play/test_play.py::TestPlay::test_play[api_info8] [32m INFO - 2025-07-07 11:06:57,670 - conftest.py:7 -[conftest:print_info] - ---------------接口测试开始---------------[0m [32m INFO - 2025-07-07 11:06:57,677 - sendrequests.py:57 -[sendrequests:execute_api_request] - 接口名称：演出场次列表[0m [32m INFO - 2025-07-07 11:06:57,677 - sendrequests.py:58 -[sendrequests:execute_api_request] - 请求地址：http://*************/api/show/info/performance_shows[0m [32m INFO - 2025-07-07 11:06:57,677 - sendrequests.py:59 -[sendrequests:execute_api_request] - 请求方式：GET[0m [32m INFO - 2025-07-07 11:06:57,677 - sendrequests.py:60 -[sendrequests:execute_api_request] - 请求头：{'Content-Type': 'application/x-www-form-urlencoded', 'Connection': 'Keep-Alive', 'Accept-Encoding': 'gzip'}[0m [32m INFO - 2025-07-07 11:06:57,677 - sendrequests.py:61 -[sendrequests:execute_api_request] - 测试用例名：获取演出场次列表信息[0m [32m INFO - 2025-07-07 11:06:57,677 - sendrequests.py:62 -[sendrequests:execute_api_request] - cookies值：None[0m [32m INFO - 2025-07-07 11:06:57,677 - sendrequests.py:67 -[sendrequests:execute_api_request] - 参数类型：params[0m [32m INFO - 2025-07-07 11:06:57,677 - sendrequests.py:69 -[sendrequests:execute_api_request] - 请求参数：{"performanceId": "26", "channelCode": "*********", "token": "HIx4GcZCV9rNSVvCDHxLnTd9lqe4T+yvqwm3lMQTCY7gdc7GaMgINq64QVyeD0XxEC+AHo9ONgxjBFMyfqlPosl+78aoOuYgI9gLHmRnbRGmlT2bSdpg/kI22Kc6vOUF,*********"}[0m [32m INFO - 2025-07-07 11:06:58,332 - apiutils_business.py:179 -[apiutils_business:execute_test_cases] - 接口实际返回结果：{"code":0,"msg":"","data":{"shows":[{"showId":"217","performanceId":"26","name":"2025-08-30 周六 20:00","showNote":"","startTime":"2025-08-30 20:00:00","endTime":"2025-08-30 22:00:00","showType":1,"showSeatType":0,"areaSvg":null,"areaUrl":null,"showUnusualStatus":0,"showRealNameLimit":null,"maxBuyLimitPerUser":null,"maxBuyLimitPerOrder":6,"onSaleTime":"2025-05-20 16:46:26","offSaleTime":"2025-08-30 20:00:00","saleable":1},{"showId":"48","performanceId":"26","name":"2025-12-01 周一 00:00","showNote":"","startTime":"2025-12-01 00:00:00","endTime":"2025-12-01 02:00:00","showType":1,"showSeatType":0,"areaSvg":null,"areaUrl":null,"showUnusualStatus":0,"showRealNameLimit":null,"maxBuyLimitPerUser":null,"maxBuyLimitPerOrder":6,"onSaleTime":"2025-03-13 14:21:40","offSaleTime":"2025-12-01 00:00:00","saleable":1}]}}[0m [32m INFO - 2025-07-07 11:06:58,335 - assertion_utils.py:35 -[assertion_utils:status_code_assert] - 状态码断言成功：接口实际返回状态码 200 == 200[0m [32m INFO - 2025-07-07 11:06:58,336 - assertion_utils.py:94 -[assertion_utils:equal_assert] - 相等断言成功：接口实际结果 {'code': 0} == 预期结果：{'code': 0}[0m [32m INFO - 2025-07-07 11:06:58,336 - assertion_utils.py:206 -[assertion_utils:assert_result] - 测试成功[0m PASSED[32m INFO - 2025-07-07 11:06:58,348 - conftest.py:9 -[conftest:print_info] - ---------------接口测试结束---------------[0m  testcase/play/test_play.py::TestPlay::test_play[api_info9] [32m INFO - 2025-07-07 11:06:59,864 - conftest.py:7 -[conftest:print_info] - ---------------接口测试开始---------------[0m [32m INFO - 2025-07-07 11:06:59,872 - sendrequests.py:57 -[sendrequests:execute_api_request] - 接口名称：项目下场次票品列表[0m [32m INFO - 2025-07-07 11:06:59,872 - sendrequests.py:58 -[sendrequests:execute_api_request] - 请求地址：http://*************/api/show/info/show_ticket_units[0m [32m INFO - 2025-07-07 11:06:59,872 - sendrequests.py:59 -[sendrequests:execute_api_request] - 请求方式：GET[0m [32m INFO - 2025-07-07 11:06:59,872 - sendrequests.py:60 -[sendrequests:execute_api_request] - 请求头：{'Content-Type': 'application/x-www-form-urlencoded', 'Connection': 'Keep-Alive', 'Accept-Encoding': 'gzip'}[0m [32m INFO - 2025-07-07 11:06:59,872 - sendrequests.py:61 -[sendrequests:execute_api_request] - 测试用例名：获取项目下场次票品列表信息[0m [32m INFO - 2025-07-07 11:06:59,872 - sendrequests.py:62 -[sendrequests:execute_api_request] - cookies值：None[0m [32m INFO - 2025-07-07 11:06:59,872 - sendrequests.py:67 -[sendrequests:execute_api_request] - 参数类型：params[0m [32m INFO - 2025-07-07 11:06:59,872 - sendrequests.py:69 -[sendrequests:execute_api_request] - 请求参数：{"showId": "48", "channelCode": "*********", "token": "HIx4GcZCV9rNSVvCDHxLnTd9lqe4T+yvqwm3lMQTCY7gdc7GaMgINq64QVyeD0XxEC+AHo9ONgxjBFMyfqlPosl+78aoOuYgI9gLHmRnbRGmlT2bSdpg/kI22Kc6vOUF,*********"}[0m [32m INFO - 2025-07-07 11:07:00,397 - apiutils_business.py:179 -[apiutils_business:execute_test_cases] - 接口实际返回结果：{"code":0,"msg":"","data":{"ticketUnits":[{"ticketUnitId":"214","showId":"48","name":null,"ticketName":"","minBuyLimit":1,"maxBuyLimit":6,"needRealName":false,"realNameLimit":0,"ticketLevel":"VVIP","rgb":"#FF4242","ticketPrice":"10","setNumber":1,"sellPriceList":[11,22,33,44,55,66,77,88,99,110,121,132,143,154,165,176,187,198,209,220],"limited":true,"sellStatus":3,"currentAmount":20,"baseTicketUnitId":null,"thirdTicketUnitId":16905302,"thirdBaseTicketUnitId":0},{"ticketUnitId":"215","showId":"48","name":null,"ticketName":"","minBuyLimit":1,"maxBuyLimit":6,"needRealName":false,"realNameLimit":0,"ticketLevel":"VIP","rgb":"#FF6FE4","ticketPrice":"20","setNumber":1,"sellPriceList":[22,44,66,88,110,132,154,176,198,220,242,264,286,308,330,352,374,396,418,440],"limited":true,"sellStatus":3,"currentAmount":20,"baseTicketUnitId":null,"thirdTicketUnitId":16905303,"thirdBaseTicketUnitId":0},{"ticketUnitId":"216","showId":"48","name":null,"ticketName":"","minBuyLimit":1,"maxBuyLimit":6,"needRealName":false,"realNameLimit":0,"ticketLevel":"A","rgb":"#FF8014","ticketPrice":"30","setNumber":1,"sellPriceList":[33,66,99,132,165,198,231,264,297,330,363,396,429,462,495,528,561,594,627,660],"limited":true,"sellStatus":3,"currentAmount":20,"baseTicketUnitId":null,"thirdTicketUnitId":16905304,"thirdBaseTicketUnitId":0},{"ticketUnitId":"217","showId":"48","name":null,"ticketName":"","minBuyLimit":1,"maxBuyLimit":6,"needRealName":false,"realNameLimit":0,"ticketLevel":"B","rgb":"#FFD70E","ticketPrice":"40","setNumber":1,"sellPriceList":[44,88,132,176,220,264,308,352,396,440,484,528,572,616,660,704,748,792,836,880],"limited":true,"sellStatus":3,"currentAmount":20,"baseTicketUnitId":null,"thirdTicketUnitId":16905305,"thirdBaseTicketUnitId":0},{"ticketUnitId":"218","showId":"48","name":null,"ticketName":"","minBuyLimit":1,"maxBuyLimit":6,"needRealName":false,"realNameLimit":0,"ticketLevel":"C","rgb":"#5A84FF","ticketPrice":"50","setNumber":1,"sellPriceList":[55,110,165,220,275,330,385,440,495,550,605,660,715,770,825,880,935,990,1045,1100],"limited":true,"sellStatus":3,"currentAmount":20,"baseTicketUnitId":null,"thirdTicketUnitId":16905306,"thirdBaseTicketUnitId":0},{"ticketUnitId":"219","showId":"48","name":"","ticketName":"","minBuyLimit":1,"maxBuyLimit":3,"needRealName":false,"realNameLimit":0,"ticketLevel":"VVIP","rgb":"#FF4242","ticketPrice":"10","setNumber":2,"sellPriceList":[20,40,60,80,99,119,139,159,179,198,218,238,258,278,297,317,337,357,377,396],"limited":true,"sellStatus":3,"currentAmount":20,"baseTicketUnitId":"214","thirdTicketUnitId":16905478,"thirdBaseTicketUnitId":16905302}]}}[0m [32m INFO - 2025-07-07 11:07:00,400 - assertion_utils.py:35 -[assertion_utils:status_code_assert] - 状态码断言成功：接口实际返回状态码 200 == 200[0m [32m INFO - 2025-07-07 11:07:00,401 - assertion_utils.py:94 -[assertion_utils:equal_assert] - 相等断言成功：接口实际结果 {'code': 0} == 预期结果：{'code': 0}[0m [32m INFO - 2025-07-07 11:07:00,402 - assertion_utils.py:206 -[assertion_utils:assert_result] - 测试成功[0m PASSED[32m INFO - 2025-07-07 11:07:00,412 - conftest.py:9 -[conftest:print_info] - ---------------接口测试结束---------------[0m  testcase/play/test_play.py::TestPlay::test_play[api_info10] [32m INFO - 2025-07-07 11:07:01,926 - conftest.py:7 -[conftest:print_info] - ---------------接口测试开始---------------[0m [32m INFO - 2025-07-07 11:07:01,935 - sendrequests.py:57 -[sendrequests:execute_api_request] - 接口名称：根据场次获取取票方式[0m [32m INFO - 2025-07-07 11:07:01,935 - sendrequests.py:58 -[sendrequests:execute_api_request] - 请求地址：http://*************/api/show/info/show_fetch_ticket_ways[0m [32m INFO - 2025-07-07 11:07:01,935 - sendrequests.py:59 -[sendrequests:execute_api_request] - 请求方式：GET[0m [32m INFO - 2025-07-07 11:07:01,935 - sendrequests.py:60 -[sendrequests:execute_api_request] - 请求头：{'Content-Type': 'application/x-www-form-urlencoded', 'Connection': 'Keep-Alive', 'Accept-Encoding': 'gzip'}[0m [32m INFO - 2025-07-07 11:07:01,935 - sendrequests.py:61 -[sendrequests:execute_api_request] - 测试用例名：获取该场次的取票方式[0m [32m INFO - 2025-07-07 11:07:01,936 - sendrequests.py:62 -[sendrequests:execute_api_request] - cookies值：None[0m [32m INFO - 2025-07-07 11:07:01,936 - sendrequests.py:67 -[sendrequests:execute_api_request] - 参数类型：params[0m [32m INFO - 2025-07-07 11:07:01,936 - sendrequests.py:69 -[sendrequests:execute_api_request] - 请求参数：{"showId": "48", "channelCode": "*********", "token": "HIx4GcZCV9rNSVvCDHxLnTd9lqe4T+yvqwm3lMQTCY7gdc7GaMgINq64QVyeD0XxEC+AHo9ONgxjBFMyfqlPosl+78aoOuYgI9gLHmRnbRGmlT2bSdpg/kI22Kc6vOUF,*********"}[0m [32m INFO - 2025-07-07 11:07:02,378 - apiutils_business.py:179 -[apiutils_business:execute_test_cases] - 接口实际返回结果：{"code":0,"msg":"","data":{"fetchTicketWays":[{"fetchTicketWayId":4386733,"showId":"48","fetchType":5,"needIdCard":false,"tips":"","postage":"0","onTime":"2025-03-13 14:21:40","offTime":"2025-12-01 00:00:00","fetchAddress":null,"contactMobile":null,"fetchTime":null,"feePayType":0,"feeType":0}]}}[0m [32m INFO - 2025-07-07 11:07:02,380 - assertion_utils.py:35 -[assertion_utils:status_code_assert] - 状态码断言成功：接口实际返回状态码 200 == 200[0m [32m INFO - 2025-07-07 11:07:02,381 - assertion_utils.py:94 -[assertion_utils:equal_assert] - 相等断言成功：接口实际结果 {'code': 0} == 预期结果：{'code': 0}[0m [32m INFO - 2025-07-07 11:07:02,381 - assertion_utils.py:206 -[assertion_utils:assert_result] - 测试成功[0m PASSED[32m INFO - 2025-07-07 11:07:02,400 - conftest.py:9 -[conftest:print_info] - ---------------接口测试结束---------------[0m  testcase/play/test_play.py::TestPlay::test_play[api_info11] [32m INFO - 2025-07-07 11:07:03,914 - conftest.py:7 -[conftest:print_info] - ---------------接口测试开始---------------[0m [32m INFO - 2025-07-07 11:07:03,937 - sendrequests.py:57 -[sendrequests:execute_api_request] - 接口名称：下单[0m [32m INFO - 2025-07-07 11:07:03,937 - sendrequests.py:58 -[sendrequests:execute_api_request] - 请求地址：http://*************/api/show/order/create[0m [32m INFO - 2025-07-07 11:07:03,937 - sendrequests.py:59 -[sendrequests:execute_api_request] - 请求方式：POST[0m [32m INFO - 2025-07-07 11:07:03,937 - sendrequests.py:60 -[sendrequests:execute_api_request] - 请求头：{'Content-Type': 'application/x-www-form-urlencoded', 'Connection': 'Keep-Alive', 'Accept-Encoding': 'gzip'}[0m [32m INFO - 2025-07-07 11:07:03,937 - sendrequests.py:61 -[sendrequests:execute_api_request] - 测试用例名：创建订单[0m [32m INFO - 2025-07-07 11:07:03,937 - sendrequests.py:62 -[sendrequests:execute_api_request] - cookies值：None[0m [32m INFO - 2025-07-07 11:07:03,937 - sendrequests.py:67 -[sendrequests:execute_api_request] - 参数类型：data[0m [32m INFO - 2025-07-07 11:07:03,937 - sendrequests.py:69 -[sendrequests:execute_api_request] - 请求参数：{"recipientMobile": "19877292898", "recipientName": "购票人", "recipientIdNo": "", "fetchTicketWayId": 4386733, "recipientAddressId": "", "fetchTicketWayType": 5, "performanceId": "26", "showId": "48", "salesPlanId": "214", "salesPlanCount": "1", "totalTicketPrice": 11, "deliveryPrice": "0", "orderPrice": 11, "realNameIds": "", "seatRequest": "", "channelCode": "*********", "merchant_code": "", "token": "HIx4GcZCV9rNSVvCDHxLnTd9lqe4T+yvqwm3lMQTCY7gdc7GaMgINq64QVyeD0XxEC+AHo9ONgxjBFMyfqlPosl+78aoOuYgI9gLHmRnbRGmlT2bSdpg/kI22Kc6vOUF,*********"}[0m [32m INFO - 2025-07-07 11:07:05,332 - apiutils_business.py:179 -[apiutils_business:execute_test_cases] - 接口实际返回结果：{"code":0,"msg":"","data":{"orderId":"2025070711072766223"}}[0m [32m INFO - 2025-07-07 11:07:05,337 - assertion_utils.py:35 -[assertion_utils:status_code_assert] - 状态码断言成功：接口实际返回状态码 200 == 200[0m [32m INFO - 2025-07-07 11:07:05,338 - assertion_utils.py:94 -[assertion_utils:equal_assert] - 相等断言成功：接口实际结果 {'code': 0} == 预期结果：{'code': 0}[0m [32m INFO - 2025-07-07 11:07:05,339 - assertion_utils.py:206 -[assertion_utils:assert_result] - 测试成功[0m PASSED[32m INFO - 2025-07-07 11:07:05,352 - conftest.py:9 -[conftest:print_info] - ---------------接口测试结束---------------[0m  testcase/play/test_play.py::TestPlay::test_play[api_info12] [32m INFO - 2025-07-07 11:07:06,864 - conftest.py:7 -[conftest:print_info] - ---------------接口测试开始---------------[0m [32m INFO - 2025-07-07 11:07:06,871 - sendrequests.py:57 -[sendrequests:execute_api_request] - 接口名称：取消订单[0m [32m INFO - 2025-07-07 11:07:06,872 - sendrequests.py:58 -[sendrequests:execute_api_request] - 请求地址：http://*************/api/show/order/cancel[0m [32m INFO - 2025-07-07 11:07:06,872 - sendrequests.py:59 -[sendrequests:execute_api_request] - 请求方式：POST[0m [32m INFO - 2025-07-07 11:07:06,872 - sendrequests.py:60 -[sendrequests:execute_api_request] - 请求头：{'Content-Type': 'application/x-www-form-urlencoded', 'Connection': 'Keep-Alive', 'Accept-Encoding': 'gzip'}[0m [32m INFO - 2025-07-07 11:07:06,872 - sendrequests.py:61 -[sendrequests:execute_api_request] - 测试用例名：取消订单[0m [32m INFO - 2025-07-07 11:07:06,872 - sendrequests.py:62 -[sendrequests:execute_api_request] - cookies值：None[0m [32m INFO - 2025-07-07 11:07:06,872 - sendrequests.py:67 -[sendrequests:execute_api_request] - 参数类型：data[0m [32m INFO - 2025-07-07 11:07:06,872 - sendrequests.py:69 -[sendrequests:execute_api_request] - 请求参数：{"orderId": "2025070711072766223", "channelCode": "*********", "token": "HIx4GcZCV9rNSVvCDHxLnTd9lqe4T+yvqwm3lMQTCY7gdc7GaMgINq64QVyeD0XxEC+AHo9ONgxjBFMyfqlPosl+78aoOuYgI9gLHmRnbRGmlT2bSdpg/kI22Kc6vOUF,*********"}[0m [32m INFO - 2025-07-07 11:07:08,128 - apiutils_business.py:179 -[apiutils_business:execute_test_cases] - 接口实际返回结果：{"code":0,"msg":"","data":[]}[0m [32m INFO - 2025-07-07 11:07:08,131 - assertion_utils.py:35 -[assertion_utils:status_code_assert] - 状态码断言成功：接口实际返回状态码 200 == 200[0m [32m INFO - 2025-07-07 11:07:08,132 - assertion_utils.py:94 -[assertion_utils:equal_assert] - 相等断言成功：接口实际结果 {'code': 0} == 预期结果：{'code': 0}[0m [32m INFO - 2025-07-07 11:07:08,132 - assertion_utils.py:206 -[assertion_utils:assert_result] - 测试成功[0m PASSED[32m INFO - 2025-07-07 11:07:08,142 - conftest.py:9 -[conftest:print_info] - ---------------接口测试结束---------------[0m  testcase/demo_test/test_demo.py::TestFilmOrder::test_film_order[api_info0] [32m INFO - 2025-07-07 11:07:09,658 - conftest.py:7 -[conftest:print_info] - ---------------接口测试开始---------------[0m [32m INFO - 2025-07-07 11:07:09,660 - sendrequests.py:57 -[sendrequests:execute_api_request] - 接口名称：查询影讯接口[0m [32m INFO - 2025-07-07 11:07:09,660 - sendrequests.py:58 -[sendrequests:execute_api_request] - 请求地址：http://*************:40/proxy/cnews/article/list[0m [32m INFO - 2025-07-07 11:07:09,660 - sendrequests.py:59 -[sendrequests:execute_api_request] - 请求方式：POST[0m [32m INFO - 2025-07-07 11:07:09,661 - sendrequests.py:60 -[sendrequests:execute_api_request] - 请求头：None[0m [32m INFO - 2025-07-07 11:07:09,661 - sendrequests.py:61 -[sendrequests:execute_api_request] - 测试用例名：查询上架状态的影讯[0m [32m INFO - 2025-07-07 11:07:09,661 - sendrequests.py:62 -[sendrequests:execute_api_request] - cookies值：None[0m [32m INFO - 2025-07-07 11:07:09,661 - sendrequests.py:67 -[sendrequests:execute_api_request] - 参数类型：data[0m [32m INFO - 2025-07-07 11:07:09,661 - sendrequests.py:69 -[sendrequests:execute_api_request] - 请求参数："pagination=%7B%22pageNumber%22%3A1%2C%22pageSize%22%3A50%2C%22queryParams%22%3A%7B%22title%22%3A%22%22%2C%22source%22%3A%22%22%2C%22tag_id%22%3A%22%22%2C%22status%22%3A%221%22%2C%22istop%22%3A%22%22%2C%22film_name%22%3A%22%22%2C%22startintime%22%3A%22%22%2C%22endinttime%22%3A%22%22%2C%22showtype%22%3A%22%22%2C%22startshowtime%22%3A%22%22%2C%22endshowttime%22%3A%22%22%2C%22min_program_video_status%22%3A%22%22%2C%22isplat%22%3A0%7D%7D"[0m [32m INFO - 2025-07-07 11:07:09,885 - apiutils_business.py:179 -[apiutils_business:execute_test_cases] - 接口实际返回结果：{"code":0,"msg":"操作成功","data":{"totalCount":"111","records":[{"id":292,"title":"看一下资讯缩进1","author":"11","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":579,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2023\/09\/18\/6507a64bd42b1\/o.png"},"showtime":"2023-09-18 09:21:00","uptime":"2025-06-27 14:42:31","intime":"2023-09-18 09:22:24","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":1,"ui_type":"文章","type":0,"min_program_video_status":0},{"id":291,"title":"看一下文章缩进","author":"测试","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":578,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2023\/09\/18\/6507a5c183a72\/o.jpg"},"showtime":"2023-09-18 09:19:00","uptime":"2025-06-27 14:41:44","intime":"2023-09-18 09:20:11","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":2,"ui_type":"文章","type":0,"min_program_video_status":0},{"id":299,"title":"NS2","author":"百度","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":591,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2025\/06\/19\/6853c4adeca12\/o.jpg"},"showtime":"2025-06-19 16:04:00","uptime":"2025-06-19 16:05:20","intime":"2025-06-19 16:05:20","status":1,"ui_status":"上架","film_code":"001X02212020","film_name":"图兰朵：魔咒缘起","isplat":0,"showtype":1,"ui_type":"文章","type":0,"min_program_video_status":0},{"id":277,"title":"测试通看播放按钮不显示","author":"测试12121","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":588,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2025\/04\/11\/67f8e2be2112b\/o.jpg"},"showtime":"2022-07-29 09:45:00","uptime":"2025-04-11 17:47:32","intime":"2022-07-29 09:47:49","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":2,"ui_type":"小视频","type":1,"min_program_video_status":0},{"id":276,"title":"文章类","author":"luowt","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":589,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2025\/04\/11\/67f8e4a211f3d\/o.jpg"},"showtime":"2022-07-29 09:45:00","uptime":"2025-04-11 17:45:06","intime":"2022-07-29 09:46:15","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":1,"ui_type":"文章","type":0,"min_program_video_status":0},{"id":295,"title":"测试云点播上传","author":"liean","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":583,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2024\/04\/24\/66286143f28be\/o.jpg"},"showtime":"2024-04-24 09:30:00","uptime":"2024-10-22 10:58:25","intime":"2024-04-24 09:33:14","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":1,"ui_type":"小视频","type":1,"min_program_video_status":1},{"id":248,"title":"华纳发布新片片花混剪","author":"中国电影通1","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":525,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2022\/07\/18\/62d5061ab8754\/o.png"},"showtime":"2021-01-28 10:17:00","uptime":"2024-01-25 14:43:59","intime":"2021-01-28 10:19:58","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":1,"ui_type":"小视频","type":1,"min_program_video_status":0},{"id":294,"title":"智桐反馈资讯问题","author":"测试","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":582,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2023\/09\/18\/6507a7cb1e6fb\/o.gif"},"showtime":"2023-09-18 09:27:00","uptime":"2023-11-24 15:39:09","intime":"2023-09-18 09:28:44","status":1,"ui_status":"上架","film_code":"001X04052021","film_name":"金手指","isplat":0,"showtype":2,"ui_type":"文章","type":0,"min_program_video_status":0},{"id":293,"title":"智桐周末反馈问题1","author":"测试","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":580,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2023\/09\/18\/6507a6ebdec5a\/o.png"},"showtime":"2023-09-18 09:24:00","uptime":"2023-09-18 09:27:40","intime":"2023-09-18 09:25:08","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":2,"ui_type":"文章","type":0,"min_program_video_status":0},{"id":260,"title":"type=2","author":"dd","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":540,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2022\/07\/18\/62d530a7801eb\/o.png"},"showtime":"2021-04-25 10:58:00","uptime":"2023-09-18 09:18:32","intime":"2021-04-25 10:52:29","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":2,"ui_type":"文章","type":0,"min_program_video_status":0},{"id":287,"title":"写潘多拉传奇！","author":"中影电影通","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":574,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2022\/11\/25\/638066dde28eb\/o.png"},"showtime":"2022-11-25 14:54:00","uptime":"2022-11-30 11:23:04","intime":"2022-11-25 14:55:28","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":2,"ui_type":"小视频","type":1,"min_program_video_status":1},{"id":288,"title":"主题曲《无名英雄》MV，由周笔畅深情献声致敬生活中默默无闻的平凡人。凡人微光，星火成炬！","author":"中国电影","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":575,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2022\/11\/25\/638067df3606f\/o.jpeg"},"showtime":"2022-11-25 14:59:00","uptime":"2022-11-25 14:59:46","intime":"2022-11-25 14:59:46","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":2,"ui_type":"小视频","type":1,"min_program_video_status":1},{"id":285,"title":"七十一岁的老鱼塘淤泥太深 不过今天挖机没陷车 确把公鸡陷得很深","author":"@棒哥带你开挖机确把公鸡陷得很","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":572,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2022\/11\/25\/6380634e3f558\/o.png"},"showtime":"2022-11-25 14:39:00","uptime":"2022-11-25 14:41:54","intime":"2022-11-25 14:40:20","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":2,"ui_type":"小视频","type":1,"min_program_video_status":0},{"id":284,"title":"你可以怀疑","author":"@one_live","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":571,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2022\/11\/25\/63805fe81244f\/o.png"},"showtime":"2022-11-25 14:24:00","uptime":"2022-11-25 14:26:42","intime":"2022-11-25 14:25:59","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":2,"ui_type":"小视频","type":1,"min_program_video_status":0},{"id":283,"title":"高清重温：邓卓翔世界波，国足破32年恐韩症！","author":"@尚足球","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":570,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2022\/11\/25\/6380583ac513b\/o.png"},"showtime":"2022-11-25 13:52:00","uptime":"2022-11-25 13:54:11","intime":"2022-11-25 13:53:03","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":2,"ui_type":"小视频","type":1,"min_program_video_status":0},{"id":282,"title":"李云龙成功伏击鬼子观摩团，全歼200名鬼子军官!","author":"@特务兔说剧","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":569,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2022\/11\/25\/63803b694352f\/o.png"},"showtime":"2022-11-25 11:49:00","uptime":"2022-11-25 11:50:07","intime":"2022-11-25 11:50:07","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":2,"ui_type":"小视频","type":1,"min_program_video_status":0},{"id":281,"title":"跌宕起伏！C罗点射创纪录！葡萄牙3比2加纳取开门红","author":"@小鱼人足球","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":568,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2022\/11\/25\/63803a7390a4b\/o.jpg"},"showtime":"2022-11-24 11:45:00","uptime":"2022-11-25 11:47:03","intime":"2022-11-25 11:47:03","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":2,"ui_type":"小视频","type":1,"min_program_video_status":0},{"id":280,"title":"test-logo","author":"seif","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":567,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2022\/08\/16\/62fb3000c40df\/o.jpeg"},"showtime":"2022-08-16 13:46:00","uptime":"2022-08-16 13:50:34","intime":"2022-08-16 13:50:34","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":1,"ui_type":"小视频","type":1,"min_program_video_status":1},{"id":278,"title":"视频","author":"luowt","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":565,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2022\/07\/29\/62e33ea6b2740\/o.jpg"},"showtime":"2022-07-29 09:57:00","uptime":"2022-08-16 13:45:59","intime":"2022-07-29 09:58:20","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":1,"ui_type":"小视频","type":1,"min_program_video_status":1},{"id":279,"title":"GIF","author":"luowt","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":566,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2022\/07\/29\/62e33fca0b1a5\/o.gif"},"showtime":"2022-07-29 10:01:00","uptime":"2022-07-29 10:02:56","intime":"2022-07-29 10:02:56","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":1,"ui_type":"小视频","type":1,"min_program_video_status":0},{"id":247,"title":"测试数据","author":"测试数据","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":561,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2022\/07\/28\/62e2077f83eba\/o.png"},"showtime":"2021-01-28 09:56:00","uptime":"2022-07-28 11:50:24","intime":"2021-01-28 09:56:50","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":1,"ui_type":"小视频","type":1,"min_program_video_status":0},{"id":245,"title":"压缩测试","author":"aa","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":560,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2022\/07\/28\/62e2075cabcb2\/o.png"},"showtime":"2021-01-27 18:42:00","uptime":"2022-07-28 11:49:49","intime":"2021-01-27 18:44:05","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":2,"ui_type":"小视频","type":1,"min_program_video_status":0},{"id":271,"title":"深圳晚霞","author":"luowt","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":559,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2022\/07\/28\/62e206e09f4d3\/o.png"},"showtime":"2022-07-15 15:14:00","uptime":"2022-07-28 11:47:45","intime":"2022-07-15 15:18:27","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":1,"ui_type":"小视频","type":1,"min_program_video_status":0},{"id":261,"title":"辣妹又辣了辣妹又辣了辣妹又辣了辣妹又辣了辣妹又辣了辣妹又辣了辣妹又辣了辣妹又辣了辣妹又辣了","author":"luowt","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":548,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2022\/07\/20\/62d79aeca8720\/o.jpg"},"showtime":"2021-07-07 17:01:00","uptime":"2022-07-22 09:54:53","intime":"2021-07-07 17:02:32","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":3,"ui_type":"小视频","type":1,"min_program_video_status":0},{"id":235,"title":"《疯狂原始人2》发布中文配音版预告","author":"中影电影通","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":539,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2022\/07\/18\/62d51fe1c5c6d\/o.png"},"showtime":"2020-11-13 13:50:00","uptime":"2022-07-18 18:07:31","intime":"2020-11-13 13:55:06","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":1,"ui_type":"文章","type":0,"min_program_video_status":0},{"id":236,"title":"《疯狂原始人2》发布中文配音版预告","author":"中影电影通","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":537,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2022\/07\/18\/62d506b230614\/o.png"},"showtime":"2020-11-13 13:55:00","uptime":"2022-07-18 15:07:31","intime":"2020-11-13 13:56:15","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":1,"ui_type":"小视频","type":1,"min_program_video_status":0},{"id":237,"title":"《古董局中局》曝光先导预告","author":"中影电影通","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":536,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2022\/07\/18\/62d5068c1bd56\/o.png"},"showtime":"2020-11-23 13:36:00","uptime":"2022-07-18 15:06:57","intime":"2020-11-23 13:39:28","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":1,"ui_type":"小视频","type":1,"min_program_video_status":0},{"id":238,"title":"《外太空的莫扎特》曝光概念海报","author":"中影电影通","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":535,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2022\/07\/18\/62d5068212d25\/o.png"},"showtime":"2020-11-23 13:39:00","uptime":"2022-07-18 15:06:42","intime":"2020-11-23 13:40:50","status":1,"ui_status":"上架","film_code":"001X03752019","film_name":"征途","isplat":0,"showtype":2,"ui_type":"小视频","type":1,"min_program_video_status":0},{"id":239,"title":"范娟测试1123-1","author":"范娟","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":534,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2022\/07\/18\/62d5067691601\/o.png"},"showtime":"2020-11-23 15:46:00","uptime":"2022-07-18 15:06:31","intime":"2020-11-23 15:44:03","status":1,"ui_status":"上架","film_code":"001X02042019","film_name":"悟空奇遇记","isplat":0,"showtype":1,"ui_type":"小视频","type":1,"min_program_video_status":0},{"id":240,"title":"范娟测试1126-1","author":"范娟","tag_title":"test","tag_id":17,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":533,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2022\/07\/18\/62d506669b96f\/o.png"},"showtime":"2020-11-26 11:44:00","uptime":"2022-07-18 15:06:15","intime":"2020-11-26 11:44:50","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":2,"ui_type":"小视频","type":1,"min_program_video_status":0},{"id":242,"title":"fj-12.10短视频","author":"范娟","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":532,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2022\/07\/18\/62d50657a543c\/o.png"},"showtime":"2020-12-10 14:30:00","uptime":"2022-07-18 15:06:00","intime":"2020-12-10 14:27:56","status":1,"ui_status":"上架","film_code":"001X05262020","film_name":"送你一朵小红花","isplat":0,"showtype":1,"ui_type":"小视频","type":1,"min_program_video_status":0},{"id":243,"title":"xxxxx","author":"xxxx","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":531,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2022\/07\/18\/62d5064cdc5b5\/o.png"},"showtime":"2021-01-11 15:51:00","uptime":"2022-07-18 15:05:49","intime":"2021-01-11 15:52:05","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":2,"ui_type":"小视频","type":1,"min_program_video_status":0},{"id":246,"title":"360分辨率","author":"11","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":527,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2022\/07\/18\/62d5063129a25\/o.png"},"showtime":"2021-01-27 18:51:00","uptime":"2022-07-18 15:05:21","intime":"2021-01-27 18:52:56","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":2,"ui_type":"小视频","type":1,"min_program_video_status":0},{"id":274,"title":"上班那点小事type=1","author":"zkf","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":524,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2022\/07\/18\/62d5011d5278d\/o.png"},"showtime":"2022-07-18 14:42:00","uptime":"2022-07-18 14:44:55","intime":"2022-07-18 14:44:55","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":1,"ui_type":"小视频","type":1,"min_program_video_status":0},{"id":273,"title":"type=1","author":"中国电影","tag_title":"幕后","tag_id":4,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":523,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2022\/07\/18\/62d4ff5d004fe\/o.jpg"},"showtime":"2022-07-18 14:35:00","uptime":"2022-07-18 14:36:17","intime":"2022-07-18 14:36:17","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":1,"ui_type":"文章","type":0,"min_program_video_status":0},{"id":258,"title":"showtype=3","author":"范娟","tag_title":"幕后","tag_id":4,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":518,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2022\/07\/18\/62d4fc939b274\/o.jpg"},"showtime":"2021-04-23 12:05:00","uptime":"2022-07-18 14:34:19","intime":"2021-04-23 11:59:55","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":3,"ui_type":"文章","type":0,"min_program_video_status":0},{"id":263,"title":"张同学幕后团队","author":"张同学","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":507,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2022\/07\/15\/62d103f1bcf47\/o.jpeg"},"showtime":"2021-11-30 14:48:00","uptime":"2022-07-15 14:06:46","intime":"2021-11-30 14:50:06","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":1,"ui_type":"文章","type":0,"min_program_video_status":0},{"id":270,"title":"测试原图1.2M-liugx","author":"刘庚鑫","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":503,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2022\/07\/05\/62c400711f272\/o.jpeg"},"showtime":"2022-07-05 16:38:00","uptime":"2022-07-05 17:12:18","intime":"2022-07-05 17:02:07","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":1,"ui_type":"文章","type":0,"min_program_video_status":0},{"id":269,"title":"lance小视频","author":"lance","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":496,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2022\/01\/10\/61dbab2e749c3\/o.gif"},"showtime":"2022-01-10 11:41:00","uptime":"2022-01-10 11:46:06","intime":"2022-01-10 11:43:37","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":1,"ui_type":"小视频","type":1,"min_program_video_status":0},{"id":232,"title":"范娟测试","author":"范娟","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":390,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2020\/10\/29\/5f9a368e09299\/o.png"},"showtime":"2020-10-29 11:29:00","uptime":"2020-11-13 10:29:05","intime":"2020-10-29 11:27:12","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":2,"ui_type":"文章","type":0,"min_program_video_status":0},{"id":223,"title":"建行H5","author":"范德萨","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":389,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2020\/10\/29\/5f9a3611f093e\/o.png"},"showtime":"2020-10-15 10:08:00","uptime":"2020-10-29 11:25:19","intime":"2020-10-15 10:10:38","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":1,"ui_type":"文章","type":0,"min_program_video_status":0},{"id":224,"title":"测试视频10.22","author":"测试","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":378,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2020\/10\/22\/5f913f651a4f5\/o.png"},"showtime":"2020-10-22 11:07:00","uptime":"2020-10-23 13:50:31","intime":"2020-10-22 11:10:43","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":1,"ui_type":"文章","type":0,"min_program_video_status":0},{"id":222,"title":"范娟-短视频","author":"范娟","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":376,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2020\/10\/15\/5f8813933246e\/o.png"},"showtime":"2020-10-14 09:37:00","uptime":"2020-10-16 11:13:17","intime":"2020-10-15 09:37:07","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":1,"ui_type":"文章","type":0,"min_program_video_status":0},{"id":221,"title":"范娟-纯文字","author":"范娟","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":364,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2020\/10\/15\/5f87a68067430\/o.png"},"showtime":"2020-10-14 09:33:00","uptime":"2020-10-15 09:31:52","intime":"2020-10-15 09:31:52","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":1,"ui_type":"文章","type":0,"min_program_video_status":0},{"id":296,"title":"测试测试","author":"呵呵","tag_title":"推荐","tag_id":3,"istop":0,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":584,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2024\/10\/17\/671072cb02393\/o.jpg"},"showtime":"2024-10-17 10:10:00","uptime":"2024-10-17 10:14:06","intime":"2024-10-17 10:14:06","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":2,"ui_type":"小视频","type":1,"min_program_video_status":1},{"id":289,"title":"这是视频标题","author":"丹丹","tag_title":"推荐","tag_id":3,"istop":0,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":576,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2022\/11\/29\/63857ecde065c\/o.png"},"showtime":"2022-11-29 11:34:00","uptime":"2022-11-29 11:39:27","intime":"2022-11-29 11:39:27","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":1,"ui_type":"小视频","type":1,"min_program_video_status":1},{"id":275,"title":"测试播放按钮是否显示","author":"测试12121","tag_title":"推荐","tag_id":3,"istop":0,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":562,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2022\/07\/29\/62e33bc2d2560\/o.jpg"},"showtime":"2022-07-29 09:44:00","uptime":"2022-07-29 09:45:47","intime":"2022-07-29 09:45:47","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":2,"ui_type":"文章","type":0,"min_program_video_status":0},{"id":272,"title":"showtype=1","author":"中国电影","tag_title":"推荐","tag_id":3,"istop":0,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":521,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2022\/07\/18\/62d4fe866fe15\/o.jpg"},"showtime":"2022-07-18 14:27:00","uptime":"2022-07-18 14:32:47","intime":"2022-07-18 14:32:47","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":1,"ui_type":"文章","type":0,"min_program_video_status":0},{"id":267,"title":"测试GIF-0105","author":"测试GIF-0105","tag_title":"推荐","tag_id":3,"istop":0,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":472,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2022\/01\/05\/61d536d46cc0f\/o.gif"},"showtime":"2022-01-05 14:11:00","uptime":"2022-01-07 10:25:45","intime":"2022-01-05 14:12:37","status":1,"ui_status":"上架","film_code":"001X05442020","film_name":"村里来了个洋媳妇","isplat":0,"showtype":2,"ui_type":"文章","type":0,"min_program_video_status":0},{"id":266,"title":"tea.0102","author":"tea.0102","tag_title":"推荐","tag_id":3,"istop":0,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":464,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2022\/01\/04\/61d3a5c58bac3\/o.gif"},"showtime":"2022-01-04 09:40:00","uptime":"2022-01-04 09:42:01","intime":"2022-01-04 09:42:01","status":1,"ui_status":"上架","film_code":"001X04142021","film_name":"农民院士","isplat":0,"showtype":2,"ui_type":"小视频","type":1,"min_program_video_status":0}]}}[0m [32m INFO - 2025-07-07 11:07:09,891 - assertion_utils.py:35 -[assertion_utils:status_code_assert] - 状态码断言成功：接口实际返回状态码 200 == 200[0m [32m INFO - 2025-07-07 11:07:09,894 - assertion_utils.py:94 -[assertion_utils:equal_assert] - 相等断言成功：接口实际结果 {'code': 0} == 预期结果：{'code': 0}[0m [32m INFO - 2025-07-07 11:07:09,897 - assertion_utils.py:94 -[assertion_utils:equal_assert] - 相等断言成功：接口实际结果 {'msg': '操作成功'} == 预期结果：{'msg': '操作成功'}[0m [32m INFO - 2025-07-07 11:07:09,898 - assertion_utils.py:206 -[assertion_utils:assert_result] - 测试成功[0m PASSED[32m INFO - 2025-07-07 11:07:09,913 - conftest.py:9 -[conftest:print_info] - ---------------接口测试结束---------------[0m [32m INFO - 2025-07-07 11:07:10,697 - conftest.py:201 -[conftest:pytest_terminal_summary] - 测试环境！！！！！ 详情请查看附件~~~ 自动化测试结果简单汇总，通知如下，执行结果如下： 测试用例总数：14 测试用例通过数：14 通过率：100% 测试用例失败数：0 失败率：0% 错误数量：0 错误率：0% 跳过执行数量：0 执行总时长：29.91s (0:00:29) [0m [32m INFO - 2025-07-07 11:07:10,704 - run.py:77 -[run:<module>] - 生成Allure报告...[0m Report successfully generated to .\report\report [32m INFO - 2025-07-07 11:07:13,900 - run.py:82 -[run:<module>] - Allure报告生成成功[0m [32m INFO - 2025-07-07 11:07:15,639 - run.py:93 -[run:<module>] - report.zip 已经创建在 C:\vae\python_project\zy_ApiAuto\files[0m [32m INFO - 2025-07-07 11:07:19,962 - qq_email.py:54 -[qq_email:qq_email] - 邮件发送成功！[0m   ============================= 14 passed in 29.92s =============================
INFO - 2025-07-07 11:07:20,077 - web_service.py:545 -[web_service:execute_command_simple] - 命令执行成功
INFO - 2025-07-07 13:33:32,051 - run.py:17 -[run:<module>] - Python版本: 3.12.6 (tags/v3.12.6:a4a2d2b, Sep  6 2024, 20:11:23) [MSC v.1940 64 bit (AMD64)]
INFO - 2025-07-07 13:33:32,051 - run.py:18 -[run:<module>] - Python可执行文件: C:\vae\python_project\zy_ApiAuto\.venv\Scripts\python.exe
INFO - 2025-07-07 13:33:32,051 - run.py:19 -[run:<module>] - 当前工作目录: C:\vae\python_project\zy_ApiAuto
INFO - 2025-07-07 13:33:32,051 - run.py:20 -[run:<module>] - 脚本参数: ['C:\\vae\\python_project\\zy_ApiAuto\\run.py']
INFO - 2025-07-07 13:33:32,051 - run.py:37 -[run:<module>] - 执行路径【['./testcase/play', './testcase/demo_test']】
INFO - 2025-07-07 13:33:32,051 - run.py:40 -[run:<module>] - 开始清理所有旧的报告和结果文件...
INFO - 2025-07-07 13:33:32,052 - run.py:45 -[run:<module>] - 删除旧的报告目录...
INFO - 2025-07-07 13:33:32,083 - run.py:51 -[run:<module>] - 清理temp目录下的所有旧文件...
INFO - 2025-07-07 13:33:34,115 - run.py:65 -[run:<module>] - 旧文件清理完成，开始执行测试...
INFO - 2025-07-07 13:33:34,115 - run.py:73 -[run:<module>] - pytest参数: ['./testcase/play', './testcase/demo_test', '--tb=short', '--strict-markers', '--disable-warnings']
INFO - 2025-07-07 13:33:35,155 - zy_backend_login.py:57 -[zy_backend_login:login] - 登录成功！{'msg': '登录成功', 'uid': '1942094699148967936', 'code': '0', 'data': {'name': '管理员', 'roleName': '租户管理员', 'isDefaultPwd': '0', 'userType': '1'}}
INFO - 2025-07-07 13:33:35,448 - connectMysql.py:22 -[connectMysql:__init__] - 成功连接到数据库：数据库ip：*************
INFO - 2025-07-07 13:33:35,491 - connectMysql.py:43 -[connectMysql:insert] - 插入数据库SQL--INSERT INTO `wei_sms_code_log` (`id`, `phone`, `channel_code`, `code`, `code_status`, `type`, `create_by`, `create_date`, `update_by`, `update_date`) VALUES (58431509036801463, '19877292898', '*********', '123456', 0, 1, NULL, '2025-07-07 13:33:35', NULL, '2025-07-07 13:33:35');
INFO - 2025-07-07 13:33:35,492 - connectMysql.py:44 -[connectMysql:insert] - 数据库数据插入成功
INFO - 2025-07-07 13:33:35,707 - zy_app_login.py:103 -[zy_app_login:login] - 【++登录成功++】{'head': {'errCode': '0', 'errMsg': '', 'tradeId': 'login', 'timestamp': '20250707133353'}, 'uid': '1942094701963284480', 'body': {'birthday': '', 'lastTime': '20250707110705', 'isCurrentRegister': '0', 'zipCode': '', 'sign': '238e47b873fcdd4c125d2101d5bc3cae', 'imageNo': 0, 'points': '0', 'cityName': '', 'areaName': '', 'areaNo': '', 'imageUrl': '', 'provinceNo': '', 'email': '', 'QQ': '', 'cityNo': '', 'address': '', 'level': '1', 'nickName': '', 'sex': 2, 'mobile': '19877292898', 'MSN': '', 'identityCard': '', 'sessionId': '346518664341478696', 'userId': '346422044320518083', 'isMobileValid': 1, 'loginCount': 0, 'token': 'lrDqVLEc8jnKaORIKiJB+y6nN1Io3lnKNueVwmjECnPs3mUEcTEbKdyma0pUIUY8s2ItjSU2QLLYFxywf0Grw9Hfdp3svbaytqb6Sc0oYJ/tO8wd56XQTybpwRkS8Qt8', 'realName': '', 'createTime': '1742204432000', 'provinceName': '', 'username': ''}}
INFO - 2025-07-07 13:33:36,477 - conftest.py:7 -[conftest:print_info] - ---------------接口测试开始---------------
INFO - 2025-07-07 13:33:36,521 - sendrequests.py:57 -[sendrequests:execute_api_request] - 接口名称：演出分类
INFO - 2025-07-07 13:33:36,522 - sendrequests.py:58 -[sendrequests:execute_api_request] - 请求地址：http://*************/api/show/info/category_list
INFO - 2025-07-07 13:33:36,522 - sendrequests.py:59 -[sendrequests:execute_api_request] - 请求方式：POST
INFO - 2025-07-07 13:33:36,522 - sendrequests.py:60 -[sendrequests:execute_api_request] - 请求头：{'Content-Type': 'application/x-www-form-urlencoded', 'Connection': 'Keep-Alive', 'Accept-Encoding': 'gzip'}
INFO - 2025-07-07 13:33:36,522 - sendrequests.py:61 -[sendrequests:execute_api_request] - 测试用例名：获取猫眼演出分类
INFO - 2025-07-07 13:33:36,522 - sendrequests.py:62 -[sendrequests:execute_api_request] - cookies值：None
INFO - 2025-07-07 13:33:36,522 - sendrequests.py:67 -[sendrequests:execute_api_request] - 参数类型：data
INFO - 2025-07-07 13:33:36,522 - sendrequests.py:69 -[sendrequests:execute_api_request] - 请求参数：{"channelCode": "*********", "merchant_code": "", "token": "lrDqVLEc8jnKaORIKiJB+y6nN1Io3lnKNueVwmjECnPs3mUEcTEbKdyma0pUIUY8s2ItjSU2QLLYFxywf0Grw9Hfdp3svbaytqb6Sc0oYJ/tO8wd56XQTybpwRkS8Qt8,*********"}
INFO - 2025-07-07 13:33:36,873 - apiutils_business.py:179 -[apiutils_business:execute_test_cases] - 接口实际返回结果：{"code":0,"msg":"","data":[{"id":"10001","name":"演唱会","image":"http:\/\/*************\/resource\/play\/upload\/20250421\/175345K0n_6qP6Pi.jpg","tag_list":["测试文案","演出演唱会12"]},{"id":"10002","name":"体育赛事","image":"http:\/\/*************\/resource\/play\/upload\/20250408\/144744oaSxDsD3Qu.png","tag_list":["孙颖莎","全红婵"]},{"id":"10003","name":"戏曲艺术","image":"http:\/\/*************\/resource\/play\/upload\/20250408\/1448079S7mHpY2QU.png","tag_list":[]},{"id":"10017","name":"Livehouse","image":"http:\/\/*************\/resource\/play\/upload\/20250408\/1449279xNEIQlg9D.png","tag_list":[]},{"id":"10005","name":"舞蹈芭蕾","image":"http:\/\/*************\/resource\/play\/upload\/20250408\/145113dfAkJoKwCg.png","tag_list":[]},{"id":"10006","name":"音乐会","image":"http:\/\/*************\/resource\/play\/upload\/20250408\/1448369SbU_7vykE.png","tag_list":[]},{"id":"10007","name":"亲子演出","image":"http:\/\/*************\/resource\/play\/upload\/20250408\/144825FTdAI7yPy_.png","tag_list":[]},{"id":"10010","name":"音乐节","image":"http:\/\/*************\/resource\/play\/upload\/20250422\/145418ZAvXkbQMhU.png","tag_list":[]},{"id":"10014","name":"沉浸剧场","image":"http:\/\/*************\/resource\/play\/upload\/20250408\/144959tWsxiYBDA-.png","tag_list":[]},{"id":"10016","name":"相声","image":"http:\/\/*************\/resource\/play\/upload\/20250408\/144939LP2Ruhsuki.png","tag_list":[]},{"id":"10015","name":"脱口秀","image":"http:\/\/*************\/resource\/play\/upload\/20250408\/144949GXhLPNXxUa.png","tag_list":[]},{"id":"10009","name":"休闲展览","image":"http:\/\/*************\/resource\/play\/upload\/20250408\/144907oWhMCZrrG_.png","tag_list":[]},{"id":"10004","name":"话剧音乐剧","image":"http:\/\/*************\/resource\/play\/upload\/20250408\/145102PT7jed5WkQ.png","tag_list":[]},{"id":"10008","name":"其他","image":"http:\/\/*************\/resource\/play\/upload\/20250331\/164501dyI8a3idSN.png","tag_list":["标签 一","标签二"]}]}
INFO - 2025-07-07 13:33:36,879 - assertion_utils.py:35 -[assertion_utils:status_code_assert] - 状态码断言成功：接口实际返回状态码 200 == 200
INFO - 2025-07-07 13:33:36,880 - assertion_utils.py:94 -[assertion_utils:equal_assert] - 相等断言成功：接口实际结果 {'code': 0} == 预期结果：{'code': 0}
INFO - 2025-07-07 13:33:36,881 - assertion_utils.py:206 -[assertion_utils:assert_result] - 测试成功
INFO - 2025-07-07 13:33:36,894 - conftest.py:9 -[conftest:print_info] - ---------------接口测试结束---------------
INFO - 2025-07-07 13:33:38,408 - conftest.py:7 -[conftest:print_info] - ---------------接口测试开始---------------
INFO - 2025-07-07 13:33:38,417 - sendrequests.py:57 -[sendrequests:execute_api_request] - 接口名称：今日必抢
INFO - 2025-07-07 13:33:38,417 - sendrequests.py:58 -[sendrequests:execute_api_request] - 请求地址：http://*************/api/show/info/home_today_sale_list
INFO - 2025-07-07 13:33:38,417 - sendrequests.py:59 -[sendrequests:execute_api_request] - 请求方式：POST
INFO - 2025-07-07 13:33:38,417 - sendrequests.py:60 -[sendrequests:execute_api_request] - 请求头：{'Content-Type': 'application/x-www-form-urlencoded', 'Connection': 'Keep-Alive', 'Accept-Encoding': 'gzip'}
INFO - 2025-07-07 13:33:38,417 - sendrequests.py:61 -[sendrequests:execute_api_request] - 测试用例名：获取今日必抢演出列表
INFO - 2025-07-07 13:33:38,417 - sendrequests.py:62 -[sendrequests:execute_api_request] - cookies值：None
INFO - 2025-07-07 13:33:38,417 - sendrequests.py:67 -[sendrequests:execute_api_request] - 参数类型：data
INFO - 2025-07-07 13:33:38,417 - sendrequests.py:69 -[sendrequests:execute_api_request] - 请求参数：{"page": 1, "pageSize": 20, "channelCode": "*********", "token": "lrDqVLEc8jnKaORIKiJB+y6nN1Io3lnKNueVwmjECnPs3mUEcTEbKdyma0pUIUY8s2ItjSU2QLLYFxywf0Grw9Hfdp3svbaytqb6Sc0oYJ/tO8wd56XQTybpwRkS8Qt8,*********"}
INFO - 2025-07-07 13:33:38,725 - apiutils_business.py:179 -[apiutils_business:execute_test_cases] - 接口实际返回结果：{"code":0,"msg":"","data":{"total":65,"list":[{"id":"26","name":"老无座-0313","category_id":"10001","venue_id":"8","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/d7db73c1934ce8fcb5c4003afe3522b594040.jpg","ticket_status":"3","unusual_status":"0","need_real_name":"0","first_show_time":"2025-08-30 20:00:00","last_show_time":"2025-12-01 02:00:00","on_sale_time":"2025-03-13 19:00:00","max_buy_limit_per_id":"0","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"4.00","max_sale_price":"55.00","shelve_status":"1","third_shelve_status":"1","sort":"210","venue_info":{"venue_id":"8","venue_name":"梅赛德斯-奔驰文化中心","province_title":"上海市","city_title":"浦东新区","address":"世博大道1200号","latitude":"31.188952","longitude":"121.493201"},"show_info":{"show_id":"217","performance_id":"26","show_name":"2025-08-30 周六 20:00","show_start_time":"2025-08-30 20:00:00","show_end_time":"2025-08-30 22:00:00","show_type":"1","show_seat_type":"0","show_real_name_limit":"0","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2025-05-20 16:46:26","off_sale_time":"2025-08-30 20:00:00"},"category_info":{"category_id":"10001","category_name":"演唱会"}},{"id":"22","name":"【外部分销对接专用，不可编辑】编辑请联系上单-新选座-实名制","category_id":"10001","venue_id":"8","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/d7db73c1934ce8fcb5c4003afe3522b594040.jpg","ticket_status":"3","unusual_status":"0","need_real_name":"1","first_show_time":"2025-12-31 01:00:00","last_show_time":"2028-01-01 01:00:00","on_sale_time":"2025-04-22 16:05:43","max_buy_limit_per_id":"6","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"2.00","max_sale_price":"6.00","shelve_status":"1","third_shelve_status":"1","sort":"90","venue_info":{"venue_id":"8","venue_name":"梅赛德斯-奔驰文化中心","province_title":"上海市","city_title":"浦东新区","address":"世博大道1200号","latitude":"31.188952","longitude":"121.493201"},"show_info":{"show_id":"40","performance_id":"22","show_name":"2025-12-31 周三 01:00","show_start_time":"2025-12-31 01:00:00","show_end_time":"2025-12-31 02:00:00","show_type":"1","show_seat_type":"1","show_real_name_limit":"1","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2024-12-10 13:18:31","off_sale_time":"2025-12-31 01:00:00"},"category_info":{"category_id":"10001","category_name":"演唱会"}},{"id":"75","name":"【禁止修改】4.1  自营选座项目---日历模式（优惠混搭）+非实名","category_id":"10004","venue_id":"7","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/fa0fa284da6c8246a179c7400e8d132e59521.png","ticket_status":"12","unusual_status":"6","need_real_name":"0","first_show_time":"2025-09-01 08:00:00","last_show_time":"2026-04-11 23:59:00","on_sale_time":"2025-04-01 10:49:52","max_buy_limit_per_id":"6","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"110.00","max_sale_price":"2200.00","shelve_status":"1","third_shelve_status":"1","sort":"80","venue_info":{"venue_id":"7","venue_name":"凯迪拉克·上海音乐厅","province_title":"上海市","city_title":"黄浦区","address":"延安东路523号（近西藏南路）","latitude":"31.226854","longitude":"121.477977"},"show_info":{"show_id":"250","performance_id":"75","show_name":"2025-09-01 周一 08:00","show_start_time":"2025-09-01 08:00:00","show_end_time":"2025-09-01 10:00:00","show_type":"1","show_seat_type":"1","show_real_name_limit":"0","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2025-04-01 10:46:15","off_sale_time":"2025-08-31 00:00:00"},"category_info":{"category_id":"10004","category_name":"话剧音乐剧"}},{"id":"34","name":"朱磊业老无座线下搬单","category_id":"10002","venue_id":"7","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/84d2c832ab35b5514e8962a287d69c59631.png","ticket_status":"3","unusual_status":"0","need_real_name":"0","first_show_time":"2025-12-01 00:00:00","last_show_time":"2026-01-14 07:00:00","on_sale_time":"2025-03-11 19:23:31","max_buy_limit_per_id":"0","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"5","min_sale_price":"2.00","max_sale_price":"3.00","shelve_status":"1","third_shelve_status":"1","sort":"70","venue_info":{"venue_id":"7","venue_name":"凯迪拉克·上海音乐厅","province_title":"上海市","city_title":"黄浦区","address":"延安东路523号（近西藏南路）","latitude":"31.226854","longitude":"121.477977"},"show_info":{"show_id":"94","performance_id":"34","show_name":"2025-12-01 周一 00:00","show_start_time":"2025-12-01 00:00:00","show_end_time":"2025-12-01 17:00:00","show_type":"1","show_seat_type":"0","show_real_name_limit":"0","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2024-11-20 12:15:50","off_sale_time":"2025-11-30 00:00:00"},"category_info":{"category_id":"10002","category_name":"体育赛事"}},{"id":"8","name":"测试发票说明05","category_id":"10002","venue_id":"12","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/9353f3ac3ba4b47bd4642fcbdc393bbf101705.jpg","ticket_status":"3","unusual_status":"0","need_real_name":"0","first_show_time":"2026-01-03 00:00:00","last_show_time":"2026-01-03 02:00:00","on_sale_time":"2025-02-28 19:20:54","max_buy_limit_per_id":"6","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"33.00","max_sale_price":"33.00","shelve_status":"1","third_shelve_status":"1","sort":"60","venue_info":{"venue_id":"12","venue_name":"上海展览中心","province_title":"上海市","city_title":"静安区","address":"延安中路1000号","latitude":"31.224714","longitude":"121.452558"},"show_info":{"show_id":"16","performance_id":"8","show_name":"2026-01-03 周六 00:00","show_start_time":"2026-01-03 00:00:00","show_end_time":"2026-01-03 02:00:00","show_type":"1","show_seat_type":"0","show_real_name_limit":"0","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2025-01-14 15:12:06","off_sale_time":"2026-01-03 00:00:00"},"category_info":{"category_id":"10002","category_name":"体育赛事"}},{"id":"23","name":"【外部分销对接专用，不可编辑】编辑请联系上单-新选座-非实名制","category_id":"10007","venue_id":"8","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/d7db73c1934ce8fcb5c4003afe3522b594040.jpg","ticket_status":"3","unusual_status":"0","need_real_name":"1","first_show_time":"2028-01-01 00:00:00","last_show_time":"2028-01-01 01:00:00","on_sale_time":"2024-07-29 16:43:00","max_buy_limit_per_id":"6","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"50.00","max_sale_price":"165.00","shelve_status":"1","third_shelve_status":"1","sort":"60","venue_info":{"venue_id":"8","venue_name":"梅赛德斯-奔驰文化中心","province_title":"上海市","city_title":"浦东新区","address":"世博大道1200号","latitude":"31.188952","longitude":"121.493201"},"show_info":{"show_id":"42","performance_id":"23","show_name":"2028-01-01 周六 00:00","show_start_time":"2028-01-01 00:00:00","show_end_time":"2028-01-01 01:00:00","show_type":"1","show_seat_type":"1","show_real_name_limit":"1","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2024-07-29 16:40:00","off_sale_time":"2028-01-01 00:00:00"},"category_info":{"category_id":"10007","category_name":"亲子演出"}},{"id":"48","name":"朱磊业新配座套票配座","category_id":"10001","venue_id":"7","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/84d2c832ab35b5514e8962a287d69c59631.png","ticket_status":"3","unusual_status":"0","need_real_name":"0","first_show_time":"2026-01-02 00:00:00","last_show_time":"2026-01-02 04:00:00","on_sale_time":"2025-01-09 11:28:59","max_buy_limit_per_id":"0","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"2.00","max_sale_price":"3.00","shelve_status":"1","third_shelve_status":"1","sort":"60","venue_info":{"venue_id":"7","venue_name":"凯迪拉克·上海音乐厅","province_title":"上海市","city_title":"黄浦区","address":"延安东路523号（近西藏南路）","latitude":"31.226854","longitude":"121.477977"},"show_info":{"show_id":"179","performance_id":"48","show_name":"2026-01-02 周五 00:00","show_start_time":"2026-01-02 00:00:00","show_end_time":"2026-01-02 04:00:00","show_type":"1","show_seat_type":"1","show_real_name_limit":"9999","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2025-01-20 16:24:00","off_sale_time":"2026-01-02 00:00:00"},"category_info":{"category_id":"10001","category_name":"演唱会"}},{"id":"51","name":"朱磊业新无座快递配送转其它","category_id":"10001","venue_id":"13","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/84d2c832ab35b5514e8962a287d69c59631.png","ticket_status":"12","unusual_status":"3","need_real_name":"0","first_show_time":"2026-01-01 00:00:00","last_show_time":"2028-12-15 02:00:00","on_sale_time":"2024-12-13 17:47:21","max_buy_limit_per_id":"0","max_buy_limit_per_order":"20","min_buy_limit_per_order":"0","max_buy_limit_per_user":"1","min_sale_price":"2.00","max_sale_price":"6.00","shelve_status":"1","third_shelve_status":"1","sort":"60","venue_info":{"venue_id":"13","venue_name":"虹桥天地演艺中心","province_title":"上海市","city_title":"闵行区","address":"绍虹路33号","latitude":"31.19331","longitude":"121.31583"},"show_info":{"show_id":"182","performance_id":"51","show_name":"2026-01-01 周四 00:00","show_start_time":"2026-01-01 00:00:00","show_end_time":"2026-01-01 03:00:00","show_type":"1","show_seat_type":"0","show_real_name_limit":"0","max_buy_limit_per_order":"20","show_unusual_status":"1","on_sale_time":"2024-12-24 15:57:38","off_sale_time":"2026-01-01 00:00:00"},"category_info":{"category_id":"10001","category_name":"演唱会"}},{"id":"53","name":"小红书推送商品验证（老项目喽）","category_id":"10001","venue_id":"7","poster_url":"https:\/\/p1.meituan.net\/myvideodistribute\/6563cbda84d54511adb254e69b0c8001239854.jpg","ticket_status":"3","unusual_status":"0","need_real_name":"0","first_show_time":"2025-07-30 11:00:00","last_show_time":"2025-09-30 20:00:00","on_sale_time":"2025-02-11 13:00:00","max_buy_limit_per_id":"0","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"11.00","max_sale_price":"367.00","shelve_status":"1","third_shelve_status":"1","sort":"60","venue_info":{"venue_id":"7","venue_name":"凯迪拉克·上海音乐厅","province_title":"上海市","city_title":"黄浦区","address":"延安东路523号（近西藏南路）","latitude":"31.226854","longitude":"121.477977"},"show_info":{"show_id":"312","performance_id":"53","show_name":"2025-07-30 周三 11:00","show_start_time":"2025-07-30 11:00:00","show_end_time":"2025-07-30 19:00:00","show_type":"1","show_seat_type":"0","show_real_name_limit":"0","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2025-06-26 14:33:53","off_sale_time":"2025-07-30 11:00:00"},"category_info":{"category_id":"10001","category_name":"演唱会"}},{"id":"108","name":"场馆---项目开售时间校验优化","category_id":"10002","venue_id":"13","poster_url":"https:\/\/p1.meituan.net\/myvideodistribute\/54c4fc8913710b6cbd587f52bce3cb4a305726.jpg","ticket_status":"3","unusual_status":"0","need_real_name":"0","first_show_time":"2025-07-08 00:00:00","last_show_time":"2025-07-15 01:00:00","on_sale_time":"2025-06-10 19:45:00","max_buy_limit_per_id":"6","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"110.00","max_sale_price":"110.00","shelve_status":"1","third_shelve_status":"1","sort":"60","venue_info":{"venue_id":"13","venue_name":"虹桥天地演艺中心","province_title":"上海市","city_title":"闵行区","address":"绍虹路33号","latitude":"31.19331","longitude":"121.31583"},"show_info":{"show_id":"341","performance_id":"108","show_name":"2025-07-08 周二 00:00","show_start_time":"2025-07-08 00:00:00","show_end_time":"2025-07-08 01:00:00","show_type":"1","show_seat_type":"0","show_real_name_limit":"0","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2025-06-10 19:20:54","off_sale_time":"2025-07-08 00:00:00"},"category_info":{"category_id":"10002","category_name":"体育赛事"}}]}}
INFO - 2025-07-07 13:33:38,731 - assertion_utils.py:35 -[assertion_utils:status_code_assert] - 状态码断言成功：接口实际返回状态码 200 == 200
INFO - 2025-07-07 13:33:38,732 - assertion_utils.py:94 -[assertion_utils:equal_assert] - 相等断言成功：接口实际结果 {'code': 0} == 预期结果：{'code': 0}
INFO - 2025-07-07 13:33:38,732 - assertion_utils.py:206 -[assertion_utils:assert_result] - 测试成功
INFO - 2025-07-07 13:33:38,752 - conftest.py:9 -[conftest:print_info] - ---------------接口测试结束---------------
INFO - 2025-07-07 13:33:40,274 - conftest.py:7 -[conftest:print_info] - ---------------接口测试开始---------------
INFO - 2025-07-07 13:33:40,284 - sendrequests.py:57 -[sendrequests:execute_api_request] - 接口名称：近期特惠
INFO - 2025-07-07 13:33:40,284 - sendrequests.py:58 -[sendrequests:execute_api_request] - 请求地址：http://*************/api/show/info/home_near_show_list
INFO - 2025-07-07 13:33:40,284 - sendrequests.py:59 -[sendrequests:execute_api_request] - 请求方式：POST
INFO - 2025-07-07 13:33:40,284 - sendrequests.py:60 -[sendrequests:execute_api_request] - 请求头：{'Content-Type': 'application/x-www-form-urlencoded', 'Connection': 'Keep-Alive', 'Accept-Encoding': 'gzip'}
INFO - 2025-07-07 13:33:40,284 - sendrequests.py:61 -[sendrequests:execute_api_request] - 测试用例名：获取近期特惠演出列表
INFO - 2025-07-07 13:33:40,284 - sendrequests.py:62 -[sendrequests:execute_api_request] - cookies值：None
INFO - 2025-07-07 13:33:40,284 - sendrequests.py:67 -[sendrequests:execute_api_request] - 参数类型：data
INFO - 2025-07-07 13:33:40,284 - sendrequests.py:69 -[sendrequests:execute_api_request] - 请求参数：{"page": 1, "pageSize": 20, "channelCode": "*********", "token": "lrDqVLEc8jnKaORIKiJB+y6nN1Io3lnKNueVwmjECnPs3mUEcTEbKdyma0pUIUY8s2ItjSU2QLLYFxywf0Grw9Hfdp3svbaytqb6Sc0oYJ/tO8wd56XQTybpwRkS8Qt8,*********"}
INFO - 2025-07-07 13:33:40,562 - apiutils_business.py:179 -[apiutils_business:execute_test_cases] - 接口实际返回结果：{"code":0,"msg":"","data":{"total":65,"list":[{"id":"26","name":"老无座-0313","category_id":"10001","venue_id":"8","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/d7db73c1934ce8fcb5c4003afe3522b594040.jpg","ticket_status":"3","unusual_status":"0","need_real_name":"0","first_show_time":"2025-08-30 20:00:00","last_show_time":"2025-12-01 02:00:00","on_sale_time":"2025-03-13 19:00:00","max_buy_limit_per_id":"0","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"4.00","max_sale_price":"55.00","shelve_status":"1","third_shelve_status":"1","sort":"210","venue_info":{"venue_id":"8","venue_name":"梅赛德斯-奔驰文化中心","province_title":"上海市","city_title":"浦东新区","address":"世博大道1200号","latitude":"31.188952","longitude":"121.493201"},"show_info":{"show_id":"217","performance_id":"26","show_name":"2025-08-30 周六 20:00","show_start_time":"2025-08-30 20:00:00","show_end_time":"2025-08-30 22:00:00","show_type":"1","show_seat_type":"0","show_real_name_limit":"0","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2025-05-20 16:46:26","off_sale_time":"2025-08-30 20:00:00"},"category_info":{"category_id":"10001","category_name":"演唱会"}},{"id":"22","name":"【外部分销对接专用，不可编辑】编辑请联系上单-新选座-实名制","category_id":"10001","venue_id":"8","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/d7db73c1934ce8fcb5c4003afe3522b594040.jpg","ticket_status":"3","unusual_status":"0","need_real_name":"1","first_show_time":"2025-12-31 01:00:00","last_show_time":"2028-01-01 01:00:00","on_sale_time":"2025-04-22 16:05:43","max_buy_limit_per_id":"6","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"2.00","max_sale_price":"6.00","shelve_status":"1","third_shelve_status":"1","sort":"90","venue_info":{"venue_id":"8","venue_name":"梅赛德斯-奔驰文化中心","province_title":"上海市","city_title":"浦东新区","address":"世博大道1200号","latitude":"31.188952","longitude":"121.493201"},"show_info":{"show_id":"40","performance_id":"22","show_name":"2025-12-31 周三 01:00","show_start_time":"2025-12-31 01:00:00","show_end_time":"2025-12-31 02:00:00","show_type":"1","show_seat_type":"1","show_real_name_limit":"1","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2024-12-10 13:18:31","off_sale_time":"2025-12-31 01:00:00"},"category_info":{"category_id":"10001","category_name":"演唱会"}},{"id":"75","name":"【禁止修改】4.1  自营选座项目---日历模式（优惠混搭）+非实名","category_id":"10004","venue_id":"7","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/fa0fa284da6c8246a179c7400e8d132e59521.png","ticket_status":"12","unusual_status":"6","need_real_name":"0","first_show_time":"2025-09-01 08:00:00","last_show_time":"2026-04-11 23:59:00","on_sale_time":"2025-04-01 10:49:52","max_buy_limit_per_id":"6","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"110.00","max_sale_price":"2200.00","shelve_status":"1","third_shelve_status":"1","sort":"80","venue_info":{"venue_id":"7","venue_name":"凯迪拉克·上海音乐厅","province_title":"上海市","city_title":"黄浦区","address":"延安东路523号（近西藏南路）","latitude":"31.226854","longitude":"121.477977"},"show_info":{"show_id":"250","performance_id":"75","show_name":"2025-09-01 周一 08:00","show_start_time":"2025-09-01 08:00:00","show_end_time":"2025-09-01 10:00:00","show_type":"1","show_seat_type":"1","show_real_name_limit":"0","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2025-04-01 10:46:15","off_sale_time":"2025-08-31 00:00:00"},"category_info":{"category_id":"10004","category_name":"话剧音乐剧"}},{"id":"34","name":"朱磊业老无座线下搬单","category_id":"10002","venue_id":"7","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/84d2c832ab35b5514e8962a287d69c59631.png","ticket_status":"3","unusual_status":"0","need_real_name":"0","first_show_time":"2025-12-01 00:00:00","last_show_time":"2026-01-14 07:00:00","on_sale_time":"2025-03-11 19:23:31","max_buy_limit_per_id":"0","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"5","min_sale_price":"2.00","max_sale_price":"3.00","shelve_status":"1","third_shelve_status":"1","sort":"70","venue_info":{"venue_id":"7","venue_name":"凯迪拉克·上海音乐厅","province_title":"上海市","city_title":"黄浦区","address":"延安东路523号（近西藏南路）","latitude":"31.226854","longitude":"121.477977"},"show_info":{"show_id":"94","performance_id":"34","show_name":"2025-12-01 周一 00:00","show_start_time":"2025-12-01 00:00:00","show_end_time":"2025-12-01 17:00:00","show_type":"1","show_seat_type":"0","show_real_name_limit":"0","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2024-11-20 12:15:50","off_sale_time":"2025-11-30 00:00:00"},"category_info":{"category_id":"10002","category_name":"体育赛事"}},{"id":"8","name":"测试发票说明05","category_id":"10002","venue_id":"12","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/9353f3ac3ba4b47bd4642fcbdc393bbf101705.jpg","ticket_status":"3","unusual_status":"0","need_real_name":"0","first_show_time":"2026-01-03 00:00:00","last_show_time":"2026-01-03 02:00:00","on_sale_time":"2025-02-28 19:20:54","max_buy_limit_per_id":"6","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"33.00","max_sale_price":"33.00","shelve_status":"1","third_shelve_status":"1","sort":"60","venue_info":{"venue_id":"12","venue_name":"上海展览中心","province_title":"上海市","city_title":"静安区","address":"延安中路1000号","latitude":"31.224714","longitude":"121.452558"},"show_info":{"show_id":"16","performance_id":"8","show_name":"2026-01-03 周六 00:00","show_start_time":"2026-01-03 00:00:00","show_end_time":"2026-01-03 02:00:00","show_type":"1","show_seat_type":"0","show_real_name_limit":"0","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2025-01-14 15:12:06","off_sale_time":"2026-01-03 00:00:00"},"category_info":{"category_id":"10002","category_name":"体育赛事"}},{"id":"23","name":"【外部分销对接专用，不可编辑】编辑请联系上单-新选座-非实名制","category_id":"10007","venue_id":"8","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/d7db73c1934ce8fcb5c4003afe3522b594040.jpg","ticket_status":"3","unusual_status":"0","need_real_name":"1","first_show_time":"2028-01-01 00:00:00","last_show_time":"2028-01-01 01:00:00","on_sale_time":"2024-07-29 16:43:00","max_buy_limit_per_id":"6","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"50.00","max_sale_price":"165.00","shelve_status":"1","third_shelve_status":"1","sort":"60","venue_info":{"venue_id":"8","venue_name":"梅赛德斯-奔驰文化中心","province_title":"上海市","city_title":"浦东新区","address":"世博大道1200号","latitude":"31.188952","longitude":"121.493201"},"show_info":{"show_id":"42","performance_id":"23","show_name":"2028-01-01 周六 00:00","show_start_time":"2028-01-01 00:00:00","show_end_time":"2028-01-01 01:00:00","show_type":"1","show_seat_type":"1","show_real_name_limit":"1","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2024-07-29 16:40:00","off_sale_time":"2028-01-01 00:00:00"},"category_info":{"category_id":"10007","category_name":"亲子演出"}},{"id":"48","name":"朱磊业新配座套票配座","category_id":"10001","venue_id":"7","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/84d2c832ab35b5514e8962a287d69c59631.png","ticket_status":"3","unusual_status":"0","need_real_name":"0","first_show_time":"2026-01-02 00:00:00","last_show_time":"2026-01-02 04:00:00","on_sale_time":"2025-01-09 11:28:59","max_buy_limit_per_id":"0","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"2.00","max_sale_price":"3.00","shelve_status":"1","third_shelve_status":"1","sort":"60","venue_info":{"venue_id":"7","venue_name":"凯迪拉克·上海音乐厅","province_title":"上海市","city_title":"黄浦区","address":"延安东路523号（近西藏南路）","latitude":"31.226854","longitude":"121.477977"},"show_info":{"show_id":"179","performance_id":"48","show_name":"2026-01-02 周五 00:00","show_start_time":"2026-01-02 00:00:00","show_end_time":"2026-01-02 04:00:00","show_type":"1","show_seat_type":"1","show_real_name_limit":"9999","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2025-01-20 16:24:00","off_sale_time":"2026-01-02 00:00:00"},"category_info":{"category_id":"10001","category_name":"演唱会"}},{"id":"51","name":"朱磊业新无座快递配送转其它","category_id":"10001","venue_id":"13","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/84d2c832ab35b5514e8962a287d69c59631.png","ticket_status":"12","unusual_status":"3","need_real_name":"0","first_show_time":"2026-01-01 00:00:00","last_show_time":"2028-12-15 02:00:00","on_sale_time":"2024-12-13 17:47:21","max_buy_limit_per_id":"0","max_buy_limit_per_order":"20","min_buy_limit_per_order":"0","max_buy_limit_per_user":"1","min_sale_price":"2.00","max_sale_price":"6.00","shelve_status":"1","third_shelve_status":"1","sort":"60","venue_info":{"venue_id":"13","venue_name":"虹桥天地演艺中心","province_title":"上海市","city_title":"闵行区","address":"绍虹路33号","latitude":"31.19331","longitude":"121.31583"},"show_info":{"show_id":"182","performance_id":"51","show_name":"2026-01-01 周四 00:00","show_start_time":"2026-01-01 00:00:00","show_end_time":"2026-01-01 03:00:00","show_type":"1","show_seat_type":"0","show_real_name_limit":"0","max_buy_limit_per_order":"20","show_unusual_status":"1","on_sale_time":"2024-12-24 15:57:38","off_sale_time":"2026-01-01 00:00:00"},"category_info":{"category_id":"10001","category_name":"演唱会"}},{"id":"53","name":"小红书推送商品验证（老项目喽）","category_id":"10001","venue_id":"7","poster_url":"https:\/\/p1.meituan.net\/myvideodistribute\/6563cbda84d54511adb254e69b0c8001239854.jpg","ticket_status":"3","unusual_status":"0","need_real_name":"0","first_show_time":"2025-07-30 11:00:00","last_show_time":"2025-09-30 20:00:00","on_sale_time":"2025-02-11 13:00:00","max_buy_limit_per_id":"0","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"11.00","max_sale_price":"367.00","shelve_status":"1","third_shelve_status":"1","sort":"60","venue_info":{"venue_id":"7","venue_name":"凯迪拉克·上海音乐厅","province_title":"上海市","city_title":"黄浦区","address":"延安东路523号（近西藏南路）","latitude":"31.226854","longitude":"121.477977"},"show_info":{"show_id":"312","performance_id":"53","show_name":"2025-07-30 周三 11:00","show_start_time":"2025-07-30 11:00:00","show_end_time":"2025-07-30 19:00:00","show_type":"1","show_seat_type":"0","show_real_name_limit":"0","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2025-06-26 14:33:53","off_sale_time":"2025-07-30 11:00:00"},"category_info":{"category_id":"10001","category_name":"演唱会"}},{"id":"108","name":"场馆---项目开售时间校验优化","category_id":"10002","venue_id":"13","poster_url":"https:\/\/p1.meituan.net\/myvideodistribute\/54c4fc8913710b6cbd587f52bce3cb4a305726.jpg","ticket_status":"3","unusual_status":"0","need_real_name":"0","first_show_time":"2025-07-08 00:00:00","last_show_time":"2025-07-15 01:00:00","on_sale_time":"2025-06-10 19:45:00","max_buy_limit_per_id":"6","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"110.00","max_sale_price":"110.00","shelve_status":"1","third_shelve_status":"1","sort":"60","venue_info":{"venue_id":"13","venue_name":"虹桥天地演艺中心","province_title":"上海市","city_title":"闵行区","address":"绍虹路33号","latitude":"31.19331","longitude":"121.31583"},"show_info":{"show_id":"341","performance_id":"108","show_name":"2025-07-08 周二 00:00","show_start_time":"2025-07-08 00:00:00","show_end_time":"2025-07-08 01:00:00","show_type":"1","show_seat_type":"0","show_real_name_limit":"0","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2025-06-10 19:20:54","off_sale_time":"2025-07-08 00:00:00"},"category_info":{"category_id":"10002","category_name":"体育赛事"}}]}}
INFO - 2025-07-07 13:33:40,573 - assertion_utils.py:35 -[assertion_utils:status_code_assert] - 状态码断言成功：接口实际返回状态码 200 == 200
INFO - 2025-07-07 13:33:40,575 - assertion_utils.py:94 -[assertion_utils:equal_assert] - 相等断言成功：接口实际结果 {'code': 0} == 预期结果：{'code': 0}
INFO - 2025-07-07 13:33:40,575 - assertion_utils.py:206 -[assertion_utils:assert_result] - 测试成功
INFO - 2025-07-07 13:33:40,583 - conftest.py:9 -[conftest:print_info] - ---------------接口测试结束---------------
INFO - 2025-07-07 13:33:42,103 - conftest.py:7 -[conftest:print_info] - ---------------接口测试开始---------------
INFO - 2025-07-07 13:33:42,113 - sendrequests.py:57 -[sendrequests:execute_api_request] - 接口名称：为你推荐
INFO - 2025-07-07 13:33:42,113 - sendrequests.py:58 -[sendrequests:execute_api_request] - 请求地址：http://*************/api/show/info/home_recommend_list
INFO - 2025-07-07 13:33:42,113 - sendrequests.py:59 -[sendrequests:execute_api_request] - 请求方式：POST
INFO - 2025-07-07 13:33:42,113 - sendrequests.py:60 -[sendrequests:execute_api_request] - 请求头：{'Content-Type': 'application/x-www-form-urlencoded', 'Connection': 'Keep-Alive', 'Accept-Encoding': 'gzip'}
INFO - 2025-07-07 13:33:42,113 - sendrequests.py:61 -[sendrequests:execute_api_request] - 测试用例名：获取为你推荐演出列表
INFO - 2025-07-07 13:33:42,113 - sendrequests.py:62 -[sendrequests:execute_api_request] - cookies值：None
INFO - 2025-07-07 13:33:42,113 - sendrequests.py:67 -[sendrequests:execute_api_request] - 参数类型：data
INFO - 2025-07-07 13:33:42,113 - sendrequests.py:69 -[sendrequests:execute_api_request] - 请求参数：{"page": 1, "pageSize": 20, "channelCode": "*********", "token": "lrDqVLEc8jnKaORIKiJB+y6nN1Io3lnKNueVwmjECnPs3mUEcTEbKdyma0pUIUY8s2ItjSU2QLLYFxywf0Grw9Hfdp3svbaytqb6Sc0oYJ/tO8wd56XQTybpwRkS8Qt8,*********"}
INFO - 2025-07-07 13:33:42,408 - apiutils_business.py:179 -[apiutils_business:execute_test_cases] - 接口实际返回结果：{"code":0,"msg":"","data":{"total":65,"list":[{"id":"26","name":"老无座-0313","category_id":"10001","venue_id":"8","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/d7db73c1934ce8fcb5c4003afe3522b594040.jpg","ticket_status":"3","unusual_status":"0","need_real_name":"0","first_show_time":"2025-08-30 20:00:00","last_show_time":"2025-12-01 02:00:00","on_sale_time":"2025-03-13 19:00:00","max_buy_limit_per_id":"0","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"4.00","max_sale_price":"55.00","shelve_status":"1","third_shelve_status":"1","sort":"210","venue_info":{"venue_id":"8","venue_name":"梅赛德斯-奔驰文化中心","province_title":"上海市","city_title":"浦东新区","address":"世博大道1200号","latitude":"31.188952","longitude":"121.493201"},"show_info":{"show_id":"217","performance_id":"26","show_name":"2025-08-30 周六 20:00","show_start_time":"2025-08-30 20:00:00","show_end_time":"2025-08-30 22:00:00","show_type":"1","show_seat_type":"0","show_real_name_limit":"0","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2025-05-20 16:46:26","off_sale_time":"2025-08-30 20:00:00"},"category_info":{"category_id":"10001","category_name":"演唱会"}},{"id":"22","name":"【外部分销对接专用，不可编辑】编辑请联系上单-新选座-实名制","category_id":"10001","venue_id":"8","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/d7db73c1934ce8fcb5c4003afe3522b594040.jpg","ticket_status":"3","unusual_status":"0","need_real_name":"1","first_show_time":"2025-12-31 01:00:00","last_show_time":"2028-01-01 01:00:00","on_sale_time":"2025-04-22 16:05:43","max_buy_limit_per_id":"6","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"2.00","max_sale_price":"6.00","shelve_status":"1","third_shelve_status":"1","sort":"90","venue_info":{"venue_id":"8","venue_name":"梅赛德斯-奔驰文化中心","province_title":"上海市","city_title":"浦东新区","address":"世博大道1200号","latitude":"31.188952","longitude":"121.493201"},"show_info":{"show_id":"40","performance_id":"22","show_name":"2025-12-31 周三 01:00","show_start_time":"2025-12-31 01:00:00","show_end_time":"2025-12-31 02:00:00","show_type":"1","show_seat_type":"1","show_real_name_limit":"1","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2024-12-10 13:18:31","off_sale_time":"2025-12-31 01:00:00"},"category_info":{"category_id":"10001","category_name":"演唱会"}},{"id":"75","name":"【禁止修改】4.1  自营选座项目---日历模式（优惠混搭）+非实名","category_id":"10004","venue_id":"7","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/fa0fa284da6c8246a179c7400e8d132e59521.png","ticket_status":"12","unusual_status":"6","need_real_name":"0","first_show_time":"2025-09-01 08:00:00","last_show_time":"2026-04-11 23:59:00","on_sale_time":"2025-04-01 10:49:52","max_buy_limit_per_id":"6","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"110.00","max_sale_price":"2200.00","shelve_status":"1","third_shelve_status":"1","sort":"80","venue_info":{"venue_id":"7","venue_name":"凯迪拉克·上海音乐厅","province_title":"上海市","city_title":"黄浦区","address":"延安东路523号（近西藏南路）","latitude":"31.226854","longitude":"121.477977"},"show_info":{"show_id":"250","performance_id":"75","show_name":"2025-09-01 周一 08:00","show_start_time":"2025-09-01 08:00:00","show_end_time":"2025-09-01 10:00:00","show_type":"1","show_seat_type":"1","show_real_name_limit":"0","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2025-04-01 10:46:15","off_sale_time":"2025-08-31 00:00:00"},"category_info":{"category_id":"10004","category_name":"话剧音乐剧"}},{"id":"34","name":"朱磊业老无座线下搬单","category_id":"10002","venue_id":"7","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/84d2c832ab35b5514e8962a287d69c59631.png","ticket_status":"3","unusual_status":"0","need_real_name":"0","first_show_time":"2025-12-01 00:00:00","last_show_time":"2026-01-14 07:00:00","on_sale_time":"2025-03-11 19:23:31","max_buy_limit_per_id":"0","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"5","min_sale_price":"2.00","max_sale_price":"3.00","shelve_status":"1","third_shelve_status":"1","sort":"70","venue_info":{"venue_id":"7","venue_name":"凯迪拉克·上海音乐厅","province_title":"上海市","city_title":"黄浦区","address":"延安东路523号（近西藏南路）","latitude":"31.226854","longitude":"121.477977"},"show_info":{"show_id":"94","performance_id":"34","show_name":"2025-12-01 周一 00:00","show_start_time":"2025-12-01 00:00:00","show_end_time":"2025-12-01 17:00:00","show_type":"1","show_seat_type":"0","show_real_name_limit":"0","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2024-11-20 12:15:50","off_sale_time":"2025-11-30 00:00:00"},"category_info":{"category_id":"10002","category_name":"体育赛事"}},{"id":"8","name":"测试发票说明05","category_id":"10002","venue_id":"12","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/9353f3ac3ba4b47bd4642fcbdc393bbf101705.jpg","ticket_status":"3","unusual_status":"0","need_real_name":"0","first_show_time":"2026-01-03 00:00:00","last_show_time":"2026-01-03 02:00:00","on_sale_time":"2025-02-28 19:20:54","max_buy_limit_per_id":"6","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"33.00","max_sale_price":"33.00","shelve_status":"1","third_shelve_status":"1","sort":"60","venue_info":{"venue_id":"12","venue_name":"上海展览中心","province_title":"上海市","city_title":"静安区","address":"延安中路1000号","latitude":"31.224714","longitude":"121.452558"},"show_info":{"show_id":"16","performance_id":"8","show_name":"2026-01-03 周六 00:00","show_start_time":"2026-01-03 00:00:00","show_end_time":"2026-01-03 02:00:00","show_type":"1","show_seat_type":"0","show_real_name_limit":"0","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2025-01-14 15:12:06","off_sale_time":"2026-01-03 00:00:00"},"category_info":{"category_id":"10002","category_name":"体育赛事"}},{"id":"23","name":"【外部分销对接专用，不可编辑】编辑请联系上单-新选座-非实名制","category_id":"10007","venue_id":"8","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/d7db73c1934ce8fcb5c4003afe3522b594040.jpg","ticket_status":"3","unusual_status":"0","need_real_name":"1","first_show_time":"2028-01-01 00:00:00","last_show_time":"2028-01-01 01:00:00","on_sale_time":"2024-07-29 16:43:00","max_buy_limit_per_id":"6","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"50.00","max_sale_price":"165.00","shelve_status":"1","third_shelve_status":"1","sort":"60","venue_info":{"venue_id":"8","venue_name":"梅赛德斯-奔驰文化中心","province_title":"上海市","city_title":"浦东新区","address":"世博大道1200号","latitude":"31.188952","longitude":"121.493201"},"show_info":{"show_id":"42","performance_id":"23","show_name":"2028-01-01 周六 00:00","show_start_time":"2028-01-01 00:00:00","show_end_time":"2028-01-01 01:00:00","show_type":"1","show_seat_type":"1","show_real_name_limit":"1","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2024-07-29 16:40:00","off_sale_time":"2028-01-01 00:00:00"},"category_info":{"category_id":"10007","category_name":"亲子演出"}},{"id":"48","name":"朱磊业新配座套票配座","category_id":"10001","venue_id":"7","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/84d2c832ab35b5514e8962a287d69c59631.png","ticket_status":"3","unusual_status":"0","need_real_name":"0","first_show_time":"2026-01-02 00:00:00","last_show_time":"2026-01-02 04:00:00","on_sale_time":"2025-01-09 11:28:59","max_buy_limit_per_id":"0","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"2.00","max_sale_price":"3.00","shelve_status":"1","third_shelve_status":"1","sort":"60","venue_info":{"venue_id":"7","venue_name":"凯迪拉克·上海音乐厅","province_title":"上海市","city_title":"黄浦区","address":"延安东路523号（近西藏南路）","latitude":"31.226854","longitude":"121.477977"},"show_info":{"show_id":"179","performance_id":"48","show_name":"2026-01-02 周五 00:00","show_start_time":"2026-01-02 00:00:00","show_end_time":"2026-01-02 04:00:00","show_type":"1","show_seat_type":"1","show_real_name_limit":"9999","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2025-01-20 16:24:00","off_sale_time":"2026-01-02 00:00:00"},"category_info":{"category_id":"10001","category_name":"演唱会"}},{"id":"51","name":"朱磊业新无座快递配送转其它","category_id":"10001","venue_id":"13","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/84d2c832ab35b5514e8962a287d69c59631.png","ticket_status":"12","unusual_status":"3","need_real_name":"0","first_show_time":"2026-01-01 00:00:00","last_show_time":"2028-12-15 02:00:00","on_sale_time":"2024-12-13 17:47:21","max_buy_limit_per_id":"0","max_buy_limit_per_order":"20","min_buy_limit_per_order":"0","max_buy_limit_per_user":"1","min_sale_price":"2.00","max_sale_price":"6.00","shelve_status":"1","third_shelve_status":"1","sort":"60","venue_info":{"venue_id":"13","venue_name":"虹桥天地演艺中心","province_title":"上海市","city_title":"闵行区","address":"绍虹路33号","latitude":"31.19331","longitude":"121.31583"},"show_info":{"show_id":"182","performance_id":"51","show_name":"2026-01-01 周四 00:00","show_start_time":"2026-01-01 00:00:00","show_end_time":"2026-01-01 03:00:00","show_type":"1","show_seat_type":"0","show_real_name_limit":"0","max_buy_limit_per_order":"20","show_unusual_status":"1","on_sale_time":"2024-12-24 15:57:38","off_sale_time":"2026-01-01 00:00:00"},"category_info":{"category_id":"10001","category_name":"演唱会"}},{"id":"53","name":"小红书推送商品验证（老项目喽）","category_id":"10001","venue_id":"7","poster_url":"https:\/\/p1.meituan.net\/myvideodistribute\/6563cbda84d54511adb254e69b0c8001239854.jpg","ticket_status":"3","unusual_status":"0","need_real_name":"0","first_show_time":"2025-07-30 11:00:00","last_show_time":"2025-09-30 20:00:00","on_sale_time":"2025-02-11 13:00:00","max_buy_limit_per_id":"0","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"11.00","max_sale_price":"367.00","shelve_status":"1","third_shelve_status":"1","sort":"60","venue_info":{"venue_id":"7","venue_name":"凯迪拉克·上海音乐厅","province_title":"上海市","city_title":"黄浦区","address":"延安东路523号（近西藏南路）","latitude":"31.226854","longitude":"121.477977"},"show_info":{"show_id":"312","performance_id":"53","show_name":"2025-07-30 周三 11:00","show_start_time":"2025-07-30 11:00:00","show_end_time":"2025-07-30 19:00:00","show_type":"1","show_seat_type":"0","show_real_name_limit":"0","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2025-06-26 14:33:53","off_sale_time":"2025-07-30 11:00:00"},"category_info":{"category_id":"10001","category_name":"演唱会"}},{"id":"108","name":"场馆---项目开售时间校验优化","category_id":"10002","venue_id":"13","poster_url":"https:\/\/p1.meituan.net\/myvideodistribute\/54c4fc8913710b6cbd587f52bce3cb4a305726.jpg","ticket_status":"3","unusual_status":"0","need_real_name":"0","first_show_time":"2025-07-08 00:00:00","last_show_time":"2025-07-15 01:00:00","on_sale_time":"2025-06-10 19:45:00","max_buy_limit_per_id":"6","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"110.00","max_sale_price":"110.00","shelve_status":"1","third_shelve_status":"1","sort":"60","venue_info":{"venue_id":"13","venue_name":"虹桥天地演艺中心","province_title":"上海市","city_title":"闵行区","address":"绍虹路33号","latitude":"31.19331","longitude":"121.31583"},"show_info":{"show_id":"341","performance_id":"108","show_name":"2025-07-08 周二 00:00","show_start_time":"2025-07-08 00:00:00","show_end_time":"2025-07-08 01:00:00","show_type":"1","show_seat_type":"0","show_real_name_limit":"0","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2025-06-10 19:20:54","off_sale_time":"2025-07-08 00:00:00"},"category_info":{"category_id":"10002","category_name":"体育赛事"}},{"id":"4","name":"三方导码","category_id":"10001","venue_id":"8","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/680352446992ad3846ab6542da1d1400288377.jpg","ticket_status":"3","unusual_status":"0","need_real_name":"1","first_show_time":"2025-11-06 00:00:00","last_show_time":"2025-11-06 01:00:00","on_sale_time":"2024-11-06 15:30:47","max_buy_limit_per_id":"6","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"2.00","max_sale_price":"3.00","shelve_status":"1","third_shelve_status":"1","sort":"50","venue_info":{"venue_id":"8","venue_name":"梅赛德斯-奔驰文化中心","province_title":"上海市","city_title":"浦东新区","address":"世博大道1200号","latitude":"31.188952","longitude":"121.493201"},"show_info":{"show_id":"10","performance_id":"4","show_name":"2025-11-06 周四 00:00","show_start_time":"2025-11-06 00:00:00","show_end_time":"2025-11-06 01:00:00","show_type":"1","show_seat_type":"0","show_real_name_limit":"0","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2024-11-06 15:29:42","off_sale_time":"2025-11-06 00:00:00"},"category_info":{"category_id":"10001","category_name":"演唱会"}},{"id":"9","name":"20241202分销配座测试","category_id":"10002","venue_id":"13","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/bfb0c3a9cc0695646bbee5de5288ed9c950360.jpg","ticket_status":"3","unusual_status":"0","need_real_name":"0","first_show_time":"2025-12-01 00:00:00","last_show_time":"2025-12-11 02:00:00","on_sale_time":"2025-02-14 15:46:50","max_buy_limit_per_id":"0","max_buy_limit_per_order":"20","min_buy_limit_per_order":"0","max_buy_limit_per_user":"99999","min_sale_price":"0.00","max_sale_price":"1056.00","shelve_status":"1","third_shelve_status":"1","sort":"50","venue_info":{"venue_id":"13","venue_name":"虹桥天地演艺中心","province_title":"上海市","city_title":"闵行区","address":"绍虹路33号","latitude":"31.19331","longitude":"121.31583"},"show_info":{"show_id":"17","performance_id":"9","show_name":"2025-12-01 周一 00:00","show_start_time":"2025-12-01 00:00:00","show_end_time":"2025-12-01 01:00:00","show_type":"1","show_seat_type":"1","show_real_name_limit":"0","max_buy_limit_per_order":"20","show_unusual_status":"0","on_sale_time":"2024-12-02 14:05:56","off_sale_time":"2025-12-01 00:00:00"},"category_info":{"category_id":"10002","category_name":"体育赛事"}},{"id":"10","name":"老上单实名制","category_id":"10001","venue_id":"7","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/cabb76c33aad7247623a0a827cec6a7e8066.jpg","ticket_status":"3","unusual_status":"0","need_real_name":"1","first_show_time":"2025-09-01 02:00:00","last_show_time":"2025-09-01 04:00:00","on_sale_time":"2025-04-30 23:00:00","max_buy_limit_per_id":"6","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"2.00","max_sale_price":"2.00","shelve_status":"1","third_shelve_status":"1","sort":"50","venue_info":{"venue_id":"7","venue_name":"凯迪拉克·上海音乐厅","province_title":"上海市","city_title":"黄浦区","address":"延安东路523号（近西藏南路）","latitude":"31.226854","longitude":"121.477977"},"show_info":{"show_id":"28","performance_id":"10","show_name":"2025-09-01 周一 02:00","show_start_time":"2025-09-01 02:00:00","show_end_time":"2025-09-01 04:00:00","show_type":"1","show_seat_type":"0","show_real_name_limit":"1","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2025-02-24 10:33:25","off_sale_time":"2025-09-01 02:00:00"},"category_info":{"category_id":"10001","category_name":"演唱会"}},{"id":"12","name":"【售卖场】2.11无座-2026.12.28","category_id":"10001","venue_id":"7","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/03fdb9f41adcf253ff9211097ab444421598915.png","ticket_status":"3","unusual_status":"0","need_real_name":"1","first_show_time":"2026-12-28 19:00:00","last_show_time":"2026-12-31 23:00:00","on_sale_time":"2025-02-11 14:53:19","max_buy_limit_per_id":"10","max_buy_limit_per_order":"4","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"748.00","max_sale_price":"2178.00","shelve_status":"1","third_shelve_status":"1","sort":"50","venue_info":{"venue_id":"7","venue_name":"凯迪拉克·上海音乐厅","province_title":"上海市","city_title":"黄浦区","address":"延安东路523号（近西藏南路）","latitude":"31.226854","longitude":"121.477977"},"show_info":{"show_id":"30","performance_id":"12","show_name":"2026-12-28 周一 19:00","show_start_time":"2026-12-28 19:00:00","show_end_time":"2026-12-28 23:00:00","show_type":"1","show_seat_type":"0","show_real_name_limit":"1","max_buy_limit_per_order":"2","show_unusual_status":"0","on_sale_time":"2025-02-11 11:33:38","off_sale_time":"2026-12-28 00:00:00"},"category_info":{"category_id":"10001","category_name":"演唱会"}},{"id":"14","name":"zly测试渠道","category_id":"10001","venue_id":"10","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/84d2c832ab35b5514e8962a287d69c59631.png","ticket_status":"3","unusual_status":"0","need_real_name":"0","first_show_time":"2026-02-06 00:00:00","last_show_time":"2026-02-06 05:00:00","on_sale_time":"2025-03-07 00:00:00","max_buy_limit_per_id":"6","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"2.00","max_sale_price":"2.00","shelve_status":"1","third_shelve_status":"1","sort":"50","venue_info":{"venue_id":"10","venue_name":"猫眼演出321","province_title":"北京市","city_title":"朝阳区","address":"望京sohoT3B栋2楼","latitude":"39.996126","longitude":"116.480553"},"show_info":{"show_id":"34","performance_id":"14","show_name":"2026-02-06 周五 00:00","show_start_time":"2026-02-06 00:00:00","show_end_time":"2026-02-06 05:00:00","show_type":"1","show_seat_type":"1","show_real_name_limit":"0","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2025-02-17 15:57:48","off_sale_time":"2026-02-06 00:00:00"},"category_info":{"category_id":"10001","category_name":"演唱会"}},{"id":"17","name":"老无座-非大演-0227","category_id":"10004","venue_id":"8","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/d7db73c1934ce8fcb5c4003afe3522b594040.jpg","ticket_status":"3","unusual_status":"0","need_real_name":"0","first_show_time":"2025-12-31 00:00:00","last_show_time":"2025-12-31 01:00:00","on_sale_time":"2025-02-27 19:55:09","max_buy_limit_per_id":"0","max_buy_limit_per_order":"10","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"11.00","max_sale_price":"33.00","shelve_status":"1","third_shelve_status":"1","sort":"50","venue_info":{"venue_id":"8","venue_name":"梅赛德斯-奔驰文化中心","province_title":"上海市","city_title":"浦东新区","address":"世博大道1200号","latitude":"31.188952","longitude":"121.493201"},"show_info":{"show_id":"36","performance_id":"17","show_name":"2025-12-31 周三 00:00","show_start_time":"2025-12-31 00:00:00","show_end_time":"2025-12-31 01:00:00","show_type":"1","show_seat_type":"0","show_real_name_limit":"0","max_buy_limit_per_order":"10","show_unusual_status":"0","on_sale_time":"2025-02-27 19:54:03","off_sale_time":"2025-12-31 00:00:00"},"category_info":{"category_id":"10004","category_name":"话剧音乐剧"}},{"id":"19","name":"和净子账号测试项目2-0641","category_id":"10001","venue_id":"15","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/7f13fafffa5464aec7c728efca22be0a24721.jpg","ticket_status":"12","unusual_status":"1","need_real_name":"0","first_show_time":"2025-11-01 00:00:00","last_show_time":"2025-11-01 01:00:00","on_sale_time":"2025-03-07 18:35:00","max_buy_limit_per_id":"0","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"4.00","max_sale_price":"5.00","shelve_status":"1","third_shelve_status":"1","sort":"50","venue_info":{"venue_id":"15","venue_name":"北京展览馆剧场","province_title":"北京市","city_title":"西城区","address":"西直门外大街135号","latitude":"39.942007","longitude":"116.343911"},"show_info":{"show_id":"38","performance_id":"19","show_name":"2025-11-01 周六 00:00","show_start_time":"2025-11-01 00:00:00","show_end_time":"2025-11-01 01:00:00","show_type":"1","show_seat_type":"0","show_real_name_limit":"0","max_buy_limit_per_order":"6","show_unusual_status":"2","on_sale_time":"2024-12-26 19:40:48","off_sale_time":"2025-11-01 00:00:00"},"category_info":{"category_id":"10001","category_name":"演唱会"}},{"id":"21","name":"商家上单","category_id":"10001","venue_id":"17","poster_url":"https:\/\/p1.meituan.net\/myvideodistribute\/78c1cb8f72c5a9d08f7879bd1cb0f6d4279046.jpg","ticket_status":"3","unusual_status":"0","need_real_name":"0","first_show_time":"2025-11-01 00:00:00","last_show_time":"2025-11-01 05:00:00","on_sale_time":"2025-03-31 19:56:37","max_buy_limit_per_id":"0","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"136.00","max_sale_price":"136.00","shelve_status":"1","third_shelve_status":"1","sort":"50","venue_info":{"venue_id":"17","venue_name":"上海体育馆","province_title":"上海市","city_title":"徐汇区","address":"漕溪北路1111号","latitude":"31.181511","longitude":"121.438358"},"show_info":{"show_id":"39","performance_id":"21","show_name":"2025-11-01 周六 00:00","show_start_time":"2025-11-01 00:00:00","show_end_time":"2025-11-01 05:00:00","show_type":"1","show_seat_type":"0","show_real_name_limit":"6","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2025-03-03 19:27:53","off_sale_time":"2025-11-01 00:00:00"},"category_info":{"category_id":"10001","category_name":"演唱会"}},{"id":"24","name":"【外部分销对接专用，不可编辑】编辑请联系上单-选座-实名制","category_id":"10001","venue_id":"17","poster_url":"https:\/\/p1.meituan.net\/myvideodistribute\/d69af400577449d5ea329a53cbe0bb8d1185076.jpg","ticket_status":"3","unusual_status":"0","need_real_name":"1","first_show_time":"2027-01-01 19:00:00","last_show_time":"2029-01-01 21:00:00","on_sale_time":"2024-04-18 14:49:55","max_buy_limit_per_id":"6","max_buy_limit_per_order":"3","min_buy_limit_per_order":"0","max_buy_limit_per_user":"10","min_sale_price":"6.00","max_sale_price":"11.00","shelve_status":"1","third_shelve_status":"1","sort":"50","venue_info":{"venue_id":"17","venue_name":"上海体育馆","province_title":"上海市","city_title":"徐汇区","address":"漕溪北路1111号","latitude":"31.181511","longitude":"121.438358"},"show_info":{"show_id":"170","performance_id":"24","show_name":"2025国庆专场","show_start_time":"2025-10-01 15:00:00","show_end_time":"2025-10-01 18:00:00","show_type":"1","show_seat_type":"1","show_real_name_limit":"0","max_buy_limit_per_order":"3","show_unusual_status":"0","on_sale_time":"2025-04-28 14:46:21","off_sale_time":"2025-10-07 19:00:00"},"category_info":{"category_id":"10001","category_name":"演唱会"}},{"id":"29","name":"老无座-日历-0318","category_id":"10004","venue_id":"8","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/d7db73c1934ce8fcb5c4003afe3522b594040.jpg","ticket_status":"3","unusual_status":"0","need_real_name":"0","first_show_time":"2025-11-27 01:00:00","last_show_time":"2025-12-31 02:00:00","on_sale_time":"2025-03-18 11:30:41","max_buy_limit_per_id":"0","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"11.00","max_sale_price":"55.00","shelve_status":"1","third_shelve_status":"1","sort":"50","venue_info":{"venue_id":"8","venue_name":"梅赛德斯-奔驰文化中心","province_title":"上海市","city_title":"浦东新区","address":"世博大道1200号","latitude":"31.188952","longitude":"121.493201"},"show_info":{"show_id":"51","performance_id":"29","show_name":"2025-11-27 周四 01:00","show_start_time":"2025-11-27 01:00:00","show_end_time":"2025-11-27 02:00:00","show_type":"1","show_seat_type":"0","show_real_name_limit":"0","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2025-03-18 11:29:39","off_sale_time":"2025-11-27 01:00:00"},"category_info":{"category_id":"10004","category_name":"话剧音乐剧"}}]}}
INFO - 2025-07-07 13:33:42,412 - assertion_utils.py:35 -[assertion_utils:status_code_assert] - 状态码断言成功：接口实际返回状态码 200 == 200
INFO - 2025-07-07 13:33:42,415 - assertion_utils.py:94 -[assertion_utils:equal_assert] - 相等断言成功：接口实际结果 {'code': 0} == 预期结果：{'code': 0}
INFO - 2025-07-07 13:33:42,416 - assertion_utils.py:206 -[assertion_utils:assert_result] - 测试成功
INFO - 2025-07-07 13:33:42,425 - conftest.py:9 -[conftest:print_info] - ---------------接口测试结束---------------
INFO - 2025-07-07 13:33:43,946 - conftest.py:7 -[conftest:print_info] - ---------------接口测试开始---------------
INFO - 2025-07-07 13:33:43,969 - sendrequests.py:57 -[sendrequests:execute_api_request] - 接口名称：首页广告位
INFO - 2025-07-07 13:33:43,969 - sendrequests.py:58 -[sendrequests:execute_api_request] - 请求地址：http://*************/api/show/ad/select_list
INFO - 2025-07-07 13:33:43,969 - sendrequests.py:59 -[sendrequests:execute_api_request] - 请求方式：POST
INFO - 2025-07-07 13:33:43,969 - sendrequests.py:60 -[sendrequests:execute_api_request] - 请求头：{'Content-Type': 'application/x-www-form-urlencoded', 'Connection': 'Keep-Alive', 'Accept-Encoding': 'gzip'}
INFO - 2025-07-07 13:33:43,969 - sendrequests.py:61 -[sendrequests:execute_api_request] - 测试用例名：获取首页广告列表
INFO - 2025-07-07 13:33:43,969 - sendrequests.py:62 -[sendrequests:execute_api_request] - cookies值：None
INFO - 2025-07-07 13:33:43,969 - sendrequests.py:67 -[sendrequests:execute_api_request] - 参数类型：data
INFO - 2025-07-07 13:33:43,969 - sendrequests.py:69 -[sendrequests:execute_api_request] - 请求参数：{"channelCode": "*********", "token": "lrDqVLEc8jnKaORIKiJB+y6nN1Io3lnKNueVwmjECnPs3mUEcTEbKdyma0pUIUY8s2ItjSU2QLLYFxywf0Grw9Hfdp3svbaytqb6Sc0oYJ/tO8wd56XQTybpwRkS8Qt8,*********"}
INFO - 2025-07-07 13:33:44,258 - apiutils_business.py:179 -[apiutils_business:execute_test_cases] - 接口实际返回结果：{"code":0,"msg":"","data":[{"id":"31","name":"端午活动","position_type":"home_middle","image_url":"upload\/20250529\/102446xt4IfG0Kf0.jpg","link_type":"customize","link_value":"https:\/\/www.msn.cn\/zh-cn\/news\/other\/%E7%AB%AF%E5%8D%88%E6%B0%9B%E5%9B%B4%E6%84%9F%E6%8B%89%E6%BB%A1-%E8%B5%9B%E9%BE%99%E8%88%9F-%E7%B2%BD%E5%AD%90-%E9%A6%99%E5%8C%85%E9%83%BD%E6%9C%89%E4%BA%86%E6%96%B0%E8%8A%B1%E6%A0%B7\/ar-AA1Fz2TU?ocid=msedgntp&pc=CNNDDB","sort_value":"99","link_type_desc":"自定义","image_url_show":"http:\/\/*************\/resource\/play\/upload\/20250529\/102446xt4IfG0Kf0.jpg"},{"id":"32","name":"陈慧娴","position_type":"home_middle","image_url":"upload\/20250529\/105313SLTl4LjSBR.png","link_type":"performance","link_value":"34","sort_value":"67","link_type_desc":"演出项目","image_url_show":"http:\/\/*************\/resource\/play\/upload\/20250529\/105313SLTl4LjSBR.png"},{"id":"40","name":"【专用】深圳大运中心体育场","position_type":"home_middle","image_url":"upload\/20250529\/13382349PX4yl_Dk.png","link_type":"performance","link_value":"69","sort_value":"66","link_type_desc":"演出项目","image_url_show":"http:\/\/*************\/resource\/play\/upload\/20250529\/13382349PX4yl_Dk.png"},{"id":"45","name":"luowt","position_type":"home_middle","image_url":"upload\/20250630\/144818XuaAA54TKR.png","link_type":"performance","link_value":"106","sort_value":"23","link_type_desc":"演出项目","image_url_show":"http:\/\/*************\/resource\/play\/upload\/20250630\/144818XuaAA54TKR.png"},{"id":"42","name":"wk新建的没有推广时间的","position_type":"home_middle","image_url":"upload\/20250529\/1732116kcMEpIoAV.png","link_type":"customize","link_value":"http:\/\/www.baoLY.com","sort_value":"22","link_type_desc":"自定义","image_url_show":"http:\/\/*************\/resource\/play\/upload\/20250529\/1732116kcMEpIoAV.png"},{"id":"30","name":"【勿动】测试城市","position_type":"home_middle","image_url":"upload\/20250528\/174625-aUjLRKWr1.jpg","link_type":"category","link_value":"10002","sort_value":"0","link_type_desc":"演出分类","image_url_show":"http:\/\/*************\/resource\/play\/upload\/20250528\/174625-aUjLRKWr1.jpg"},{"id":"29","name":"【勿动】QQR测试","position_type":"home_middle","image_url":"upload\/20250528\/173340QP-ZIAp90D.png","link_type":"performance","link_value":"26","sort_value":"-1","link_type_desc":"演出项目","image_url_show":"http:\/\/*************\/resource\/play\/upload\/20250528\/173340QP-ZIAp90D.png"}]}
INFO - 2025-07-07 13:33:44,267 - assertion_utils.py:35 -[assertion_utils:status_code_assert] - 状态码断言成功：接口实际返回状态码 200 == 200
INFO - 2025-07-07 13:33:44,268 - assertion_utils.py:94 -[assertion_utils:equal_assert] - 相等断言成功：接口实际结果 {'code': 0} == 预期结果：{'code': 0}
INFO - 2025-07-07 13:33:44,268 - assertion_utils.py:206 -[assertion_utils:assert_result] - 测试成功
INFO - 2025-07-07 13:33:44,279 - conftest.py:9 -[conftest:print_info] - ---------------接口测试结束---------------
INFO - 2025-07-07 13:33:45,802 - conftest.py:7 -[conftest:print_info] - ---------------接口测试开始---------------
INFO - 2025-07-07 13:33:45,814 - sendrequests.py:57 -[sendrequests:execute_api_request] - 接口名称：演唱会分类
INFO - 2025-07-07 13:33:45,814 - sendrequests.py:58 -[sendrequests:execute_api_request] - 请求地址：http://*************/api/show/info/performance_search
INFO - 2025-07-07 13:33:45,814 - sendrequests.py:59 -[sendrequests:execute_api_request] - 请求方式：POST
INFO - 2025-07-07 13:33:45,814 - sendrequests.py:60 -[sendrequests:execute_api_request] - 请求头：{'Content-Type': 'application/x-www-form-urlencoded', 'Connection': 'Keep-Alive', 'Accept-Encoding': 'gzip'}
INFO - 2025-07-07 13:33:45,814 - sendrequests.py:61 -[sendrequests:execute_api_request] - 测试用例名：按类型搜索=演唱会
INFO - 2025-07-07 13:33:45,814 - sendrequests.py:62 -[sendrequests:execute_api_request] - cookies值：None
INFO - 2025-07-07 13:33:45,814 - sendrequests.py:67 -[sendrequests:execute_api_request] - 参数类型：data
INFO - 2025-07-07 13:33:45,814 - sendrequests.py:69 -[sendrequests:execute_api_request] - 请求参数：{"category_id": "10001", "channelCode": "*********", "token": "lrDqVLEc8jnKaORIKiJB+y6nN1Io3lnKNueVwmjECnPs3mUEcTEbKdyma0pUIUY8s2ItjSU2QLLYFxywf0Grw9Hfdp3svbaytqb6Sc0oYJ/tO8wd56XQTybpwRkS8Qt8,*********"}
INFO - 2025-07-07 13:33:46,101 - apiutils_business.py:179 -[apiutils_business:execute_test_cases] - 接口实际返回结果：{"code":0,"msg":"","data":{"total":27,"list":[{"id":"26","name":"老无座-0313","category_id":"10001","venue_id":"8","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/d7db73c1934ce8fcb5c4003afe3522b594040.jpg","ticket_status":"3","unusual_status":"0","need_real_name":"0","first_show_time":"2025-08-30 20:00:00","last_show_time":"2025-12-01 02:00:00","on_sale_time":"2025-03-13 19:00:00","max_buy_limit_per_id":"0","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"4.00","max_sale_price":"55.00","shelve_status":"1","third_shelve_status":"1","sort":"210","venue_info":{"venue_id":"8","venue_name":"梅赛德斯-奔驰文化中心","province_title":"上海市","city_title":"浦东新区","address":"世博大道1200号","latitude":"31.188952","longitude":"121.493201"},"show_info":{"show_id":"217","performance_id":"26","show_name":"2025-08-30 周六 20:00","show_start_time":"2025-08-30 20:00:00","show_end_time":"2025-08-30 22:00:00","show_type":"1","show_seat_type":"0","show_real_name_limit":"0","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2025-05-20 16:46:26","off_sale_time":"2025-08-30 20:00:00"},"category_info":{"category_id":"10001","category_name":"演唱会"}},{"id":"22","name":"【外部分销对接专用，不可编辑】编辑请联系上单-新选座-实名制","category_id":"10001","venue_id":"8","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/d7db73c1934ce8fcb5c4003afe3522b594040.jpg","ticket_status":"3","unusual_status":"0","need_real_name":"1","first_show_time":"2025-12-31 01:00:00","last_show_time":"2028-01-01 01:00:00","on_sale_time":"2025-04-22 16:05:43","max_buy_limit_per_id":"6","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"2.00","max_sale_price":"6.00","shelve_status":"1","third_shelve_status":"1","sort":"90","venue_info":{"venue_id":"8","venue_name":"梅赛德斯-奔驰文化中心","province_title":"上海市","city_title":"浦东新区","address":"世博大道1200号","latitude":"31.188952","longitude":"121.493201"},"show_info":{"show_id":"40","performance_id":"22","show_name":"2025-12-31 周三 01:00","show_start_time":"2025-12-31 01:00:00","show_end_time":"2025-12-31 02:00:00","show_type":"1","show_seat_type":"1","show_real_name_limit":"1","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2024-12-10 13:18:31","off_sale_time":"2025-12-31 01:00:00"},"category_info":{"category_id":"10001","category_name":"演唱会"}},{"id":"48","name":"朱磊业新配座套票配座","category_id":"10001","venue_id":"7","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/84d2c832ab35b5514e8962a287d69c59631.png","ticket_status":"3","unusual_status":"0","need_real_name":"0","first_show_time":"2026-01-02 00:00:00","last_show_time":"2026-01-02 04:00:00","on_sale_time":"2025-01-09 11:28:59","max_buy_limit_per_id":"0","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"2.00","max_sale_price":"3.00","shelve_status":"1","third_shelve_status":"1","sort":"60","venue_info":{"venue_id":"7","venue_name":"凯迪拉克·上海音乐厅","province_title":"上海市","city_title":"黄浦区","address":"延安东路523号（近西藏南路）","latitude":"31.226854","longitude":"121.477977"},"show_info":{"show_id":"179","performance_id":"48","show_name":"2026-01-02 周五 00:00","show_start_time":"2026-01-02 00:00:00","show_end_time":"2026-01-02 04:00:00","show_type":"1","show_seat_type":"1","show_real_name_limit":"9999","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2025-01-20 16:24:00","off_sale_time":"2026-01-02 00:00:00"},"category_info":{"category_id":"10001","category_name":"演唱会"}},{"id":"51","name":"朱磊业新无座快递配送转其它","category_id":"10001","venue_id":"13","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/84d2c832ab35b5514e8962a287d69c59631.png","ticket_status":"12","unusual_status":"3","need_real_name":"0","first_show_time":"2026-01-01 00:00:00","last_show_time":"2028-12-15 02:00:00","on_sale_time":"2024-12-13 17:47:21","max_buy_limit_per_id":"0","max_buy_limit_per_order":"20","min_buy_limit_per_order":"0","max_buy_limit_per_user":"1","min_sale_price":"2.00","max_sale_price":"6.00","shelve_status":"1","third_shelve_status":"1","sort":"60","venue_info":{"venue_id":"13","venue_name":"虹桥天地演艺中心","province_title":"上海市","city_title":"闵行区","address":"绍虹路33号","latitude":"31.19331","longitude":"121.31583"},"show_info":{"show_id":"182","performance_id":"51","show_name":"2026-01-01 周四 00:00","show_start_time":"2026-01-01 00:00:00","show_end_time":"2026-01-01 03:00:00","show_type":"1","show_seat_type":"0","show_real_name_limit":"0","max_buy_limit_per_order":"20","show_unusual_status":"1","on_sale_time":"2024-12-24 15:57:38","off_sale_time":"2026-01-01 00:00:00"},"category_info":{"category_id":"10001","category_name":"演唱会"}},{"id":"53","name":"小红书推送商品验证（老项目喽）","category_id":"10001","venue_id":"7","poster_url":"https:\/\/p1.meituan.net\/myvideodistribute\/6563cbda84d54511adb254e69b0c8001239854.jpg","ticket_status":"3","unusual_status":"0","need_real_name":"0","first_show_time":"2025-07-30 11:00:00","last_show_time":"2025-09-30 20:00:00","on_sale_time":"2025-02-11 13:00:00","max_buy_limit_per_id":"0","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"11.00","max_sale_price":"367.00","shelve_status":"1","third_shelve_status":"1","sort":"60","venue_info":{"venue_id":"7","venue_name":"凯迪拉克·上海音乐厅","province_title":"上海市","city_title":"黄浦区","address":"延安东路523号（近西藏南路）","latitude":"31.226854","longitude":"121.477977"},"show_info":{"show_id":"312","performance_id":"53","show_name":"2025-07-30 周三 11:00","show_start_time":"2025-07-30 11:00:00","show_end_time":"2025-07-30 19:00:00","show_type":"1","show_seat_type":"0","show_real_name_limit":"0","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2025-06-26 14:33:53","off_sale_time":"2025-07-30 11:00:00"},"category_info":{"category_id":"10001","category_name":"演唱会"}},{"id":"4","name":"三方导码","category_id":"10001","venue_id":"8","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/680352446992ad3846ab6542da1d1400288377.jpg","ticket_status":"3","unusual_status":"0","need_real_name":"1","first_show_time":"2025-11-06 00:00:00","last_show_time":"2025-11-06 01:00:00","on_sale_time":"2024-11-06 15:30:47","max_buy_limit_per_id":"6","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"2.00","max_sale_price":"3.00","shelve_status":"1","third_shelve_status":"1","sort":"50","venue_info":{"venue_id":"8","venue_name":"梅赛德斯-奔驰文化中心","province_title":"上海市","city_title":"浦东新区","address":"世博大道1200号","latitude":"31.188952","longitude":"121.493201"},"show_info":{"show_id":"10","performance_id":"4","show_name":"2025-11-06 周四 00:00","show_start_time":"2025-11-06 00:00:00","show_end_time":"2025-11-06 01:00:00","show_type":"1","show_seat_type":"0","show_real_name_limit":"0","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2024-11-06 15:29:42","off_sale_time":"2025-11-06 00:00:00"},"category_info":{"category_id":"10001","category_name":"演唱会"}},{"id":"10","name":"老上单实名制","category_id":"10001","venue_id":"7","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/cabb76c33aad7247623a0a827cec6a7e8066.jpg","ticket_status":"3","unusual_status":"0","need_real_name":"1","first_show_time":"2025-09-01 02:00:00","last_show_time":"2025-09-01 04:00:00","on_sale_time":"2025-04-30 23:00:00","max_buy_limit_per_id":"6","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"2.00","max_sale_price":"2.00","shelve_status":"1","third_shelve_status":"1","sort":"50","venue_info":{"venue_id":"7","venue_name":"凯迪拉克·上海音乐厅","province_title":"上海市","city_title":"黄浦区","address":"延安东路523号（近西藏南路）","latitude":"31.226854","longitude":"121.477977"},"show_info":{"show_id":"28","performance_id":"10","show_name":"2025-09-01 周一 02:00","show_start_time":"2025-09-01 02:00:00","show_end_time":"2025-09-01 04:00:00","show_type":"1","show_seat_type":"0","show_real_name_limit":"1","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2025-02-24 10:33:25","off_sale_time":"2025-09-01 02:00:00"},"category_info":{"category_id":"10001","category_name":"演唱会"}},{"id":"12","name":"【售卖场】2.11无座-2026.12.28","category_id":"10001","venue_id":"7","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/03fdb9f41adcf253ff9211097ab444421598915.png","ticket_status":"3","unusual_status":"0","need_real_name":"1","first_show_time":"2026-12-28 19:00:00","last_show_time":"2026-12-31 23:00:00","on_sale_time":"2025-02-11 14:53:19","max_buy_limit_per_id":"10","max_buy_limit_per_order":"4","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"748.00","max_sale_price":"2178.00","shelve_status":"1","third_shelve_status":"1","sort":"50","venue_info":{"venue_id":"7","venue_name":"凯迪拉克·上海音乐厅","province_title":"上海市","city_title":"黄浦区","address":"延安东路523号（近西藏南路）","latitude":"31.226854","longitude":"121.477977"},"show_info":{"show_id":"30","performance_id":"12","show_name":"2026-12-28 周一 19:00","show_start_time":"2026-12-28 19:00:00","show_end_time":"2026-12-28 23:00:00","show_type":"1","show_seat_type":"0","show_real_name_limit":"1","max_buy_limit_per_order":"2","show_unusual_status":"0","on_sale_time":"2025-02-11 11:33:38","off_sale_time":"2026-12-28 00:00:00"},"category_info":{"category_id":"10001","category_name":"演唱会"}},{"id":"14","name":"zly测试渠道","category_id":"10001","venue_id":"10","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/84d2c832ab35b5514e8962a287d69c59631.png","ticket_status":"3","unusual_status":"0","need_real_name":"0","first_show_time":"2026-02-06 00:00:00","last_show_time":"2026-02-06 05:00:00","on_sale_time":"2025-03-07 00:00:00","max_buy_limit_per_id":"6","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"2.00","max_sale_price":"2.00","shelve_status":"1","third_shelve_status":"1","sort":"50","venue_info":{"venue_id":"10","venue_name":"猫眼演出321","province_title":"北京市","city_title":"朝阳区","address":"望京sohoT3B栋2楼","latitude":"39.996126","longitude":"116.480553"},"show_info":{"show_id":"34","performance_id":"14","show_name":"2026-02-06 周五 00:00","show_start_time":"2026-02-06 00:00:00","show_end_time":"2026-02-06 05:00:00","show_type":"1","show_seat_type":"1","show_real_name_limit":"0","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2025-02-17 15:57:48","off_sale_time":"2026-02-06 00:00:00"},"category_info":{"category_id":"10001","category_name":"演唱会"}},{"id":"19","name":"和净子账号测试项目2-0641","category_id":"10001","venue_id":"15","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/7f13fafffa5464aec7c728efca22be0a24721.jpg","ticket_status":"12","unusual_status":"1","need_real_name":"0","first_show_time":"2025-11-01 00:00:00","last_show_time":"2025-11-01 01:00:00","on_sale_time":"2025-03-07 18:35:00","max_buy_limit_per_id":"0","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"4.00","max_sale_price":"5.00","shelve_status":"1","third_shelve_status":"1","sort":"50","venue_info":{"venue_id":"15","venue_name":"北京展览馆剧场","province_title":"北京市","city_title":"西城区","address":"西直门外大街135号","latitude":"39.942007","longitude":"116.343911"},"show_info":{"show_id":"38","performance_id":"19","show_name":"2025-11-01 周六 00:00","show_start_time":"2025-11-01 00:00:00","show_end_time":"2025-11-01 01:00:00","show_type":"1","show_seat_type":"0","show_real_name_limit":"0","max_buy_limit_per_order":"6","show_unusual_status":"2","on_sale_time":"2024-12-26 19:40:48","off_sale_time":"2025-11-01 00:00:00"},"category_info":{"category_id":"10001","category_name":"演唱会"}}]}}
INFO - 2025-07-07 13:33:46,108 - assertion_utils.py:35 -[assertion_utils:status_code_assert] - 状态码断言成功：接口实际返回状态码 200 == 200
INFO - 2025-07-07 13:33:46,110 - assertion_utils.py:94 -[assertion_utils:equal_assert] - 相等断言成功：接口实际结果 {'code': 0} == 预期结果：{'code': 0}
INFO - 2025-07-07 13:33:46,110 - assertion_utils.py:206 -[assertion_utils:assert_result] - 测试成功
INFO - 2025-07-07 13:33:46,120 - conftest.py:9 -[conftest:print_info] - ---------------接口测试结束---------------
INFO - 2025-07-07 13:33:47,642 - conftest.py:7 -[conftest:print_info] - ---------------接口测试开始---------------
INFO - 2025-07-07 13:33:47,652 - sendrequests.py:57 -[sendrequests:execute_api_request] - 接口名称：**搜索**
INFO - 2025-07-07 13:33:47,652 - sendrequests.py:58 -[sendrequests:execute_api_request] - 请求地址：http://*************/api/show/info/performance_search
INFO - 2025-07-07 13:33:47,652 - sendrequests.py:59 -[sendrequests:execute_api_request] - 请求方式：POST
INFO - 2025-07-07 13:33:47,652 - sendrequests.py:60 -[sendrequests:execute_api_request] - 请求头：{'Content-Type': 'application/x-www-form-urlencoded', 'Connection': 'Keep-Alive', 'Accept-Encoding': 'gzip'}
INFO - 2025-07-07 13:33:47,652 - sendrequests.py:61 -[sendrequests:execute_api_request] - 测试用例名：按演出名称关键字搜索
INFO - 2025-07-07 13:33:47,652 - sendrequests.py:62 -[sendrequests:execute_api_request] - cookies值：None
INFO - 2025-07-07 13:33:47,652 - sendrequests.py:67 -[sendrequests:execute_api_request] - 参数类型：data
INFO - 2025-07-07 13:33:47,652 - sendrequests.py:69 -[sendrequests:execute_api_request] - 请求参数：{"keyword": "演", "channelCode": "*********", "token": "lrDqVLEc8jnKaORIKiJB+y6nN1Io3lnKNueVwmjECnPs3mUEcTEbKdyma0pUIUY8s2ItjSU2QLLYFxywf0Grw9Hfdp3svbaytqb6Sc0oYJ/tO8wd56XQTybpwRkS8Qt8,*********"}
INFO - 2025-07-07 13:33:47,928 - apiutils_business.py:179 -[apiutils_business:execute_test_cases] - 接口实际返回结果：{"code":0,"msg":"","data":{"total":12,"list":[{"id":"17","name":"老无座-非大演-0227","category_id":"10004","venue_id":"8","poster_url":"https:\/\/p0.meituan.net\/myvideodistribute\/d7db73c1934ce8fcb5c4003afe3522b594040.jpg","ticket_status":"3","unusual_status":"0","need_real_name":"0","first_show_time":"2025-12-31 00:00:00","last_show_time":"2025-12-31 01:00:00","on_sale_time":"2025-02-27 19:55:09","max_buy_limit_per_id":"0","max_buy_limit_per_order":"10","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"11.00","max_sale_price":"33.00","shelve_status":"1","third_shelve_status":"1","sort":"50","venue_info":{"venue_id":"8","venue_name":"梅赛德斯-奔驰文化中心","province_title":"上海市","city_title":"浦东新区","address":"世博大道1200号","latitude":"31.188952","longitude":"121.493201"},"show_info":{"show_id":"36","performance_id":"17","show_name":"2025-12-31 周三 00:00","show_start_time":"2025-12-31 00:00:00","show_end_time":"2025-12-31 01:00:00","show_type":"1","show_seat_type":"0","show_real_name_limit":"0","max_buy_limit_per_order":"10","show_unusual_status":"0","on_sale_time":"2025-02-27 19:54:03","off_sale_time":"2025-12-31 00:00:00"},"category_info":{"category_id":"10004","category_name":"话剧音乐剧"}},{"id":"36","name":"新创建2.0项目wt0402--大演1","category_id":"10001","venue_id":"13","poster_url":"https:\/\/p0.pipi.cn\/mediaplus\/fantasy_perform_fe\/0fa3343d52905119bf8457350de54ce83cc10.jpeg?imageMogr2\/quality\/80","ticket_status":"3","unusual_status":"0","need_real_name":"1","first_show_time":"2026-04-01 00:00:00","last_show_time":"2026-04-10 03:00:00","on_sale_time":null,"max_buy_limit_per_id":"6","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"25.00","max_sale_price":"123.00","shelve_status":"1","third_shelve_status":"1","sort":"50","venue_info":{"venue_id":"13","venue_name":"虹桥天地演艺中心","province_title":"上海市","city_title":"闵行区","address":"绍虹路33号","latitude":"31.19331","longitude":"121.31583"},"show_info":{"show_id":"98","performance_id":"36","show_name":"2026-04-01 周三 00:00","show_start_time":"2026-04-01 00:00:00","show_end_time":"2026-04-01 01:00:00","show_type":"1","show_seat_type":"0","show_real_name_limit":"1","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2025-04-02 17:54:44","off_sale_time":"2026-04-01 00:00:00"},"category_info":{"category_id":"10001","category_name":"演唱会"}},{"id":"37","name":"新创建2.0项目wt0402--大演","category_id":"10001","venue_id":"13","poster_url":"https:\/\/p0.pipi.cn\/mediaplus\/fantasy_perform_fe\/0fa3343d52905119bf8457350de54ce83cc10.jpeg?imageMogr2\/quality\/80","ticket_status":"3","unusual_status":"0","need_real_name":"1","first_show_time":"2026-04-01 00:00:00","last_show_time":"2026-05-03 02:00:00","on_sale_time":"2025-04-02 16:34:30","max_buy_limit_per_id":"6","max_buy_limit_per_order":"6","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"13.00","max_sale_price":"17.00","shelve_status":"1","third_shelve_status":"1","sort":"50","venue_info":{"venue_id":"13","venue_name":"虹桥天地演艺中心","province_title":"上海市","city_title":"闵行区","address":"绍虹路33号","latitude":"31.19331","longitude":"121.31583"},"show_info":{"show_id":"108","performance_id":"37","show_name":"2026-04-01 周三 00:00","show_start_time":"2026-04-01 00:00:00","show_end_time":"2026-04-01 02:00:00","show_type":"1","show_seat_type":"0","show_real_name_limit":"1","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2025-04-02 16:33:14","off_sale_time":"2026-04-01 00:00:00"},"category_info":{"category_id":"10001","category_name":"演唱会"}},{"id":"65","name":"李胜素、于魁智联袂中国戏曲学院名家教授演绎梅派名剧《穆桂英挂帅》+丁蕾","category_id":"10003","venue_id":"25","poster_url":"https:\/\/p0.pipi.cn\/mediaplus\/fantasy_perform_fe\/0fa3345bc8b4a115d5f87bbcda03261e22dfb.jpg?imageMogr2\/quality\/80","ticket_status":"3","unusual_status":"0","need_real_name":"0","first_show_time":"2025-09-06 19:30:00","last_show_time":"2025-09-06 21:00:00","on_sale_time":"2025-05-22 18:20:54","max_buy_limit_per_id":"0","max_buy_limit_per_order":"9999","min_buy_limit_per_order":"0","max_buy_limit_per_user":"9999","min_sale_price":"198.00","max_sale_price":"968.00","shelve_status":"1","third_shelve_status":"1","sort":"50","venue_info":{"venue_id":"25","venue_name":"北京音乐厅","province_title":"北京市","city_title":"西城区","address":"北新华街1号","latitude":"39.906784","longitude":"116.382384"},"show_info":{"show_id":"219","performance_id":"65","show_name":"2025-09-06 周六 19:30","show_start_time":"2025-09-06 19:30:00","show_end_time":"2025-09-06 21:00:00","show_type":"1","show_seat_type":"0","show_real_name_limit":"1","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2025-05-22 12:00:00","off_sale_time":"2025-09-06 19:30:00"},"category_info":{"category_id":"10003","category_name":"戏曲艺术"}},{"id":"66","name":"陈奕迅《FEAR AND DREAMS世界巡回演唱会》-北京站+丁蕾","category_id":"10001","venue_id":"25","poster_url":"https:\/\/p0.pipi.cn\/mediaplus\/fantasy_perform_fe\/0fa3345bc8be328dd477e1f0196d9d6164381.png?imageMogr2\/quality\/80","ticket_status":"1","unusual_status":"0","need_real_name":"1","first_show_time":"2025-07-11 19:00:00","last_show_time":"2025-07-20 22:00:00","on_sale_time":null,"max_buy_limit_per_id":"6","max_buy_limit_per_order":"9999","min_buy_limit_per_order":"0","max_buy_limit_per_user":"24","min_sale_price":"748.00","max_sale_price":"2838.00","shelve_status":"1","third_shelve_status":"1","sort":"50","venue_info":{"venue_id":"25","venue_name":"北京音乐厅","province_title":"北京市","city_title":"西城区","address":"北新华街1号","latitude":"39.906784","longitude":"116.382384"},"show_info":{"show_id":"220","performance_id":"66","show_name":"2025-07-11 周五 19:00","show_start_time":"2025-07-11 19:00:00","show_end_time":"2025-07-11 22:00:00","show_type":"1","show_seat_type":"1","show_real_name_limit":"1","max_buy_limit_per_order":"4","show_unusual_status":"0","on_sale_time":"2025-05-22 17:53:40","off_sale_time":"2025-07-11 22:00:00"},"category_info":{"category_id":"10001","category_name":"演唱会"}},{"id":"80","name":"杨丽萍导演舞剧《春之祭》+dinglei","category_id":"10005","venue_id":"26","poster_url":"https:\/\/p0.pipi.cn\/mediaplus\/fantasy_perform_fe\/0fa33425e328d3d8a1ea40daaf1b74ca02eaa.png?imageMogr2\/quality\/80","ticket_status":"3","unusual_status":"0","need_real_name":"1","first_show_time":"2025-07-26 19:30:00","last_show_time":"2025-07-26 21:00:00","on_sale_time":"2025-06-17 14:31:23","max_buy_limit_per_id":"9999","max_buy_limit_per_order":"9999","min_buy_limit_per_order":"0","max_buy_limit_per_user":"9999","min_sale_price":"1.00","max_sale_price":"1162.00","shelve_status":"1","third_shelve_status":"1","sort":"50","venue_info":{"venue_id":"26","venue_name":"工人体育场","province_title":"北京市","city_title":"朝阳区","address":"工体北路与新东街交叉口西南角","latitude":"39.93069","longitude":"116.447211"},"show_info":{"show_id":"279","performance_id":"80","show_name":"2025-07-26 周六 19:30","show_start_time":"2025-07-26 19:30:00","show_end_time":"2025-07-26 21:00:00","show_type":"1","show_seat_type":"1","show_real_name_limit":"1","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2025-06-17 00:30:00","off_sale_time":"2025-07-26 19:30:00"},"category_info":{"category_id":"10005","category_name":"舞蹈芭蕾"}},{"id":"83","name":"次元盛典.热门二次元动漫ACG乐队番演唱会+丁蕾","category_id":"10006","venue_id":"25","poster_url":"https:\/\/p0.pipi.cn\/mediaplus\/fantasy_perform_fe\/0fa33425e3277e25ce25cbab374b995fae36f.png?imageMogr2\/quality\/80","ticket_status":"3","unusual_status":"0","need_real_name":"0","first_show_time":"2025-07-26 19:30:00","last_show_time":"2025-07-26 21:00:00","on_sale_time":"2025-06-17 13:47:18","max_buy_limit_per_id":"0","max_buy_limit_per_order":"9999","min_buy_limit_per_order":"0","max_buy_limit_per_user":"9999","min_sale_price":"1.00","max_sale_price":"1.00","shelve_status":"1","third_shelve_status":"1","sort":"50","venue_info":{"venue_id":"25","venue_name":"北京音乐厅","province_title":"北京市","city_title":"西城区","address":"北新华街1号","latitude":"39.906784","longitude":"116.382384"},"show_info":{"show_id":"283","performance_id":"83","show_name":"2025-07-26 周六 19:30","show_start_time":"2025-07-26 19:30:00","show_end_time":"2025-07-26 21:00:00","show_type":"1","show_seat_type":"1","show_real_name_limit":"1","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2025-06-17 00:30:00","off_sale_time":"2025-07-26 19:30:00"},"category_info":{"category_id":"10006","category_name":"音乐会"}},{"id":"85","name":"2025虚拟歌姬 IA 中国巡演13周年纪念版PARTY A GOGO巡游派对「轨迹与奇迹」广州站","category_id":"10017","venue_id":"25","poster_url":"https:\/\/p0.pipi.cn\/mediaplus\/fantasy_perform_fe\/0fa334254a177e14dca219726706cf7bca309.jpg?imageMogr2\/quality\/80","ticket_status":"2","unusual_status":"0","need_real_name":"1","first_show_time":"2025-08-10 20:00:00","last_show_time":"2025-08-10 21:40:00","on_sale_time":"2025-06-18 19:18:00","max_buy_limit_per_id":"1","max_buy_limit_per_order":"9999","min_buy_limit_per_order":"0","max_buy_limit_per_user":"2","min_sale_price":"418.00","max_sale_price":"968.00","shelve_status":"1","third_shelve_status":"1","sort":"50","venue_info":{"venue_id":"25","venue_name":"北京音乐厅","province_title":"北京市","city_title":"西城区","address":"北新华街1号","latitude":"39.906784","longitude":"116.382384"},"show_info":{"show_id":"285","performance_id":"85","show_name":"2025-08-10 周日 20:00","show_start_time":"2025-08-10 20:00:00","show_end_time":"2025-08-10 21:40:00","show_type":"1","show_seat_type":"0","show_real_name_limit":"1","max_buy_limit_per_order":"2","show_unusual_status":"0","on_sale_time":"2025-06-18 00:00:00","off_sale_time":"2025-08-10 20:00:00"},"category_info":{"category_id":"10017","category_name":"Livehouse"}},{"id":"88","name":"哈瓦那的偶像演唱团中国巡回演唱会（赖）","category_id":"10001","venue_id":"25","poster_url":"https:\/\/p0.pipi.cn\/mediaplus\/fantasy_perform_fe\/0fa334254a1ea46b1b374cf3266bdd1cc1245.jpg?imageMogr2\/quality\/80","ticket_status":"3","unusual_status":"0","need_real_name":"0","first_show_time":"2025-07-23 19:00:00","last_show_time":"2025-07-23 20:30:00","on_sale_time":"2025-06-18 15:34:46","max_buy_limit_per_id":"0","max_buy_limit_per_order":"9999","min_buy_limit_per_order":"0","max_buy_limit_per_user":"20","min_sale_price":"132.00","max_sale_price":"220.00","shelve_status":"1","third_shelve_status":"1","sort":"50","venue_info":{"venue_id":"25","venue_name":"北京音乐厅","province_title":"北京市","city_title":"西城区","address":"北新华街1号","latitude":"39.906784","longitude":"116.382384"},"show_info":{"show_id":"287","performance_id":"88","show_name":"2025-07-23 周三 19:00","show_start_time":"2025-07-23 19:00:00","show_end_time":"2025-07-23 20:30:00","show_type":"1","show_seat_type":"1","show_real_name_limit":"1","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2025-06-18 15:36:22","off_sale_time":"2025-07-23 19:00:00"},"category_info":{"category_id":"10001","category_name":"演唱会"}},{"id":"93","name":"有我·天使MAX成团演唱会+罗媛","category_id":"10017","venue_id":"25","poster_url":"https:\/\/p0.pipi.cn\/mediaplus\/fantasy_perform_fe\/0fa3342577ee323cbca2194cb125aba838a8d.jpg?imageMogr2\/quality\/80","ticket_status":"3","unusual_status":"0","need_real_name":"1","first_show_time":"2025-07-18 19:30:00","last_show_time":"2025-07-18 21:00:00","on_sale_time":"2025-06-19 20:00:00","max_buy_limit_per_id":"9999","max_buy_limit_per_order":"9999","min_buy_limit_per_order":"0","max_buy_limit_per_user":"9999","min_sale_price":"418.00","max_sale_price":"748.00","shelve_status":"1","third_shelve_status":"1","sort":"50","venue_info":{"venue_id":"25","venue_name":"北京音乐厅","province_title":"北京市","city_title":"西城区","address":"北新华街1号","latitude":"39.906784","longitude":"116.382384"},"show_info":{"show_id":"293","performance_id":"93","show_name":"2025-07-18 周五 19:30","show_start_time":"2025-07-18 19:30:00","show_end_time":"2025-07-18 21:00:00","show_type":"1","show_seat_type":"0","show_real_name_limit":"6","max_buy_limit_per_order":"6","show_unusual_status":"0","on_sale_time":"2025-06-19 17:01:30","off_sale_time":"2025-07-18 19:30:00"},"category_info":{"category_id":"10017","category_name":"Livehouse"}}]}}
INFO - 2025-07-07 13:33:47,935 - assertion_utils.py:35 -[assertion_utils:status_code_assert] - 状态码断言成功：接口实际返回状态码 200 == 200
INFO - 2025-07-07 13:33:47,936 - assertion_utils.py:94 -[assertion_utils:equal_assert] - 相等断言成功：接口实际结果 {'code': 0} == 预期结果：{'code': 0}
INFO - 2025-07-07 13:33:47,936 - assertion_utils.py:206 -[assertion_utils:assert_result] - 测试成功
INFO - 2025-07-07 13:33:47,946 - conftest.py:9 -[conftest:print_info] - ---------------接口测试结束---------------
INFO - 2025-07-07 13:33:49,466 - conftest.py:7 -[conftest:print_info] - ---------------接口测试开始---------------
INFO - 2025-07-07 13:33:49,477 - sendrequests.py:57 -[sendrequests:execute_api_request] - 接口名称：演出项目详情
INFO - 2025-07-07 13:33:49,477 - sendrequests.py:58 -[sendrequests:execute_api_request] - 请求地址：http://*************/api/show/info/performance_info
INFO - 2025-07-07 13:33:49,477 - sendrequests.py:59 -[sendrequests:execute_api_request] - 请求方式：GET
INFO - 2025-07-07 13:33:49,477 - sendrequests.py:60 -[sendrequests:execute_api_request] - 请求头：{'Content-Type': 'application/x-www-form-urlencoded', 'Connection': 'Keep-Alive', 'Accept-Encoding': 'gzip'}
INFO - 2025-07-07 13:33:49,477 - sendrequests.py:61 -[sendrequests:execute_api_request] - 测试用例名：获取演出项目详情信息
INFO - 2025-07-07 13:33:49,477 - sendrequests.py:62 -[sendrequests:execute_api_request] - cookies值：None
INFO - 2025-07-07 13:33:49,477 - sendrequests.py:67 -[sendrequests:execute_api_request] - 参数类型：params
INFO - 2025-07-07 13:33:49,477 - sendrequests.py:69 -[sendrequests:execute_api_request] - 请求参数：{"performanceId": "26", "channelCode": "*********", "token": "lrDqVLEc8jnKaORIKiJB+y6nN1Io3lnKNueVwmjECnPs3mUEcTEbKdyma0pUIUY8s2ItjSU2QLLYFxywf0Grw9Hfdp3svbaytqb6Sc0oYJ/tO8wd56XQTybpwRkS8Qt8,*********"}
INFO - 2025-07-07 13:33:50,058 - apiutils_business.py:179 -[apiutils_business:execute_test_cases] - 接口实际返回结果：{"code":0,"msg":"","data":{"performanceId":"26","city":{"cityId":1,"cityName":"上海"},"category":{"categoryId":1,"categoryName":"演唱会"},"venue":{"venueId":"8","name":"梅赛德斯-奔驰文化中心","address":"世博大道1200号","cityId":1,"latitude":31.188952,"longitude":121.493201},"name":"老无座-0313","posterUrl":"https:\/\/p0.meituan.net\/myvideodistribute\/d7db73c1934ce8fcb5c4003afe3522b594040.jpg","seatUrl":"","ticketNotes":"<p><strong>√演出\/活动时长<\/strong><\/p><p>以现场为准<\/p><p><br><\/p><p><strong>√最低演出曲目<\/strong><\/p><p>以现场为准<\/p><p><br><\/p><p><strong>√主要演员<\/strong><\/p><p>以现场为准<\/p><p><br><\/p><p><strong>√最低演出\/活动时长<\/strong><\/p><p>以现场为准<\/p><p><br><\/p><p><strong>√预约说明<\/strong><\/p><p>无需预约<\/p><p><br><\/p><p><strong>√儿童说明<\/strong><\/p><p>1.2米以下儿童谢绝入场，1.2米以上儿童需持票入场<\/p><p><br><\/p><p><strong>√发票说明<\/strong><\/p><p>该项目支持开具电子发票，发票由第三方主办提供，请在演出结束前通过订单详情页提交发票申请，一般演出结束后1个月左右开具，开票方名称以实际收到的发票为准。<\/p><p><br><\/p><p><strong>√异常购票<\/strong><\/p><p>为了确保广大消费者的利益，对于异常订购行为，猫眼演出有权取消相应订单并且通过系统原路退回该订单下全部票款，不予配票。异常订购行为包括但不限于：<br>（1）同一用户订购超出限购张数的订单；<br>（2）经合理判断为非真实消费者的订购行为，包括但不限于使用软件、爬虫技术等进行恶意刷票；<br>（3）通过批量相同或虚构的支付账号、收货地址、收货人、电话号码订购超出限购张数的订单。<\/p><p><br><\/p><p><strong>√禁止携带物品<\/strong><\/p><p>由于安保和版权的原因，大多数演出、展览及比赛场所禁止携带食品、饮料、专业摄录设备、打火机等物品，请您注意现场工作人员和广播的提示，予以配合。<\/p><p><br><\/p><p><strong>√付款时效提醒<\/strong><\/p><p>下单成功后需在指定时间内完成支付，未支付成功的订单，将在下单指定时间后系统自动取消，请及时刷新购票页面进行购买。<\/p><p><br><\/p><p><strong>√特殊提示<\/strong><\/p><p>因市场变化、不可抗力等客观情形以及演出票随订随售的性质，可能会出现演出变更、取消、或正式出票时票品库存不足等情况，该等情形下猫眼客服会及时与您联系并尽快退款。<\/p><p><br><\/p><p><strong>√退换政策<\/strong><\/p><p>退款<\/p><p><br><\/p><p><strong>√实名制购票<\/strong><\/p><p>无需实名制购票<\/p><p><br><\/p><p><strong>√限购说明<\/strong><\/p><p>每笔订单最多购买6张，以实际购票情况为准<\/p><p><br><\/p><p><strong>√入场规则<\/strong><\/p><p>·电子票：购买电子票的用户，可凭二维码直接入场，无需纸质门票<br><\/p><p><br><\/p>","detail":"<p>11111<\/p>","celebrityList":[],"ticketStatus":3,"shelveStatus":1,"needRealName":false,"firstShowTime":"2025-08-30 20:00:00","lastShowTime":"2025-12-01 02:00:00","sort":50,"createTime":"2025-03-13 14:19:59","onSaleTime":"","openBuyProjectLimit":{"maxBuyLimitPerId":0,"maxBuyLimitPerOrder":6,"minBuyLimitPerOrder":0,"maxBuyLimitPerUser":20},"exclusiveSales":0,"soleAgent":0,"seatType":0,"unusualStatus":0,"performanceDetailUrl":null,"minPrice":"4.00","maxPrice":"55.00","seatMode":0,"fetchTicketWayList":["电子票"],"idTypeList":null,"minDiscount":null,"payExpireTime":15,"priceRangeType":0,"conditionRefund":1,"isNewMode":0,"showInfo":[]}}
INFO - 2025-07-07 13:33:50,064 - assertion_utils.py:35 -[assertion_utils:status_code_assert] - 状态码断言成功：接口实际返回状态码 200 == 200
INFO - 2025-07-07 13:33:50,064 - assertion_utils.py:94 -[assertion_utils:equal_assert] - 相等断言成功：接口实际结果 {'code': 0} == 预期结果：{'code': 0}
INFO - 2025-07-07 13:33:50,065 - assertion_utils.py:206 -[assertion_utils:assert_result] - 测试成功
INFO - 2025-07-07 13:33:50,074 - conftest.py:9 -[conftest:print_info] - ---------------接口测试结束---------------
INFO - 2025-07-07 13:33:51,592 - conftest.py:7 -[conftest:print_info] - ---------------接口测试开始---------------
INFO - 2025-07-07 13:33:51,603 - sendrequests.py:57 -[sendrequests:execute_api_request] - 接口名称：演出场次列表
INFO - 2025-07-07 13:33:51,603 - sendrequests.py:58 -[sendrequests:execute_api_request] - 请求地址：http://*************/api/show/info/performance_shows
INFO - 2025-07-07 13:33:51,603 - sendrequests.py:59 -[sendrequests:execute_api_request] - 请求方式：GET
INFO - 2025-07-07 13:33:51,603 - sendrequests.py:60 -[sendrequests:execute_api_request] - 请求头：{'Content-Type': 'application/x-www-form-urlencoded', 'Connection': 'Keep-Alive', 'Accept-Encoding': 'gzip'}
INFO - 2025-07-07 13:33:51,603 - sendrequests.py:61 -[sendrequests:execute_api_request] - 测试用例名：获取演出场次列表信息
INFO - 2025-07-07 13:33:51,603 - sendrequests.py:62 -[sendrequests:execute_api_request] - cookies值：None
INFO - 2025-07-07 13:33:51,603 - sendrequests.py:67 -[sendrequests:execute_api_request] - 参数类型：params
INFO - 2025-07-07 13:33:51,603 - sendrequests.py:69 -[sendrequests:execute_api_request] - 请求参数：{"performanceId": "26", "channelCode": "*********", "token": "lrDqVLEc8jnKaORIKiJB+y6nN1Io3lnKNueVwmjECnPs3mUEcTEbKdyma0pUIUY8s2ItjSU2QLLYFxywf0Grw9Hfdp3svbaytqb6Sc0oYJ/tO8wd56XQTybpwRkS8Qt8,*********"}
INFO - 2025-07-07 13:33:52,124 - apiutils_business.py:179 -[apiutils_business:execute_test_cases] - 接口实际返回结果：{"code":0,"msg":"","data":{"shows":[{"showId":"217","performanceId":"26","name":"2025-08-30 周六 20:00","showNote":"","startTime":"2025-08-30 20:00:00","endTime":"2025-08-30 22:00:00","showType":1,"showSeatType":0,"areaSvg":null,"areaUrl":null,"showUnusualStatus":0,"showRealNameLimit":null,"maxBuyLimitPerUser":null,"maxBuyLimitPerOrder":6,"onSaleTime":"2025-05-20 16:46:26","offSaleTime":"2025-08-30 20:00:00","saleable":1},{"showId":"48","performanceId":"26","name":"2025-12-01 周一 00:00","showNote":"","startTime":"2025-12-01 00:00:00","endTime":"2025-12-01 02:00:00","showType":1,"showSeatType":0,"areaSvg":null,"areaUrl":null,"showUnusualStatus":0,"showRealNameLimit":null,"maxBuyLimitPerUser":null,"maxBuyLimitPerOrder":6,"onSaleTime":"2025-03-13 14:21:40","offSaleTime":"2025-12-01 00:00:00","saleable":1}]}}
INFO - 2025-07-07 13:33:52,130 - assertion_utils.py:35 -[assertion_utils:status_code_assert] - 状态码断言成功：接口实际返回状态码 200 == 200
INFO - 2025-07-07 13:33:52,132 - assertion_utils.py:94 -[assertion_utils:equal_assert] - 相等断言成功：接口实际结果 {'code': 0} == 预期结果：{'code': 0}
INFO - 2025-07-07 13:33:52,132 - assertion_utils.py:206 -[assertion_utils:assert_result] - 测试成功
INFO - 2025-07-07 13:33:52,143 - conftest.py:9 -[conftest:print_info] - ---------------接口测试结束---------------
INFO - 2025-07-07 13:33:53,660 - conftest.py:7 -[conftest:print_info] - ---------------接口测试开始---------------
INFO - 2025-07-07 13:33:53,681 - sendrequests.py:57 -[sendrequests:execute_api_request] - 接口名称：项目下场次票品列表
INFO - 2025-07-07 13:33:53,681 - sendrequests.py:58 -[sendrequests:execute_api_request] - 请求地址：http://*************/api/show/info/show_ticket_units
INFO - 2025-07-07 13:33:53,681 - sendrequests.py:59 -[sendrequests:execute_api_request] - 请求方式：GET
INFO - 2025-07-07 13:33:53,681 - sendrequests.py:60 -[sendrequests:execute_api_request] - 请求头：{'Content-Type': 'application/x-www-form-urlencoded', 'Connection': 'Keep-Alive', 'Accept-Encoding': 'gzip'}
INFO - 2025-07-07 13:33:53,681 - sendrequests.py:61 -[sendrequests:execute_api_request] - 测试用例名：获取项目下场次票品列表信息
INFO - 2025-07-07 13:33:53,681 - sendrequests.py:62 -[sendrequests:execute_api_request] - cookies值：None
INFO - 2025-07-07 13:33:53,681 - sendrequests.py:67 -[sendrequests:execute_api_request] - 参数类型：params
INFO - 2025-07-07 13:33:53,681 - sendrequests.py:69 -[sendrequests:execute_api_request] - 请求参数：{"showId": "48", "channelCode": "*********", "token": "lrDqVLEc8jnKaORIKiJB+y6nN1Io3lnKNueVwmjECnPs3mUEcTEbKdyma0pUIUY8s2ItjSU2QLLYFxywf0Grw9Hfdp3svbaytqb6Sc0oYJ/tO8wd56XQTybpwRkS8Qt8,*********"}
INFO - 2025-07-07 13:33:54,230 - apiutils_business.py:179 -[apiutils_business:execute_test_cases] - 接口实际返回结果：{"code":0,"msg":"","data":{"ticketUnits":[{"ticketUnitId":"214","showId":"48","name":null,"ticketName":"","minBuyLimit":1,"maxBuyLimit":6,"needRealName":false,"realNameLimit":0,"ticketLevel":"VVIP","rgb":"#FF4242","ticketPrice":"10","setNumber":1,"sellPriceList":[11,22,33,44,55,66,77,88,99,110,121,132,143,154,165,176,187,198,209,220],"limited":true,"sellStatus":3,"currentAmount":20,"baseTicketUnitId":null,"thirdTicketUnitId":16905302,"thirdBaseTicketUnitId":0},{"ticketUnitId":"215","showId":"48","name":null,"ticketName":"","minBuyLimit":1,"maxBuyLimit":6,"needRealName":false,"realNameLimit":0,"ticketLevel":"VIP","rgb":"#FF6FE4","ticketPrice":"20","setNumber":1,"sellPriceList":[22,44,66,88,110,132,154,176,198,220,242,264,286,308,330,352,374,396,418,440],"limited":true,"sellStatus":3,"currentAmount":20,"baseTicketUnitId":null,"thirdTicketUnitId":16905303,"thirdBaseTicketUnitId":0},{"ticketUnitId":"216","showId":"48","name":null,"ticketName":"","minBuyLimit":1,"maxBuyLimit":6,"needRealName":false,"realNameLimit":0,"ticketLevel":"A","rgb":"#FF8014","ticketPrice":"30","setNumber":1,"sellPriceList":[33,66,99,132,165,198,231,264,297,330,363,396,429,462,495,528,561,594,627,660],"limited":true,"sellStatus":3,"currentAmount":20,"baseTicketUnitId":null,"thirdTicketUnitId":16905304,"thirdBaseTicketUnitId":0},{"ticketUnitId":"217","showId":"48","name":null,"ticketName":"","minBuyLimit":1,"maxBuyLimit":6,"needRealName":false,"realNameLimit":0,"ticketLevel":"B","rgb":"#FFD70E","ticketPrice":"40","setNumber":1,"sellPriceList":[44,88,132,176,220,264,308,352,396,440,484,528,572,616,660,704,748,792,836,880],"limited":true,"sellStatus":3,"currentAmount":20,"baseTicketUnitId":null,"thirdTicketUnitId":16905305,"thirdBaseTicketUnitId":0},{"ticketUnitId":"218","showId":"48","name":null,"ticketName":"","minBuyLimit":1,"maxBuyLimit":6,"needRealName":false,"realNameLimit":0,"ticketLevel":"C","rgb":"#5A84FF","ticketPrice":"50","setNumber":1,"sellPriceList":[55,110,165,220,275,330,385,440,495,550,605,660,715,770,825,880,935,990,1045,1100],"limited":true,"sellStatus":3,"currentAmount":20,"baseTicketUnitId":null,"thirdTicketUnitId":16905306,"thirdBaseTicketUnitId":0},{"ticketUnitId":"219","showId":"48","name":"","ticketName":"","minBuyLimit":1,"maxBuyLimit":3,"needRealName":false,"realNameLimit":0,"ticketLevel":"VVIP","rgb":"#FF4242","ticketPrice":"10","setNumber":2,"sellPriceList":[20,40,60,80,99,119,139,159,179,198,218,238,258,278,297,317,337,357,377,396],"limited":true,"sellStatus":3,"currentAmount":20,"baseTicketUnitId":"214","thirdTicketUnitId":16905478,"thirdBaseTicketUnitId":16905302}]}}
INFO - 2025-07-07 13:33:54,238 - assertion_utils.py:35 -[assertion_utils:status_code_assert] - 状态码断言成功：接口实际返回状态码 200 == 200
INFO - 2025-07-07 13:33:54,240 - assertion_utils.py:94 -[assertion_utils:equal_assert] - 相等断言成功：接口实际结果 {'code': 0} == 预期结果：{'code': 0}
INFO - 2025-07-07 13:33:54,241 - assertion_utils.py:206 -[assertion_utils:assert_result] - 测试成功
INFO - 2025-07-07 13:33:54,256 - conftest.py:9 -[conftest:print_info] - ---------------接口测试结束---------------
INFO - 2025-07-07 13:33:55,786 - conftest.py:7 -[conftest:print_info] - ---------------接口测试开始---------------
INFO - 2025-07-07 13:33:55,805 - sendrequests.py:57 -[sendrequests:execute_api_request] - 接口名称：根据场次获取取票方式
INFO - 2025-07-07 13:33:55,805 - sendrequests.py:58 -[sendrequests:execute_api_request] - 请求地址：http://*************/api/show/info/show_fetch_ticket_ways
INFO - 2025-07-07 13:33:55,805 - sendrequests.py:59 -[sendrequests:execute_api_request] - 请求方式：GET
INFO - 2025-07-07 13:33:55,805 - sendrequests.py:60 -[sendrequests:execute_api_request] - 请求头：{'Content-Type': 'application/x-www-form-urlencoded', 'Connection': 'Keep-Alive', 'Accept-Encoding': 'gzip'}
INFO - 2025-07-07 13:33:55,805 - sendrequests.py:61 -[sendrequests:execute_api_request] - 测试用例名：获取该场次的取票方式
INFO - 2025-07-07 13:33:55,806 - sendrequests.py:62 -[sendrequests:execute_api_request] - cookies值：None
INFO - 2025-07-07 13:33:55,806 - sendrequests.py:67 -[sendrequests:execute_api_request] - 参数类型：params
INFO - 2025-07-07 13:33:55,806 - sendrequests.py:69 -[sendrequests:execute_api_request] - 请求参数：{"showId": "48", "channelCode": "*********", "token": "lrDqVLEc8jnKaORIKiJB+y6nN1Io3lnKNueVwmjECnPs3mUEcTEbKdyma0pUIUY8s2ItjSU2QLLYFxywf0Grw9Hfdp3svbaytqb6Sc0oYJ/tO8wd56XQTybpwRkS8Qt8,*********"}
INFO - 2025-07-07 13:33:56,801 - apiutils_business.py:179 -[apiutils_business:execute_test_cases] - 接口实际返回结果：{"code":0,"msg":"","data":{"fetchTicketWays":[{"fetchTicketWayId":4386733,"showId":"48","fetchType":5,"needIdCard":false,"tips":"","postage":"0","onTime":"2025-03-13 14:21:40","offTime":"2025-12-01 00:00:00","fetchAddress":null,"contactMobile":null,"fetchTime":null,"feePayType":0,"feeType":0}]}}
INFO - 2025-07-07 13:33:56,808 - assertion_utils.py:35 -[assertion_utils:status_code_assert] - 状态码断言成功：接口实际返回状态码 200 == 200
INFO - 2025-07-07 13:33:56,809 - assertion_utils.py:94 -[assertion_utils:equal_assert] - 相等断言成功：接口实际结果 {'code': 0} == 预期结果：{'code': 0}
INFO - 2025-07-07 13:33:56,809 - assertion_utils.py:206 -[assertion_utils:assert_result] - 测试成功
INFO - 2025-07-07 13:33:56,827 - conftest.py:9 -[conftest:print_info] - ---------------接口测试结束---------------
INFO - 2025-07-07 13:33:58,345 - conftest.py:7 -[conftest:print_info] - ---------------接口测试开始---------------
INFO - 2025-07-07 13:33:58,381 - sendrequests.py:57 -[sendrequests:execute_api_request] - 接口名称：下单
INFO - 2025-07-07 13:33:58,381 - sendrequests.py:58 -[sendrequests:execute_api_request] - 请求地址：http://*************/api/show/order/create
INFO - 2025-07-07 13:33:58,381 - sendrequests.py:59 -[sendrequests:execute_api_request] - 请求方式：POST
INFO - 2025-07-07 13:33:58,381 - sendrequests.py:60 -[sendrequests:execute_api_request] - 请求头：{'Content-Type': 'application/x-www-form-urlencoded', 'Connection': 'Keep-Alive', 'Accept-Encoding': 'gzip'}
INFO - 2025-07-07 13:33:58,382 - sendrequests.py:61 -[sendrequests:execute_api_request] - 测试用例名：创建订单
INFO - 2025-07-07 13:33:58,382 - sendrequests.py:62 -[sendrequests:execute_api_request] - cookies值：None
INFO - 2025-07-07 13:33:58,382 - sendrequests.py:67 -[sendrequests:execute_api_request] - 参数类型：data
INFO - 2025-07-07 13:33:58,382 - sendrequests.py:69 -[sendrequests:execute_api_request] - 请求参数：{"recipientMobile": "19877292898", "recipientName": "购票人", "recipientIdNo": "", "fetchTicketWayId": 4386733, "recipientAddressId": "", "fetchTicketWayType": 5, "performanceId": "26", "showId": "48", "salesPlanId": "214", "salesPlanCount": "1", "totalTicketPrice": 11, "deliveryPrice": "0", "orderPrice": 11, "realNameIds": "", "seatRequest": "", "channelCode": "*********", "merchant_code": "", "token": "lrDqVLEc8jnKaORIKiJB+y6nN1Io3lnKNueVwmjECnPs3mUEcTEbKdyma0pUIUY8s2ItjSU2QLLYFxywf0Grw9Hfdp3svbaytqb6Sc0oYJ/tO8wd56XQTybpwRkS8Qt8,*********"}
INFO - 2025-07-07 13:34:00,034 - apiutils_business.py:179 -[apiutils_business:execute_test_cases] - 接口实际返回结果：{"code":0,"msg":"","data":{"orderId":"2025070713341762949"}}
INFO - 2025-07-07 13:34:00,040 - assertion_utils.py:35 -[assertion_utils:status_code_assert] - 状态码断言成功：接口实际返回状态码 200 == 200
INFO - 2025-07-07 13:34:00,041 - assertion_utils.py:94 -[assertion_utils:equal_assert] - 相等断言成功：接口实际结果 {'code': 0} == 预期结果：{'code': 0}
INFO - 2025-07-07 13:34:00,041 - assertion_utils.py:206 -[assertion_utils:assert_result] - 测试成功
INFO - 2025-07-07 13:34:00,058 - conftest.py:9 -[conftest:print_info] - ---------------接口测试结束---------------
INFO - 2025-07-07 13:34:01,595 - conftest.py:7 -[conftest:print_info] - ---------------接口测试开始---------------
INFO - 2025-07-07 13:34:01,606 - sendrequests.py:57 -[sendrequests:execute_api_request] - 接口名称：取消订单
INFO - 2025-07-07 13:34:01,606 - sendrequests.py:58 -[sendrequests:execute_api_request] - 请求地址：http://*************/api/show/order/cancel
INFO - 2025-07-07 13:34:01,606 - sendrequests.py:59 -[sendrequests:execute_api_request] - 请求方式：POST
INFO - 2025-07-07 13:34:01,606 - sendrequests.py:60 -[sendrequests:execute_api_request] - 请求头：{'Content-Type': 'application/x-www-form-urlencoded', 'Connection': 'Keep-Alive', 'Accept-Encoding': 'gzip'}
INFO - 2025-07-07 13:34:01,606 - sendrequests.py:61 -[sendrequests:execute_api_request] - 测试用例名：取消订单
INFO - 2025-07-07 13:34:01,606 - sendrequests.py:62 -[sendrequests:execute_api_request] - cookies值：None
INFO - 2025-07-07 13:34:01,606 - sendrequests.py:67 -[sendrequests:execute_api_request] - 参数类型：data
INFO - 2025-07-07 13:34:01,607 - sendrequests.py:69 -[sendrequests:execute_api_request] - 请求参数：{"orderId": "2025070713341762949", "channelCode": "*********", "token": "lrDqVLEc8jnKaORIKiJB+y6nN1Io3lnKNueVwmjECnPs3mUEcTEbKdyma0pUIUY8s2ItjSU2QLLYFxywf0Grw9Hfdp3svbaytqb6Sc0oYJ/tO8wd56XQTybpwRkS8Qt8,*********"}
INFO - 2025-07-07 13:34:02,852 - apiutils_business.py:179 -[apiutils_business:execute_test_cases] - 接口实际返回结果：{"code":0,"msg":"","data":[]}
INFO - 2025-07-07 13:34:02,859 - assertion_utils.py:35 -[assertion_utils:status_code_assert] - 状态码断言成功：接口实际返回状态码 200 == 200
INFO - 2025-07-07 13:34:02,859 - assertion_utils.py:94 -[assertion_utils:equal_assert] - 相等断言成功：接口实际结果 {'code': 0} == 预期结果：{'code': 0}
INFO - 2025-07-07 13:34:02,859 - assertion_utils.py:206 -[assertion_utils:assert_result] - 测试成功
INFO - 2025-07-07 13:34:02,869 - conftest.py:9 -[conftest:print_info] - ---------------接口测试结束---------------
INFO - 2025-07-07 13:34:04,382 - conftest.py:7 -[conftest:print_info] - ---------------接口测试开始---------------
INFO - 2025-07-07 13:34:04,386 - sendrequests.py:57 -[sendrequests:execute_api_request] - 接口名称：查询影讯接口
INFO - 2025-07-07 13:34:04,387 - sendrequests.py:58 -[sendrequests:execute_api_request] - 请求地址：http://*************:40/proxy/cnews/article/list
INFO - 2025-07-07 13:34:04,387 - sendrequests.py:59 -[sendrequests:execute_api_request] - 请求方式：POST
INFO - 2025-07-07 13:34:04,388 - sendrequests.py:60 -[sendrequests:execute_api_request] - 请求头：None
INFO - 2025-07-07 13:34:04,388 - sendrequests.py:61 -[sendrequests:execute_api_request] - 测试用例名：查询上架状态的影讯
INFO - 2025-07-07 13:34:04,388 - sendrequests.py:62 -[sendrequests:execute_api_request] - cookies值：None
INFO - 2025-07-07 13:34:04,388 - sendrequests.py:67 -[sendrequests:execute_api_request] - 参数类型：data
INFO - 2025-07-07 13:34:04,388 - sendrequests.py:69 -[sendrequests:execute_api_request] - 请求参数："pagination=%7B%22pageNumber%22%3A1%2C%22pageSize%22%3A50%2C%22queryParams%22%3A%7B%22title%22%3A%22%22%2C%22source%22%3A%22%22%2C%22tag_id%22%3A%22%22%2C%22status%22%3A%221%22%2C%22istop%22%3A%22%22%2C%22film_name%22%3A%22%22%2C%22startintime%22%3A%22%22%2C%22endinttime%22%3A%22%22%2C%22showtype%22%3A%22%22%2C%22startshowtime%22%3A%22%22%2C%22endshowttime%22%3A%22%22%2C%22min_program_video_status%22%3A%22%22%2C%22isplat%22%3A0%7D%7D"
INFO - 2025-07-07 13:34:04,728 - apiutils_business.py:179 -[apiutils_business:execute_test_cases] - 接口实际返回结果：{"code":0,"msg":"操作成功","data":{"totalCount":"111","records":[{"id":292,"title":"看一下资讯缩进1","author":"11","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":579,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2023\/09\/18\/6507a64bd42b1\/o.png"},"showtime":"2023-09-18 09:21:00","uptime":"2025-06-27 14:42:31","intime":"2023-09-18 09:22:24","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":1,"ui_type":"文章","type":0,"min_program_video_status":0},{"id":291,"title":"看一下文章缩进","author":"测试","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":578,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2023\/09\/18\/6507a5c183a72\/o.jpg"},"showtime":"2023-09-18 09:19:00","uptime":"2025-06-27 14:41:44","intime":"2023-09-18 09:20:11","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":2,"ui_type":"文章","type":0,"min_program_video_status":0},{"id":299,"title":"NS2","author":"百度","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":591,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2025\/06\/19\/6853c4adeca12\/o.jpg"},"showtime":"2025-06-19 16:04:00","uptime":"2025-06-19 16:05:20","intime":"2025-06-19 16:05:20","status":1,"ui_status":"上架","film_code":"001X02212020","film_name":"图兰朵：魔咒缘起","isplat":0,"showtype":1,"ui_type":"文章","type":0,"min_program_video_status":0},{"id":277,"title":"测试通看播放按钮不显示","author":"测试12121","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":588,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2025\/04\/11\/67f8e2be2112b\/o.jpg"},"showtime":"2022-07-29 09:45:00","uptime":"2025-04-11 17:47:32","intime":"2022-07-29 09:47:49","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":2,"ui_type":"小视频","type":1,"min_program_video_status":0},{"id":276,"title":"文章类","author":"luowt","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":589,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2025\/04\/11\/67f8e4a211f3d\/o.jpg"},"showtime":"2022-07-29 09:45:00","uptime":"2025-04-11 17:45:06","intime":"2022-07-29 09:46:15","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":1,"ui_type":"文章","type":0,"min_program_video_status":0},{"id":295,"title":"测试云点播上传","author":"liean","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":583,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2024\/04\/24\/66286143f28be\/o.jpg"},"showtime":"2024-04-24 09:30:00","uptime":"2024-10-22 10:58:25","intime":"2024-04-24 09:33:14","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":1,"ui_type":"小视频","type":1,"min_program_video_status":1},{"id":248,"title":"华纳发布新片片花混剪","author":"中国电影通1","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":525,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2022\/07\/18\/62d5061ab8754\/o.png"},"showtime":"2021-01-28 10:17:00","uptime":"2024-01-25 14:43:59","intime":"2021-01-28 10:19:58","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":1,"ui_type":"小视频","type":1,"min_program_video_status":0},{"id":294,"title":"智桐反馈资讯问题","author":"测试","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":582,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2023\/09\/18\/6507a7cb1e6fb\/o.gif"},"showtime":"2023-09-18 09:27:00","uptime":"2023-11-24 15:39:09","intime":"2023-09-18 09:28:44","status":1,"ui_status":"上架","film_code":"001X04052021","film_name":"金手指","isplat":0,"showtype":2,"ui_type":"文章","type":0,"min_program_video_status":0},{"id":293,"title":"智桐周末反馈问题1","author":"测试","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":580,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2023\/09\/18\/6507a6ebdec5a\/o.png"},"showtime":"2023-09-18 09:24:00","uptime":"2023-09-18 09:27:40","intime":"2023-09-18 09:25:08","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":2,"ui_type":"文章","type":0,"min_program_video_status":0},{"id":260,"title":"type=2","author":"dd","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":540,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2022\/07\/18\/62d530a7801eb\/o.png"},"showtime":"2021-04-25 10:58:00","uptime":"2023-09-18 09:18:32","intime":"2021-04-25 10:52:29","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":2,"ui_type":"文章","type":0,"min_program_video_status":0},{"id":287,"title":"写潘多拉传奇！","author":"中影电影通","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":574,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2022\/11\/25\/638066dde28eb\/o.png"},"showtime":"2022-11-25 14:54:00","uptime":"2022-11-30 11:23:04","intime":"2022-11-25 14:55:28","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":2,"ui_type":"小视频","type":1,"min_program_video_status":1},{"id":288,"title":"主题曲《无名英雄》MV，由周笔畅深情献声致敬生活中默默无闻的平凡人。凡人微光，星火成炬！","author":"中国电影","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":575,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2022\/11\/25\/638067df3606f\/o.jpeg"},"showtime":"2022-11-25 14:59:00","uptime":"2022-11-25 14:59:46","intime":"2022-11-25 14:59:46","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":2,"ui_type":"小视频","type":1,"min_program_video_status":1},{"id":285,"title":"七十一岁的老鱼塘淤泥太深 不过今天挖机没陷车 确把公鸡陷得很深","author":"@棒哥带你开挖机确把公鸡陷得很","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":572,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2022\/11\/25\/6380634e3f558\/o.png"},"showtime":"2022-11-25 14:39:00","uptime":"2022-11-25 14:41:54","intime":"2022-11-25 14:40:20","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":2,"ui_type":"小视频","type":1,"min_program_video_status":0},{"id":284,"title":"你可以怀疑","author":"@one_live","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":571,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2022\/11\/25\/63805fe81244f\/o.png"},"showtime":"2022-11-25 14:24:00","uptime":"2022-11-25 14:26:42","intime":"2022-11-25 14:25:59","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":2,"ui_type":"小视频","type":1,"min_program_video_status":0},{"id":283,"title":"高清重温：邓卓翔世界波，国足破32年恐韩症！","author":"@尚足球","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":570,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2022\/11\/25\/6380583ac513b\/o.png"},"showtime":"2022-11-25 13:52:00","uptime":"2022-11-25 13:54:11","intime":"2022-11-25 13:53:03","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":2,"ui_type":"小视频","type":1,"min_program_video_status":0},{"id":282,"title":"李云龙成功伏击鬼子观摩团，全歼200名鬼子军官!","author":"@特务兔说剧","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":569,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2022\/11\/25\/63803b694352f\/o.png"},"showtime":"2022-11-25 11:49:00","uptime":"2022-11-25 11:50:07","intime":"2022-11-25 11:50:07","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":2,"ui_type":"小视频","type":1,"min_program_video_status":0},{"id":281,"title":"跌宕起伏！C罗点射创纪录！葡萄牙3比2加纳取开门红","author":"@小鱼人足球","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":568,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2022\/11\/25\/63803a7390a4b\/o.jpg"},"showtime":"2022-11-24 11:45:00","uptime":"2022-11-25 11:47:03","intime":"2022-11-25 11:47:03","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":2,"ui_type":"小视频","type":1,"min_program_video_status":0},{"id":280,"title":"test-logo","author":"seif","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":567,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2022\/08\/16\/62fb3000c40df\/o.jpeg"},"showtime":"2022-08-16 13:46:00","uptime":"2022-08-16 13:50:34","intime":"2022-08-16 13:50:34","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":1,"ui_type":"小视频","type":1,"min_program_video_status":1},{"id":278,"title":"视频","author":"luowt","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":565,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2022\/07\/29\/62e33ea6b2740\/o.jpg"},"showtime":"2022-07-29 09:57:00","uptime":"2022-08-16 13:45:59","intime":"2022-07-29 09:58:20","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":1,"ui_type":"小视频","type":1,"min_program_video_status":1},{"id":279,"title":"GIF","author":"luowt","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":566,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2022\/07\/29\/62e33fca0b1a5\/o.gif"},"showtime":"2022-07-29 10:01:00","uptime":"2022-07-29 10:02:56","intime":"2022-07-29 10:02:56","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":1,"ui_type":"小视频","type":1,"min_program_video_status":0},{"id":247,"title":"测试数据","author":"测试数据","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":561,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2022\/07\/28\/62e2077f83eba\/o.png"},"showtime":"2021-01-28 09:56:00","uptime":"2022-07-28 11:50:24","intime":"2021-01-28 09:56:50","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":1,"ui_type":"小视频","type":1,"min_program_video_status":0},{"id":245,"title":"压缩测试","author":"aa","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":560,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2022\/07\/28\/62e2075cabcb2\/o.png"},"showtime":"2021-01-27 18:42:00","uptime":"2022-07-28 11:49:49","intime":"2021-01-27 18:44:05","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":2,"ui_type":"小视频","type":1,"min_program_video_status":0},{"id":271,"title":"深圳晚霞","author":"luowt","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":559,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2022\/07\/28\/62e206e09f4d3\/o.png"},"showtime":"2022-07-15 15:14:00","uptime":"2022-07-28 11:47:45","intime":"2022-07-15 15:18:27","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":1,"ui_type":"小视频","type":1,"min_program_video_status":0},{"id":261,"title":"辣妹又辣了辣妹又辣了辣妹又辣了辣妹又辣了辣妹又辣了辣妹又辣了辣妹又辣了辣妹又辣了辣妹又辣了","author":"luowt","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":548,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2022\/07\/20\/62d79aeca8720\/o.jpg"},"showtime":"2021-07-07 17:01:00","uptime":"2022-07-22 09:54:53","intime":"2021-07-07 17:02:32","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":3,"ui_type":"小视频","type":1,"min_program_video_status":0},{"id":235,"title":"《疯狂原始人2》发布中文配音版预告","author":"中影电影通","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":539,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2022\/07\/18\/62d51fe1c5c6d\/o.png"},"showtime":"2020-11-13 13:50:00","uptime":"2022-07-18 18:07:31","intime":"2020-11-13 13:55:06","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":1,"ui_type":"文章","type":0,"min_program_video_status":0},{"id":236,"title":"《疯狂原始人2》发布中文配音版预告","author":"中影电影通","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":537,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2022\/07\/18\/62d506b230614\/o.png"},"showtime":"2020-11-13 13:55:00","uptime":"2022-07-18 15:07:31","intime":"2020-11-13 13:56:15","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":1,"ui_type":"小视频","type":1,"min_program_video_status":0},{"id":237,"title":"《古董局中局》曝光先导预告","author":"中影电影通","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":536,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2022\/07\/18\/62d5068c1bd56\/o.png"},"showtime":"2020-11-23 13:36:00","uptime":"2022-07-18 15:06:57","intime":"2020-11-23 13:39:28","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":1,"ui_type":"小视频","type":1,"min_program_video_status":0},{"id":238,"title":"《外太空的莫扎特》曝光概念海报","author":"中影电影通","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":535,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2022\/07\/18\/62d5068212d25\/o.png"},"showtime":"2020-11-23 13:39:00","uptime":"2022-07-18 15:06:42","intime":"2020-11-23 13:40:50","status":1,"ui_status":"上架","film_code":"001X03752019","film_name":"征途","isplat":0,"showtype":2,"ui_type":"小视频","type":1,"min_program_video_status":0},{"id":239,"title":"范娟测试1123-1","author":"范娟","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":534,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2022\/07\/18\/62d5067691601\/o.png"},"showtime":"2020-11-23 15:46:00","uptime":"2022-07-18 15:06:31","intime":"2020-11-23 15:44:03","status":1,"ui_status":"上架","film_code":"001X02042019","film_name":"悟空奇遇记","isplat":0,"showtype":1,"ui_type":"小视频","type":1,"min_program_video_status":0},{"id":240,"title":"范娟测试1126-1","author":"范娟","tag_title":"test","tag_id":17,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":533,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2022\/07\/18\/62d506669b96f\/o.png"},"showtime":"2020-11-26 11:44:00","uptime":"2022-07-18 15:06:15","intime":"2020-11-26 11:44:50","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":2,"ui_type":"小视频","type":1,"min_program_video_status":0},{"id":242,"title":"fj-12.10短视频","author":"范娟","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":532,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2022\/07\/18\/62d50657a543c\/o.png"},"showtime":"2020-12-10 14:30:00","uptime":"2022-07-18 15:06:00","intime":"2020-12-10 14:27:56","status":1,"ui_status":"上架","film_code":"001X05262020","film_name":"送你一朵小红花","isplat":0,"showtype":1,"ui_type":"小视频","type":1,"min_program_video_status":0},{"id":243,"title":"xxxxx","author":"xxxx","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":531,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2022\/07\/18\/62d5064cdc5b5\/o.png"},"showtime":"2021-01-11 15:51:00","uptime":"2022-07-18 15:05:49","intime":"2021-01-11 15:52:05","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":2,"ui_type":"小视频","type":1,"min_program_video_status":0},{"id":246,"title":"360分辨率","author":"11","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":527,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2022\/07\/18\/62d5063129a25\/o.png"},"showtime":"2021-01-27 18:51:00","uptime":"2022-07-18 15:05:21","intime":"2021-01-27 18:52:56","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":2,"ui_type":"小视频","type":1,"min_program_video_status":0},{"id":274,"title":"上班那点小事type=1","author":"zkf","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":524,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2022\/07\/18\/62d5011d5278d\/o.png"},"showtime":"2022-07-18 14:42:00","uptime":"2022-07-18 14:44:55","intime":"2022-07-18 14:44:55","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":1,"ui_type":"小视频","type":1,"min_program_video_status":0},{"id":273,"title":"type=1","author":"中国电影","tag_title":"幕后","tag_id":4,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":523,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2022\/07\/18\/62d4ff5d004fe\/o.jpg"},"showtime":"2022-07-18 14:35:00","uptime":"2022-07-18 14:36:17","intime":"2022-07-18 14:36:17","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":1,"ui_type":"文章","type":0,"min_program_video_status":0},{"id":258,"title":"showtype=3","author":"范娟","tag_title":"幕后","tag_id":4,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":518,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2022\/07\/18\/62d4fc939b274\/o.jpg"},"showtime":"2021-04-23 12:05:00","uptime":"2022-07-18 14:34:19","intime":"2021-04-23 11:59:55","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":3,"ui_type":"文章","type":0,"min_program_video_status":0},{"id":263,"title":"张同学幕后团队","author":"张同学","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":507,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2022\/07\/15\/62d103f1bcf47\/o.jpeg"},"showtime":"2021-11-30 14:48:00","uptime":"2022-07-15 14:06:46","intime":"2021-11-30 14:50:06","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":1,"ui_type":"文章","type":0,"min_program_video_status":0},{"id":270,"title":"测试原图1.2M-liugx","author":"刘庚鑫","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":503,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2022\/07\/05\/62c400711f272\/o.jpeg"},"showtime":"2022-07-05 16:38:00","uptime":"2022-07-05 17:12:18","intime":"2022-07-05 17:02:07","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":1,"ui_type":"文章","type":0,"min_program_video_status":0},{"id":269,"title":"lance小视频","author":"lance","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":496,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2022\/01\/10\/61dbab2e749c3\/o.gif"},"showtime":"2022-01-10 11:41:00","uptime":"2022-01-10 11:46:06","intime":"2022-01-10 11:43:37","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":1,"ui_type":"小视频","type":1,"min_program_video_status":0},{"id":232,"title":"范娟测试","author":"范娟","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":390,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2020\/10\/29\/5f9a368e09299\/o.png"},"showtime":"2020-10-29 11:29:00","uptime":"2020-11-13 10:29:05","intime":"2020-10-29 11:27:12","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":2,"ui_type":"文章","type":0,"min_program_video_status":0},{"id":223,"title":"建行H5","author":"范德萨","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":389,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2020\/10\/29\/5f9a3611f093e\/o.png"},"showtime":"2020-10-15 10:08:00","uptime":"2020-10-29 11:25:19","intime":"2020-10-15 10:10:38","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":1,"ui_type":"文章","type":0,"min_program_video_status":0},{"id":224,"title":"测试视频10.22","author":"测试","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":378,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2020\/10\/22\/5f913f651a4f5\/o.png"},"showtime":"2020-10-22 11:07:00","uptime":"2020-10-23 13:50:31","intime":"2020-10-22 11:10:43","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":1,"ui_type":"文章","type":0,"min_program_video_status":0},{"id":222,"title":"范娟-短视频","author":"范娟","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":376,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2020\/10\/15\/5f8813933246e\/o.png"},"showtime":"2020-10-14 09:37:00","uptime":"2020-10-16 11:13:17","intime":"2020-10-15 09:37:07","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":1,"ui_type":"文章","type":0,"min_program_video_status":0},{"id":221,"title":"范娟-纯文字","author":"范娟","tag_title":"推荐","tag_id":3,"istop":1,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":364,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2020\/10\/15\/5f87a68067430\/o.png"},"showtime":"2020-10-14 09:33:00","uptime":"2020-10-15 09:31:52","intime":"2020-10-15 09:31:52","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":1,"ui_type":"文章","type":0,"min_program_video_status":0},{"id":296,"title":"测试测试","author":"呵呵","tag_title":"推荐","tag_id":3,"istop":0,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":584,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2024\/10\/17\/671072cb02393\/o.jpg"},"showtime":"2024-10-17 10:10:00","uptime":"2024-10-17 10:14:06","intime":"2024-10-17 10:14:06","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":2,"ui_type":"小视频","type":1,"min_program_video_status":1},{"id":289,"title":"这是视频标题","author":"丹丹","tag_title":"推荐","tag_id":3,"istop":0,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":576,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2022\/11\/29\/63857ecde065c\/o.png"},"showtime":"2022-11-29 11:34:00","uptime":"2022-11-29 11:39:27","intime":"2022-11-29 11:39:27","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":1,"ui_type":"小视频","type":1,"min_program_video_status":1},{"id":275,"title":"测试播放按钮是否显示","author":"测试12121","tag_title":"推荐","tag_id":3,"istop":0,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":562,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2022\/07\/29\/62e33bc2d2560\/o.jpg"},"showtime":"2022-07-29 09:44:00","uptime":"2022-07-29 09:45:47","intime":"2022-07-29 09:45:47","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":2,"ui_type":"文章","type":0,"min_program_video_status":0},{"id":272,"title":"showtype=1","author":"中国电影","tag_title":"推荐","tag_id":3,"istop":0,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":521,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2022\/07\/18\/62d4fe866fe15\/o.jpg"},"showtime":"2022-07-18 14:27:00","uptime":"2022-07-18 14:32:47","intime":"2022-07-18 14:32:47","status":1,"ui_status":"上架","film_code":null,"film_name":null,"isplat":0,"showtype":1,"ui_type":"文章","type":0,"min_program_video_status":0},{"id":267,"title":"测试GIF-0105","author":"测试GIF-0105","tag_title":"推荐","tag_id":3,"istop":0,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":472,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2022\/01\/05\/61d536d46cc0f\/o.gif"},"showtime":"2022-01-05 14:11:00","uptime":"2022-01-07 10:25:45","intime":"2022-01-05 14:12:37","status":1,"ui_status":"上架","film_code":"001X05442020","film_name":"村里来了个洋媳妇","isplat":0,"showtype":2,"ui_type":"文章","type":0,"min_program_video_status":0},{"id":266,"title":"tea.0102","author":"tea.0102","tag_title":"推荐","tag_id":3,"istop":0,"source":0,"ui_source":"原创","originaltitle":null,"originalurl":null,"coverimg":{"id":464,"url":"http:\/\/*************:11280\/cnews\/upload\/base\/2022\/01\/04\/61d3a5c58bac3\/o.gif"},"showtime":"2022-01-04 09:40:00","uptime":"2022-01-04 09:42:01","intime":"2022-01-04 09:42:01","status":1,"ui_status":"上架","film_code":"001X04142021","film_name":"农民院士","isplat":0,"showtype":2,"ui_type":"小视频","type":1,"min_program_video_status":0}]}}
INFO - 2025-07-07 13:34:04,741 - assertion_utils.py:35 -[assertion_utils:status_code_assert] - 状态码断言成功：接口实际返回状态码 200 == 200
INFO - 2025-07-07 13:34:04,744 - assertion_utils.py:94 -[assertion_utils:equal_assert] - 相等断言成功：接口实际结果 {'code': 0} == 预期结果：{'code': 0}
INFO - 2025-07-07 13:34:04,748 - assertion_utils.py:94 -[assertion_utils:equal_assert] - 相等断言成功：接口实际结果 {'msg': '操作成功'} == 预期结果：{'msg': '操作成功'}
INFO - 2025-07-07 13:34:04,749 - assertion_utils.py:206 -[assertion_utils:assert_result] - 测试成功
INFO - 2025-07-07 13:34:04,764 - conftest.py:9 -[conftest:print_info] - ---------------接口测试结束---------------
INFO - 2025-07-07 13:34:05,528 - conftest.py:201 -[conftest:pytest_terminal_summary] - 
    测试环境！！！！！
    详情请查看附件~~~
    自动化测试结果简单汇总，通知如下，执行结果如下：
    测试用例总数：14  
    测试用例通过数：14
    通过率：100%
    测试用例失败数：0
    失败率：0%
    错误数量：0
    错误率：0%       
    跳过执行数量：0
    执行总时长：30.74s (0:00:30)
    
INFO - 2025-07-07 13:34:05,534 - run.py:78 -[run:<module>] - 生成Allure报告...
INFO - 2025-07-07 13:34:12,159 - run.py:83 -[run:<module>] - Allure报告生成成功
INFO - 2025-07-07 13:34:15,797 - run.py:94 -[run:<module>] - report.zip 已经创建在 C:\vae\python_project\zy_ApiAuto\files
INFO - 2025-07-07 13:34:21,181 - qq_email.py:54 -[qq_email:qq_email] - 邮件发送成功！
