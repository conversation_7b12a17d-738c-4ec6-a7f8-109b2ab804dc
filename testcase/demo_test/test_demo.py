# -*- coding:utf-8 -*-
"""
@Project    :zy_ApiAuto
@File       :test_demo.py
@IDE        :PyCharm
<AUTHOR>
@Date       :2025/6/20 16:53
"""
import allure
import pytest
from unit_tools.apiutils_business import RequestsBase
from unit_tools.generate_id import m_id, c_id
from unit_tools.handle_data.excel_utils import get_excel_case_data


@allure.feature(next(m_id) + '电影订票模块接口测试')
class TestFilmOrder:

    # @pytest.mark.flaky(reruns=2, reruns_delay=3)  # #重试2次，如果失败，将等待3秒后重试
    @pytest.mark.parametrize('api_info', get_excel_case_data('./testcase/demo_test/测试用例.xlsx'))
    @allure.story(next(c_id) + '电影下单流程')
    def test_film_order(self, api_info):
        allure.dynamic.title(api_info['baseInfo']['api_name'])
        RequestsBase().execute_test_cases(api_info)
